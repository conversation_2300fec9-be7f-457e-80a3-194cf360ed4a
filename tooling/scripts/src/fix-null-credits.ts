import { db } from "@repo/database/server";

async function fixNullCreditValues() {
	try {
		console.log("Fixing null credit values in the database...");

		// Get all users and their existing credit purchases
		const users = await db.user.findMany({
			select: {
				id: true,
				name: true,
				email: true,
				creditPurchases: {
					select: {
						credits: true
					}
				}
			},
		});

		console.log(`Found ${users.length} users to check and update`);

		let updatedCount = 0;
		const now = new Date();

		// Process each user
		for (const user of users) {
			try {
				// Calculate current total credits
				const currentCredits = user.creditPurchases.reduce((sum, purchase) => sum + purchase.credits, 0);

				// Only create a new purchase if user has no credits
				if (currentCredits === 0) {
					await db.creditPurchase.create({
						data: {
							userId: user.id,
							credits: 10,
							packageId: "fix-null-credits", // Identifier for script-added credits
							price: 0, // Free credits
							status: "completed",
							createdAt: now,
							updatedAt: now
						}
					});
					updatedCount++;
					console.log(`Created initial credits for ${user.name || user.email} (${currentCredits} → 10)`);
				} else {
					console.log(`Skipping ${user.name || user.email} - already has ${currentCredits} credits`);
				}
			} catch (error) {
				console.warn(`Could not update ${user.name || user.email}:`, error instanceof Error ? error.message : String(error));
			}
		}

		console.log(`Successfully created initial credits for ${updatedCount} out of ${users.length} users`);
	} catch (error) {
		console.error("Error fixing null credit values:", error);
		throw error;
	} finally {
		await db.$disconnect();
	}
}

// Run the script
if (require.main === module) {
	fixNullCreditValues()
		.then(() => {
			console.log("Null values fix completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Null values fix failed:", error);
			process.exit(1);
		});
}

export { fixNullCreditValues };
