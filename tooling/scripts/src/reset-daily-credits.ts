import { db } from "@repo/database/server";

interface ResetResult {
	user: string;
	organization: string;
	reset: boolean;
	error?: boolean;
}

async function resetDailyCredits() {
	try {
		console.log("Starting daily credit reset for all users...");

		// Get all users with their organizations and credit records
		const users = await db.user.findMany({
			select: {
				id: true,
				name: true,
				email: true,
				userOrgCredits: {
					select: {
						id: true,
						organizationId: true,
						creditsTotal: true,
						creditsUsed: true,
						creditsResetAt: true,
					},
				},
			},
		});

		console.log(`Found ${users.length} users to process`);

		if (users.length === 0) {
			console.log("No users found in the database");
			return;
		}

		const now = new Date();
		let resetCount = 0;
		let skippedCount = 0;

		// Process each user and their organizations
		const resetPromises = users.flatMap((user) =>
			user.userOrgCredits.map(async (creditRecord) => {
				try {
					// Check if user needs a reset (more than 1 day since last reset)
					const lastReset = creditRecord.creditsResetAt
						? new Date(creditRecord.creditsResetAt)
						: new Date(0);
					const daysSinceReset =
						(now.getTime() - lastReset.getTime()) /
						(1000 * 60 * 60 * 24);

					if (daysSinceReset >= 1) {
						await db.userOrganizationCredits.update({
							where: { id: creditRecord.id },
							data: {
								creditsTotal: 10,
								creditsUsed: 0,
								creditsResetAt: now,
							},
						});

						console.log(`Reset ${user.name || user.email} in organization ${creditRecord.organizationId}: ${creditRecord.creditsUsed || 0} → 0 used, 10 total`);
						resetCount++;
						return { user: user.email, organization: creditRecord.organizationId, reset: true };
					}

					console.log(`Skipped ${user.name || user.email} in organization ${creditRecord.organizationId}: Reset not needed (${Math.round(daysSinceReset * 24)}h ago)`);
					skippedCount++;
					return { user: user.email, organization: creditRecord.organizationId, reset: false };
				} catch (error) {
					console.error(`Failed to reset ${user.name || user.email} in organization ${creditRecord.organizationId}:`, error);
					return { user: user.email, organization: creditRecord.organizationId, reset: false, error: true };
				}
			}),
		);

		// Execute all resets
		const results = await Promise.all(resetPromises);

		console.log("\nDaily credit reset completed!");
		console.log(`Reset ${resetCount} user-organization pairs`);
		console.log(`Skipped ${skippedCount} user-organization pairs (not due for reset)`);
		console.log(`Failed ${results.filter((r: ResetResult) => r.error).length} user-organization pairs`);

		// Summary
		const totalReset = results.filter((r: ResetResult) => r.reset).length;
		console.log(`Total user-organization pairs reset: ${totalReset}/${results.length}`);
	} catch (error) {
		console.error("Error resetting daily credits:", error);
		throw error;
	} finally {
		await db.$disconnect();
	}
}

// Run the script
if (require.main === module) {
	resetDailyCredits()
		.then(() => {
			console.log("Daily credit reset script completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Daily credit reset script failed:", error);
			process.exit(1);
		});
}

export { resetDailyCredits };
