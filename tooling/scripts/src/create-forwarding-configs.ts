import { db } from "@repo/database/server";

async function createForwardingConfigs() {
  try {
    console.log("🔍 Finding existing organizations...");
    
    const organizations = await db.organization.findMany({
      select: { 
        id: true, 
        slug: true, 
        name: true
      }
    });
    
    console.log(`Found ${organizations.length} organizations`);
    
    if (organizations.length === 0) {
      console.log("No organizations found.");
      return;
    }
    
    console.log("\n📧 Creating forwarding email configurations...");
    
    for (const org of organizations) {
      const address = `${org.slug}@inbox.reliocrm.com`;
      
      try {
        // Check if config already exists
        const existingConfig = await db.forwardingEmailConfig.findUnique({
          where: { organizationId: org.id }
        });
        
        if (existingConfig) {
          console.log(`⏭️  ${org.slug} already has config: ${existingConfig.address}`);
          continue;
        }
        
        // Create new config
        const config = await db.forwardingEmailConfig.create({
          data: {
            organizationId: org.id,
            address: address,
            defaultSharingLevel: "full",
            blockedEmails: [],
            blockedDomains: [],
            autoCreateContacts: true,
            autoCreateCompanies: true,
            isActive: true
          }
        });
        
        console.log(`✅ ${org.slug} (${org.name}) -> ${config.address}`);
      } catch (error) {
        console.log(`❌ Failed to create config for ${org.slug}:`, error);
      }
    }
    
    console.log("\n🎉 Forwarding email configuration setup complete!");
    
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await db.$disconnect();
  }
}

createForwardingConfigs(); 