#!/usr/bin/env tsx

import { migrateUsers } from '../../../packages/database/src/migrate-users'

async function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  
  // Parse command line arguments
  const options: { limit?: number; testEmail?: string } = {}
  
  if (command === '--limit' && args[1]) {
    options.limit = parseInt(args[1], 10)
    if (isNaN(options.limit)) {
      console.error('❌ Error: --limit must be followed by a number')
      process.exit(1)
    }
  } else if (command === '--email' && args[1]) {
    options.testEmail = args[1]
  } else if (command === '--help' || command === '-h') {
    console.log('🚀 User Migration Script')
    console.log('')
    console.log('Usage:')
    console.log('  pnpm tsx tooling/scripts/src/migrate-users-from-old-db.ts [options]')
    console.log('')
    console.log('Options:')
    console.log('  --limit <number>    Migrate only the first N users (for testing)')
    console.log('  --email <email>     Migrate only the user with this email (for testing)')
    console.log('  --help, -h          Show this help message')
    console.log('')
    console.log('Examples:')
    console.log('  # Test with just 1 user')
    console.log('  pnpm tsx tooling/scripts/src/migrate-users-from-old-db.ts --limit 1')
    console.log('')
    console.log('  # Test with specific user')
    console.log('  pnpm tsx tooling/scripts/src/migrate-users-from-old-db.ts --email <EMAIL>')
    console.log('')
    console.log('  # Migrate all users')
    console.log('  pnpm tsx tooling/scripts/src/migrate-users-from-old-db.ts')
    return
  }

  console.log('🚀 Starting user migration from old PropBear database...')
  
  if (options.limit) {
    console.log(`🧪 TEST MODE: Will migrate only ${options.limit} user(s)`)
  } else if (options.testEmail) {
    console.log(`🧪 TEST MODE: Will migrate only user: ${options.testEmail}`)
  } else {
    console.log('📦 FULL MIGRATION: Will migrate all users')
  }
  
  try {
    await migrateUsers(options)
    console.log('✅ Migration completed successfully!')
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

main() 