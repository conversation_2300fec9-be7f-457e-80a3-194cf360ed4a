#!/usr/bin/env tsx

import { migrateUsers } from '../../../packages/database/src/migrate-users'

async function main() {
  console.log('🧪 TESTING: Single User Migration')
  console.log('This will migrate only the FIRST user from the JSON file')
  console.log('Perfect for testing before running the full migration!')
  console.log('')
  
  try {
    // Migrate only 1 user for testing
    await migrateUsers({ limit: 1 })
    console.log('')
    console.log('🎉 Single user test migration completed!')
    console.log('📋 Next steps:')
    console.log('  1. Check your database to verify the user was created correctly')
    console.log('  2. Verify the user has: Account, Member, and Credits records')
    console.log('  3. If everything looks good, run the full migration:')
    console.log('     pnpm tsx tooling/scripts/src/migrate-users-from-old-db.ts')
  } catch (error) {
    console.error('❌ Test migration failed:', error)
    process.exit(1)
  }
}

main() 