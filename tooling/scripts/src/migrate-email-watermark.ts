import { db } from "@repo/database/server";
import { logger } from "@repo/logs";

async function migrateEmailWatermark() {
	try {
		// Update all existing organizations to have emailWatermarkEnabled = true by default
		const result = await db.organization.updateMany({
			where: {},
			data: {
				emailWatermarkEnabled: true,
			},
		});

		logger.info(`Updated ${result.count} organizations with emailWatermarkEnabled field`);
		
		console.log(`✅ Migration completed successfully. Updated ${result.count} organizations.`);
	} catch (error) {
		logger.error("Migration failed:", error);
		console.error("❌ Migration failed:", error);
		process.exit(1);
	} finally {
		await db.$disconnect();
	}
}

// Run the migration
migrateEmailWatermark(); 