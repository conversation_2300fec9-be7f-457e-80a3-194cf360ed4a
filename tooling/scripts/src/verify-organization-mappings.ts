#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Organization mappings provided by user
const ORGANIZATION_MAPPINGS = {
  '628bff8d44cd3e01b746b737': '685d055a8ecc848e2aa4f1d9',
  '628ea3ebfeec685660394d1c': '685d09c38ecc848e2aa4f1db'
} as const

type OldTeamId = keyof typeof ORGANIZATION_MAPPINGS
type NewOrgId = typeof ORGANIZATION_MAPPINGS[OldTeamId]

async function verifyOrganizationMappings() {
  console.log('🔍 Verifying organization mappings...')
  
  for (const [oldTeamId, newOrgId] of Object.entries(ORGANIZATION_MAPPINGS)) {
    console.log(`\n📋 Checking mapping: ${oldTeamId} → ${newOrgId}`)
    
    try {
      // Check if the new organization exists
      const org = await prisma.organization.findUnique({
        where: { id: newOrgId },
        include: {
          members: {
            include: {
              user: {
                select: { name: true, email: true }
              }
            }
          },
          userOrgCredits: true
        }
      })

      if (!org) {
        console.log(`❌ Organization ${newOrgId} not found!`)
        continue
      }

      console.log(`✅ Organization: ${org.name}`)
      console.log(`   Slug: ${org.slug}`)
      console.log(`   Members: ${org.members.length}`)
      console.log(`   Credits records: ${org.userOrgCredits.length}`)
      
      // Check if metadata contains the old team ID
      let metadata: any = {}
      try {
        metadata = org.metadata ? JSON.parse(org.metadata) : {}
      } catch (e) {
        console.log(`   ⚠️  Metadata parsing failed`)
      }
      
      if (metadata.migratedFromTeamId === oldTeamId) {
        console.log(`   ✅ Correctly mapped from old team ID: ${oldTeamId}`)
      } else {
        console.log(`   ⚠️  Metadata shows different old team ID: ${metadata.migratedFromTeamId}`)
      }

      // List some members
      if (org.members.length > 0) {
        console.log(`   Members:`)
        org.members.slice(0, 5).forEach(member => {
          console.log(`     - ${member.user.name} (${member.user.email}) [${member.role}]`)
        })
        if (org.members.length > 5) {
          console.log(`     ... and ${org.members.length - 5} more`)
        }
      }

    } catch (error) {
      console.error(`❌ Error checking organization ${newOrgId}:`, error)
    }
  }
}

async function findUsersFromOldTeams() {
  console.log('\n👥 Finding users who were in these old teams...')
  
  for (const [oldTeamId, newOrgId] of Object.entries(ORGANIZATION_MAPPINGS)) {
    console.log(`\n🔍 Users from old team ${oldTeamId}:`)
    
    try {
      const members = await prisma.member.findMany({
        where: {
          organizationId: newOrgId
        },
        include: {
          user: {
            select: { name: true, email: true }
          }
        }
      })

      console.log(`   Found ${members.length} users in organization ${newOrgId}`)
      
      members.forEach(member => {
        console.log(`   - ${member.user.name} (${member.user.email}) [${member.role}]`)
      })

    } catch (error) {
      console.error(`❌ Error finding users for organization ${newOrgId}:`, error)
    }
  }
}

async function updateMissingMappings() {
  console.log('\n🔧 Checking and updating organization metadata...')
  
  for (const [oldTeamId, newOrgId] of Object.entries(ORGANIZATION_MAPPINGS)) {
    try {
      const org = await prisma.organization.findUnique({
        where: { id: newOrgId }
      })

      if (!org) {
        console.log(`❌ Organization ${newOrgId} not found, skipping`)
        continue
      }

      let metadata: any = {}
      try {
        metadata = org.metadata ? JSON.parse(org.metadata) : {}
      } catch (e) {
        metadata = {}
      }

      // Add or update the mapping
      if (!metadata.migratedFromTeamId) {
        metadata.migratedFromTeamId = oldTeamId
        
        await prisma.organization.update({
          where: { id: newOrgId },
          data: {
            metadata: JSON.stringify(metadata)
          }
        })
        
        console.log(`✅ Updated metadata for ${org.name} with old team ID: ${oldTeamId}`)
      } else if (metadata.migratedFromTeamId !== oldTeamId) {
        console.log(`⚠️  ${org.name} has different old team ID in metadata: ${metadata.migratedFromTeamId}`)
      } else {
        console.log(`✅ ${org.name} already has correct mapping`)
      }

    } catch (error) {
      console.error(`❌ Error updating organization ${newOrgId}:`, error)
    }
  }
}

async function generateMembershipReport() {
  console.log('\n📊 Generating membership report...')
  
  const report = {
    totalOrganizations: Object.keys(ORGANIZATION_MAPPINGS).length,
    organizations: [] as any[]
  }

  for (const [oldTeamId, newOrgId] of Object.entries(ORGANIZATION_MAPPINGS)) {
    try {
      const org = await prisma.organization.findUnique({
        where: { id: newOrgId },
        include: {
          members: {
            include: {
              user: {
                select: { name: true, email: true, createdAt: true }
              }
            }
          },
          userOrgCredits: true
        }
      })

      if (org) {
        const admins = org.members.filter(m => m.role === 'admin').length
        const members = org.members.filter(m => m.role === 'member').length
        
        report.organizations.push({
          name: org.name,
          oldTeamId,
          newOrgId,
          totalMembers: org.members.length,
          admins,
          members,
          creditsRecords: org.userOrgCredits.length,
          createdAt: org.createdAt
        })
      }
    } catch (error) {
      console.error(`Error generating report for ${newOrgId}:`, error)
    }
  }

  console.log('\n📋 ORGANIZATION REPORT')
  console.log('='.repeat(50))
  report.organizations.forEach(org => {
    console.log(`🏢 ${org.name}`)
    console.log(`   Old Team ID: ${org.oldTeamId}`)
    console.log(`   New Org ID: ${org.newOrgId}`)
    console.log(`   Total Members: ${org.totalMembers} (${org.admins} admins, ${org.members} members)`)
    console.log(`   Credits Records: ${org.creditsRecords}`)
    console.log(`   Created: ${org.createdAt}`)
    console.log('')
  })

  return report
}

async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'verify'

  try {
    switch (command) {
      case 'verify':
        await verifyOrganizationMappings()
        break
      
      case 'users':
        await findUsersFromOldTeams()
        break
        
      case 'update':
        await updateMissingMappings()
        break
        
      case 'report':
        await generateMembershipReport()
        break
        
      case 'all':
        await verifyOrganizationMappings()
        await findUsersFromOldTeams()
        await updateMissingMappings()
        await generateMembershipReport()
        break
        
      default:
        console.log('Available commands:')
        console.log('  verify  - Verify organization mappings exist')
        console.log('  users   - Find users from old teams')
        console.log('  update  - Update missing metadata mappings')
        console.log('  report  - Generate membership report')
        console.log('  all     - Run all commands')
        break
    }
  } catch (error) {
    console.error('❌ Command failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Export the mappings for use in other scripts
export { ORGANIZATION_MAPPINGS }

main() 