[{"_id": {"$oid": "61382e137d8edd0023c1539d"}, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "email": "<EMAIL>", "password": "$2a$10$moCCwsGCERkKXPoqs57KNe3N8/ZgPz.ykztgofPIhhtxgEXkSgXdu", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "team": [{"status": "Approved", "teamId": {"$oid": "613820e57d8edd0023c14faa"}}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "__v": 0, "updatedAt": {"$date": "2025-05-24T23:52:37.381Z"}, "isOnBoarded": true, "expireToken": "1645817746773", "resetToken": "ca890f3df44d150392bfef22e2bf976b4144f829b1e58ddcc146016d0b3afc3b", "expireTimeOfAccessToken": {"$numberLong": "1652358822690"}, "refreshToken": "*******************************************************************************************************************************************************************", "language": "en", "timeZone": "America/Los_Angeles", "lastActive": {"$date": "2025-05-24T23:52:37.381Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}, "company": "Marcus & Millichap", "phone": "************", "title": "First Vice President", "propertyColumns": ["name", "status", "type", "address", "city", "state", "zipCode", "createdAt", "updatedAt", "owner", "ownerEmail", "ownerPhone", "units", "sqft", "yearBuilt", "lat", "lng", "price", "tags"]}, {"_id": {"$oid": "629e5996634c4e258fae8714"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$HW2WIS8Pdsi0hHZtZQdjLeG6BHKICtIjA3rgpVOqGVvvkUKLdiOu.", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "629f983bbd95292c62fcc647"}, "status": "Approved"}], "createdAt": {"$date": "2022-06-06T19:46:30.752Z"}, "updatedAt": {"$date": "2024-03-08T19:14:53.773Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************.gbLZvQh_hGi_GyydRf0TRHG-OjS8ItwnT87OeJLPMQQ", "company": "", "firstName": "<PERSON>", "lastName": "Napolitano", "lastActive": {"$date": "2024-03-08T19:14:53.772Z"}, "teamActive": {"$oid": "629f983bbd95292c62fcc647"}}, {"_id": {"$oid": "63225d8c7e8b30f963244450"}, "timeZone": "America/Los_Angeles", "language": "en", "email": "<EMAIL>", "password": "$2a$10$uqUG01r7ZRqQ2bOwQXn.ZueymnbCMU6SDCY7vBl2rqNUfu7N8UcUq", "isOnBoarded": true, "profileImg": "https://propbear-img.s3.ap-south-1.amazonaws.com/general/blob-1663286847458", "team": [{"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}, {"teamId": {"$oid": "6323721a7e8b30f963245b92"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "invited"}], "createdAt": {"$date": "2022-09-14T23:02:36.012Z"}, "updatedAt": {"$date": "2025-06-25T18:28:03.284Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.69tYSYh_w5wofI9vKpWiBde2_voce-Alx9zjj1p6GG8", "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "<PERSON>", "lastActive": {"$date": "2025-06-25T18:28:03.284Z"}, "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}, "phone": "7022348208", "title": "Multi-Family Investments"}, {"_id": {"$oid": "6363f4a55acbe1bf42c33415"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$TwL/sFRZlPiOWf.NKfOks.UXYkXPsnDb30oQZDG5fj0CVje9bHe1C", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}, {"teamId": {"$oid": "63643b755acbe1bf42c3486a"}, "status": "Approved"}], "createdAt": {"$date": "2022-11-03T17:04:37.082Z"}, "updatedAt": {"$date": "2025-06-26T00:10:48.381Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.3d0WRJ-0itF9VWJoShwwedOCQ5fKI-kJtzpFfU8IJDA", "company": "<PERSON>", "firstName": "<PERSON> ", "lastName": "Avent", "lastActive": {"$date": "2025-06-26T00:10:48.381Z"}, "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}}, {"_id": {"$oid": "6385044b69b18ba8fbf402ce"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$bQqCB95x75MbkVtEqvA6X.wLiJxpOpkqUICh/0T/zbDToFcuUrxeG", "isOnBoarded": false, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "6374320ad768fcd1d5941df5"}, "status": "invited"}], "createdAt": {"$date": "2022-11-28T18:56:11.441Z"}, "updatedAt": {"$date": "2022-11-28T18:56:11.461Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.57XIC8C8Ie7HSjVQRKjop-pnr479gsaO4eKn147nbdU"}, {"_id": {"$oid": "6385046269b18ba8fbf402ea"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$ewqjlZRc87Uv5UeKnZQrfut6TRXrekoXNSa7YHfd5/EDFGjipn5BC", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "6374320ad768fcd1d5941df5"}, "status": "invited"}, {"teamId": {"$oid": "638506ea69b18ba8fbf40852"}, "status": "Approved"}], "createdAt": {"$date": "2022-11-28T18:56:34.097Z"}, "updatedAt": {"$date": "2022-11-28T22:00:43.007Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.7ib9iWm0PqkOpfa4-1nljZOCWF6dEigsk7tKAbpyZO0", "company": "<PERSON>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "lastActive": {"$date": "2022-11-28T22:00:43.007Z"}, "teamActive": {"$oid": "638506ea69b18ba8fbf40852"}}, {"_id": {"$oid": "6391c7e1614b2c49f6532cd8"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "NH", "timeZone": "Asia/Saigon", "language": "en", "email": "<EMAIL>", "company": "", "password": "$2a$10$zW3M51omabIMUT6P5ObqxOVG8VnHnGSzp2AHmoH8nJxDjzLo2kyk.", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/avatar-undefined?alt=media&token=bf0622f0-6aee-485d-b6ce-b1f96f5c6744", "team": [{"teamId": {"$oid": "6391c7e1614b2c49f6532cda"}, "status": "Approved"}, {"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}, {"teamId": {"$oid": "6280a9b38b17ba458509201c"}, "status": "Approved"}, {"teamId": {"$oid": "63aeb72ff13a642e469f99dd"}, "status": "Approved"}], "createdAt": {"$date": "2022-12-08T11:17:53.207Z"}, "updatedAt": {"$date": "2024-03-15T00:00:01.021Z"}, "__v": 2, "lastActive": {"$date": "2024-02-23T10:58:19.542Z"}, "teamActive": {"$oid": "6391c7e1614b2c49f6532cda"}, "contactColumns": ["fullName", "actions", "phone", "company", "status", "email", "address", "city", "state", "zipCode", "tags"], "propertyColumns": ["name", "type", "status", "address", "city", "state", "zipCode", "units", "sqft", "createdAt", "updatedAt", "owner", "ownerEmail", "ownerPhone", "yearBuilt", "lat", "lng", "price", "tags"], "subscription": {"id": "sub_1OXePWG33ndozF6ncqQ9umIE", "createdAt": {"$date": "2024-02-12T07:41:57.834Z"}, "expiredAt": {"$date": "2024-03-12T07:41:57.834Z"}}, "plan": "Free", "subscribed": false, "tags": []}, {"_id": {"$oid": "60e1f01a93969009be99b46d"}, "email": "<EMAIL>", "company": "Idylcode", "password": "$2a$12$5IVxP9e5vA.VoiSSVtJ2HO3QVqPDXS7Lce77d2trxEOrGef7vYpR2", "firstName": "<PERSON>", "lastName": "<PERSON>", "__v": 1, "team": [{"status": "Pending", "teamId": {"$oid": "612e8da42910d100230d79c9"}}, {"status": "Approved", "teamId": {"$oid": "61f4b8a2fecc1b0b4722f4d4"}}, {"status": "Approved", "teamId": {"$oid": "61f5ae27eabe7672054ad58b"}}, {"teamId": {"$oid": "6280a9b38b17ba458509201c"}, "status": "Approved"}, {"teamId": {"$oid": "628a25d09dbf14c844456194"}, "status": "Approved"}, {"teamId": {"$oid": "628b101a2c5d69d63fc8409b"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}, {"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}, {"teamId": {"$oid": "628eb3cefeec685660397d86"}, "status": "Approved"}, {"teamId": {"$oid": "62963dd2673415c6c7b9ed6e"}, "status": "Approved"}, {"teamId": {"$oid": "62964013673415c6c7b9eef2"}, "status": "Approved"}, {"teamId": {"$oid": "629ab229622524b170ee9a26"}, "status": "Approved"}, {"teamId": {"$oid": "629ae836e5fab6564aac23cc"}, "status": "Approved"}, {"teamId": {"$oid": "63036e4d614b2c49f65143a3"}, "status": "Approved"}, {"teamId": {"$oid": "632e3e2661fc1d7c49753b0e"}, "status": "Approved"}, {"teamId": {"$oid": "632ea968c0a47e50a19b770d"}, "status": "Approved"}, {"teamId": {"$oid": "636ae7fb076e8dadf298dacc"}, "status": "Approved"}, {"teamId": {"$oid": "6374320ad768fcd1d5941df5"}, "status": "Approved"}, {"teamId": {"$oid": "65a863d84e9439612f488d72"}, "status": "Approved"}, {"teamId": {"$oid": "65a88cd5195e30e3e37ea4eb"}, "status": "invited"}, {"teamId": {"$oid": "65a8db78b87bae4e63f176cd"}, "status": "Approved"}], "updatedAt": {"$date": "2025-06-11T17:48:13.164Z"}, "createdAt": {"$date": "2022-01-16T09:44:59.548Z"}, "expireToken": "1675378972752", "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/avatar-60e1f01a93969009be99b46d?alt=media&token=20b2668b-0f02-413b-b3e6-6cdd6c131d00", "isOnBoarded": true, "phone": "2108516204", "title": "Owner", "timeZone": "America/Los_Angeles", "language": "en", "uid": "XkUWGPKBBTa9DhG5Wy5qttFNEAG3", "isGoogleConnected": false, "isMicrosoftConnected": false, "lastActive": {"$date": "2025-06-11T17:48:13.164Z"}, "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}, "contactColumns": ["fullName", "actions", "address", "city", "status", "state", "zipCode", "phone", "email", "company", "tags"], "propertyColumns": ["name", "address", "city", "state", "zipCode", "county", "type", "units", "status", "createdAt", "tags", "updatedAt", "owner", "ownerEmail", "ownerPhone", "sqft", "yearBuilt", "lat", "lng", "price"], "resetToken": "38d771de7b8ac707cebcc8469d763d1f64c77c6235218efad39a0632cd6c95a3", "subscribed": false, "subscription": {"id": "sub_1OWiOVG33ndozF6nRSShE0jX", "createdAt": {"$date": "2024-02-09T17:45:42.949Z"}, "expiredAt": {"$date": "2024-03-09T17:45:42.949Z"}}, "hmacToken": "b93fecf5734ac27cd9bad9b546d32ec47c4ab0769933f83d9e10f8d2fedd0be6", "plan": "Free", "tags": []}, {"_id": {"$oid": "628c06a644cd3e01b746bb63"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$12$995vNKYnbiJcJ/202DHSVu4ByJ9pK5/Sw2.fjPUMfIrg8QPIxKdhC", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:11:50.492Z"}, "updatedAt": {"$date": "2025-06-24T18:01:08.875Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.uo9--o_JSjqbOmrY-RZsuYos8ufP0AvZTX_fhlAdH00", "expireToken": null, "resetToken": null, "firstName": "<PERSON>", "lastName": "<PERSON>", "lastActive": {"$date": "2025-06-24T18:01:08.875Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628c06b744cd3e01b746bb7a"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$12$iVLK0n1CGhGP/J7pi8zF/eiTrIoMl1alz6eC5XnuxFXOKvwaXgV.a", "isOnBoarded": true, "profileImg": "https://propbear-img.s3.ap-south-1.amazonaws.com/general/blob-1658353609907", "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}, {"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:12:07.790Z"}, "updatedAt": {"$date": "2025-06-24T00:40:00.223Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.F6G__ns6lZHTWjFpaDL3HCbackQjiTOOGXIVdpC7WCs", "expireToken": null, "resetToken": null, "firstName": "<PERSON>", "lastName": "Holden", "propertyColumns": ["name", "address", "city", "state", "zipCode", "units", "owner", "type", "status", "updatedAt", "createdAt", "ownerEmail", "ownerPhone", "sqft", "yearBuilt", "lat", "lng", "price", "tags"], "contactColumns": ["fullName", "actions", "phone", "email", "tags", "address", "status", "company", "city", "state", "zipCode"], "lastActive": {"$date": "2025-06-24T00:40:00.223Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628c06c344cd3e01b746bb86"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$yHL4.UqoXtCzSsGIx.5.WOgIvfFJJ.TQmS/u774dHj.xJ5ye8s5Bq", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "628d6105e7d11c9034b48939"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:12:19.953Z"}, "updatedAt": {"$date": "2024-09-16T18:16:03.033Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.VN6zRnSLV4yS0Rp_F5c6mlpqG3oFNXaBP9M69pM2NY8", "company": "", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "6265399134", "lastActive": {"$date": "2022-11-07T19:16:59.343Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "632245717e8b30f96324399b"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$VZsqUoGTs.W3CLj4BZWvhec9D8WPN6jQhXaEww/XPod8w3XVuKJie", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "632246f67e8b30f963243bf2"}, "status": "Approved"}, {"teamId": {"$oid": "6324a57cc680ea45180312ee"}, "status": "Approved"}], "createdAt": {"$date": "2022-09-14T21:19:45.547Z"}, "updatedAt": {"$date": "2024-09-16T18:16:07.630Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.3RB9jgdwAXpuGjA047rsVFh99XA9P0UpKSA3IyIPlEE", "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "Ferrier", "lastActive": {"$date": "2024-03-11T17:01:00.797Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628c068244cd3e01b746bb0f"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$TtKrXFTlBEXWKig0YrKUreuZrxPUl13H7f1S9twnoCqZ1oOwvTkfW", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "6336020d0ad0b0b976ad4c2f"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:11:14.515Z"}, "updatedAt": {"$date": "2022-09-29T20:38:53.244Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.cByTVQ3OPtSmSLYY45_cBn_CtDLnEszUGA3N7qmsNEY", "company": "", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "lastActive": {"$date": "2022-09-29T20:38:53.244Z"}, "teamActive": {"$oid": "6336020d0ad0b0b976ad4c2f"}}, {"_id": {"$oid": "628c068a44cd3e01b746bb1b"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$12$5rsYZ38B5pWcKkimh6YU0ODavRz4VoKxhrimqqn/0cupLzI5Solrm", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:11:22.312Z"}, "updatedAt": {"$date": "2025-06-26T01:02:08.208Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************.FKTWr_sOXoqEI12nE9lLfFeDDW8f9stssY3ZMsftZp8", "company": "Marcus and Millichap", "firstName": "<PERSON>", "lastName": "Tai<PERSON>", "phone": "7143624884", "expireToken": null, "resetToken": null, "lastActive": {"$date": "2025-06-26T01:02:08.207Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628c069244cd3e01b746bb27"}, "timeZone": "America/Los_Angeles", "language": "en", "email": "<EMAIL>", "password": "$2a$12$KbHlq70UntUKpuK23NJyGe5bBl0Cqyg/4YJRpaE8nE36LvDKxWPCq", "isOnBoarded": true, "profileImg": "", "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:11:30.560Z"}, "updatedAt": {"$date": "2025-06-26T01:31:55.098Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.cupDCj6e2MOOcL7H9IGBh5DLbYi0vxUmee3geXIkZfk", "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "<PERSON>", "expireToken": null, "resetToken": null, "lastActive": {"$date": "2025-06-26T01:31:55.098Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628c06e444cd3e01b746bb92"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$NcPfVgyFclcEQF7dKxuyBOsVDIeM1nAbzTrOFK9KbGTd/SnuXwwS6", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "633602360ad0b0b976ad4d49"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:12:52.351Z"}, "updatedAt": {"$date": "2022-09-29T20:39:44.853Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************.TUAQoAybcoOYsaIUJz4YXs1LtBbtojjpT77BtbPlWXQ", "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "Yeh", "lastActive": {"$date": "2022-09-29T20:39:44.853Z"}, "teamActive": {"$oid": "633602360ad0b0b976ad4d49"}}, {"_id": {"$oid": "6322457f7e8b30f9632439a7"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$12$5IVxP9e5vA.VoiSSVtJ2HO3QVqPDXS7Lce77d2trxEOrGef7vYpR2", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "63372426b376d0965851fb37"}, "status": "Approved"}], "createdAt": {"$date": "2022-09-14T21:19:59.087Z"}, "updatedAt": {"$date": "2024-01-26T18:01:37.049Z"}, "__v": 1, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.uhk-neMR1XtEeHIH3UxRS-RxtULEQzbEKxUBNU2F594", "company": "MarcusMillich<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "lastActive": {"$date": "2023-02-14T20:11:12.714Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}, "expireToken": "1675375297248", "resetToken": "e7da452c6bca8df283d02105aa33e2665c4db63dc1759fee3911f97c6e72ae7e", "contactColumns": [], "propertyColumns": []}, {"_id": {"$oid": "6334d9efe649ad2f1edecbae"}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "<PERSON>", "password": "$2a$10$60drbApuUj8kAq0owmeMJuSX.tKT.VLuKlYBep0k7ta0DPkeT.45q", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "6334d9efe649ad2f1edecbb0"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "createdAt": {"$date": "2022-09-28T23:34:07.086Z"}, "updatedAt": {"$date": "2024-03-07T22:48:41.609Z"}, "__v": 2, "lastActive": {"$date": "2024-03-07T22:48:41.609Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}, "contactColumns": [], "expireToken": "1708982995494", "plan": "Free", "propertyColumns": [], "resetToken": "f462f209605b12ff10623644be08867292615047a3b03c07daca345d0b294e50", "tags": []}, {"_id": {"$oid": "636025626b13702974981ad4"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$X.dTTby/SeM5hKBly2Ha6ORz3x61FmJQhXWDfqfKnlJDQd6ZPtNuS", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "636bf6ee5fc99269fe6bd342"}, "status": "Approved"}], "createdAt": {"$date": "2022-10-31T19:43:30.638Z"}, "updatedAt": {"$date": "2024-09-16T18:16:11.291Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.DM8ibzXJB1IPyOaDXqUCgYIGbuOPihKWqmxznoE7WeQ", "company": "", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "lastActive": {"$date": "2023-01-28T01:19:08.671Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628c06f044cd3e01b746bb9e"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$pLGheG7hVr.Yz3OJFwYE0eFxIK4KXMTKLLI.gsTi.3SPrJLk0CBDC", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}, {"teamId": {"$oid": "628e87c13f8875f76223011f"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-23T22:13:04.120Z"}, "updatedAt": {"$date": "2025-06-20T16:49:28.313Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************.EmySJ_Hxdx0iydomTPqI2yHiDN_kxq6FdwRAXXE0M7s", "company": "", "firstName": "matthew", "lastName": "kipp", "lastActive": {"$date": "2025-06-20T16:49:28.312Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "628e5dd88c474584362e471f"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$Ewd.hXMEa.xAhtOMTsDrm.hD6nhu4GLtg7nars4GDGxXjEbUznvPe", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "628e5f448c474584362e4740"}, "status": "Approved"}], "createdAt": {"$date": "2022-05-25T16:48:24.110Z"}, "updatedAt": {"$date": "2022-08-10T13:25:17.924Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************.fvNBHyij_Zqjxq5vzhGwbg5nkkgdpXV-BCp7qJCqi0c", "company": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"_id": {"$oid": "632245667e8b30f96324398f"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$GnVlc5fH7v7t2Fq9n9K8cOAZtxNhtuvMRR5J50XO6uo3gQ2vgoHHi", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "632a24a4b7fdeb56f3a17b51"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "createdAt": {"$date": "2022-09-14T21:19:34.886Z"}, "updatedAt": {"$date": "2025-06-26T01:12:02.652Z"}, "__v": 2, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************.j9g5uaIB5i589J0ypeCpGAPLW1J-ZztUefBKXvLWP2g", "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "<PERSON>", "lastActive": {"$date": "2025-06-26T01:12:02.652Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}, "contactColumns": [], "expireToken": "1727117174313", "propertyColumns": [], "resetToken": "9f5871cf007b6bb7e80591ba4dccd521e3002a8d7bd7d9cc5429bda4b2dd4f77", "plan": "Free", "tags": []}, {"_id": {"$oid": "6385047169b18ba8fbf40333"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$iCM2p89Jq8LVUKRuOzDW8Od0mN6RC4OKOImo670s7UD.MZui0xQmu", "isOnBoarded": false, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "6374320ad768fcd1d5941df5"}, "status": "invited"}], "createdAt": {"$date": "2022-11-28T18:56:49.817Z"}, "updatedAt": {"$date": "2022-11-28T18:56:49.831Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************.IeO5uH823i3wTdUktXO9pFdi3xzdScjwO6ZC2pCZKss"}, {"_id": {"$oid": "6385047e69b18ba8fbf4037c"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$10$Y8gzAOHSpdWdCeNBOTW2F.fIfiWGsTu5Ylad8AFDE7G3HcmZIF0ny", "isOnBoarded": false, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [{"teamId": {"$oid": "6374320ad768fcd1d5941df5"}, "status": "invited"}], "createdAt": {"$date": "2022-11-28T18:57:02.842Z"}, "updatedAt": {"$date": "2022-11-28T18:57:02.854Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************.03gbn4keHrShaorStf4cJIpLBW_Dj4-lAXyAuCUKMJY"}, {"_id": {"$oid": "64fb767285c288b0e23ddc49"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "googleId": "100956243944611770781", "timeZone": "America/Los_Angeles", "language": "en", "email": "<EMAIL>", "isOnBoarded": true, "profileImg": "https://lh3.googleusercontent.com/a/ACg8ocIyYqnhOGWB96z7sXV_49q76CahHt9FESuvNRsPHccg=s96-c", "propertyColumns": [], "contactColumns": [], "team": [{"teamId": {"$oid": "64fb767285c288b0e23ddc4b"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2023-09-08T19:30:58.680Z"}, "updatedAt": {"$date": "2024-08-23T19:59:03.099Z"}, "__v": 1, "lastActive": {"$date": "2023-09-08T19:31:32.577Z"}, "teamActive": {"$oid": "64fb767285c288b0e23ddc4b"}, "expireToken": "1724446743096", "plan": "Free", "resetToken": "b684b58fcb0f19adf0a6dbd8d8d59f2031df3b1cd71e4c4b262ebb4d7135eee1"}, {"_id": {"$oid": "65b02aa17e0077a9e42c9e6e"}, "firstName": "Regina", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "SociallyUp", "password": "$2a$10$ZzJBRq0uxc4Txyl5tPQMTuwiEGkRSxKd2Mf76iAkVerXrL7YyI0J.", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/avatar-65b02aa17e0077a9e42c9e6e?alt=media&token=c2f31dab-66aa-403b-bf6b-28100ff9e7b3", "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "65b02aa17e0077a9e42c9e70"}, "status": "Approved"}, {"teamId": {"$oid": "65b02b557e0077a9e42c9ff6"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-01-23T21:07:45.392Z"}, "updatedAt": {"$date": "2024-01-23T21:22:05.259Z"}, "__v": 1, "lastActive": {"$date": "2024-01-23T21:22:05.259Z"}, "teamActive": {"$oid": "65b02b557e0077a9e42c9ff6"}}, {"_id": {"$oid": "65b02e1a7e0077a9e42ca3a5"}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "", "password": "$2a$10$h97qgFd4vrVPCvvdpzvWmu4tGFNd4i9pZYRywufpLD6Mo2SuQag..", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "65b02e1a7e0077a9e42ca3a7"}, "status": "Approved"}, {"teamId": {"$oid": "629ab229622524b170ee9a26"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-01-23T21:22:34.654Z"}, "updatedAt": {"$date": "2024-01-25T04:45:39.740Z"}, "__v": 1, "lastActive": {"$date": "2024-01-25T04:45:39.740Z"}, "teamActive": {"$oid": "65b02e1a7e0077a9e42ca3a7"}}, {"_id": {"$oid": "65b02f0d7e0077a9e42ca44a"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "", "password": "$2a$10$/5SY.8qgAbxVTKaG403oXOw1tWAp0Wl72n8me6oQc2rO8NuXAk3Qm", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "65b02f0d7e0077a9e42ca44c"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-01-23T21:26:37.487Z"}, "updatedAt": {"$date": "2024-01-23T21:26:37.638Z"}, "__v": 1}, {"_id": {"$oid": "65b032847e0077a9e42ca4c4"}, "firstName": "Sid", "lastName": "<PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "OSCO Commercial", "password": "$2a$10$lAbzNzrrHA6jb6HijqEtvu4TgQitMBP6j6H.aGk8NYb6BGvIiG9ka", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "65b032857e0077a9e42ca4c6"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-01-23T21:41:24.993Z"}, "updatedAt": {"$date": "2024-01-23T21:41:25.146Z"}, "__v": 1}, {"_id": {"$oid": "65fb6089ad9f7a60cfd48b7f"}, "firstName": "<PERSON>", "lastName": "J<PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "Marcusmillichap", "password": "$2a$10$9OuZ8R22LDtV4xDzu7MCE.PEg6qKHmO25h1zWjG/0yaN5vbv/GOpq", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "65fb6089ad9f7a60cfd48b81"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-03-20T22:17:45.800Z"}, "updatedAt": {"$date": "2025-06-25T23:51:14.632Z"}, "__v": 1, "lastActive": {"$date": "2025-06-25T23:51:14.632Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "66020ef2ad9f7a60cfd5de5e"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "<PERSON>", "password": "asdf", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "66020ef2ad9f7a60cfd5de61"}, "status": "Approved"}, {"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-03-25T23:55:30.784Z"}, "updatedAt": {"$date": "2025-06-25T00:10:11.575Z"}, "__v": 1, "lastActive": {"$date": "2025-06-25T00:10:11.575Z"}, "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}}, {"_id": {"$oid": "66045830ad9f7a60cfd74077"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "timeZone": "America/Chicago", "language": "en", "email": "<EMAIL>", "company": "<PERSON>", "password": "$2a$10$PARV5ZFKNJICQHjMbRZhO.7YEDKA2esKiQABy8X33oVK6UW/gVKTG", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "propertyColumns": ["name", "address", "owner", "units", "ownerEmail", "ownerPhone", "zipCode", "type", "status", "city", "updatedAt", "sqft", "price", "county", "state", "createdAt", "yearBuilt", "lat", "lng", "isDeleted", "tags"], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "team": [{"teamId": {"$oid": "66045830ad9f7a60cfd74079"}, "status": "Approved"}], "tags": [], "createdAt": {"$date": "2024-03-27T17:32:32.501Z"}, "updatedAt": {"$date": "2024-05-21T19:20:53.870Z"}, "__v": 1, "lastActive": {"$date": "2024-05-21T19:20:53.869Z"}, "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}}, {"_id": {"$oid": "665fbbd06451bf23d3eab6c1"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2y$10$3XAquYXLsWQUTZJY6.yKs.uogyaYwc.M4jYozpcSplcnwo9bvu8a6", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/projectassets-pranjal.appspot.com/o/propbear%2Fde7834s-6515bd40-8b2c-4dc6-a843-5ac1a95a8b55.jpg?alt=media&token=78aae6ec-0f87-4a85-892c-899c924afa78", "team": [], "createdAt": {"$date": "2024-06-04T20:09:30.752Z"}, "updatedAt": {"$date": "2025-04-21T20:18:13.774Z"}, "__v": 1, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.nDX3y_KueEMGv29osJfbg5Vdj4fsRbADJHAVkS89oM8", "company": "", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "lastActive": {"$date": "2024-07-25T20:33:57.797Z"}, "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}, "contactColumns": [], "expireToken": "1719428801302", "plan": "Free", "propertyColumns": [], "resetToken": "3a3aa553688dff6e982adafc0a09f9086ea682746e1c3de51803ab5ad3d0e7cd", "tags": []}, {"_id": {"$oid": "677c0e89a9eb17501bb4ea0c"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$12$5IVxP9e5vA.VoiSSVtJ2HO3QVqPDXS7Lce77d2trxEOrGef7vYpR2", "isOnBoarded": true, "profileImg": null, "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "tags": [], "createdAt": {"$date": "2025-01-06T17:10:33.384Z"}, "updatedAt": {"$date": "2025-02-27T19:35:02.315Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.gBKrhhXL18c3bvif5hf0swVYfYo5oCVESpGfqxOXlZY", "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}, "lastActive": {"$date": "2025-02-27T19:35:02.315Z"}, "firstName": "<PERSON>", "lastName": "<PERSON>"}, {"_id": {"$oid": "6806a815a9eb17501be9c0db"}, "timeZone": "America/New_York", "language": "en", "email": "<EMAIL>", "password": "$2a$12$5IVxP9e5vA.VoiSSVtJ2HO3QVqPDXS7Lce77d2trxEOrGef7vYpR2", "isOnBoarded": true, "profileImg": null, "team": [{"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}], "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "tags": [], "createdAt": {"$date": "2025-04-21T20:18:29.627Z"}, "updatedAt": {"$date": "2025-06-25T21:59:14.704Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.hlQzFZwZv_qYNS8LJPJAxc6d4siU06bOlTKrEhi_BkI", "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}, "lastActive": {"$date": "2025-06-25T21:59:14.704Z"}, "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "Hill", "title": "Associate", "expireToken": "1745452389846", "resetToken": "b928f01d392755663855b415451325e86f3cba0eaa5cb300ccc8697e892f551d"}, {"_id": {"$oid": "683f7eb2a9eb17501b075cff"}, "timeZone": "America/New_York", "language": "en", "email": "brandon.ghil<PERSON><PERSON>@marcusmillichap.com", "password": "$2a$12$5IVxP9e5vA.VoiSSVtJ2HO3QVqPDXS7Lce77d2trxEOrGef7vYpR2", "isOnBoarded": true, "profileImg": "https://firebasestorage.googleapis.com/v0/b/propbear-io.appspot.com/o/noimage.png?alt=media&token=8ab4a478-81f6-4413-a4d2-8a9f94e0fc79&_gl=1*1wjacef*_ga****************************.*_ga_CW55HF8NVT*MTY4NTk5NzUxMC4zLjEuMTY4NTk5NzY1OS4wLjAuMA..", "team": [{"teamId": {"$oid": "628bff8d44cd3e01b746b737"}, "status": "Approved"}], "propertyColumns": [], "contactColumns": [], "plan": "Free", "subscription": {"createdAt": null, "expiredAt": null}, "billingPlanExpiry": null, "tags": [], "createdAt": {"$date": "2025-06-03T23:01:06.760Z"}, "updatedAt": {"$date": "2025-06-24T23:13:20.595Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.erG9d9mYeNVMcB_455uT2SWgL9aiezrzp31QWwrrhmc", "teamActive": {"$oid": "628bff8d44cd3e01b746b737"}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "lastActive": {"$date": "2025-06-24T23:13:20.594Z"}}, {"_id": {"$oid": "6849c0b4675db04f5adb31ab"}, "timeZone": "America/Los_Angeles", "language": "en", "email": "<EMAIL>", "password": "$2a$12$5IVxP9e5vA.VoiSSVtJ2HO3QVqPDXS7Lce77d2trxEOrGef7vYpR2", "isOnBoarded": true, "profileImg": "https://propbear-img.s3.ap-south-1.amazonaws.com/general/blob-1663286847458", "team": [{"teamId": {"$oid": "628ea3ebfeec685660394d1c"}, "status": "Approved"}, {"teamId": {"$oid": "6323721a7e8b30f963245b92"}, "status": "Approved"}], "createdAt": {"$date": "2025-06-11T00:55:18.860Z"}, "updatedAt": {"$date": "2025-06-25T23:45:20.237Z"}, "__v": 0, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.69tYSYh_w5wofI9vKpWiBde2_voce-Alx9zjj1p6GG8", "company": "Marcus & Millichap", "firstName": "<PERSON>", "lastName": "Taormina", "lastActive": {"$date": "2025-06-25T23:45:20.236Z"}, "teamActive": {"$oid": "628ea3ebfeec685660394d1c"}, "phone": "", "title": "Multi-Family Investments", "contactColumns": ["fullName", "actions", "status", "email", "phone", "company", "address", "city", "state", "zipCode", "tags"]}]