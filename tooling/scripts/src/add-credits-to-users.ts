import { db } from "@repo/database/server";

async function addCreditsToAllUsers() {
	try {
		const users = await db.user.findMany({
			select: {
				id: true,
				name: true,
				email: true,
				creditPurchases: {
					select: {
						credits: true
					}
				}
			},
		});

		if (users.length === 0) {
			console.log("No users found");
			return;
		}

		console.log(`Found ${users.length} users to update`);

		// Update all users to add 10 credits
		const updatePromises = users.map(async (user) => {
			// Calculate current credits from purchases
			const currentPurchased = user.creditPurchases.reduce((sum, purchase) => sum + purchase.credits, 0);

			// Create a new credit purchase
			await db.creditPurchase.create({
				data: {
					userId: user.id,
					credits: 10,
					packageId: "script-added", // Identifier for script-added credits
					price: 0, // Free credits
					status: "completed"
				}
			});

			return {
				user: user.email,
				oldCredits: currentPurchased,
				newCredits: currentPurchased + 10,
			};
		});

		// Execute all updates
		const results = await Promise.all(updatePromises);

		// Summary
		const totalOldCredits = results.reduce(
			(sum, result) => sum + result.oldCredits,
			0,
		);
		const totalNewCredits = results.reduce(
			(sum, result) => sum + result.newCredits,
			0,
		);

		console.log(`
Credits Summary:
---------------
Total users updated: ${results.length}
Total old credits: ${totalOldCredits}
Total new credits: ${totalNewCredits}
Added credits: ${totalNewCredits - totalOldCredits}
		`);

	} catch (error) {
		console.error("Error adding credits to users:", error);
		throw error;
	} finally {
		await db.$disconnect();
	}
}

// Run the script
if (require.main === module) {
	addCreditsToAllUsers()
		.then(() => {
			console.log("Script completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Script failed:", error);
			process.exit(1);
		});
}

export { addCreditsToAllUsers };
