import { db } from "@repo/database/server";
import { models } from "../../../apps/app/modules/app/ai/lib/models";

async function insertModels() {
	console.log("Inserting AI models into database...");

	for (const model of models) {
		try {
			// Check if model already exists
			const existingModel = await db.model.findFirst({
				where: {
					model: model.model,
					provider: model.provider as any, // Cast to match enum
				},
			});

			if (!existingModel) {
				// Create new model
				await db.model.create({
					data: {
						name: model.name,
						model: model.model,
						provider: model.provider as any, // Cast to match enum
						searchField: `${model.name} ${model.provider} ${model.description}`,
						icon: model.icon,
						capabilities: model.capabilities as any[], // Cast to match enum array
						description: model.description,
						isPremium: model.isPremium,
						isDisabled: model.isDisabled,
						cost: model.cost,
					},
				});
				console.log(`Created model: ${model.name} (${model.model})`);
			} else {
				// Update existing model
				await db.model.update({
					where: { id: existingModel.id },
					data: {
						name: model.name,
						model: model.model,
						provider: model.provider as any,
						searchField: `${model.name} ${model.provider} ${model.description}`,
						icon: model.icon,
						capabilities: model.capabilities as any[],
						description: model.description,
						isPremium: model.isPremium,
						isDisabled: model.isDisabled,
						cost: model.cost,
					},
				});
				console.log(`Updated model: ${model.name} (${model.model})`);
			}
		} catch (error) {
			console.error(`Error processing model ${model.name}:`, error);
		}
	}

	console.log(`Processed ${models.length} models successfully!`);
}

insertModels()
	.catch(console.error)
	.finally(() => process.exit(0));
