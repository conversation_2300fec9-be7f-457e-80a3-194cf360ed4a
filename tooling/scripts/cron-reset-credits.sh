#!/bin/bash

# Daily Credit Reset Cron Job
# This script should be run daily to reset all users' credits

# Set the working directory to the scripts folder
cd "$(dirname "$0")"

# Log the execution
echo "$(date): Starting daily credit reset" >> cron-logs.txt

# Run the reset script
npm run reset-daily-credits >> cron-logs.txt 2>&1

# Log completion
echo "$(date): Daily credit reset completed" >> cron-logs.txt
echo "---" >> cron-logs.txt 