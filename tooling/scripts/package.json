{"dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/auth": "workspace:*", "@repo/utils": "workspace:*"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.13.10", "dotenv-cli": "^8.0.0", "nanoid": "^5.1.2", "tsx": "^4.19.3"}, "name": "@repo/scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "fix-null-credits": "dotenv -c -e ../../.env -- tsx ./src/fix-null-credits.ts", "add-credits": "dotenv -c -e ../../.env -- tsx ./src/add-credits-to-users.ts", "reset-daily-credits": "dotenv -c -e ../../.env -- tsx ./src/reset-daily-credits.ts", "insert-models": "dotenv -c -e ../../.env -- tsx ./src/insert-models.ts", "migrate:test": "dotenv -c -e ../../.env -- tsx ./src/test-migration-single-user.ts", "migrate:full": "dotenv -c -e ../../.env -- tsx ./src/migrate-users-from-old-db.ts", "create-forwarding-configs": "dotenv -c -e ../../.env -- tsx ./src/create-forwarding-configs.ts", "migrate-watermark": "dotenv -c -e ../../.env -- tsx ./src/migrate-email-watermark.ts", "type-check": "tsc --noEmit"}, "version": "0.0.0"}