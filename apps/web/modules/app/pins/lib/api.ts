import { ObjectType } from "@repo/database";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export interface Pin {
	id: string;
	userId: string;
	organizationId: string;
	objectType: ObjectType;
	objectId: string;
	name: string;
	image?: string;
	position?: number;
	createdAt: string;
	updatedAt: string;
}

export interface CreatePinData {
	objectId: string;
	objectType: ObjectType;
	name: string;
	image?: string;
	organizationId: string;
}

export interface DeletePinData {
	objectId: string;
	objectType: ObjectType;
	organizationId: string;
}

// Fetch pins for an organization
export async function fetchPins(
	organizationId: string,
	objectType?: ObjectType,
): Promise<Pin[]> {
	const url = new URL("/api/pins", window.location.origin);
	url.searchParams.set("organizationId", organizationId);
	if (objectType) {
		url.searchParams.set("objectType", objectType);
	}

	const response = await fetch(url.toString(), {
		credentials: "include",
	});

	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(
			`Failed to fetch pins: ${response.status} ${errorText}`,
		);
	}

	return response.json();
}

// Create a pin
export async function createPin(data: CreatePinData): Promise<Pin> {
	const response = await fetch("/api/pins", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		credentials: "include",
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to create pin");
	}

	return response.json();
}

// Delete a pin
export async function deletePin(
	data: DeletePinData,
): Promise<{ success: boolean }> {
	const response = await fetch("/api/pins", {
		method: "DELETE",
		headers: {
			"Content-Type": "application/json",
		},
		credentials: "include",
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to delete pin");
	}

	return response.json();
}

// Delete all pins for an organization
export async function deleteAllPins(
	organizationId: string,
): Promise<{ success: boolean }> {
	const response = await fetch("/api/pins/all", {
		method: "DELETE",
		headers: {
			"Content-Type": "application/json",
		},
		credentials: "include",
		body: JSON.stringify({ organizationId }),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to delete all pins");
	}

	return response.json();
}

// Update pin order
export async function updatePinOrder({
	organizationId,
	pinIds,
}: {
	organizationId: string;
	pinIds: string[];
}): Promise<{ success: boolean }> {
	const response = await fetch("/api/pins/order", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		credentials: "include",
		body: JSON.stringify({ organizationId, pinIds }),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || "Failed to update pin order");
	}

	return response.json();
}

// React Query Hooks

export function usePins(organizationId?: string, objectType?: ObjectType) {
	return useQuery({
		queryKey: ["pins", organizationId, objectType],
		queryFn: () => {
			if (!organizationId) {
				return Promise.resolve([]);
			}
			return fetchPins(organizationId, objectType);
		},
		enabled: !!organizationId,
		// Add retry logic to handle temporary auth issues
		retry: (failureCount, error) => {
			// Don't retry if it's a 400 "No active organization" error
			if (error instanceof Error && error.message.includes('400') && error.message.includes('No active organization')) {
				return false;
			}
			// Retry up to 2 times for other errors
			return failureCount < 2;
		},
		retryDelay: 1000, // Wait 1 second before retrying
	});
}

export function useCreatePin(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createPin,
		onSuccess: (data) => {
			// Invalidate and refetch pins
			queryClient.invalidateQueries({
				queryKey: ["pins", organizationId],
			});
			queryClient.invalidateQueries({
				queryKey: ["pins", organizationId, data.objectType],
			});

			toast.success("Record pinned to header");
		},
		onError: (error: Error) => {
			toast.error(error.message || "Failed to pin record");
		},
	});
}

export function useDeletePin(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deletePin,
		onSuccess: (_, variables) => {
			// Invalidate and refetch pins
			queryClient.invalidateQueries({
				queryKey: ["pins", organizationId],
			});
			queryClient.invalidateQueries({
				queryKey: ["pins", organizationId, variables.objectType],
			});

			toast.success("Record unpinned from header");
		},
		onError: (error: Error) => {
			toast.error(error.message || "Failed to unpin record");
		},
	});
}

export function useDeleteAllPins(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteAllPins,
		onSuccess: () => {
			// Invalidate and refetch pins
			queryClient.invalidateQueries({
				queryKey: ["pins", organizationId],
			});

			toast.success("All pins removed");
		},
		onError: (error: Error) => {
			toast.error(error.message || "Failed to remove all pins");
		},
	});
}

export function useUpdatePinOrder(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updatePinOrder,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["pins", organizationId],
			});
			toast.success("Pin order updated");
		},
		onError: (error: Error) => {
			toast.error(error.message || "Failed to update pin order");
		},
	});
}

// Helper function to check if a record is pinned
export function useIsPinned(
	organizationId?: string,
	objectType?: ObjectType,
	objectId?: string,
) {
	const { data: pins = [] } = usePins(organizationId, objectType);

	return pins.some(
		(pin) => pin.objectId === objectId && pin.objectType === objectType,
	);
}
