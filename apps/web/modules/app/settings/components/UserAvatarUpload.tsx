"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { authClient } from "@repo/auth/client";
import { useEdgeStore } from "@repo/storage";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { useState, useMemo } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { CropImageDialog } from "./CropImageDialog";

type UserAvatarUploadProps = {
	caption?: string;
	onSuccess: () => void;
	onError: () => void;
};

export function UserAvatarUpload({
	caption = "Profile Picture",
	onSuccess,
	onError,
}: UserAvatarUploadProps) {
	const { user, reloadSession } = useSession();
	const [uploadProgress, setUploadProgress] = useState<number | null>(null);
	const [cropDialogOpen, setCropDialogOpen] = useState(false);
	const [image, setImage] = useState<File | null>(null);
	const { edgestore } = useEdgeStore();

	const { getRootProps, getInputProps } = useDropzone({
		onDrop: (acceptedFiles) => {
			const file = acceptedFiles[0];
			if (!file) return;
			
			setImage(file);
			setCropDialogOpen(true);
		},
		accept: {
			"image/png": [".png"],
			"image/jpeg": [".jpg", ".jpeg"],
			"image/gif": [".gif"],
		},
		multiple: false,
	});

	const onCrop = async (croppedImageData: Blob | null) => {
		if (!croppedImageData) {
			return;
		}

		setUploadProgress(0);
		try {
			// Convert blob to file for EdgeStore upload
			const croppedFile = new File([croppedImageData], `avatar-${user?.id}.png`, {
				type: 'image/png',
			});

			// Upload to EdgeStore avatars bucket
			const result = await edgestore.avatars.upload({
				file: croppedFile,
				options: {
					replaceTargetUrl: user?.image || undefined,
				},
				onProgressChange: (progress) => {
					setUploadProgress(progress);
				},
			});

			// Update user with new avatar URL
			await authClient.updateUser({
				image: result.url,
			});

			await reloadSession();
			toast.success("Profile picture updated successfully");
			onSuccess();
		} catch (e) {
			console.error('Avatar upload failed:', e);
			toast.error("Failed to update profile picture");
			onError();
		} finally {
			setUploadProgress(null);
		}
	};

	const handleRemoveImage = async () => {
		if (!user?.image) return;

		setUploadProgress(0);
		try {
			// Delete the file from EdgeStore first
			await edgestore.avatars.delete({
				url: user.image,
			});

			// Update user to remove avatar URL
			await authClient.updateUser({
				image: null,
			});

			await reloadSession();
			toast.message('Profile Updated', {
				description: 'Image removed successfully',
			});
			onSuccess();
		} catch (e) {
			console.error('Avatar removal failed:', e);
			toast.error("Failed to remove profile picture");
			onError();
		} finally {
			setUploadProgress(null);
		}
	};

	const initials = useMemo(() => {
		if (!user?.name) return user?.email?.[0]?.toUpperCase() || "";
		return user.name
			.split(" ")
			.slice(0, 2)
			.map((n) => n[0]?.toUpperCase())
			.join("");
	}, [user?.name, user?.email]);

	if (!user) {
		return null;
	}

	const isUploading = uploadProgress !== null;

	return (
		<>
			<div className="grid grid-cols-1 w-full">
				<div className="flex items-start">
					<div className="mr-4 flex items-center">
						<Avatar className="w-12 h-12 rounded-full">
							<AvatarImage src={user.image || undefined} alt={user.name || ""} />
							<AvatarFallback className="bg-secondary/10 text-muted-foreground border border-input rounded-full">
								{initials}
							</AvatarFallback>
						</Avatar>
					</div>

					<div className="flex flex-col">
						<p className="text-base mb-2">{caption}</p>
						<div className="flex flex-row items-center space-x-2">
							<div {...getRootProps()}>
								<input {...getInputProps()} />
								<Button
									type="button"
									disabled={isUploading}
									className="bg-blue-500 hover:bg-blue-600 !border !border-blue-500 text-white !py-1 px-2 rounded-lg w-fit !h-7"
								>
									{isUploading ? `${uploadProgress}%` : "Upload"}
								</Button>
							</div>
							
							{user.image && (
								<Button
									onClick={handleRemoveImage}
									disabled={isUploading}
									className="!bg-transparent hover:!text-zinc-500 !text-zinc-200 py-1 px-2 rounded-lg w-fit !h-7"
								>
									Remove
								</Button>
							)}
						</div>
						<p className="text-xs mt-2 opacity-50">
							We support PNGs, JPEGs, and GIFs under 10MB
						</p>
					</div>
				</div>
			</div>

			<CropImageDialog
				image={image}
				open={cropDialogOpen}
				onOpenChange={setCropDialogOpen}
				onCrop={onCrop}
			/>
		</>
	);
}
