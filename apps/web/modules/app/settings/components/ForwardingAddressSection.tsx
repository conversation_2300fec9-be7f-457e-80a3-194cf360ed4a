"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { Alert, AlertDescription } from "@ui/components/alert";
import { cn } from "@ui/lib";
import { 
	MoreHorizontalIcon, 
	ExternalLinkIcon,
	CheckCircleIcon,
	CopyIcon,
	InfoIcon,
	MailIcon
} from "lucide-react";
import { 
	IconDotsVertical,
	IconCopy,
	IconSettings,
	IconPlugOff,
	IconBoltFilled, 
	IconPencil
} from "@tabler/icons-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Logo } from "@shared/components/Logo";
import { Badge } from "@ui/components/badge";
import { useRouter } from "next/navigation";

interface ForwardingEmailConfig {
	id: string;
	address: string;
	isActive: boolean;
}

export function ForwardingAddressSection() {
	const { activeOrganization } = useActiveOrganization();
	const [copied, setCopied] = useState(false);
	const [config, setConfig] = useState<ForwardingEmailConfig | null>(null);
	const [loading, setLoading] = useState(true);
	const [disconnecting, setDisconnecting] = useState(false);
	const router = useRouter();

	// Fetch the forwarding email config to get the correct ID
	useEffect(() => {
		const fetchConfig = async () => {
			if (!activeOrganization?.id) return;

			try {
				const response = await fetch(`/api/forwarding-email-config/${activeOrganization.id}`, {
					credentials: "include",
				});

				if (response.ok) {
					const configData = await response.json();
					setConfig(configData);
				} else {
					console.error("Failed to fetch forwarding email config");
				}
			} catch (error) {
				console.error("Error fetching forwarding email config:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchConfig();
	}, [activeOrganization?.id]);

	if (!activeOrganization?.slug) {
		return null;
	}

	const forwardingAddress = config?.address || `${activeOrganization.slug}@inbox.reliocrm.com`;

	const copyToClipboard = async (e: React.MouseEvent) => {
		e.stopPropagation();
		try {
			await navigator.clipboard.writeText(forwardingAddress);
			setCopied(true);
			toast.success("Email address copied to clipboard");
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			toast.error("Failed to copy email address");
		}
	};

	const handleEdit = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!config?.id) {
			toast.error("Forwarding email configuration not found");
			return;
		}
		router.push(`/app/${activeOrganization.slug}/settings/account/email-calendar/${config.id}?type=forwarding`);
	};

	const handleDisconnect = async (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!activeOrganization?.id || !config?.id) {
			toast.error("Forwarding email configuration not found");
			return;
		}

		setDisconnecting(true);
		try {
			const response = await fetch(`/api/forwarding-email-config/${activeOrganization.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					isActive: false,
				}),
			});

			if (response.ok) {
				setConfig(prev => prev ? { ...prev, isActive: false } : null);
				toast.success("Forwarding email disconnected");
			} else {
				// Try to get the specific error message from the server
				try {
					const errorData = await response.json();
					const errorMessage = errorData.error || "Failed to disconnect forwarding email";
					toast.error(errorMessage);
				} catch {
					toast.error("Failed to disconnect forwarding email");
				}
			}
		} catch (error) {
			console.error("Error disconnecting forwarding email:", error);
			toast.error("Failed to disconnect forwarding email");
		} finally {
			setDisconnecting(false);
		}
	};

	const handleConnect = async (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!activeOrganization?.id || !config?.id) {
			toast.error("Forwarding email configuration not found");
			return;
		}

		setDisconnecting(true);
		try {
			const response = await fetch(`/api/forwarding-email-config/${activeOrganization.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					isActive: true,
				}),
			});

			if (response.ok) {
				setConfig(prev => prev ? { ...prev, isActive: true } : null);
				toast.success("Forwarding email connected");
			} else {
				// Try to get the specific error message from the server
				try {
					const errorData = await response.json();
					const errorMessage = errorData.error || "Failed to connect forwarding email";
					toast.error(errorMessage);
				} catch {
					toast.error("Failed to connect forwarding email");
				}
			}
		} catch (error) {
			console.error("Error connecting forwarding email:", error);
			toast.error("Failed to connect forwarding email");
		} finally {
			setDisconnecting(false);
		}
	};

	return (
		<div className="space-y-4 cursor-pointer">
			<div className="flex flex-col">
				<h2 className="text-lg font-semibold">Forwarding address</h2>
				<div>
					<span className="text-sm text-muted-foreground">Forward or BCC emails to this address to automatically add them to your records.{" "}</span>
					<a 
						href="/help/email-forwarding" 
						className="text-sm text-foreground hover:underline inline-flex items-center gap-1"
					>
						Learn more about forwarding email
						<ExternalLinkIcon className="size-3" />
					</a>
				</div>
			</div>
			
			{/* Forwarding address card */}
			<div className="space-y-3 cursor-pointer" onClick={handleEdit}>
				<div
					className={cn(
						"flex items-center justify-between w-full",
						"rounded-2xl bg-sidebar shadow-[rgb(39,40,43)_0px_0px_0px_1px_inset]",
						"transition-[background-color,border-color] duration-[140ms]",
						"p-2 pr-4 gap-2.5",
						"border border-border/50 hover:border-border",
						"hover:bg-sidebar/50",
						loading && "opacity-50"
					)}
				>
					{/* Left section with icon and details */}
					<div className="flex items-center justify-start gap-3 w-full">
						{/* Icon container */}
						<div className="w-10 h-10 rounded-xl flex items-center justify-center flex-shrink-0 bg-sidebar">
							<Logo withLabel={false} />
						</div>
						
						{/* Content area */}
						<div className="flex flex-col items-start justify-start gap-0 w-full">
							<div className="flex flex-col justify-start w-full">
								<div className="flex items-center gap-2">
									<span className="font-medium text-primary text-sm font-mono">
										{forwardingAddress}
									</span>
								</div>
							</div>
							<div className="text-xs text-muted-foreground">
								Organization inbox
							</div>
						</div>
					</div>
					
					{/* Right section with status and menu */}
					<div className="flex items-center justify-start gap-3 flex-shrink-0">
						{/* Status indicator */}
						<Badge 
							status={config?.isActive ? "success" : "warning"} 
							className="flex items-center gap-1"
						>
							<IconBoltFilled className={cn(
								"size-3.5", 
								config?.isActive ? "text-green-500" : "text-muted-foreground"
							)} />
							<span>
								{loading ? "Loading..." : 
								 disconnecting ? "Updating..." :
								 config?.isActive ? "Active" : "Inactive"}
							</span>
						</Badge>
						
						{/* More actions dropdown */}
						<span onClick={(e) => e.stopPropagation()}>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="ghost"
										size="sm"
										className="size-6 p-0 hover:bg-[rgb(39,40,43)]"
										disabled={loading}
										aria-label="More actions"
									>
										<IconDotsVertical className="size-3.5" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent
									sideOffset={8}
									className={cn(
										"z-50 min-w-[180px] rounded-xl border border-border bg-popover text-popover-foreground shadow-lg focus:outline-none animate-in fade-in-0 slide-in-from-top-1",
										"bg-sidebar",
									)}
									align="end"
								>
									{/* Edit settings */}
									<DropdownMenuItem
										className="flex items-center gap-2"
										onClick={handleEdit}
										disabled={!config?.id}
									>
										<IconPencil className="w-4 h-4" />
										Edit settings
									</DropdownMenuItem>

									{/* Copy address */}
									<DropdownMenuItem
										className="flex items-center gap-2"
										onClick={copyToClipboard}
									>
										<IconCopy className="w-4 h-4" />
										Copy address
									</DropdownMenuItem>

									<DropdownMenuSeparator className="my-1" />

									{/* Connect/Disconnect */}
									{config?.isActive ? (
										<DropdownMenuItem
											className="flex items-center gap-2 text-orange-500 hover:!text-orange-600"
											onClick={handleDisconnect}
											disabled={disconnecting}
										>
											<IconPlugOff className="w-4 h-4" />
											{disconnecting ? "Disconnecting..." : "Disconnect"}
										</DropdownMenuItem>
									) : (
										<DropdownMenuItem
											className="flex items-center gap-2 text-green-500 hover:!text-green-600"
											onClick={handleConnect}
											disabled={disconnecting}
										>
											<IconBoltFilled className="w-4 h-4" />
											{disconnecting ? "Connecting..." : "Connect"}
										</DropdownMenuItem>
									)}
								</DropdownMenuContent>
							</DropdownMenu>
						</span>
					</div>
				</div>
			</div>

			{/* Usage instructions */}
			{/* <div className="text-sm text-muted-foreground space-y-2">
				<div className="flex items-start gap-2">
					<InfoIcon className="size-4 mt-0.5 flex-shrink-0" />
					<div>
						<p className="font-medium text-foreground">How to use:</p>
						<p>Forward or BCC emails from your registered email address to automatically add them to your CRM.</p>
					</div>
				</div>
				<div className="flex items-start gap-2">
					<InfoIcon className="size-4 mt-0.5 flex-shrink-0" />
					<div>
						<p className="font-medium text-foreground">Security:</p>
						<p>Only emails from organization members will be processed for security.</p>
					</div>
				</div>
				<div className="flex items-start gap-2">
					<InfoIcon className="size-4 mt-0.5 flex-shrink-0" />
					<div>
						<p className="font-medium text-foreground">Contact Matching:</p>
						<p>Emails will automatically be linked to existing contacts or create new ones based on email addresses.</p>
					</div>
				</div>
			</div> */}

			{/* Plus addressing tip */}
			{/* <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
				<InfoIcon className="size-4" />
				<AlertDescription className="space-y-2">
					<p className="font-medium">Pro tip: Use plus addressing for categorization</p>
					<div className="space-y-1 text-sm">
						<p><code className="bg-muted px-1 rounded">{activeOrganization.slug}+<EMAIL></code> - for deal-related emails</p>
						<p><code className="bg-muted px-1 rounded">{activeOrganization.slug}+<EMAIL></code> - for lead generation emails</p>
					</div>
				</AlertDescription>
			</Alert> */}
		</div>
	);
} 