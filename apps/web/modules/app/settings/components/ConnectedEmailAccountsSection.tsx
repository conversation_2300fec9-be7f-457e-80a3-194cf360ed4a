"use client";

import { SettingsItem } from "@app/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { 
	MoreHorizontalIcon, 
	ExternalLinkIcon,
	CheckCircleIcon,
	InfoIcon 
} from "lucide-react";
import { 
	IconBoltFilled, 
	IconBrandGoogle, 
	IconDotsVertical,
	IconSettings,
	IconRefresh,
	IconTrash,
	IconPencil
} from "@tabler/icons-react";
import { PlusIcon } from "@radix-ui/react-icons";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useConnectedAccounts, useReconnectAccount, useRemoveAccount, type ConnectedAccount } from "@shared/hooks/useConnectedAccounts";
import { authClient } from "@repo/auth/client";

export function ConnectedEmailAccountsSection() {
	const router = useRouter();
	const { activeOrganization } = useActiveOrganization();

	// Use the hooks
	const { data, isLoading: isPending } = useConnectedAccounts();
	const reconnectMutation = useReconnectAccount();
	const removeMutation = useRemoveAccount();

	const connectedAccounts = data?.accounts || [];

	const handleEdit = (account: ConnectedAccount) => {
		// Navigate to unified account settings page with connected type
		if (!activeOrganization?.slug) {
			return;
		}
		router.push(`/app/${activeOrganization.slug}/settings/account/email-calendar/${account.id}?type=connected`);
	};

	const handleConnectGoogle = () => {
		// Use better-auth's social sign-in flow for account linking
		authClient.signIn.social({
			provider: "google",
			callbackURL: `${window.location.origin}/app/${activeOrganization?.slug}/settings/account/email-calendar`,
		});
	};

	const handleReconnect = async (account: ConnectedAccount) => {
		// Use better-auth's social sign-in flow to reconnect
		authClient.signIn.social({
			provider: account.provider as "google" | "microsoft",
			callbackURL: `${window.location.origin}/app/${activeOrganization?.slug}/settings/account/email-calendar`,
		});
	};

	const handleRemove = async (account: ConnectedAccount) => {
		removeMutation.mutate(account.id);
	};

	if (isPending) {
		return (
			<div className="space-y-4">
				<div className="flex flex-col">
					<h2 className="text-lg font-semibold">Connected accounts</h2>
					<div>
						<span className="text-sm text-muted-foreground">We take your privacy very seriously. Read our{" "}</span>
						<a 
							href="#" 
							className="text-sm text-foreground hover:underline inline-flex items-center gap-1"
						>
							Privacy Policy
							<ExternalLinkIcon className="size-3" />
						</a>
					</div>
				</div>
				<div className="space-y-3">
					<Skeleton className="h-16 w-full rounded-2xl" />
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4 cursor-pointer" onClick={() => handleEdit(connectedAccounts[0])}>
			<div className="flex flex-col">
				<h2 className="text-lg font-semibold">Connected accounts</h2>
				<div>
					<span className="text-sm text-muted-foreground">We take your privacy very seriously. Read our{" "}</span>
					<a 
						href="#" 
						className="text-sm text-foreground hover:underline inline-flex items-center gap-1"
					>
						Privacy Policy
						<ExternalLinkIcon className="size-3" />
					</a>
				</div>
			</div>
			
			{/* Show alert if any accounts need reconnection */}
			{connectedAccounts.some(account => !account.refreshToken) && (
				<Alert className="border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20">
					<InfoIcon className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
					<AlertDescription className="text-yellow-800 dark:text-yellow-200">
						<div className="space-y-2">
							<p>
								Some email accounts need to be reconnected to enable email sending.
							</p>
							<p className="text-sm">
								<strong>Option 1:</strong> Click "Reconnect Required" to automatically reconnect via popup.
							</p>
							<p className="text-sm">
								<strong>Option 2:</strong> Manually remove the account below, then click "Connect Google Account" to add it back with proper permissions.
							</p>
						</div>
					</AlertDescription>
				</Alert>
			)}

			{/* Connect new account button */}
			{/* <div className="flex justify-end">
				<Button
					variant="outline"
					size="sm"
					onClick={(e) => {
						e.stopPropagation();
						handleConnectGoogle();
					}}
					className="flex items-center gap-2"
				>
					<PlusIcon className="h-4 w-4" />
					Connect Google Account
				</Button>
			</div> */}

			{/* Connected accounts list */}
			<div className="space-y-3">
				{connectedAccounts.length === 0 ? (
					<div className="text-center py-8 text-muted-foreground">
						<p className="text-sm">No connected accounts found.</p>
					</div>
				) : (
					connectedAccounts.map((account) => {
						const isLoading = reconnectMutation.isPending || removeMutation.isPending;
						
						return (
							<div
								key={account.id}
								className={cn(
									"flex items-center justify-between w-full",
									"rounded-2xl bg-sidebar shadow-[rgb(39,40,43)_0px_0px_0px_1px_inset]",
									"transition-[background-color,border-color] duration-[140ms]",
									"p-2 pr-4 gap-2.5",
									"border border-border/50 hover:border-border",
									"hover:bg-sidebar/50",
									isLoading && "opacity-50"
								)}
							>
								{/* Left section with icon and details */}
								<div className="flex items-center justify-start gap-3 w-full">
									{/* Icon container - Shows provider logo, not company logo */}
									<div className="w-10 h-10 rounded-xl bg-sidebar flex items-center justify-center flex-shrink-0">
										{/* Google provider */}
										{account.provider === 'google' && (
											<svg xmlns="http://www.w3.org/2000/svg" height="20" viewBox="0 0 24 24" width="20">
												<path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
												<path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
												<path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
												<path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
											</svg>
										)}
										
										{/* Microsoft/Outlook provider */}
										{account.provider === 'microsoft' && (
											<svg xmlns="http://www.w3.org/2000/svg" height="20" viewBox="0 0 24 24" width="20">
												<path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z" fill="#00BCF2"/>
											</svg>
										)}
										
										{/* Fallback for other providers */}
										{!['google', 'microsoft'].includes(account.provider) && (
											<div className="size-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center">
												<span className="text-white font-semibold text-xs">
													{account.provider.charAt(0).toUpperCase()}
												</span>
											</div>
										)}
									</div>
									
									{/* Content area */}
									<div className="flex flex-col items-start justify-start gap-0 w-full">
										<div className="flex flex-col justify-start w-full">
											<div className="flex items-center gap-2">
												<span className="font-medium text-primary text-sm">
													{account.email || `${account.name}@${account.provider}.com`}
												</span>
											</div>
										</div>
										<div className="text-xs text-muted-foreground">
											{account.services.join(", ")}
										</div>
									</div>
								</div>
								
								{/* Right section with status and menu */}
								<div className="flex items-center justify-start gap-3 flex-shrink-0">
									{/* Status indicator */}
									{!account.refreshToken ? (
										<Badge 
											status="warning" 
											className="flex items-center gap-1"
										>
											<IconBoltFilled className="size-3.5 text-yellow-500" />
											<span>Needs Reconnect</span>
										</Badge>
									) : (
										<Badge 
											status={account.status === "In Sync" ? "success" : "warning"} 
											className="flex items-center gap-1"
										>
											<IconBoltFilled className={cn(
												"size-3.5", 
												account.status === "In Sync" ? "text-green-500" : "text-muted-foreground"
											)} />
											<span>
												{account.status === "In Sync" ? "Active" : "Inactive"}
											</span>
										</Badge>
									)}
									
									{/* More actions dropdown */}
									<span onClick={(e) => e.stopPropagation()}>
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button
													variant="ghost"
													size="sm"
													className="size-6 p-0 hover:bg-[rgb(39,40,43)]"
													disabled={isLoading}
													aria-label="More actions"
												>
													<IconDotsVertical className="size-3.5" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent
												sideOffset={8}
												className={cn(
													"z-50 min-w-[180px] rounded-xl border border-border bg-popover text-popover-foreground shadow-lg focus:outline-none animate-in fade-in-0 slide-in-from-top-1",
													"bg-sidebar",
												)}
												align="end"
											>
												{/* Edit settings */}
												<DropdownMenuItem
													className="flex items-center gap-2"
													onClick={(e) => {
														e.stopPropagation();
														handleEdit(account);
													}}
												>
													<IconPencil className="w-4 h-4" />
													Edit
												</DropdownMenuItem>

												{/* Reconnect */}
												<DropdownMenuItem
													className={cn(
														"flex items-center gap-2",
														!account.refreshToken && "text-yellow-600 dark:text-yellow-400"
													)}
													onClick={(e) => {
														e.stopPropagation();
														handleReconnect(account);
													}}
													disabled={isLoading}
												>
													<IconRefresh className="w-4 h-4" />
													{isLoading 
														? "Reconnecting..." 
														: !account.refreshToken 
															? "Reconnect Required" 
															: "Reconnect"
													}
												</DropdownMenuItem>

												<DropdownMenuSeparator className="my-1" />

												{/* Remove email */}
												<DropdownMenuItem
													className="flex items-center gap-2 text-red-500 hover:!text-red-600"
													onClick={(e) => {
														e.stopPropagation();
														handleRemove(account);
													}}
													disabled={isLoading}
												>
													<IconTrash className="w-4 h-4" />
													{isLoading ? "Removing..." : "Remove email"}
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									</span>
								</div>
							</div>
						);
					})
				)}
			</div>


		</div>
	);
} 