"use client";

import { CreateContactModal } from "@app/contacts/components/CreateContactModal";
import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import * as React from "react";

interface ContactsContextType {
	openCreateContact: (initialValues?: Record<string, any>) => void;
}

const ContactsContext = React.createContext<ContactsContextType | undefined>(
	undefined,
);

export function useContacts() {
	const context = React.useContext(ContactsContext);
	if (!context) {
		throw new Error("useContacts must be used within a ContactsProvider");
	}
	return context;
}

export function ContactsProvider({ children }: { children: React.ReactNode }) {
	const [isCreateModalOpen, setIsCreateModalOpen] = React.useState(false);
	const [initialValues, setInitialValues] = React.useState<
		Record<string, any> | undefined
	>(undefined);

	useHotkeys([
		{
			key: "c",
			callback: () => {
				if (!isCreateModalOpen) {
					setIsCreateModalOpen(true);
				}
			},
			ignoreInputs: true,
			preventModifiers: {
				ctrlKey: true,
				altKey: true,
				metaKey: true,
			},
		},
	]);

	const openCreateContact = React.useCallback(
		(values?: Record<string, any>) => {
			setInitialValues(values);
			setIsCreateModalOpen(true);
		},
		[],
	);

	const handleModalOpenChange = (open: boolean) => {
		setIsCreateModalOpen(open);
		if (!open) {
			setInitialValues(undefined);
		}
	};

	return (
		<ContactsContext.Provider value={{ openCreateContact }}>
			{children}
			<CreateContactModal
				open={isCreateModalOpen}
				onOpenChange={handleModalOpenChange}
				initialValues={initialValues}
			/>
		</ContactsContext.Provider>
	);
}
