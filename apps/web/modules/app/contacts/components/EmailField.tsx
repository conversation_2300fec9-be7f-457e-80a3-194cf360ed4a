"use client";

import { IconPlus, IconX } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import React, { useEffect, useRef, useState } from "react";

interface EmailItem {
	label: string;
	address: string;
	isPrimary: boolean;
	isBad: boolean;
}

interface EmailFieldProps {
	emails: EmailItem[];
	onEmailsChange: (emails: EmailItem[]) => void;
	placeholder?: string;
}

export function EmailField({
	emails,
	onEmailsChange,
	placeholder = "Set Email addresses...",
}: EmailFieldProps) {
	const [open, setOpen] = useState(false);
	const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
	const [recentlyAdded, setRecentlyAdded] = useState<number[]>([]);
	const [focusIndex, setFocusIndex] = useState<number | null>(null);
	const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

	const hasEmails = emails.some((email) => email.address.trim() !== "");
	const displayText = hasEmails
		? emails
				.filter((e) => e.address.trim() !== "")
				.map((e) => e.address)
				.join(", ")
		: placeholder;

	// Focus the input when a new email is added
	useEffect(() => {
		if (focusIndex !== null && inputRefs.current[focusIndex]) {
			inputRefs.current[focusIndex]?.focus();
			setFocusIndex(null);
		}
	}, [focusIndex, recentlyAdded]);

	const addEmail = () => {
		const newIndex = emails.length;
		const newEmails = [
			...emails,
			{
				label: "Work",
				address: "",
				isPrimary: emails.length === 0,
				isBad: false,
			},
		];
		setRecentlyAdded([...recentlyAdded, newIndex]);
		setFocusIndex(newIndex);
		onEmailsChange(newEmails);
	};

	const removeEmail = (index: number) => {
		const newEmails = emails.filter((_, i) => i !== index);
		// Update recentlyAdded indices after removal
		setRecentlyAdded(
			recentlyAdded
				.filter((i) => i !== index)
				.map((i) => (i > index ? i - 1 : i)),
		);
		onEmailsChange(newEmails);
	};

	const updateEmail = (index: number, address: string) => {
		const newEmails = [...emails];
		newEmails[index] = { ...newEmails[index], address };
		// If email now has content, remove from recentlyAdded
		if (address.trim() !== "" && recentlyAdded.includes(index)) {
			setRecentlyAdded(recentlyAdded.filter((i) => i !== index));
		}
		onEmailsChange(newEmails);
	};

	const clearEmail = (index: number) => {
		updateEmail(index, "");
	};

	const handleKeyDown = (
		e: React.KeyboardEvent<HTMLInputElement>,
		index: number,
	) => {
		if (e.key === "Enter") {
			e.preventDefault();
			// Only add a new email if the current one has content
			if (emails[index].address.trim() !== "") {
				addEmail();
			}
		}
	};

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className="w-full justify-start bg-transparent text-left font-normal"
				>
					<span
						className={cn(
							hasEmails
								? "text-foreground"
								: "text-muted-foreground",
							"truncate",
						)}
					>
						{displayText}
					</span>
				</Button>
			</PopoverTrigger>
			<PopoverContent
				className="w-[var(--radix-popover-trigger-width)] p-1 bg-muted/50 backdrop-blur-lg"
				align="start"
			>
				<div className="space-y-2">
					{emails.map((email, originalIndex) => {
						const shouldShow =
							email.address.trim() !== "" ||
							recentlyAdded.includes(originalIndex);
						if (!shouldShow) return null;

						return (
							<div
								key={originalIndex}
								className="relative"
								onMouseEnter={() =>
									setHoveredIndex(originalIndex)
								}
								onMouseLeave={() => setHoveredIndex(null)}
							>
								<div className="flex items-center px-4 py-2">
									<Input
										ref={(el) => {
											inputRefs.current[originalIndex] =
												el;
										}}
										value={email.address}
										onChange={(e) =>
											updateEmail(
												originalIndex,
												e.target.value,
											)
										}
										onKeyDown={(e) =>
											handleKeyDown(e, originalIndex)
										}
										placeholder="<EMAIL>"
										className="border-0 shadow-none focus-visible:ring-0 bg-neutral-800 !p-0 h-auto flex-1 text-white"
									/>
									{emails.length > 1 &&
										hoveredIndex === originalIndex && (
											<Button
												type="button"
												variant="ghost"
												size="sm"
												onClick={() =>
													removeEmail(originalIndex)
												}
												className="h-3 w-3 p-0 hover:bg-muted hover:text-primary rounded-lg flex-shrink-0 ml-1"
											>
												<IconX className="h-4 w-4" />
											</Button>
										)}
								</div>
							</div>
						);
					})}

					<Button
						type="button"
						variant="ghost"
						onClick={addEmail}
						className="w-full justify-start text-muted-foreground hover:text-foreground"
					>
						<IconPlus className="mr-2 h-4 w-4" />
						Add email
					</Button>
				</div>
			</PopoverContent>
		</Popover>
	);
}
