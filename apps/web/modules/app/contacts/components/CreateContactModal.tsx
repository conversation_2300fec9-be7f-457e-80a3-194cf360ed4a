"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useCustomFieldDefinitions } from "@app/custom-field-definitions/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { CompanySelector } from "@app/shared/components/CompanySelector";
import { useCopyToClipboard } from "@app/shared/hooks/useCopyToClipboard";
import { zodResolver } from "@hookform/resolvers/zod";
import type { ContactCreateInput } from "@repo/api/src/routes/contacts/types";
import type { Contact } from "@repo/database/src/zod";
import {
	IconCircleFilled,
	IconCopy,
	IconSquareRoundedCheck,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useCreateContact } from "../lib/api";
import { EmailField as EmailFieldComponent } from "./EmailField";
import { NameField } from "./NameField";
import { PhoneField as PhoneFieldComponent } from "./PhoneField";
import { CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

interface CreateContactModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	initialValues?: Record<string, any>;
}

const contactSchema = z.object({
	firstName: z.string().min(1, "First name is required"),
	lastName: z.string().optional(),
	image: z.string().optional(),
	persona: z.string().optional(),
	status: z.string().optional(),
	stage: z.string().optional(),
	title: z.string().optional(),
	summary: z.string().optional(),
	website: z.string().optional(),
	company: z
		.object({
			id: z.string(),
			name: z.string(),
			website: z.string().optional(),
			logo: z.string().optional(),
			domain: z.string().optional(),
		})
		.optional()
		.nullable(),
	email: z
		.array(
			z.object({
				label: z.string().default("Work"),
				address: z.string(),
				isPrimary: z.boolean().default(false),
				isBad: z.boolean().default(false),
			}),
		)
		.default([]),
	phone: z
		.array(
			z.object({
				label: z.string().default("Work"),
				number: z.string(),
				isPrimary: z.boolean().default(false),
				isBad: z.boolean().default(false),
			}),
		)
		.default([]),
	address: z
		.array(
			z.object({
				label: z.string().default("Primary"),
				street: z.string().optional(),
				street2: z.string().optional(),
				city: z.string().optional(),
				state: z.string().optional(),
				zip: z.string().optional(),
				country: z.string().optional(),
			}),
		)
		.default([]),
	social: z
		.object({
			linkedin: z.string().optional(),
			facebook: z.string().optional(),
			twitter: z.string().optional(),
			instagram: z.string().optional(),
		})
		.optional(),
});

type ContactFormData = z.infer<typeof contactSchema>;

function UrlField({
	value,
	onChange,
	placeholder,
}: {
	value: string;
	onChange: (value: string) => void;
	placeholder?: string;
}) {
	const [isFocused, setIsFocused] = useState(false);
	const [isHovered, setIsHovered] = useState(false);
	const { copy, isCopied } = useCopyToClipboard();

	const isValidUrl =
		value &&
		(value.startsWith("http") ||
			value.startsWith("www.") ||
			value.includes("."));
	const handleCopy = async (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		await copy(`${value}?utm_source=relio`, { withToast: true });
	};

	const handleLinkClick = (e: React.MouseEvent) => {
		e.preventDefault();
		if (isValidUrl) {
			let url = value;
			// Add protocol if missing
			if (!url.startsWith("http://") && !url.startsWith("https://")) {
				url = `https://${url}?utm_source=relio`;
			}
			window.open(url, "_blank", "noopener,noreferrer");
		}
	};

	if (!isFocused && isValidUrl) {
		return (
			<div
				className="relative group flex items-center justify-between w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm hover:bg-secondary/10 transition-colors cursor-pointer"
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				onClick={(e) => {
					// Only focus if we didn't click on the link text
					if (
						e.target === e.currentTarget ||
						!(e.target as HTMLElement).closest("span[data-link]")
					) {
						setIsFocused(true);
					}
				}}
			>
				<div className="flex items-center gap-2 flex-1 min-w-0">
					<span
						data-link="true"
						className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline underline-offset-4 truncate cursor-pointer"
						onClick={handleLinkClick}
					>
						{value}
					</span>
				</div>

				{isHovered && (
					<Button
						type="button"
						variant="ghost"
						size="sm"
						className="h-3 w-3 p-0 opacity-70 hover:opacity-100 flex-shrink-0"
						onClick={handleCopy}
					>
						<IconCopy className="h-3 w-3" />
					</Button>
				)}
			</div>
		);
	}

	return (
		<Input
			value={value}
			onChange={(e) => onChange(e.target.value)}
			onFocus={() => setIsFocused(true)}
			onBlur={() => setIsFocused(false)}
			placeholder={placeholder}
			type="url"
			className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
		/>
	);
}

const WebsiteField = UrlField;

export function CreateContactModal({
	open,
	onOpenChange,
	initialValues,
}: CreateContactModalProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const createContact = useCreateContact();

	// Fetch custom field definitions to get colors
	const { data: customFieldDefinitions = [] } = useCustomFieldDefinitions(
		"contact",
		activeOrganization?.id,
		!!activeOrganization?.id,
	);

	// Get status and stage options with colors from custom field definitions
	const statusOptions = React.useMemo(() => {
		const statusField = customFieldDefinitions.find(
			(def) => def.name === "status" && def.type === "select",
		);
		return (
			statusField?.options?.choices?.map((choice) => ({
				value: choice.value,
				label: choice.label,
				color: choice.color || "#6b7280",
			})) ||
			CONTACT_STATUS.map((status) => ({
				value: status.value,
				label: status.label,
				color: "#6b7280",
			}))
		);
	}, [customFieldDefinitions]);

	const stageOptions = React.useMemo(() => {
		const stageField = customFieldDefinitions.find(
			(def) => def.name === "stage" && def.type === "select",
		);
		return (
			stageField?.options?.choices?.map((choice) => ({
				value: choice.value,
				label: choice.label,
				color: choice.color || "#6b7280",
			})) ||
			CONTACT_STAGE.map((stage) => ({
				value: stage.value,
				label: stage.label,
				color: "#6b7280",
			}))
		);
	}, [customFieldDefinitions]);

	const [isSubmitting, setIsSubmitting] = useState(false);
	const [keepOpen, setKeepOpen] = useState(false);

	const form = useForm<ContactFormData>({
		resolver: zodResolver(contactSchema),
		mode: "onSubmit",
		reValidateMode: "onSubmit",
		defaultValues: {
			firstName: "",
			lastName: "",
			image: "",
			persona: "",
			status: "",
			stage: "",
			title: "",
			summary: "",
			website: "",
			company: null,
			email: [
				{ label: "Work", address: "", isPrimary: true, isBad: false },
			],
			phone: [
				{ label: "Work", number: "", isPrimary: true, isBad: false },
			],
			address: [],
			social: {
				linkedin: "",
				facebook: "",
				twitter: "",
				instagram: "",
			},
		},
	});

	// Apply initial values when modal opens or initialValues change
	React.useEffect(() => {
		if (open && initialValues) {
			// Apply any initial values that were provided
			Object.entries(initialValues).forEach(([key, value]) => {
				if (value !== undefined && value !== null && value !== "") {
					form.setValue(key as any, value);
				}
			});
		}
	}, [open, initialValues, form]);

	// Reset form when modal opens/closes or contact changes
	React.useEffect(() => {
		if (!open) {
			form.reset({
				firstName: "",
				lastName: "",
				image: "",
				persona: "",
				status: "",
				stage: "",
				title: "",
				summary: "",
				website: "",
				company: null,
				email: [
					{
						label: "Work",
						address: "",
						isPrimary: true,
						isBad: false,
					},
				],
				phone: [
					{
						label: "Work",
						number: "",
						isPrimary: true,
						isBad: false,
					},
				],
				address: [],
				social: {
					linkedin: "",
					facebook: "",
					twitter: "",
					instagram: "",
				},
			});
			return;
		}
	}, [open, form]);

	async function onSubmit(formData: ContactFormData) {
		// Check for form validation errors
		if (Object.keys(form.formState.errors).length > 0) {
			const firstError = Object.values(form.formState.errors)[0];
			toast.error(
				firstError?.message ||
					"Please fix the form errors before submitting",
			);
			return;
		}

		if (!activeOrganization?.id || !user?.id) {
			toast.error("Missing organization or user data");
			return;
		}

		setIsSubmitting(true);

		try {
			// Helper function to validate and clean URLs
			const cleanUrl = (url: string | undefined): string | undefined => {
				if (!url || url.trim() === "") return undefined;
				const trimmed = url.trim();

				// Add protocol if missing
				if (
					!trimmed.startsWith("http://") &&
					!trimmed.startsWith("https://")
				) {
					return `https://${trimmed}`;
				}
				return trimmed;
			};

			// Convert empty social media fields to null/undefined (backend expects valid URLs or null)
			const cleanedWebsite = cleanUrl(formData.website);
			const cleanedSocial: any = {};

			if (
				formData.social?.linkedin &&
				formData.social.linkedin.trim() !== ""
			) {
				cleanedSocial.linkedin = cleanUrl(formData.social.linkedin);
			}
			if (
				formData.social?.facebook &&
				formData.social.facebook.trim() !== ""
			) {
				cleanedSocial.facebook = cleanUrl(formData.social.facebook);
			}
			if (
				formData.social?.twitter &&
				formData.social.twitter.trim() !== ""
			) {
				cleanedSocial.twitter = cleanUrl(formData.social.twitter);
			}
			if (
				formData.social?.instagram &&
				formData.social.instagram.trim() !== ""
			) {
				cleanedSocial.instagram = cleanUrl(formData.social.instagram);
			}

			const payload: ContactCreateInput = {
				firstName: formData.firstName || undefined,
				lastName: formData.lastName || undefined,
				image: formData.image || undefined,
				persona: formData.persona || undefined,
				status: formData.status || undefined,
				stage: formData.stage || undefined,
				title: formData.title || undefined,
				summary: formData.summary || undefined,
				website: cleanedWebsite,
				companyId: formData.company?.id || undefined,
				email:
					formData.email?.filter((e) => e.address.trim() !== "") ||
					[],
				phone:
					formData.phone?.filter((p) => p.number.trim() !== "") || [],
				address:
					formData.address?.map((a) => ({
						...a,
						zip: a.zip ? Number(a.zip) : undefined,
					})) || [],
				social:
					Object.keys(cleanedSocial).length > 0
						? cleanedSocial
						: undefined,
				organizationId: activeOrganization.id,
			};

			try {
				await createContact.mutateAsync(payload);
				toast.success("Contact created successfully");

				setIsSubmitting(false);

				if (keepOpen) {
					const resetValues = {
						firstName: "",
						lastName: "",
						title: "",
						summary: "",
						website: "",
						company: null,
						status: "",
						stage: "",
						email: [
							{
								label: "Work",
								address: "",
								isPrimary: true,
								isBad: false,
							},
						],
						phone: [
							{
								label: "Work",
								number: "",
								isPrimary: true,
								isBad: false,
							},
						],
						address: [],
						social: {
							linkedin: "",
							facebook: "",
							twitter: "",
							instagram: "",
						},
						...(initialValues || {}),
					};
					form.reset(resetValues);
				} else {
					onOpenChange(false);
				}
			} catch (error: any) {
				toast.error(error?.message || "Failed to create contact");
				setIsSubmitting(false);
			}
		} catch (error: any) {
			console.error("Contact submission error:", error);
			toast.error(error?.message || "Failed to save contact");
			setIsSubmitting(false);
		}
	}

	// Show form errors as toast messages
	React.useEffect(() => {
		const errors = form.formState.errors;
		if (Object.keys(errors).length > 0) {
			const firstError = Object.values(errors)[0];
			if (firstError?.message) {
				toast.error(firstError.message as string);
			}
		}
	}, [form.formState.errors]);

	const isLoading = createContact.isPending || isSubmitting;

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0"
				onPointerDownOutside={(e) => e.preventDefault()}
			>
				<DialogTitle className="sr-only">
					{"Create Contact"}
				</DialogTitle>
				<DialogDescription className="sr-only">
					{"Create a new contact"}
				</DialogDescription>

				{/* Fixed Header */}
				<div className="flex items-center justify-between px-4 pt-2 pb-1">
					<div className="flex flex-row items-center gap-x-2">
						<IconSquareRoundedCheck className="h-5 w-5" />
						<span className="text-md font-light">
							{"Create Contact"}
						</span>
					</div>
				</div>

				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="flex flex-col flex-1 min-h-0 overflow-y-auto max-h-[600px]"
				>
					{/* Scrollable Content */}
					<div className="flex flex-col px-2 pt-2 pb-4 space-y-4 overflow-y-auto flex-1">
						{/* Name Section */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Name
								<span className="text-red-500 ml-1">*</span>
								{form.formState.errors.firstName && (
									<span className="text-red-500 text-xs ml-2">
										{
											form.formState.errors.firstName
												.message
										}
									</span>
								)}
							</h3>
							<NameField
								firstName={form.watch("firstName") || ""}
								lastName={form.watch("lastName") || ""}
								onFirstNameChange={(value) =>
									form.setValue("firstName", value)
								}
								onLastNameChange={(value) =>
									form.setValue("lastName", value)
								}
								placeholder="Set Name (First name required)"
							/>
						</div>

						{/* Email Section */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Email addresses
							</h3>
							<EmailFieldComponent
								emails={form.watch("email") || []}
								onEmailsChange={(emails) =>
									form.setValue("email", emails)
								}
								placeholder="Set Email addresses"
							/>
						</div>

						{/* Description */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Summary
							</h3>
							<Textarea
								{...form.register("summary")}
								placeholder="Set Summary"
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						</div>

						{/* Company */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Company
							</h3>
							<CompanySelector
								value={form.watch("company") || null}
								onValueChange={(company) => {
									form.setValue("company", company);
								}}
								organizationId={activeOrganization?.id || ""}
							/>
						</div>

						{/* Status */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Status
							</h3>
							<Select
								value={form.watch("status") || ""}
								onValueChange={(value) =>
									form.setValue("status", value)
								}
							>
								<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
									<SelectValue placeholder="Select status..." />
								</SelectTrigger>
								<SelectContent>
									{statusOptions.map((status) => (
										<SelectItem
											key={status.value}
											value={status.value}
										>
											<div className="flex items-center gap-2">
												<IconCircleFilled
													className="w-4 h-4"
													style={{
														color: status.color,
													}}
												/>
												{status.label}
											</div>
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Stage */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Stage
							</h3>
							<Select
								value={form.watch("stage") || ""}
								onValueChange={(value) =>
									form.setValue("stage", value)
								}
							>
								<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
									<SelectValue placeholder="Select stage..." />
								</SelectTrigger>
								<SelectContent>
									{stageOptions.map((stage) => (
										<SelectItem
											key={stage.value}
											value={stage.value}
										>
											<div className="flex items-center gap-2">
												<IconCircleFilled
													className="w-4 h-4"
													style={{
														color: stage.color,
													}}
												/>
												{stage.label}
											</div>
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Job title */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Job title
							</h3>
							<Input
								{...form.register("title")}
								placeholder="Set Job title..."
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						</div>

						{/* Phone numbers */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Phone numbers
							</h3>
							<PhoneFieldComponent
								phones={form.watch("phone") || []}
								onPhonesChange={(phones) =>
									form.setValue("phone", phones)
								}
								placeholder="Set Phone numbers"
							/>
						</div>

						{/* Website */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Website
							</h3>
							<WebsiteField
								value={form.watch("website") || ""}
								onChange={(value) =>
									form.setValue("website", value)
								}
								placeholder="https://example.com"
							/>
						</div>

						{/* Social Media */}
						<div className="space-y-1">
							<h3 className="text-xs text-muted-foreground">
								Social Media
							</h3>

							<div className="space-y-2">
								<UrlField
									value={form.watch("social.linkedin") || ""}
									onChange={(value) =>
										form.setValue("social.linkedin", value)
									}
									placeholder="LinkedIn URL"
								/>
								<UrlField
									value={form.watch("social.facebook") || ""}
									onChange={(value) =>
										form.setValue("social.facebook", value)
									}
									placeholder="Facebook URL"
								/>
								<UrlField
									value={form.watch("social.twitter") || ""}
									onChange={(value) =>
										form.setValue("social.twitter", value)
									}
									placeholder="Twitter URL"
								/>
								<UrlField
									value={form.watch("social.instagram") || ""}
									onChange={(value) =>
										form.setValue("social.instagram", value)
									}
									placeholder="Instagram URL"
								/>
							</div>
						</div>
					</div>

					{/* Fixed Footer */}
					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<div className="flex items-center gap-2 mr-auto">
							<Switch
								id="keep-open"
								checked={keepOpen}
								onCheckedChange={setKeepOpen}
							/>
							<Label
								htmlFor="keep-open"
								className="text-sm text-muted-foreground"
							>
								Create more
							</Label>
						</div>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={isLoading}
						>
							{isLoading ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>{"Creating..."}</span>
								</div>
							) : (
								"Create Contact"
							)}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
