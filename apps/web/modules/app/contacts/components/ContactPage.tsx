"use client";

import React, { useState } from "react";
import { useContact, useRelatedContacts, useUpdateContact, useUpdateRelatedContacts } from "@app/contacts/lib/api";
import PageHeader from "@shared/components/PageHeader";
import ReorderList from "@ui/components/reorder-list";
import GridItemContainer from "@ui/components/grid-item-container";
import ContactDetails from "./sections/ContactDetails";
import ContactSummary from "./sections/ContactSummary";
import ContactMetadata from "./sections/ContactMetadata";
import ConfigurePageModal from "./ConfigurePageModal";
import { leftColItems, rightColItems } from "./sections/columns";
import type { ActiveOrganization } from "@repo/auth";
import RelatedContacts from "./sections/RelatedContacts";
import { TagSelector } from "@app/shared/components/TagSelector";
import { MoreInfo } from "@app/shared/components/MoreInfo";
import { Activities } from "@app/activities";
import { useSession } from "@app/auth/hooks/use-session";
import Portfolio from "@app/contacts/components/sections/Portfolio";
import LinkedContacts from "@app/contacts/components/sections/LinkedContacts";
import ContactNotes from "@app/contacts/components/sections/ContactNotes";
import ContactTasks from "@app/contacts/components/sections/ContactTasks";
import { useContactProperties } from "@shared/hooks/useContactProperties";
import { useLinkedContacts } from "@shared/hooks/useLinkedContacts";
import { IconPlus } from "@tabler/icons-react";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import { createNote } from "@app/notes/lib/api";
import { useQueryClient } from "@tanstack/react-query";

interface ContactPageProps {
	id: string;
	activeOrganization: ActiveOrganization | null;
	isFavorite: boolean;
	onNavigate?: (direction: "prev" | "next") => void;
	hasPrevious?: boolean;
	hasNext?: boolean;
}

const ContactPage = ({
	id,
	activeOrganization,
	isFavorite,
	onNavigate,
	hasPrevious,
	hasNext,
}: ContactPageProps) => {
	const { data: contact } = useContact(id, activeOrganization?.id);
	const { data: relatedContacts } = useRelatedContacts(id, activeOrganization?.id);
	const { data: contactProperties, isLoading: isLoadingProperties } = useContactProperties(id, activeOrganization?.id || '');
	const { data: linkedContacts, isLoading: isLoadingLinkedContacts } = useLinkedContacts(id, activeOrganization?.id || '');
	const { mutateAsync: updateContact } = useUpdateContact(activeOrganization?.id);
	const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
	const { user } = useSession();
	const { openCreateTask } = useTasks();
	const queryClient = useQueryClient();

	const renderLeftColumn = (item: { id: string; title: string }) => {
		if (!activeOrganization) return null;
		
		switch (item.id) {
			case "contact-details":
				return (
					<GridItemContainer item={item}>
						<ContactDetails data={contact as any} organization={activeOrganization} />
					</GridItemContainer>
				);
			case "related-contacts":
				const transformedRelatedContacts = relatedContacts?.map(contact => ({
					...contact,
					id: contact._id?.$oid || "", 
				})) || [];

				return (
					<GridItemContainer item={item}>
						<RelatedContacts 
							data={contact as any} 
							organization={activeOrganization} 
							relatedContacts={transformedRelatedContacts}
							loading={!relatedContacts}
						/>
					</GridItemContainer>
				);
			case "more-info":
				return (
					<GridItemContainer item={item}>
						<MoreInfo 
							data={contact as any} 
							objectType="contact" 
							organization={{ id: activeOrganization.id }}
							onUpdate={async (data) => {
								await updateContact({
									...data,
								});
							}}
						/>
					</GridItemContainer>
				);
			case "tags":
				return (
					<GridItemContainer item={item}>
						{contact?.id ? (
							<TagSelector objectId={contact.id} objectType="contact" />
						) : (
							<div className="animate-pulse h-6 bg-muted rounded w-full" />
						)}
					</GridItemContainer>
				);
			default:
				return null;
		}
	};

	const renderRightColumn = (item: { id: string; title: string }) => {
		if (!activeOrganization) return null;
		
		switch (item.id) {
			case "summary":
				return (
					<GridItemContainer item={item}>
						<ContactSummary data={contact as any} organization={activeOrganization} />
					</GridItemContainer>
				);
			case "tasks":
				return (
					<GridItemContainer item={item} icon={<IconPlus className="h-4 w-4 text-muted-foreground" />} onIconClick={() => {
						if (contact?.id) {
							const relatedObject = {
								id: contact.id,
								name: (contact as any).name || `${(contact as any).firstName || ''} ${(contact as any).lastName || ''}`.trim() || "Unnamed Contact",
								recordType: "contact" as const,
							};
							
							openCreateTask({
								defaultStatus: "todo",
								relatedObject,
							});
						}
					}}>
						<ContactTasks 
							contact={contact as any}
							organization={activeOrganization} 
							user={user}
						/>
					</GridItemContainer>
				);
			case "portfolio":
				return (
					<GridItemContainer item={item}>
						<Portfolio
							data={contact as any}
							organization={activeOrganization}
							properties={contactProperties as any || []}
							loading={isLoadingProperties}
						/>
					</GridItemContainer>
				);
			case "linked-contacts":
				return (
					<GridItemContainer item={item} icon={<IconPlus className="h-4 w-4 text-muted-foreground" />} >
						<LinkedContacts
							data={contact as any}
							organization={activeOrganization}
							linkedContacts={linkedContacts as any || []}
							loading={isLoadingLinkedContacts}
						/>
					</GridItemContainer>
				);
			// case "notes":
			// 	return (
			// 		<GridItemContainer 
			// 			item={item} 
			// 			icon={<IconPlus className="h-4 w-4 text-muted-foreground" />}
			// 			onIconClick={async () => {
			// 				if (contact?.id && activeOrganization?.id) {
			// 					try {
			// 						const contactName = (contact as any).name || 
			// 							`${(contact as any).firstName || ''} ${(contact as any).lastName || ''}`.trim() || 
			// 							"Unnamed Contact";
									
			// 						await createNote({
			// 							title: `Note for ${contactName}`,
			// 							objectId: contact.id,
			// 							objectType: "contact",
			// 							organizationId: activeOrganization.id,
			// 						});
									
			// 						// Invalidate queries to refresh the notes list
			// 						queryClient.invalidateQueries({
			// 							queryKey: ["notes", "object", contact.id, "contact", activeOrganization.id],
			// 						});
			// 					} catch (error) {
			// 						console.error('Failed to create note:', error);
			// 					}
			// 				}
			// 			}}
			// 		>
			// 			<ContactNotes 
			// 				contact={contact as any}
			// 				organization={activeOrganization} 
			// 			/>
			// 		</GridItemContainer>
			// 	);
			case "metadata":
				return (
					<GridItemContainer item={item}>
						<ContactMetadata 
							data={contact as any} 
							organization={activeOrganization} 
						/>
					</GridItemContainer>
				);
			default:
				return null;
		}
	};

	const handleConfigurePage = () => {
		setIsConfigureModalOpen(true);
	};

	if (!activeOrganization) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-muted-foreground">Loading...</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-[calc(100vh-50px)]">
			{/* Header */}
			<PageHeader
				activeOrganization={activeOrganization}
				data={contact}
				isFavorite={isFavorite}
				objectType="contact"
				onTitleUpdate={() => {}}
				onNavigate={onNavigate}
				hasPrevious={hasPrevious}
				hasNext={hasNext}
				onConfigurePage={handleConfigurePage}
			/>

			{/* Body with three columns */}
			<div className="flex-1 flex overflow-hidden">
				{/* Left Column - Contact Details */}
				<div className="w-md border-r border-border overflow-y-auto">
					<ReorderList
						initialItems={leftColItems}
						renderItem={renderLeftColumn}
						storageKey="contact:left-column-order"
					/>
				</div>

				{/* Center Column - Activity Feed */}
				<div className="flex-1 border-r border-border overflow-y-auto">
					{contact && activeOrganization && (
						<Activities 
							data={contact}
							organization={activeOrganization}
							user={user}
							recordType="contact"
						/>
					)}
				</div>

				{/* Right Column - Additional Information */}
				<div className="w-lg overflow-y-auto">
					<ReorderList
						initialItems={rightColItems}
						renderItem={renderRightColumn}
						storageKey="contact:right-column-order"
					/>
				</div>
			</div>

			{/* Configure Page Modal */}
			<ConfigurePageModal
				open={isConfigureModalOpen}
				onOpenChange={setIsConfigureModalOpen}
				objectType="contact"
			/>
		</div>
	);
};

export default ContactPage;