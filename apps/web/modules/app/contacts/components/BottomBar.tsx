import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconGitMerge, IconTrash, IconX } from "@tabler/icons-react";
import { NumberBadge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
// import AddToListModal from "@app/organizations/components/modals/add-to-list-modal";
// import CreateListModal from "@app/organizations/components/modals/create-list-modal";
import { toast } from "sonner";
import { useBatchDeleteContacts } from "../lib/api";

interface BottomBarProps {
	selectedRecords?: any[];
	objectType?: string;
	setSelectedRecords?: (records: any[]) => void;
}

const BottomBar = ({
	selectedRecords,
	objectType,
	setSelectedRecords,
}: BottomBarProps) => {
	const { activeOrganization } = useActiveOrganization();
	const { mutate: batchDeleteContacts, isPending: isDeleting } =
		useBatchDeleteContacts();

	const handleBatchDelete = () => {
		if (!selectedRecords || selectedRecords.length === 0) {
			toast.error("No records selected");
			return;
		}

		if (!activeOrganization?.id) {
			toast.error("Organization not found");
			return;
		}

		const ids = selectedRecords.map((record) => record.id);

		batchDeleteContacts(
			{
				ids,
				organizationId: activeOrganization.id,
			},
			{
				onSuccess: (data) => {
					toast.success(
						`${data.deletedCount} record${data.deletedCount > 1 ? "s" : ""} deleted successfully`,
					);
				},
				onError: (error) => {
					console.error("Batch delete error:", error);
					toast.error("Failed to delete records");
				},
			},
		);
	};

	if (!objectType || !selectedRecords || selectedRecords.length === 0)
		return null;

	return (
		<div className="fixed bottom-5 left-1/2 transform -translate-x-1/2 app-bg/75 border border-border bg-sidebar rounded-xl shadow-lg p-4 flex justify-between items-center w-11/12 max-w-3xl h-12 backdrop-blur-xl z-50">
			<div className="flex items-center gap-1">
				<NumberBadge number={selectedRecords?.length} />
				<span className="text-xs text-muted-foreground">selected</span>
			</div>
			<div className="flex items-center space-x-1">
				{/* TODO: Create List Modal */}
				{/* <CreateListModal objectType={objectType} userId={user?.id} selectedRecords={selectedRecords} /> */}

				{/* TODO: Create Add to List Modal */}
				{/* <AddToListModal
          userId={user?._id as Id<"users">}
          recordType={recordType as string}
          selectedRecords={selectedRecords as any[]}
        /> */}
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button variant="relio" size="sm">
							More
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="rounded-xl space-y-1 mb-2 -mr-4 gap-x-1"
						align="end"
					>
						{objectType &&
							selectedRecords &&
							selectedRecords?.length > 1 && (
								<>
									<DropdownMenuItem
										className="rounded-lg gap-1"
										onClick={() => {
											// TODO: Create api call for merging records
											// setState("openMergeRecordModal", true);
										}}
									>
										<IconGitMerge className="h-3 w-3 mr-2" />
										Merge {selectedRecords?.length} record
										{selectedRecords &&
										selectedRecords?.length > 1
											? "s"
											: ""}
									</DropdownMenuItem>
									<DropdownMenuSeparator />
								</>
							)}
						<DropdownMenuItem
							className="rounded-lg !bg-destructive hover:!bg-red-800 gap-1"
							onClick={handleBatchDelete}
							disabled={isDeleting}
						>
							<IconTrash className="h-3 w-3" />
							{isDeleting
								? "Deleting..."
								: `Delete ${selectedRecords?.length} record${selectedRecords && selectedRecords?.length > 1 ? "s" : ""}`}
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
				{setSelectedRecords && (
					<Button
						variant="relio"
						size="sm"
						onClick={() => {
							setSelectedRecords([]);
						}}
					>
						<IconX className="h-3 w-3" />
					</Button>
				)}
			</div>
		</div>
	);
};

export default BottomBar;
