"use client";

import React, { useState } from "react";
import GridItem from "@ui/components/grid-item";
import { IconEdit, IconPlus, IconUser, IconMail, IconPhone, IconMapPin } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";
import { cn } from "@ui/lib";
import { RelatedContactModal } from "./RelatedContactModal";
import { formatPhoneNumber } from "@shared/lib/utils";
import { Skeleton } from "@ui/components/skeleton";

interface RelatedContactData {
  id: string;
  firstName?: string;
  lastName?: string;
  label?: string;
  phone?: Array<{
    number: string;
    label?: string;
    isPrimary?: boolean;
    isBad?: boolean;
  }>;
  email?: Array<{
    address: string;
    label?: string;
    isPrimary?: boolean;
    isBad?: boolean;
  }>;
  address?: Array<{
    street: string;
    street2?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
    label?: string;
  }>;
}

interface RelatedContactsProps {
  data: {
    id: string;
  };
  organization: ActiveOrganization;
  loading?: boolean;
  relatedContacts?: RelatedContactData[];
}

const ContactCardSkeleton: React.FC = () => (
  <GridItem
    value={
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-32" /> {/* Name */}
          <Skeleton className="h-4 w-16 rounded-full" /> {/* Label */}
        </div>
        <div className="flex flex-col gap-0.5 text-xs">
          <div className="flex items-center gap-1.5">
            <IconPhone className="h-3 w-3 shrink-0 text-muted-foreground" />
            <Skeleton className="h-3 w-24" /> {/* Phone */}
            <Skeleton className="h-3 w-8" /> {/* Phone label */}
          </div>
          <div className="flex items-center gap-1.5">
            <IconMail className="h-3 w-3 shrink-0 text-muted-foreground" />
            <Skeleton className="h-3 w-36" /> {/* Email */}
            <Skeleton className="h-3 w-8" /> {/* Email label */}
          </div>
          <div className="flex items-center gap-1.5">
            <IconMapPin className="h-3 w-3 shrink-0 text-muted-foreground" />
            <Skeleton className="h-3 w-48" /> {/* Address */}
            <Skeleton className="h-3 w-8" /> {/* Address label */}
          </div>
        </div>
      </div>
    }
    icon={<IconUser className="h-4 w-4 text-muted-foreground" />}
  />
);

const RelatedContacts = ({ data, organization, loading = false, relatedContacts = [] }: RelatedContactsProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleEditContact = (contact: RelatedContactData, index: number) => {
    setEditingIndex(index);
    setIsModalOpen(true);
  };

  const handleAddContact = () => {
    setEditingIndex(null);
    setIsModalOpen(true);
  };

  const handleRelatedContactsChange = (relatedContacts: any[]) => {
    // This callback is used for non-database scenarios
    // When contactId is provided, the modal handles database updates directly
    console.warn("Related contacts updated via callback:", relatedContacts);
  };

  const getPrimaryOrFirst = <T extends { isPrimary?: boolean }>(items?: T[]): T | undefined => {
    if (!items?.length) return undefined;
    return items.find(item => item.isPrimary) || items[0];
  };

  const formatAddress = (address: NonNullable<RelatedContactData["address"]>[0]) => {
    const parts = [
      address.street,
      address.street2,
      [address.city, address.state, address.zip].filter(Boolean).join(", "),
      address.country !== "United States" ? address.country : undefined
    ].filter(Boolean);
    return parts.join(", ");
  };

  if (!data) {
    return (
      <div className="space-y-2">
        <ContactCardSkeleton />
      </div>
    );
  }

  return (
    <>
      <div className="space-y-1">
        {loading ? (
          <>
            <ContactCardSkeleton />
          </>
        ) : relatedContacts && relatedContacts.length > 0 ? (
          <>
            {relatedContacts.map((contact, index) => {
              const primaryPhone = getPrimaryOrFirst(contact.phone);
              const primaryEmail = getPrimaryOrFirst(contact.email);
              const primaryAddress = contact.address?.[0];

              return (
                <GridItem
                  key={`${contact.id || index}-${contact.firstName}-${contact.lastName}`}
                  value={
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <span className="text-foreground font-medium">
                          {contact.firstName} {contact.lastName}
                        </span>
                        {contact.label && (
                          <span className="text-muted-foreground text-xs px-1.5 py-0.5 bg-muted rounded-full">
                            {contact.label}
                          </span>
                        )}
                      </div>
                      <div className="flex flex-col gap-0.5 text-xs text-muted-foreground">
                        {primaryPhone && (
                          <div className="flex items-center gap-1.5">
                            <IconPhone className="h-3 w-3 shrink-0" />
                            <a 
                              href={`tel:${primaryPhone.number}`}
                              className={cn(
                                "hover:underline",
                                primaryPhone.isBad && "text-red-500 line-through"
                              )}
                            >
                              {formatPhoneNumber(primaryPhone.number)}
                            </a>
                            {primaryPhone.label && (
                              <span className="text-[10px] text-muted-foreground/70">
                                • {primaryPhone.label}
                              </span>
                            )}
                          </div>
                        )}
                        {primaryEmail && (
                          <div className="flex items-center gap-1.5">
                            <IconMail className="h-3 w-3 shrink-0" />
                            <a 
                              href={`mailto:${primaryEmail.address}`}
                              className={cn(
                                "hover:underline",
                                primaryEmail.isBad && "text-red-500 line-through"
                              )}
                            >
                              {primaryEmail.address}
                            </a>
                            {primaryEmail.label && (
                              <span className="text-[10px] text-muted-foreground/70">
                                • {primaryEmail.label}
                              </span>
                            )}
                          </div>
                        )}
                        {primaryAddress && (
                          <div className="flex items-center gap-1.5">
                            <IconMapPin className="h-3 w-3 shrink-0" />
                            <span className="truncate">
                              {formatAddress(primaryAddress)}
                            </span>
                            {primaryAddress.label && (
                              <span className="text-[10px] text-muted-foreground/70">
                                • {primaryAddress.label}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  }
                  edit={<IconEdit className="h-3 w-3" />}
                  add={<IconPlus className="h-3 w-3" />}
                  icon={<IconUser className="h-4 w-4 text-muted-foreground" />}
                  onEdit={() => handleEditContact(contact, index)}
                  onAdd={handleAddContact}
                />
              );
            })}
          </>
        ) : (
          <GridItem
            value="Add Related Contact"
            onClick={handleAddContact}
            icon={<IconUser className="h-4 w-4 text-muted-foreground" />}
          />
        )}
      </div>

      <RelatedContactModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        relatedContacts={relatedContacts}
        editingIndex={editingIndex}
        onRelatedContactsChange={handleRelatedContactsChange}
        contactId={data.id}
        organizationId={organization?.id}
        loading={loading}
      />
    </>
  );
};

export default RelatedContacts; 