"use client";

import React, { useState } from "react";
import GridItem from "@ui/components/grid-item";
import { IconEdit, IconPlus, IconMail, IconPhone, IconMapPin } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";
import { PhoneNumbersModal } from "./PhoneNumbersModal";
import { EmailModal } from "./EmailModal";
import { AddressModal } from "./AddressModal";
import { cn } from "@ui/lib";
import { Skeleton } from "@ui/components/skeleton";

interface Address {
	street: string;
	street2?: string;
	city?: string;
	state?: string;
	zip?: string;
	country?: string;
	label?: string;
}

interface ContactData {
	id: string;
	phone?: Array<{
		number: string;
		label?: string;
		isPrimary?: boolean;
		isBad?: boolean;
	}>;
	email?: Array<{
		address: string;
		label?: string;
		isPrimary?: boolean;
		isBad?: boolean;
	}>;
	address?: Address[];
	[key: string]: any;
}

interface ContactDetailsProps {
	data: ContactData;
	organization: ActiveOrganization;
	loading?: boolean;
}

const ContactDetails = ({ data, organization, loading = false }: ContactDetailsProps) => {
	const [phoneModalOpen, setPhoneModalOpen] = useState(false);
	const [editingPhoneIndex, setEditingPhoneIndex] = useState<number | null>(null);
	const [emailModalOpen, setEmailModalOpen] = useState(false);
	const [editingEmailIndex, setEditingEmailIndex] = useState<number | null>(null);
	const [addressModalOpen, setAddressModalOpen] = useState(false);
	const [editingAddressIndex, setEditingAddressIndex] = useState<number | null>(null);

	// Phone handlers
	const handleEditPhone = (phone: any, index: number) => {
		setEditingPhoneIndex(index);
		setPhoneModalOpen(true);
	};

	const handleAddPhone = () => {
		setEditingPhoneIndex(null);
		setPhoneModalOpen(true);
	};

	const handlePhoneNumbersChange = (phoneNumbers: any[]) => {
		// This callback is used for non-database scenarios (like during contact creation)
		// When contactId is provided, the modal handles database updates directly
		console.warn("Phone numbers updated via callback:", phoneNumbers);
	};

	// Email handlers
	const handleEditEmail = (email: any, index: number) => {
		setEditingEmailIndex(index);
		setEmailModalOpen(true);
	};

	const handleAddEmail = () => {
		setEditingEmailIndex(null);
		setEmailModalOpen(true);
	};

	const handleEmailsChange = (emails: any[]) => {
		// This callback is used for non-database scenarios (like during contact creation)
		// When contactId is provided, the modal handles database updates directly
		console.warn("Emails updated via callback:", emails);
	};

	const handleEditAddress = (address: any, index: number) => {
		setEditingAddressIndex(index);
		setAddressModalOpen(true);
	};

	const handleAddAddress = () => {
		setEditingAddressIndex(null);
		setAddressModalOpen(true);
	};

	const handleAddressesChange = (addresses: any[]) => {
		// This callback is used for non-database scenarios (like during contact creation)
		// When contactId is provided, the modal handles database updates directly
		console.warn("Addresses updated via callback:", addresses);
	};

	return (
		<>
			<div className="space-y-1">
				{/* Phone Numbers */}
				{data?.phone && data?.phone?.length > 0 ? (
					<>
						{data.phone.map((phone, index) => (
							<GridItem
								key={index}
								value={loading ? (
									<Skeleton className="h-4 w-32" />
								) : (
									<a 
										href={`tel:${phone.number}`} 
										className={cn(
											"hover:underline",
											phone.isBad && "text-red-500 line-through"
										)}
									>
										{phone.number}
									</a>
								)}
								badge={loading ? undefined : phone.label}
								edit={loading ? undefined : <IconEdit className="h-3 w-3" />}
								add={loading ? undefined : <IconPlus className="h-3 w-3" />}
								icon={<IconPhone className="h-4 w-4 text-muted-foreground" />}
								onEdit={() => !loading && handleEditPhone(phone, index)}
								onAdd={() => !loading && handleAddPhone()}
							/>
						))}
					</>
				) : (
					<GridItem
						value={loading ? <Skeleton className="h-4 w-32" /> : "Add Phone Number"}
						onClick={() => !loading && handleAddPhone()}
						icon={<IconPhone className="h-4 w-4 text-muted-foreground" />}
					/>
				)}

				{/* Emails */}
				{data?.email && data?.email?.length > 0 ? (
					<>
						{data.email.map((email, index) => (
							<GridItem
								key={index}
								value={loading ? (
									<Skeleton className="h-4 w-48" />
								) : (
									<a href={`mailto:${email.address}`} className={cn(
										"hover:underline",
										email.isBad && "text-red-500 line-through"
									)}>
										{email.address}
									</a>
								)}
								badge={loading ? undefined : email.label}
								edit={loading ? undefined : <IconEdit className="h-3 w-3" />}
								add={loading ? undefined : <IconPlus className="h-3 w-3" />}
								icon={<IconMail className="h-4 w-4 text-muted-foreground" />}
								onEdit={() => !loading && handleEditEmail(email, index)}
								onAdd={() => !loading && handleAddEmail()}
							/>
						))}
					</>
				) : (
					<GridItem
						value={loading ? <Skeleton className="h-4 w-24" /> : "Add Email"}
						onClick={() => !loading && handleAddEmail()}
						icon={<IconMail className="h-4 w-4 text-muted-foreground" />}
					/>
				)}

				{/* Address */}
				{data?.address && Array.isArray(data?.address) && data?.address?.length > 0 ? (
					<>
						{data.address.map((address: any, index: number) => (
							<GridItem
								key={index}
								value={loading ? (
									<div className="flex flex-col gap-1">
										<Skeleton className="h-4 w-48" />
										<Skeleton className="h-3 w-32" />
									</div>
								) : (
									<div className="text-sm truncate flex flex-col">
										<span className="text-foreground">
											{address.street}
											{address.street2 && `, ${address.street2}`}
										</span>
										<span className="text-muted-foreground text-xs">
											{[address.city, address.state, address.zip]
												.filter(Boolean)
												.join(", ")}
											{address.country && address.country !== "United States" && `, ${address.country}`}
										</span>
									</div>
								)}
								badge={loading ? undefined : address.label}
								edit={loading ? undefined : <IconEdit className="h-3 w-3" />}
								add={loading ? undefined : <IconPlus className="h-3 w-3" />}
								icon={<IconMapPin className="h-4 w-4 text-muted-foreground" />}
								onEdit={() => !loading && handleEditAddress(address, index)}
								onAdd={() => !loading && handleAddAddress()}
							/>
						))}
					</>
				) : (
					<GridItem
						value={loading ? <Skeleton className="h-4 w-28" /> : "Add Address"}
						onClick={() => !loading && handleAddAddress()}
						icon={<IconMapPin className="h-4 w-4 text-muted-foreground" />}
					/>
				)}
			</div>

			<PhoneNumbersModal
				open={phoneModalOpen}
				onOpenChange={setPhoneModalOpen}
				phoneNumbers={data?.phone || []}
				editingIndex={editingPhoneIndex}
				onPhoneNumbersChange={handlePhoneNumbersChange}
				contactId={data?.id}
				organizationId={organization?.id}
			/>

			<EmailModal
				open={emailModalOpen}
				onOpenChange={setEmailModalOpen}
				emails={data?.email || []}
				editingIndex={editingEmailIndex}
				onEmailsChange={handleEmailsChange}
				contactId={data?.id}
				organizationId={organization?.id}
			/>

			<AddressModal
				open={addressModalOpen}
				onOpenChange={setAddressModalOpen}
				addresses={data?.address || []}
				editingIndex={editingAddressIndex}
				onAddressesChange={handleAddressesChange}
				contactId={data?.id}
				organizationId={organization?.id}
			/>
		</>
	);
};

export default ContactDetails; 