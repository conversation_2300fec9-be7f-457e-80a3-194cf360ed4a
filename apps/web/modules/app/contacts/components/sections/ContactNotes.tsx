"use client";

import React from "react";
import { useNotesForObjectRecord, createNote } from "@app/notes/lib/api";
import type { ActiveOrganization } from "@repo/auth";
import type { Contact, Note } from "@repo/database";
import { useQueryClient } from "@tanstack/react-query";
import { IconNotes } from "@tabler/icons-react";
import { ScrollArea } from "@ui/components/scroll-area";
import { NoteCard } from "../../../../../app/(authenticated)/app/(organizations)/[organizationSlug]/notes/NoteCard";
import GridItem from "@ui/components/grid-item";

interface ContactNotesProps {
	contact: Contact;
	organization: ActiveOrganization | null;
}

export default function ContactNotes({ contact, organization }: ContactNotesProps) {
	const queryClient = useQueryClient();
	
	// Always call hooks at the top level - never inside conditions
	const { data: notes = [], isLoading } = useNotesForObjectRecord(
		contact?.id,
		"contact",
		organization?.id
	);
	
	if (!organization) {
		return (
			<div className="p-4">
				<div className="text-muted-foreground text-sm">
					Organization not available
				</div>
			</div>
		);
	}

	if (!contact || !contact.id) {
		return (
			<div className="p-4">
				<div className="text-muted-foreground text-sm">
					Contact not available
				</div>
			</div>
		);
	}

	const handleCreateNote = async () => {
		try {
			const contactName = `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 
				"Unnamed Contact";
			
			await createNote({
				title: `Note for ${contactName}`,
				objectId: contact.id,
				objectType: "contact",
				organizationId: organization.id,
			});
			
			// Invalidate queries to refresh the notes list
			queryClient.invalidateQueries({
				queryKey: ["notes", "object", contact.id, "contact", organization.id],
			});
		} catch (error) {
			console.error('Failed to create note:', error);
		}
	};

	return (
		<div>
			{notes.length === 0 ? (
				<GridItem
					value="Create Note"
					onClick={handleCreateNote}
					icon={<IconNotes className="h-4 w-4 text-muted-foreground" />}
				/>
			) : (
				<ScrollArea className="">
					<div className="space-y-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
						{notes.map((note: any) => (
							<NoteCard key={note.id} note={note} className="!max-w-full" />
						))}
					</div>
				</ScrollArea>
			)}
		</div>
	);
}   