"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { useUpdateContact } from "../../lib/api";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { IconMail, IconPlus, IconTrash } from "@tabler/icons-react";
import { cn } from "@ui/lib";

interface EmailModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	emails: any[];
	editingIndex: number | null;
	onEmailsChange: (emails: any[]) => void;
	contactId?: string;
	organizationId?: string;
}

const emailSchema = z.object({
	email: z
		.array(
			z.object({
				label: z.string().default("Work"),
				address: z.string().email("Invalid email address"),
				isPrimary: z.boolean().default(false),
				isBad: z.boolean().default(false),
			}),
		)
		.min(1, "At least one email address is required"),
});

type EmailFormData = z.infer<typeof emailSchema>;

export function EmailModal({
	open,
	onOpenChange,
	emails,
	editingIndex,
	onEmailsChange,
	contactId,
	organizationId,
}: EmailModalProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [customLabelIndices, setCustomLabelIndices] = useState<Set<number>>(new Set());
	const updateContact = useUpdateContact(organizationId);

	const form = useForm<EmailFormData>({
		resolver: zodResolver(emailSchema),
		mode: "onSubmit",
		reValidateMode: "onSubmit",
		defaultValues: {
			email: [],
		},
	});

	// Initialize form data when modal opens
	React.useEffect(() => {
		if (open) {
			let emailsToSet: any[] = [];
			
			if (editingIndex !== null && emails[editingIndex]) {
				// Editing a specific email
				emailsToSet = [emails[editingIndex]];
			} else if (emails.length > 0) {
				// Editing all emails
				emailsToSet = emails;
			} else {
				// Adding first email
				emailsToSet = [
					{
						label: "Work",
						address: "",
						isPrimary: true,
						isBad: false,
					},
				];
			}
			
			form.reset({
				email: emailsToSet,
			});
			
			// Initialize custom label indices for existing custom labels
			const predefinedLabels = ["Work", "Home", "School", "Other"];
			const customIndices = new Set<number>();
			emailsToSet.forEach((email, index) => {
				if (email.label && !predefinedLabels.includes(email.label)) {
					customIndices.add(index);
				}
			});
			setCustomLabelIndices(customIndices);
		}
	}, [open, emails, editingIndex, form]);

	// Reset form when modal closes
	React.useEffect(() => {
		if (!open) {
			form.reset({
				email: [],
			});
			setCustomLabelIndices(new Set());
		}
	}, [open, form]);

	async function onSubmit(formData: EmailFormData) {
		setIsSubmitting(true);

		try {
			// Filter out empty email addresses
			const validEmails = formData.email.filter(
				(email) => email.address.trim() !== ""
			);

			if (validEmails.length === 0) {
				toast.error("Please enter at least one email address");
				setIsSubmitting(false);
				return;
			}

			let finalEmails: any[];

			// If editing a specific email, replace it in the original array
			if (editingIndex !== null) {
				finalEmails = [...emails];
				finalEmails[editingIndex] = validEmails[0];
			} else {
				// Replace all emails or add new ones
				finalEmails = validEmails;
			}

			// Update in database if contactId is provided
			if (contactId) {
				await updateContact.mutateAsync({
					id: contactId,
					email: finalEmails.map((email) => ({
						address: email.address,
						label: email.label,
						isPrimary: email.isPrimary,
						isBad: email.isBad,
					})),
				});
				toast.success(
					editingIndex !== null
						? "Email address updated successfully"
						: "Email addresses saved successfully"
				);
			} else {
				// Fallback to callback for non-database scenarios (like during contact creation)
				onEmailsChange(finalEmails);
				toast.success("Email addresses updated");
			}

			onOpenChange(false);
		} catch (error: any) {
			toast.error(error?.message || "Failed to save email addresses");
		} finally {
			setIsSubmitting(false);
		}
	}

	// Show form errors as toast messages
	React.useEffect(() => {
		const errors = form.formState.errors;
		if (Object.keys(errors).length > 0) {
			const firstError = Object.values(errors)[0];
			if (firstError?.message) {
				toast.error(firstError.message as string);
			}
		}
	}, [form.formState.errors]);

	const title = editingIndex !== null ? "Edit Email Address" : "Manage Email Addresses";

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0"
				onPointerDownOutside={(e) => e.preventDefault()}
			>
				<DialogTitle className="sr-only">{title}</DialogTitle>
				<DialogDescription className="sr-only">
					{editingIndex !== null
						? "Edit an existing email address"
						: "Add or edit email addresses"}
				</DialogDescription>

				<div className="flex items-center justify-between px-4 pt-2 pb-1">
					<div className="flex flex-row items-center gap-x-2">
						<IconMail className="h-5 w-5" />
						<span className="text-md font-light">{title}</span>
					</div>
				</div>

				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="flex flex-col flex-1 min-h-0 overflow-y-auto max-h-[600px]"
				>
					<div className="flex flex-col px-2 pt-2 pb-4 space-y-4 overflow-y-auto flex-1">
						<div className="space-y-1">
							<div className="space-y-3">
								{form.watch("email")?.map((email, index) => (
									<div key={index} className="border rounded-lg p-4 space-y-3 bg-muted/50">

										{/* Email and Label Row */}
										<div className="grid grid-cols-2 gap-3">
											{/* Email Input */}
											<div className="space-y-1">
												<Label className="text-xs text-muted-foreground">
													Email Address
												</Label>
												<Input
													type="email"
													value={email.address}
													onChange={(e) => {
														const currentEmails = form.watch("email") || [];
														const newEmails = [...currentEmails];
														newEmails[index] = {
															...newEmails[index],
															address: e.target.value,
														};
														form.setValue("email", newEmails);
													}}
													placeholder="<EMAIL>"
													className={cn(
														"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
														email.isBad && "text-red-500 line-through"
													)}
												/>
											</div>

											{/* Label Select/Input */}
											<div className="space-y-1">
												<Label className="text-xs text-muted-foreground">
													Label
												</Label>
												<div>
													{(() => {
														const predefinedLabels = ["Work", "Home", "School", "Other"];
														const isCustomMode = customLabelIndices.has(index);
														const currentLabel = email.label || "";
														
														// Determine select value based on custom mode state, not label content
														const selectValue = isCustomMode ? "Other" : (currentLabel || "Work");

														return (
															<>
																<Select
																	value={selectValue}
																	onValueChange={(value) => {
																		const currentEmails = form.watch("email") || [];
																		const newEmails = [...currentEmails];
																		
																		if (value === "Other") {
																			// Switch to custom mode
																			setCustomLabelIndices(prev => new Set(Array.from(prev).concat(index)));
																			// Clear the label when switching to custom mode
																			newEmails[index] = {
																				...newEmails[index],
																				label: "",
																			};
																		} else {
																			// Switch to predefined mode
																			setCustomLabelIndices(prev => {
																				const newSet = new Set(prev);
																				newSet.delete(index);
																				return newSet;
																			});
																			newEmails[index] = {
																				...newEmails[index],
																				label: value,
																			};
																		}
																		form.setValue("email", newEmails);
																	}}
																>
																	<SelectTrigger className="cursor-pointer hover:!bg-secondary/10">
																		<SelectValue placeholder="Select label..." />
																	</SelectTrigger>
																	<SelectContent>
																		<SelectItem value="Work">Work</SelectItem>
																		<SelectItem value="Home">Home</SelectItem>
																		<SelectItem value="School">School</SelectItem>
																		<SelectItem value="Other">Other</SelectItem>
																	</SelectContent>
																</Select>
																
																{isCustomMode && (
																	<Input
																		value={currentLabel}
																		onChange={(e) => {
																			const currentEmails = form.watch("email") || [];
																			const newEmails = [...currentEmails];
																			newEmails[index] = {
																				...newEmails[index],
																				label: e.target.value,
																			};
																			form.setValue("email", newEmails);
																		}}
																		placeholder="Custom label..."
																		className="mt-1 cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
																		autoFocus
																	/>
																)}
															</>
														);
													})()}
												</div>
											</div>
										</div>

										{/* Checkboxes */}
										<div className="flex items-center justify-between">
											<div className="flex items-center space-x-6">
												<div className="flex items-center space-x-2">
													<Checkbox
														id={`primary-${index}`}
														checked={email.isPrimary || false}
														onCheckedChange={(checked) => {
															const currentEmails = form.watch("email") || [];
															const newEmails = [...currentEmails];
															
															// If setting this as primary, unset all others
															if (checked) {
																newEmails.forEach((e, i) => {
																	e.isPrimary = i === index;
																});
															} else {
																newEmails[index] = {
																	...newEmails[index],
																	isPrimary: false,
																};
															}
															form.setValue("email", newEmails);
														}}
													/>
													<Label 
														htmlFor={`primary-${index}`}
														className="text-xs text-muted-foreground cursor-pointer"
													>
														Primary
													</Label>
												</div>

												<div className="flex items-center space-x-2">
													<Checkbox
														id={`bad-${index}`}
														checked={email.isBad || false}
														onCheckedChange={(checked) => {
															const currentEmails = form.watch("email") || [];
															const newEmails = [...currentEmails];
															newEmails[index] = {
																...newEmails[index],
																isBad: checked as boolean,
															};
															form.setValue("email", newEmails);
														}}
													/>
													<Label 
														htmlFor={`bad-${index}`}
														className="text-xs text-muted-foreground cursor-pointer"
													>
														Bad Email
													</Label>
												</div>
											</div>

											<div className="flex items-center justify-end">
												{form.watch("email").length > 1 && (
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => {
															const currentEmails = form.watch("email") || [];
															const newEmails = currentEmails.filter((_, i) => i !== index);
															form.setValue("email", newEmails);
															
															// Update custom label indices when removing an email
															setCustomLabelIndices(prev => {
																const newSet = new Set<number>();
																Array.from(prev).forEach(i => {
																	if (i < index) {
																		newSet.add(i);
																	} else if (i > index) {
																		newSet.add(i - 1);
																	}
																	// Skip i === index as it's being removed
																});
																return newSet;
															});
														}}
														className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
													>
														<IconTrash className="h-4 w-4" />
													</Button>
												)}
											</div>
										</div>
									</div>
								))}

								{/* Add Email Button */}
								<Button
									type="button"
									variant="outline"
									onClick={() => {
										const currentEmails = form.watch("email") || [];
										const newEmails = [
											...currentEmails,
											{
												label: "Work",
												address: "",
												isPrimary: currentEmails.length === 0,
												isBad: false,
											},
										];
										form.setValue("email", newEmails);
									}}
									className="w-full border-dashed !bg-transparent hover:!bg-muted/30 items-center gap-1"
								>
									<IconPlus className="h-4 w-4" />
									Add Email Address
								</Button>
							</div>
						</div>
					</div>

					{/* Fixed Footer */}
					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting || updateContact.isPending}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={isSubmitting || updateContact.isPending}
						>
							{(isSubmitting || updateContact.isPending) ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>Saving...</span>
								</div>
							) : (
								"Save"
							)}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
} 