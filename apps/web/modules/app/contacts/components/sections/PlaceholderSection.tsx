"use client";

import React from "react";
import { IconBuildingStore } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";

interface PlaceholderSectionProps {
	title: string;
	data?: any;
	organization?: ActiveOrganization;
}

const PlaceholderSection = ({ title }: PlaceholderSectionProps) => {
	return (
		<div className="flex flex-col items-center justify-center p-8 text-center">
			<div className="p-3 rounded-full bg-muted/50 mb-4">
				<IconBuildingStore className="h-6 w-6 text-muted-foreground" />
			</div>
			<div className="text-sm font-medium text-muted-foreground mb-1">
				{title}
			</div>
			<div className="text-xs text-muted-foreground">
				This section is coming soon
			</div>
		</div>
	);
};

export default PlaceholderSection; 