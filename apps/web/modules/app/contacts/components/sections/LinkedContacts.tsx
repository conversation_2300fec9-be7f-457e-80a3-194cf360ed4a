"use client";

import React, { useState } from "react";
import GridItem from "@ui/components/grid-item";
import { IconEdit, IconPlus, IconUser, IconMail, IconPhone } from "@tabler/icons-react";
import type { ActiveOrganization } from "@repo/auth";
import { cn } from "@ui/lib";
import { Skeleton } from "@ui/components/skeleton";
import { LinkContactModal } from "../LinkContactModal";
import { useLinkedContacts, getPrimaryEmail, getPrimaryPhone, type LinkedContactData } from "@shared/hooks/useLinkedContacts";
import { Avatar, AvatarImage, AvatarFallback } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { CopyableValue } from "@shared/components/CopyableValue";

interface LinkedContactsProps {
  data: {
    id: string;
    firstName?: string;
    lastName?: string;
    name?: string;
    email?: string[];
    image?: string;
  };
  organization: ActiveOrganization;
  loading?: boolean;
  linkedContacts?: LinkedContactData[];
  onAddContact?: () => void;
  onEditContact?: (contact: LinkedContactData) => void;
  onRemoveContact?: (contactId: string) => void;
}

const LinkedContactCardSkeleton = () => (
  <div className="w-full">
    <div className="flex items-center space-x-2">
      <div className="flex-1 space-y-1">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </div>
      <Skeleton className="h-8 w-8 rounded-full" />
    </div>
  </div>
);

const LinkedContactCard = ({ 
  linkedContact,
  onEdit 
}: { 
  linkedContact: LinkedContactData;
  onEdit: () => void;
}) => {
  const contact = linkedContact.linkedContact;
  const displayName = contact.firstName || contact.lastName 
    ? `${contact.firstName || ''} ${contact.lastName || ''}`.trim()
    : 'Unnamed person';

  const emailResult = getPrimaryEmail(contact.email);
  const primaryEmail = emailResult === null ? undefined : emailResult;

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg bg-card hover:bg-muted/50 transition-colors">
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <ContactAvatar
          name={displayName}
          avatarUrl={contact.image}
          className="h-8 w-8"
        />

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium truncate">
              {displayName}
            </span>
            {linkedContact.relation && (
              <Badge variant="views" className="text-xs">
                {linkedContact.relation}
              </Badge>
            )}
          </div>
          
          {primaryEmail && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <IconMail className="h-3 w-3" />
              <span className="truncate">{primaryEmail}</span>
            </div>
          )}
        </div>
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={onEdit}
        className="ml-2"
      >
        <IconEdit className="h-4 w-4" />
      </Button>
    </div>
  );
};

const LinkedContacts = ({ 
  data, 
  organization, 
  loading = false, 
  linkedContacts,
  onAddContact,
  onEditContact,
  onRemoveContact 
}: LinkedContactsProps) => {
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");
  const [selectedContact, setSelectedContact] = useState<LinkedContactData | null>(null);
  const contact = data;

  // Use the hook to fetch linked contacts
  const { data: fetchedLinkedContacts, isLoading: isLoadingLinkedContacts } = useLinkedContacts(
    data?.id || '',
    organization?.id || ''
  );

  // Use prop data if available, otherwise use fetched data
  const contactsData = linkedContacts || fetchedLinkedContacts || [];
  const isLoading = loading || isLoadingLinkedContacts;

  const handleEditContact = (linkedContact: LinkedContactData) => {
    setSelectedContact(linkedContact);
    setModalMode("edit");
    setIsLinkModalOpen(true);
    
    if (onEditContact) {
      onEditContact(linkedContact);
    }
  };

  const handleAddContact = () => {
    setSelectedContact(null);
    setModalMode("add");
    setIsLinkModalOpen(true);
    
    if (onAddContact) {
      onAddContact();
    }
  };

  const handleModalClose = (open: boolean) => {
    setIsLinkModalOpen(open);
    if (!open) {
      setSelectedContact(null);
      setModalMode("add");
    }
  };

  const getDisplayName = (contact: LinkedContactData['linkedContact']) => {
    if (contact.firstName || contact.lastName) {
      return `${contact.firstName || ''} ${contact.lastName || ''}`.trim();
    }
    return 'Unknown Contact';
  };

  if (!data) {
    return (
      <div className="space-y-2">
        <LinkedContactCardSkeleton />
      </div>
    );
  }

  return (
    <>
      <div className="space-y-1">
        {isLoading ? (
          <>
            <LinkedContactCardSkeleton />
          </>
        ) : contactsData && contactsData.length > 0 ? (
          <>
            {contactsData.map((linkedContact, index) => {
              const contactData = linkedContact.linkedContact;
              const displayName = getDisplayName(contactData);
              const primaryEmail = getPrimaryEmail(contactData.email);
              const primaryPhone = getPrimaryPhone(contactData.phone);
              
              return (
                <GridItem
                  key={linkedContact.id}
                  value={
                    <div className="flex items-center gap-2">
                      <div className="border rounded-xl p-1">
                      <ContactAvatar
                        name={displayName}
                        avatarUrl={contact.image}
                        className="h-8 w-8"
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">
                          {displayName}
                        </span>
                      </div>
                      <div className="flex flex-col gap-0.5 text-xs text-muted-foreground">
                        {primaryEmail && (
                          <CopyableValue
                            value={primaryEmail}
                            type="email"
                            className="text-xs"
                          />
                        )}
                        {primaryPhone && (
                          <div className="flex items-center gap-1.5">
                            <IconPhone className="h-3 w-3 shrink-0" />
                            <span className="truncate">
                              {primaryPhone}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    </div>
                  }
                  edit={<IconEdit className="h-3 w-3" />}
                  add={<IconPlus className="h-3 w-3" />}
                  onEdit={() => handleEditContact(linkedContact)}
                  onAdd={handleAddContact}
                  badge={linkedContact.relation}
                  badgeClassName="bg-muted rounded-full text-xs px-1.5 py-0.5"
                />
              );
            })}
          </>
        ) : (
          <GridItem
            value="Link Contact"
            onClick={handleAddContact}
            icon={<IconUser className="h-4 w-4 text-muted-foreground" />}
          />
        )}
      </div>
      
      {/* Link Contact Modal */}
      <LinkContactModal
        open={isLinkModalOpen}
        onOpenChange={handleModalClose}
        contact={contact}
        organization={organization}
        mode={modalMode}
        contactToEdit={selectedContact as any}
      />
    </>
  );
};

export default LinkedContacts; 