"use client";

import { IconX } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import React, { useState } from "react";

interface NameFieldProps {
	firstName: string;
	lastName: string;
	onFirstNameChange: (value: string) => void;
	onLastNameChange: (value: string) => void;
	placeholder?: string;
}

export function NameField({
	firstName,
	lastName,
	onFirstNameChange,
	onLastNameChange,
	placeholder = "Set Name...",
}: NameFieldProps) {
	const [open, setOpen] = useState(false);
	const [firstNameHovered, setFirstNameHovered] = useState(false);
	const [lastNameHovered, setLastNameHovered] = useState(false);

	const hasValues = firstName.trim() !== "" || lastName.trim() !== "";
	const displayText = hasValues
		? `${firstName} ${lastName}`.trim() || placeholder
		: placeholder;

	const clearFirstName = () => {
		onFirstNameChange("");
	};

	const clearLastName = () => {
		onLastNameChange("");
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			setOpen(false);
		}
	};

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className="w-full justify-start bg-transparent text-left font-normal"
				>
					<span
						className={cn(
							hasValues
								? "text-foreground"
								: "text-muted-foreground",
						)}
					>
						{displayText}
					</span>
				</Button>
			</PopoverTrigger>
			<PopoverContent
				className="w-[var(--radix-popover-trigger-width)] p-4 bg-muted/50 backdrop-blur-lg"
				align="start"
			>
				<div className="space-y-4">
					<div>
						<label className="text-xs text-muted-foreground">
							First name
						</label>
						<div
							className="relative"
							onMouseEnter={() => setFirstNameHovered(true)}
							onMouseLeave={() => setFirstNameHovered(false)}
						>
							<Input
								value={firstName}
								onChange={(e) =>
									onFirstNameChange(e.target.value)
								}
								onKeyDown={handleKeyDown}
								placeholder="Enter first name..."
								className="pr-8"
							/>
							{firstName.trim() !== "" && firstNameHovered && (
								<Button
									type="button"
									variant="ghost"
									size="sm"
									onClick={clearFirstName}
									className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
								>
									<IconX className="h-3 w-3" />
								</Button>
							)}
						</div>
					</div>

					<div>
						<label className="text-sm font-medium mb-2 block">
							Last name
						</label>
						<div
							className="relative"
							onMouseEnter={() => setLastNameHovered(true)}
							onMouseLeave={() => setLastNameHovered(false)}
						>
							<Input
								value={lastName}
								onChange={(e) =>
									onLastNameChange(e.target.value)
								}
								onKeyDown={handleKeyDown}
								placeholder="Enter last name..."
								className="pr-8"
							/>
							{lastName.trim() !== "" && lastNameHovered && (
								<Button
									type="button"
									variant="ghost"
									size="sm"
									onClick={clearLastName}
									className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
								>
									<IconX className="h-3 w-3" />
								</Button>
							)}
						</div>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}
