"use client";

import React, { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { But<PERSON> } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Label } from "@ui/components/label";
import { 
	IconPlus, 
	IconGripVertical, 
	IconX,
	IconEye,
	IconEyeOff,
	IconActivity,
	IconMail,
	IconBuilding,
	IconNote,
	IconChecklist,
	IconFile,
	IconUser,
	IconAt,
	IconFileDescription,
	IconBriefcase,
} from "@tabler/icons-react";
import { cn } from "@ui/lib";
import { Reorder, useDragControls } from "framer-motion";

interface Tab {
	id: string;
	title: string;
	icon: React.ReactNode;
	enabled: boolean;
}

interface Field {
	id: string;
	title: string;
	icon: React.ReactNode;
	enabled: boolean;
}

interface ConfigurePageModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	objectType: string;
}

const defaultTabs: Tab[] = [
	{ id: "overview", title: "Overview", icon: <IconEye className="h-4 w-4" />, enabled: true },
	{ id: "activity", title: "Activity", icon: <IconActivity className="h-4 w-4" />, enabled: true },
	{ id: "emails", title: "Emails", icon: <IconMail className="h-4 w-4" />, enabled: true },
	{ id: "company", title: "Company", icon: <IconBuilding className="h-4 w-4" />, enabled: true },
	{ id: "notes", title: "Notes", icon: <IconNote className="h-4 w-4" />, enabled: true },
	{ id: "tasks", title: "Tasks", icon: <IconChecklist className="h-4 w-4" />, enabled: true },
	{ id: "files", title: "Files", icon: <IconFile className="h-4 w-4" />, enabled: false },
];

const defaultFields: Field[] = [
	{ id: "name", title: "Name", icon: <IconUser className="h-4 w-4" />, enabled: true },
	{ id: "email", title: "Email addresses", icon: <IconAt className="h-4 w-4" />, enabled: true },
	{ id: "description", title: "Description", icon: <IconFileDescription className="h-4 w-4" />, enabled: true },
	{ id: "company", title: "Company", icon: <IconBuilding className="h-4 w-4" />, enabled: true },
	{ id: "job-title", title: "Job title", icon: <IconBriefcase className="h-4 w-4" />, enabled: true },
];

const DraggableTab = ({ tab, onToggle, onRemove }: { 
	tab: Tab; 
	onToggle: (id: string) => void; 
	onRemove: (id: string) => void;
}) => {
	const dragControls = useDragControls();

	return (
		<Reorder.Item value={tab} dragListener={false} dragControls={dragControls}>
			<div className={cn(
				"flex items-center gap-3 p-3 bg-background border border-border rounded-lg",
				"hover:bg-muted/50 transition-colors"
			)}>
				<div
					className="cursor-grab active:cursor-grabbing p-1"
					onPointerDown={(e) => dragControls.start(e)}
				>
					<IconGripVertical className="h-4 w-4 text-muted-foreground" />
				</div>
				
				<div className="flex items-center gap-2 flex-1">
					{tab.icon}
					<span className="font-medium">{tab.title}</span>
				</div>

				<div className="flex items-center gap-2">
					<Switch
						checked={tab.enabled}
						onCheckedChange={() => onToggle(tab.id)}
					/>
					{tab.id !== "overview" && (
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onRemove(tab.id)}
							className="h-8 w-8 text-muted-foreground hover:text-destructive"
						>
							<IconX className="h-4 w-4" />
						</Button>
					)}
				</div>
			</div>
		</Reorder.Item>
	);
};

const ConfigurePageModal = ({ open, onOpenChange, objectType }: ConfigurePageModalProps) => {
	const [tabs, setTabs] = useState<Tab[]>(defaultTabs);
	const [fields, setFields] = useState<Field[]>(defaultFields);

	const handleTabToggle = (id: string) => {
		setTabs(prev => prev.map(tab => 
			tab.id === id ? { ...tab, enabled: !tab.enabled } : tab
		));
	};

	const handleTabRemove = (id: string) => {
		setTabs(prev => prev.filter(tab => tab.id !== id));
	};

	const handleFieldToggle = (id: string) => {
		setFields(prev => prev.map(field => 
			field.id === id ? { ...field, enabled: !field.enabled } : field
		));
	};

	const handleAddTab = () => {
		// TODO: Open add tab modal
		console.warn("Add new tab");
	};

	const handleSave = () => {
		// TODO: Save configuration
		console.warn("Save configuration", { tabs, fields });
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl h-[80vh] flex flex-col">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<IconUser className="h-5 w-5" />
						Configure {objectType} page
					</DialogTitle>
				</DialogHeader>

				<div className="flex-1 flex gap-6 overflow-hidden">
					{/* Left Side - Page Layout */}
					<div className="flex-1 flex flex-col">
						<div className="flex items-center justify-between mb-4">
							<h3 className="font-semibold">Page Layout</h3>
							<Button onClick={handleAddTab} size="sm" className="gap-2">
								<IconPlus className="h-4 w-4" />
								Add tab
							</Button>
						</div>

						<div className="flex-1 overflow-y-auto">
							<Reorder.Group
								axis="y"
								values={tabs}
								onReorder={setTabs}
								className="space-y-2"
							>
								{tabs.map((tab) => (
									<DraggableTab
										key={tab.id}
										tab={tab}
										onToggle={handleTabToggle}
										onRemove={handleTabRemove}
									/>
								))}
							</Reorder.Group>
						</div>
					</div>

					{/* Right Side - Record Details */}
					<div className="w-80 border-l border-border pl-6">
						<h3 className="font-semibold mb-4">Record Details</h3>
						
						<div className="space-y-3">
							{fields.map((field) => (
								<div key={field.id} className="flex items-center justify-between">
									<div className="flex items-center gap-2">
										{field.icon}
										<Label className="font-medium">{field.title}</Label>
									</div>
									<Switch
										checked={field.enabled}
										onCheckedChange={() => handleFieldToggle(field.id)}
									/>
								</div>
							))}
						</div>

						{/* Lists Section */}
						<div className="mt-8">
							<h4 className="font-medium mb-3">Lists</h4>
							<div className="space-y-2">
								<div className="flex items-center justify-between text-sm">
									<span className="text-muted-foreground">Company contacts</span>
									<Switch defaultChecked />
								</div>
								<div className="flex items-center justify-between text-sm">
									<span className="text-muted-foreground">Related deals</span>
									<Switch defaultChecked />
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Footer */}
				<div className="flex justify-end gap-2 pt-4 border-t border-border">
					<Button variant="outline" onClick={() => onOpenChange(false)}>
						Cancel
					</Button>
					<Button onClick={handleSave}>
						Save changes
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default ConfigurePageModal; 