import type { ObjectViewType } from "@app/object-views/lib/types";
import { auth } from "@repo/auth";
import { db } from "@repo/database/server";
import { headers } from "next/headers";
import type { ObjectView } from "./api";

// Get the default view for a specific object type in the active organization
export async function getDefaultView(
	objectType: string,
	organizationId?: string,
): Promise<ObjectView | null> {
	try {
		// Get session from headers
		const session = await auth.api.getSession({
			headers: await headers(),
		});

		// Use provided organizationId as fallback if session doesn't have activeOrganizationId
		const finalOrganizationId =
			organizationId || session?.session?.activeOrganizationId;

		if (!finalOrganizationId || !session?.user?.id) {
			return null;
		}

		const userId = session.user.id;

		// First, check for user-specific default view
		const userPreference = await db.userViewPreference.findUnique({
			where: {
				userId_organizationId_objectType: {
					userId,
					organizationId: finalOrganizationId,
					objectType,
				},
			},
			include: {
				view: {
					include: {
						creator: {
							select: { id: true, name: true, email: true },
						},
					},
				},
			},
		});

		if (userPreference?.view) {
			const view = userPreference.view;
			return {
				id: view.id,
				name: view.name,
				objectType: view.objectType,
				organizationId: view.organizationId,
				viewType: view.viewType as ObjectViewType,
				columnDefs: view.columnDefs as Array<{
					field: string;
					headerName: string;
					width: number;
				}>,
				filters: view.filters as
					| Array<{
							field: string;
							logic: string;
							text?: string;
							number?: number;
					  }>
					| undefined,
				filterCondition: view.filterCondition as
					| "and"
					| "or"
					| undefined,
				statusAttribute: view.statusAttribute || undefined,
				mapConfig: view.mapConfig as {
					displayType?: 'table' | 'grid';
					rowDensity?: 'compact' | 'normal' | 'comfortable';
					showExportOptions?: boolean;
					allowColumnReorder?: boolean;
					showSearchBar?: boolean;
				} | undefined,
				isDefault: view.isDefault,
				isPublic: view.isPublic,
				creator: (view as any).creator,
				createdAt: view.createdAt.toISOString(),
				updatedAt: view.updatedAt.toISOString(),
			} as ObjectView;
		}

		// Fallback to organization default views with proper filtering
		const views = await db.objectView.findMany({
			where: {
				organizationId: finalOrganizationId,
				objectType,
				OR: [
					{ isPublic: true }, // Public views visible to all workspace members
					{ createdBy: userId }, // Private views visible only to creator
				],
			},
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
			},
			orderBy: [
				{ isDefault: "desc" }, // Default views first
				{ createdAt: "asc" },
			],
		});

		const defaultView = views.find((view) => view.isDefault) || views[0];

		if (!defaultView) {
			return null;
		}

		const view = defaultView;
		return {
			id: view.id,
			name: view.name,
			objectType: view.objectType,
			organizationId: view.organizationId,
			viewType: view.viewType as ObjectViewType,
			columnDefs: view.columnDefs as Array<{
				field: string;
				headerName: string;
				width: number;
			}>,
			filters: view.filters as
				| Array<{
						field: string;
						logic: string;
						text?: string;
						number?: number;
				  }>
				| undefined,
			filterCondition: view.filterCondition as "and" | "or" | undefined,
			statusAttribute: view.statusAttribute || undefined,
			mapConfig: view.mapConfig as {
				displayType?: 'table' | 'grid';
				rowDensity?: 'compact' | 'normal' | 'comfortable';
				showExportOptions?: boolean;
				allowColumnReorder?: boolean;
				showSearchBar?: boolean;
			} | undefined,
			isDefault: view.isDefault,
			isPublic: view.isPublic,
			creator: (view as any).creator,
			createdAt: view.createdAt.toISOString(),
			updatedAt: view.updatedAt.toISOString(),
		} as ObjectView;
	} catch (error) {
		console.error("Error in getDefaultView:", error);
		return null;
	}
}

// Get all views for a specific object type in the active organization
export async function getObjectViews(
	objectType: string,
): Promise<ObjectView[]> {
	try {
		// Get session from headers
		const session = await auth.api.getSession({
			headers: await headers(),
		});

		if (!session?.session?.activeOrganizationId || !session?.user?.id) {
			return [];
		}

		const organizationId = session.session.activeOrganizationId;
		const userId = session.user.id;

		// Fetch views from database with proper filtering
		const views = await db.objectView.findMany({
			where: {
				organizationId,
				objectType,
				OR: [
					{ isPublic: true }, // Public views visible to all workspace members
					{ createdBy: userId }, // Private views visible only to creator
				],
			},
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
			},
			orderBy: [
				{ isDefault: "desc" }, // Default views first
				{ createdAt: "asc" },
			],
		});

		return views.map((view) => ({
			id: view.id,
			name: view.name,
			objectType: view.objectType,
			organizationId: view.organizationId,
			viewType: view.viewType as ObjectViewType,
			columnDefs: view.columnDefs as Array<{
				field: string;
				headerName: string;
				width: number;
			}>,
			filters: view.filters as
				| Array<{
						field: string;
						logic: string;
						text?: string;
						number?: number;
				  }>
				| undefined,
			filterCondition: view.filterCondition as "and" | "or" | undefined,
			statusAttribute: view.statusAttribute || undefined,
			mapConfig: view.mapConfig as {
				displayType?: 'table' | 'grid';
				rowDensity?: 'compact' | 'normal' | 'comfortable';
				showExportOptions?: boolean;
				allowColumnReorder?: boolean;
				showSearchBar?: boolean;
			} | undefined,
			isDefault: view.isDefault,
			isPublic: view.isPublic,
			creator: (view as any).creator,
			createdAt: view.createdAt.toISOString(),
			updatedAt: view.updatedAt.toISOString(),
		})) as ObjectView[];
	} catch (error) {
		console.error("Error fetching object views:", error);
		return [];
	}
}
