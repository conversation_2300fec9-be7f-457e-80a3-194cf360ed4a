import { ArrayFieldType, FieldType, FilterType, ObjectType, ObjectViewType } from "@repo/database";
import type { ColumnDef } from "@tanstack/react-table";
import type { ParserBuilder } from "nuqs";

export type { ObjectType, ObjectViewType };

export interface BaseObjectSchema {
	id: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface FilterOption {
	label: string;
	value: any;
}

export interface DataTableFilterField<TData> {
	label: string;
	value: keyof TData | string;
	type: FieldType;
	defaultOpen?: boolean;
	commandDisabled?: boolean;
	options?: FilterOption[];
	min?: number;
	max?: number;
}

export interface SheetField<TData, TMeta> {
	id: keyof TData | string;
	label: string;
	type: FilterType;
	condition?: (props: any) => boolean;
	className?: string;
	skeletonClassName?: string;
}

export interface FacetMetadataSchema {
	rows: Array<{ value: any; total: number }>;
	total: number;
	min?: number;
	max?: number;
}

export interface BaseChartSchema {
	timestamp: number;
	[key: string]: number;
}

export interface InfiniteQueryMeta<TMeta = Record<string, unknown>> {
	totalRowCount: number;
	filterRowCount: number;
	chartData: BaseChartSchema[];
	facets: Record<string, FacetMetadataSchema>;
	metadata?: TMeta;
}

export interface InfiniteQueryResponse<TData, TMeta = unknown> {
	data: TData;
	meta: InfiniteQueryMeta<TMeta>;
	prevCursor: string | null;
	nextCursor: string | null;
}

export interface BaseSearchParams {
	sort?: { id: string; desc: boolean };
	size: number;
	start: number;
	direction: "prev" | "next";
	cursor: Date;
	live: boolean;
	id?: string;
}

export interface ApiHandlers<TData extends BaseObjectSchema> {
	updateField: {
		mutate: (params: {
			id: string;
			field: string;
			value: any;
			organizationId: string;
		}) => Promise<any>;
		hook: () => any; // React Query mutation hook
	};
	clearField: {
		mutate: (params: {
			id: string;
			field: string;
			organizationId: string;
		}) => Promise<any>;
		hook: () => any; // React Query mutation hook
	};
	deleteRecord: {
		mutate: (params: {
			id: string;
			organizationId: string;
		}) => Promise<any>;
		hook: () => any; // React Query mutation hook
	};
}

export interface ObjectConfig<TData extends BaseObjectSchema, TMeta = any> {
	type: ObjectType;
	schema: any;
	columns: ColumnDef<TData>[];
	filterFields: DataTableFilterField<TData>[];
	sheetFields: SheetField<TData, TMeta>[];
	searchParamsParser: Record<string, ParserBuilder<any>>;
	apiEndpoint: string;
	primaryColumn?: string;
	apiHandlers?: ApiHandlers<TData>;
	fieldTypes?: Record<string, FieldType>;
	arrayFieldTypes?: Record<string, ArrayFieldType>;
	selectOptions?: Record<string, Array<{ label: string; value: string }>>;
	readonlyColumns?: string[];
}
