"use client";

import { useToggleFavorite } from "@app/favorites/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconStar, IconStarFilled } from "@tabler/icons-react";
import { cn } from "@ui/lib";

export const ToggleStar = ({
	id,
	isFavorite,
	objectType,
}: {
	id: string;
	isFavorite: boolean;
	objectType: string;
}) => {
	const { activeOrganization } = useActiveOrganization();
	const toggleFavorite = useToggleFavorite(activeOrganization?.id || "");

	const handleToggleFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!activeOrganization?.id || !id) return;
		toggleFavorite.mutate({
			objectId: id,
			objectType,
			organizationId: activeOrganization?.id || "",
		});
	};

	return (
		<div>
			<button
				onClick={handleToggleFavorite}
				className={cn(
					"cursor-pointer",
					"hover:bg-muted/50 p-2 rounded-lg",
					"hover:text-yellow-500",
					isFavorite && "text-yellow-500",
				)}
				aria-label={isFavorite ? "Unfavorite" : "Favorite"}
			>
				{isFavorite ? (
					<IconStarFilled className="size-4" />
				) : (
					<IconStar className="size-4" />
				)}
			</button>
		</div>
	);
};
