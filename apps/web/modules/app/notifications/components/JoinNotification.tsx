import { useSession } from "@app/auth/hooks/use-session";
import { OrganizationLogo } from "@app/organizations/components/OrganizationLogo";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { formatDistanceToNow } from "date-fns";
import { toast } from "sonner";

type JoinNotificationProps = {
	notification: any;
};

const JoinNotification = ({ notification }: JoinNotificationProps) => {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();

	if (!user) {
		throw new Error("User not found");
	}

	// TODO: Handle accept user
	// const handleAcceptUser = () => {
	//   acceptUser.mutate({
	//     orgId: currentOrg?.id ?? undefined,
	//     userId: (notification as any).user?.id ?? undefined
	//   }, {
	//     onSuccess: () => {
	//       toast.success('User accepted')
	//       createNotification.mutate({
	//         orgId: currentOrg?.id ?? undefined,
	//         type: 'org:join',
	//         message: `${(notification as any).user?.name} has joined the organization`,
	//         notificationType: "notification",
	//       })
	//     },
	//     onError: (error) => {
	//       console.error("Error accepting user: ", error)
	//     },
	//     onFinally: () => {
	//       removeNotification.mutate({
	//         id: notification.id
	//       })
	//     }
	//   })
	// }

	// TODO: Handle reject user
	// const handleRemoveUser = () => {
	//   removeUser.mutate({
	//     orgId: currentOrg?.id ?? undefined,
	//     userId: (notification as any).user?.id ?? undefined
	//   }, {
	//     onSuccess: () => {
	//       toast.success('User removed')
	//     },
	//     onError: (error) => {
	//       console.error("Error removing user: ", error)
	//     },
	//     onFinally: () => {
	//       removeNotification.mutate({
	//         id: notification.id
	//       })
	//     }
	//   })
	// }

	return (
		<div className="p-4 max-w-sm border-b border-zinc-800">
			<div className="flex items-start space-x-3">
				<div className="flex-shrink-0">
					<OrganizationLogo
						name={activeOrganization?.name ?? ""}
						logoUrl={activeOrganization?.logo}
					/>
				</div>
				<div className="flex-1 min-w-0">
					<p className="text-sm font-medium dark:text-zinc-200">
						{notification.message}
					</p>
					<p className="text-xs text-gray-400">
						{formatDistanceToNow(
							new Date(notification._creationTime),
							{ addSuffix: true },
						)}
					</p>
				</div>
			</div>
			<div className="mt-4 flex justify-end space-x-1">
				<Button
					className="!bg-transparent !h-7 hover:underline"
					// onClick={handleRemoveUser}
				>
					Reject
				</Button>
				<Button
					className="!h-7 border border-indigo-600 dark:!text-zinc-200 dark:!bg-indigo-700 dark:hover:!bg-indigo-500"
					// onClick={handleAcceptUser}
				>
					Accept
				</Button>
			</div>
		</div>
	);
};

export default JoinNotification;
