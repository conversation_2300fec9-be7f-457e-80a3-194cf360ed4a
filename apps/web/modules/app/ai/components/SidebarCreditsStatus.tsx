"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { apiClient } from "@shared/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Progress } from "@ui/components/progress";
import {
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from "@ui/components/sidebar";
import { AlertTriangle, Brain, Sparkles, TrendingUp, Zap } from "lucide-react";
import { useRouter } from "next/navigation";

interface CreditsStatus {
	total: number;
	used: number;
	remaining: number;
	resetDate: string;
	needsReset: boolean;
	hoursUntilReset: number;
}

interface SidebarCreditsStatusProps {
	organizationId: string;
	organizationSlug: string;
}

export function SidebarCreditsStatus({
	organizationId,
	organizationSlug,
}: SidebarCreditsStatusProps) {
	const { user } = useSession();
	const router = useRouter();

	// Fetch credits status from your API
	const {
		data: creditsStatus,
		isLoading,
		error,
	} = useQuery({
		queryKey: ["credits-status", organizationId],
		queryFn: async (): Promise<CreditsStatus> => {
			const response = await apiClient.ai.credits.$get();
			if (!response.ok) {
				throw new Error("Failed to fetch credits status");
			}
			return await response.json();
		},
		enabled: !!user && !!organizationId,
		refetchInterval: 30000, // Refetch every 30 seconds
	});

	if (isLoading || error || !creditsStatus) {
		return null;
	}

	const usagePercentage = (creditsStatus.used / creditsStatus.total) * 100;
	const isLowCredits = creditsStatus.remaining < creditsStatus.total * 0.2;
	const isOutOfCredits = creditsStatus.remaining <= 0;

	const getStatusColor = (percentage: number) => {
		if (percentage >= 90) return "text-red-400";
		if (percentage >= 70) return "text-amber-400";
		return "text-cyan-400";
	};

	const getGlowColor = (percentage: number) => {
		if (percentage >= 90) return "shadow-red-500/20";
		if (percentage >= 70) return "shadow-amber-500/20";
		return "shadow-cyan-500/20";
	};

	const getProgressColor = (percentage: number) => {
		if (percentage >= 90)
			return "bg-gradient-to-r from-red-500 to-red-400 shadow-red-500/50";
		if (percentage >= 70)
			return "bg-gradient-to-r from-amber-500 to-amber-400 shadow-amber-500/50";
		return "bg-gradient-to-r from-cyan-500 to-blue-500 shadow-cyan-500/50";
	};

	return (
		<SidebarGroup>
			<SidebarGroupContent>
				<div
					className={`relative space-y-4 p-4 rounded-xl bg-gradient-to-br from-zinc-900/90 to-zinc-800/90 border border-zinc-700/50 ${getGlowColor(usagePercentage)}`}
				>
					{/* Animated background pattern */}
					{/* <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-transparent to-purple-500/5 rounded-xl animate-pulse" /> */}

					{/* Neural network dots */}
					{/* <div className="absolute top-2 right-2 flex gap-1">
            <div className="w-1 h-1 bg-cyan-400 rounded-full animate-ping" />
            <div className="w-1 h-1 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: "0.5s" }} />
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: "1s" }} />
          </div> */}

					{/* Credits Display */}
					<div className="relative z-10 flex justify-between items-start">
						<div>
							<p className="text-xs text-slate-400 font-mono">
								CREDITS USED
							</p>
							<p className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
								{creditsStatus.used.toLocaleString()}
							</p>
							<p className="text-xs text-slate-500">tokens</p>
						</div>
						<div className="text-right">
							<p className="text-xs text-slate-400 font-mono">
								MAX CAPACITY
							</p>
							<p className="text-sm font-semibold text-slate-300">
								{creditsStatus.total.toLocaleString()}
							</p>
						</div>
					</div>

					{/* AI Progress Visualization */}
					<div className="relative z-10 space-y-3">
						<div className="flex justify-between items-center">
							<span className="text-xs text-slate-400 font-mono">
								NEURAL LOAD
							</span>
							<span
								className={`text-xs font-bold ${getStatusColor(usagePercentage)} drop-shadow-sm`}
							>
								{usagePercentage.toFixed(1)}%
							</span>
						</div>

						{/* Custom AI-style progress bar */}
						<div className="relative h-3 bg-slate-800 rounded-full overflow-hidden border border-slate-700">
							<div className="absolute inset-0 bg-gradient-to-r from-slate-700 to-slate-600 opacity-50" />
							<div
								className={`absolute top-0 left-0 h-full rounded-full transition-all duration-1000 ease-out ${getProgressColor(usagePercentage)} shadow-lg`}
								style={{
									width: `${Math.min(usagePercentage, 100)}%`,
								}}
							/>
							{/* Animated scanning line */}
							<div
								className="absolute top-0 left-0 h-full w-1 bg-white/60 animate-pulse"
								style={{
									left: `${Math.min(usagePercentage, 100)}%`,
									transform: "translateX(-50%)",
									boxShadow: "0 0 10px rgba(255,255,255,0.5)",
								}}
							/>
						</div>
					</div>

					{/* Available Processing Power */}
					<div className="relative z-10 flex items-center justify-between text-xs bg-slate-800/50 rounded-lg p-2 border border-slate-700/30">
						<span className="text-slate-400 font-mono">
							AVAILABLE
						</span>
						<span className="font-bold text-green-400 drop-shadow-sm flex items-center gap-1">
							<Sparkles className="h-3 w-3" />
							{creditsStatus.remaining.toLocaleString()} tokens
						</span>
					</div>

					{/* Status Alerts */}
					{isOutOfCredits && (
						<div className="relative z-10 flex items-center gap-2 text-xs bg-red-900/40 rounded-lg p-2 border border-red-500/30">
							<AlertTriangle className="h-3 w-3 text-red-400" />
							<span className="text-red-300 font-mono">
								CREDITS EXHAUSTED
							</span>
						</div>
					)}

					{isLowCredits && !isOutOfCredits && (
						<div className="relative z-10 flex items-center gap-2 text-xs bg-amber-900/40 rounded-lg p-2 border border-amber-500/30">
							<Zap className="h-3 w-3 text-amber-400" />
							<span className="text-amber-300 font-mono">
								LOW CREDITS WARNING
							</span>
						</div>
					)}

					{/* AI Action Buttons */}
					<div className="relative z-10 space-y-1">
						<SidebarMenu>
							<SidebarMenuItem>
								<SidebarMenuButton
									asChild
									size="sm"
									className="bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-600/30 hover:to-blue-600/30 border border-cyan-500/30 text-cyan-300"
								>
									<button
										className="w-full justify-start"
										onClick={() =>
											router.push(
												`/app/${organizationSlug}/settings/ai/credits`,
											)
										}
									>
										<Zap className="h-3 w-3" />
										<span className="font-mono text-xs">
											BOOST CREDITS
										</span>
									</button>
								</SidebarMenuButton>
							</SidebarMenuItem>
							<SidebarMenuItem>
								<SidebarMenuButton
									asChild
									size="sm"
									className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 hover:from-purple-600/30 hover:to-pink-600/30 border border-purple-500/30 text-purple-300"
								>
									<button
										className="w-full justify-start"
										onClick={() =>
											router.push(
												`/app/${organizationSlug}/settings/ai/analytics`,
											)
										}
									>
										<TrendingUp className="h-3 w-3" />
										<span className="font-mono text-xs">
											ANALYTICS
										</span>
									</button>
								</SidebarMenuButton>
							</SidebarMenuItem>
						</SidebarMenu>
					</div>
				</div>
			</SidebarGroupContent>
		</SidebarGroup>
	);
}
