import AnthropicIcon from "@ui/icons/anthropic";
import ClaudeIcon from "@ui/icons/claude";
import DeepseekIcon from "@ui/icons/deepseek";
import GeminiIcon from "@ui/icons/gemini";
import GoogleIcon from "@ui/icons/google";
import GrokIcon from "@ui/icons/grok";
import MetaIcon from "@ui/icons/meta";
import MistralIcon from "@ui/icons/mistral";
import OllamaIcon from "@ui/icons/ollama";
import OpenAIIcon from "@ui/icons/openai";
import OpenRouterIcon from "@ui/icons/openrouter";
import XIcon from "@ui/icons/x";
import XAIIcon from "@ui/icons/xai";
import type { SVGProps } from "react";
import * as React from "react";

export type ModelType =
	| "anthropic"
	| "claude"
	| "deepseek"
	| "gemini"
	| "google"
	| "grok"
	| "meta"
	| "mistral"
	| "ollama"
	| "openai"
	| "openrouter"
	| "x"
	| "xai";

interface ModelIconProps extends SVGProps<SVGSVGElement> {
	model: ModelType;
}

const ModelIcon = ({ model, ...props }: ModelIconProps) => {
	const icons: Record<
		ModelType,
		React.ComponentType<SVGProps<SVGSVGElement>>
	> = {
		anthropic: AnthropicIcon,
		claude: ClaudeIcon,
		deepseek: DeepseekIcon,
		gemini: GeminiIcon,
		google: GoogleIcon,
		grok: GrokIcon,
		meta: MetaIcon,
		mistral: MistralIcon,
		ollama: OllamaIcon,
		openai: OpenAIIcon,
		openrouter: OpenRouterIcon,
		x: XIcon,
		xai: XAIIcon,
	};

	const Icon = icons[model];

	return <Icon {...props} />;
};

export default ModelIcon;
