"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Co<PERSON>, Edit, GitBranch, RotateCcw } from "lucide-react";
import { useTheme } from "next-themes";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { getModelById, resolveModelById } from "../lib/models";
import { ChatMessageLoaders } from "./ChatMessageLoaders";
import { Markdown } from "./Markdown";
import ModelIcon, { type ModelType } from "./ModelIcon";
import { type SearchAnnotation, SearchResults } from "./SearchResults";
import { ToolResults } from "./ToolResults";

interface CRMMessageProps {
	message: {
		id: string;
		role: "user" | "assistant";
		content: string;
		toolCalls?: Array<{
			name: string;
			parameters: any;
			result?: any;
		}>;
		annotations?: Array<{
			type: string;
			data: any;
		}>;
		timestamp?: Date;
		isStreaming?: boolean;
		model?: string;
	};
	isEditable?: boolean;
	onEdit?: (content: string) => void;
	onRegenerate?: () => void;
	onCopy?: () => void;
	className?: string;
}

export function ChatMessage({
	message,
	isEditable = false,
	onEdit,
	onRegenerate,
	onCopy,
	className,
}: CRMMessageProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [editContent, setEditContent] = useState(message.content);

	const handleCopy = () => {
		navigator.clipboard.writeText(message.content);
		onCopy?.();
	};

	const handleEdit = () => {
		if (isEditing) {
			onEdit?.(editContent);
			setIsEditing(false);
		} else {
			setEditContent(message.content);
			setIsEditing(true);
		}
	};

	const handleCancelEdit = () => {
		setEditContent(message.content);
		setIsEditing(false);
	};

	const searchAnnotations = useMemo(() => {
		return (message.annotations || [])
			.filter((annotation) => annotation.type === "search_completion")
			.map(
				(annotation) =>
					annotation as {
						type: "search_completion";
						data: SearchAnnotation["data"];
					},
			);
	}, [message.annotations]);

	const searchQueries = useMemo(() => {
		return Array.from(
			new Set(
				searchAnnotations.map((annotation) => annotation.data.query),
			),
		);
	}, [searchAnnotations]);

	const searchResults = useMemo(() => {
		const searchToolCalls = (message.toolCalls || []).filter(
			(toolCall) => toolCall.name === "search",
		);

		if (searchToolCalls.length > 0) {
			return searchToolCalls.flatMap((toolCall) => toolCall.result || []);
		}

		return undefined;
	}, [message.toolCalls]);

	const messageVariants = {
		initial: { opacity: 0, y: 20, scale: 0.95 },
		animate: {
			opacity: 1,
			y: 0,
			scale: 1,
		},
	};

	return (
		<motion.div
			initial="initial"
			animate="animate"
			variants={messageVariants}
			transition={{ duration: 0.3, ease: "easeOut" }}
			className={cn("flex gap-2 w-full group", className)}
		>
			{/* {message.role === "assistant" && (
        <motion.div 
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, duration: 0.2 }}
          className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-accent/40 flex items-center justify-center mt-1 shadow-sm"
        >
          {message.isStreaming ? (
            <Sparkles className="w-4 h-4 text-primary animate-pulse" />
          ) : (
            <Logo withLabel={false} />
          )}
        </motion.div>
      )} */}

			<div className="flex-1 space-y-3">
				<div className="flex flex-col space-y-3">
					{isEditing ? (
						<motion.div
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: "auto" }}
							className="space-y-3"
						>
							<Card className="border-dashed border-2 border-primary/20">
								<CardContent className="p-4">
									<Textarea
										value={editContent}
										onChange={(e) =>
											setEditContent(e.target.value)
										}
										className="min-h-[120px] resize-none border-none shadow-none focus-visible:ring-0 bg-transparent"
										placeholder="Edit your message..."
									/>
								</CardContent>
							</Card>
							<div className="flex gap-2 justify-end">
								<Button
									variant="outline"
									size="sm"
									onClick={handleCancelEdit}
								>
									Cancel
								</Button>
								<Button
									size="sm"
									onClick={handleEdit}
									className="bg-primary"
								>
									Save Changes
								</Button>
							</div>
						</motion.div>
					) : (
						<>
							{message.role === "user" ? (
								<motion.div
									initial={{ x: 20, opacity: 0 }}
									animate={{ x: 0, opacity: 1 }}
									transition={{ delay: 0.1 }}
									className="ml-auto max-w-[85%]"
								>
									<Card className="rounded-2xl">
										<CardContent className="px-4 py-3">
											<div className="whitespace-pre-wrap text-sm leading-relaxed">
												{message.content}
											</div>
										</CardContent>
									</Card>
								</motion.div>
							) : (
								<motion.div
									initial={{ x: -10, opacity: 0 }}
									animate={{ x: 0, opacity: 1 }}
									transition={{ delay: 0.1 }}
									className="space-y-4"
								>
									{message.isStreaming ? (
										<Card className="bg-muted/30 border-dashed">
											<CardContent className="p-4">
												<div className="flex items-center gap-3">
													<ChatMessageLoaders variant="typing" />
													<span className="text-muted-foreground text-sm">
														AI is thinking...
													</span>
												</div>
											</CardContent>
										</Card>
									) : (
										<Card className="!border-none bg-transparent !p-0 rounded-2xl">
											<CardContent className="!p-0">
												<div className="prose prose-sm max-w-none text-foreground">
													<Markdown className="[&>*:first-child]:mt-0 [&>*:last-child]:mb-0">
														{message.content}
													</Markdown>
												</div>
											</CardContent>
										</Card>
									)}
								</motion.div>
							)}
						</>
					)}
				</div>

				{message.toolCalls && message.toolCalls.length > 0 && (
					<motion.div
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.3 }}
					>
						<ToolResults toolCalls={message.toolCalls} />
					</motion.div>
				)}

				{(searchResults || searchAnnotations.length > 0) && (
					<motion.div
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.4 }}
					>
						<SearchResults
							result={searchResults}
							queries={searchQueries}
							annotations={searchAnnotations}
							animate={true}
						/>
					</motion.div>
				)}
				{!isEditing && !message.isStreaming && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 0.4 }}
						className={cn(
							"flex items-center gap-1",
							message.role === "user"
								? "justify-end opacity-0 group-hover:opacity-100 transition-all duration-200"
								: "opacity-100",
						)}
					>
						<div
							className="flex items-center gap-1"
							style={{
								opacity: message.role === "assistant" ? 1 : 0,
							}}
						>
							<Button
								variant="ghost"
								size="icon"
								onClick={handleCopy}
								className="size-7"
							>
								<Copy className="size-3" />
							</Button>

							{message.role === "assistant" && onRegenerate && (
								<Button
									variant="ghost"
									size="icon"
									onClick={onRegenerate}
									className="size-7"
								>
									<RotateCcw className="size-3" />
								</Button>
							)}

							<Button
								variant="ghost"
								size="icon"
								className="size-7"
							>
								<GitBranch className="size-3" />
							</Button>

							{message.role === "assistant" && message.model && (
								<ModelIndicator modelId={message.model} />
							)}
						</div>

						{message.role === "user" && (
							<div className="flex items-center gap-1">
								<Button
									variant="ghost"
									size="icon"
									onClick={handleCopy}
									className="size-7"
								>
									<Copy className="size-3" />
								</Button>

								{isEditable && (
									<Button
										variant="ghost"
										size="icon"
										onClick={handleEdit}
										className="size-7"
									>
										<Edit className="size-3" />
									</Button>
								)}
							</div>
						)}
					</motion.div>
				)}
			</div>
		</motion.div>
	);
}

function ModelIndicator({ modelId }: { modelId: string }) {
	const [modelData, setModelData] = useState<any>(null);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		const resolveModel = async () => {
			// First try static models (immediate)
			const staticModel = getModelById(modelId);
			if (staticModel) {
				setModelData(staticModel);
				return;
			}

			// If not found and looks like ObjectId, try database lookup
			if (modelId.length === 24 && /^[0-9a-fA-F]{24}$/.test(modelId)) {
				setIsLoading(true);
				try {
					const resolvedModel = await resolveModelById(modelId);
					if (resolvedModel) {
						setModelData(resolvedModel);
					}
				} catch (error) {
					console.warn(
						"[ModelIndicator] Failed to resolve model:",
						modelId,
						error,
					);
				} finally {
					setIsLoading(false);
				}
			}
		};

		resolveModel();
	}, [modelId]);

	const displayName = modelData?.name || (isLoading ? "Loading..." : modelId);

	return (
		<div className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 hover:bg-transparent! cursor-default">
			{modelData && (
				<ModelIcon
					model={modelData.icon as ModelType}
					className="fill-primary"
				/>
			)}
			<span className="text-xs text-muted-foreground font-normal">
				{displayName}
			</span>
		</div>
	);
}
