"use client";

import AlertDialog from "@app/shared/components/AlertDialog";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { cn } from "@ui/lib";
import { useEffect, useState } from "react";
import { useModelSelection } from "../hooks/useModelSelection";
import { aiChatListQuery<PERSON>ey, aiChatQuery<PERSON>ey, type Chat } from "../lib/api";
import type { Model } from "../lib/models";
import { AiChatHeader } from "./AiChatHeader";
import { ChatSidebar } from "./ChatSidebar";
import { EnhancedAiChat } from "./EnhancedAiChat";

interface ChatData {
	id: string;
	title: string;
	messages?: Array<{
		role: "user" | "assistant";
		content: string;
	}>;
	createdAt: string;
	updatedAt: string;
}

interface AiChatInterfaceProps {
	organizationId: string;
	organizationSlug: string;
	className?: string;
}

export function AiChatInterface({
	organizationId,
	organizationSlug,
	className,
}: AiChatInterfaceProps) {
	const [currentChatId, setCurrentChatId] = useState<string | null>(null);
	const [showSidebar, setShowSidebar] = useState(true);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [chatToDelete, setChatToDelete] = useState<string | null>(null);
	const queryClient = useQueryClient();
	const { selectedModelId, selectModel } = useModelSelection();

	const { data: chats = [], isLoading: isLoadingChats } = useQuery({
		queryKey: aiChatListQueryKey(organizationId),
		queryFn: async () => {
			const response = await apiClient.ai.chats.$get({
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to fetch chats");
			}

			return response.json() as unknown as Promise<ChatData[]>;
		},
	});

	const { data: currentChat } = useQuery({
		queryKey: aiChatQueryKey(currentChatId || ""),
		queryFn: async () => {
			if (!currentChatId) return null;

			const response = await apiClient.ai.chats[":id"].$get({
				param: { id: currentChatId },
			});

			if (!response.ok) {
				throw new Error("Failed to fetch chat");
			}

			return response.json() as unknown as Promise<ChatData>;
		},
		enabled: !!currentChatId,
	});

	const createChatMutation = useMutation({
		mutationFn: async () => {
			const response = await apiClient.ai.chats.$post({
				json: {
					title: "New Chat",
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create chat");
			}

			return response.json() as unknown as Promise<Chat>;
		},
		onSuccess: (newChat) => {
			queryClient.invalidateQueries({
				queryKey: aiChatListQueryKey(organizationId),
			});
			setCurrentChatId(newChat.id);
		},
	});

	const updateChatMutation = useMutation({
		mutationFn: async ({
			chatId,
			title,
		}: {
			chatId: string;
			title: string;
		}) => {
			const response = await apiClient.ai.chats[":id"].$put({
				param: { id: chatId },
				json: { title },
			});

			if (!response.ok) {
				throw new Error("Failed to update chat");
			}

			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: aiChatListQueryKey(organizationId),
			});
		},
	});

	const deleteChatMutation = useMutation({
		mutationFn: async (chatId: string) => {
			const response = await apiClient.ai.chats[":id"].$delete({
				param: { id: chatId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete chat");
			}
		},
		onSuccess: (_, deletedChatId) => {
			queryClient.invalidateQueries({
				queryKey: aiChatListQueryKey(organizationId),
			});

			if (currentChatId === deletedChatId) {
				const remainingChats = chats.filter(
					(chat) => chat.id !== deletedChatId,
				);
				setCurrentChatId(
					remainingChats.length > 0 ? remainingChats[0].id : null,
				);
			}
		},
	});

	useEffect(() => {
		if (!currentChatId && chats.length > 0 && !isLoadingChats) {
			setCurrentChatId(chats[0].id);
		}
	}, [chats, currentChatId, isLoadingChats]);

	const handleNewChat = () => {
		createChatMutation.mutate();
	};

	const handleChatSelect = (chatId: string) => {
		setCurrentChatId(chatId);
	};

	const handleDeleteChat = (chatId: string) => {
		setChatToDelete(chatId);
		setShowDeleteDialog(true);
	};

	const confirmDeleteChat = () => {
		if (chatToDelete) {
			deleteChatMutation.mutate(chatToDelete);
			setShowDeleteDialog(false);
			setChatToDelete(null);
		}
	};

	const handleRenameChat = (chatId: string, newTitle: string) => {
		updateChatMutation.mutate({ chatId, title: newTitle });
	};

	const handleChatCreated = (chatId: string) => {
		queryClient.invalidateQueries({
			queryKey: aiChatListQueryKey(organizationId),
		});
		setCurrentChatId(chatId);
	};

	const handleModelSelect = (model: Model) => {
		selectModel(model);
	};

	return (
		<div className={cn("flex h-full", className)}>
			<div
				className={cn(
					"transition-all duration-300 ease-in-out flex-shrink-0 overflow-hidden",
					showSidebar ? "w-64 border-r border-border" : "w-0",
				)}
			>
				<ChatSidebar
					chats={chats as any}
					currentChatId={currentChatId || undefined}
					onChatSelect={handleChatSelect}
					onNewChat={handleNewChat}
					onDeleteChat={handleDeleteChat}
					onRenameChat={handleRenameChat}
					organizationId={organizationId}
					organizationSlug={organizationSlug}
					className="w-64"
				/>
			</div>

			<div className="flex-1 flex flex-col">
				<AiChatHeader
					currentChatTitle={currentChat?.title}
					onNewChat={handleNewChat}
					showSidebar={showSidebar}
					onToggleSidebar={() => setShowSidebar(!showSidebar)}
					selectedModelId={selectedModelId}
					onModelSelect={handleModelSelect}
					organizationSlug={organizationSlug}
				/>

				<div className="flex-1 overflow-y-auto">
					{currentChatId ? (
						<EnhancedAiChat
							organizationId={organizationId}
							chatId={currentChatId}
							onNewChat={handleChatCreated}
							onTitleUpdate={(chatId, title) =>
								updateChatMutation.mutate({ chatId, title })
							}
							initialMessages={currentChat?.messages ?? []}
							className="h-full"
						/>
					) : (
						<div className="h-full flex items-center justify-center">
							<div className="text-center max-w-md mx-auto px-4">
								<div className="mb-8">
									<div className="text-2xl font-semibold mb-2">
										Welcome to Alyx AI
									</div>
									<div className="text-muted-foreground">
										Start a new conversation to analyze your
										data, find contacts and ask about
										properties. Get insights about your
										business and more!
									</div>
								</div>
								<button
									onClick={handleNewChat}
									className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
									disabled={createChatMutation.isPending}
								>
									{createChatMutation.isPending
										? "Creating..."
										: "Start New Chat"}
								</button>
							</div>
						</div>
					)}
				</div>
			</div>

			<AlertDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				title="Delete Chat"
				description="Are you sure you want to delete this chat? This action cannot be undone."
				confirmLabel="Delete"
				cancelLabel="Cancel"
				confirmClassName="bg-red-500 hover:bg-red-600 text-white"
				onConfirm={confirmDeleteChat}
				loading={deleteChatMutation.isPending}
			/>
		</div>
	);
}
