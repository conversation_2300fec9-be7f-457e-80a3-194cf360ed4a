"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { apiClient } from "@shared/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import { AlertTriangle, Calendar, TrendingUp, Zap } from "lucide-react";
import { useRouter } from "next/navigation";

interface CreditsStatus {
	total: number;
	used: number;
	remaining: number;
	resetDate: string;
	hoursUntilReset: number;
	needsReset: boolean;
}

interface OrganizationCreditsStatusProps {
	organizationId: string;
	showUpgradePrompt?: boolean;
	className?: string;
}

export function OrganizationCreditsStatus({
	organizationId,
	showUpgradePrompt = true,
	className,
}: OrganizationCreditsStatusProps) {
	const { user } = useSession();
	const router = useRouter();

	const {
		data: creditsStatus,
		isLoading,
		error,
	} = useQuery({
		queryKey: ["credits-status", organizationId],
		queryFn: async (): Promise<CreditsStatus> => {
			const response = await apiClient.ai.credits.$get();
			if (!response.ok) {
				throw new Error("Failed to fetch credits status");
			}
			return await response.json();
		},
		enabled: !!user && !!organizationId,
	});

	if (isLoading) {
		return (
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Zap className="h-5 w-5" />
						AI Credits
					</CardTitle>
					<CardDescription>Loading...</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="animate-pulse">
						<div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
						<div className="h-2 bg-gray-200 rounded w-full" />
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error || !creditsStatus) {
		return (
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Zap className="h-5 w-5" />
						AI Credits
					</CardTitle>
					<CardDescription>Failed to load credits</CardDescription>
				</CardHeader>
			</Card>
		);
	}

	const usagePercentage = (creditsStatus.used / creditsStatus.total) * 100;
	const isLowCredits = creditsStatus.remaining < creditsStatus.total * 0.2;
	const isVeryLowCredits =
		creditsStatus.remaining < creditsStatus.total * 0.1;
	const isOutOfCredits = creditsStatus.remaining <= 0;

	return (
		<Card className={className}>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Zap className="h-5 w-5" />
					AI Credits
					{isOutOfCredits && <Badge>Exhausted</Badge>}
					{isVeryLowCredits && !isOutOfCredits && (
						<Badge>Very Low</Badge>
					)}
					{isLowCredits && !isVeryLowCredits && <Badge>Low</Badge>}
				</CardTitle>
				<CardDescription>
					{creditsStatus.remaining} of {creditsStatus.total} credits
					remaining
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="space-y-2">
					<div className="flex justify-between text-sm">
						<span>Used: {creditsStatus.used}</span>
						<span>Remaining: {creditsStatus.remaining}</span>
					</div>
					<Progress value={usagePercentage} className="w-full" />
				</div>

				<div className="flex items-center gap-2 text-sm text-muted-foreground">
					<Calendar className="h-4 w-4" />
					<span>
						Resets in {creditsStatus.hoursUntilReset} hour
						{creditsStatus.hoursUntilReset !== 1 ? "s" : ""}
					</span>
				</div>

				{isOutOfCredits && (
					<div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
						<div className="flex items-center gap-2 mb-1">
							<AlertTriangle className="h-4 w-4" />
							<p className="font-medium">Credits exhausted!</p>
						</div>
						<p>
							You've used all your credits. Upgrade to a paid plan
							or purchase additional credits.
						</p>
					</div>
				)}

				{isLowCredits && !isOutOfCredits && showUpgradePrompt && (
					<div className="text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-3 rounded-md">
						<p className="font-medium">Credits running low!</p>
						<p>
							Consider upgrading to a paid plan for more credits
							and unlimited features.
						</p>
					</div>
				)}

				{creditsStatus.needsReset && (
					<div className="text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md">
						<p className="font-medium">Credits will reset soon!</p>
						<p>
							Your credits are scheduled to reset within the next
							few hours.
						</p>
					</div>
				)}

				{showUpgradePrompt && (
					<div className="flex gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() =>
								router.push(
									`/app/${organizationId}/settings/ai/purchase`,
								)
							}
						>
							Buy More Credits
						</Button>
						<Button
							variant="primary"
							size="sm"
							onClick={() =>
								router.push(`/app/${organizationId}/settings`)
							}
						>
							Upgrade Plan
						</Button>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
