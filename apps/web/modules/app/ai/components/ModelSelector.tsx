"use client";

import { IconDiamondsFilled, IconPhoto } from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { BrainIcon, ChevronsUpDownIcon, WrenchIcon } from "lucide-react";
import { useState } from "react";
import type { Model } from "../lib/models";
import { getAvailableModels, models } from "../lib/models";
import ModelIcon, { type ModelType } from "./ModelIcon";

interface ModelSelectorProps {
	selectedModelId?: string;
	onModelSelect?: (model: Model) => void;
	hasGrowthAccess?: boolean;
	className?: string;
}

export function ModelSelector({
	selectedModelId = "gpt-4o-mini",
	onModelSelect,
	hasGrowthAccess = false,
	className,
}: ModelSelectorProps) {
	const [open, setOpen] = useState(false);
	const [hoveredModel, setHoveredModel] = useState<Model | null>(null);

	const availableModels = getAvailableModels();
	const selectedModel = models.find((m) => m.id === selectedModelId);

	const handleModelSelect = (model: Model) => {
		if (model.isPremium && !hasGrowthAccess) {
			return;
		}

		setOpen(false);
		setHoveredModel(null);
		onModelSelect?.(model);
	};

	const getCapabilityIcon = (capability: string) => {
		switch (capability) {
			case "thinking":
				return <BrainIcon className="size-4 text-pink-400" />;
			case "vision":
				return <IconPhoto className="size-4 text-blue-400" />;
			case "tools":
				return <WrenchIcon className="size-4 text-green-400" />;
			default:
				return null;
		}
	};

	return (
		<Popover
			open={open}
			onOpenChange={(open) => {
				setOpen(open);
				if (!open) {
					setHoveredModel(null);
				}
			}}
		>
			<PopoverTrigger asChild>
				<Button variant="relio" role="combobox" aria-expanded={open}>
					{selectedModel?.name || "Select model..."}
					<ChevronsUpDownIcon className="h-4 w-4 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[250px] p-0" align="start">
				<div className="absolute top-0 right-0 translate-x-full pl-2 hidden md:block">
					{hoveredModel && (
						<div className="rounded-xl p-2 bg-sidebar flex flex-col gap-2 w-64 border border-border">
							<div className="flex items-center gap-2">
								<ModelIcon
									className="size-4 fill-primary"
									model={hoveredModel.icon as ModelType}
								/>
								<span className="text-sm">
									{hoveredModel.name}
								</span>
							</div>
							<div className="text-sm text-muted-foreground">
								{hoveredModel.description}
							</div>
							<div className="text-sm flex gap-2 flex-wrap">
								{hoveredModel.capabilities?.map(
									(capability) => (
										<div
											key={capability}
											className="text-xs flex items-center gap-1 px-2 py-1 rounded border border-border bg-background"
										>
											{getCapabilityIcon(capability)}
											<span className="text-xs">
												{capability
													.charAt(0)
													.toUpperCase() +
													capability.slice(1)}
											</span>
										</div>
									),
								)}
							</div>
							<div className="text-xs text-muted-foreground flex gap-1 items-center pt-4">
								<IconDiamondsFilled className="!size-3 text-primary" />{" "}
								{hoveredModel.cost} credit(s) per message
							</div>
						</div>
					)}
				</div>
				<Command className="bg-sidebar">
					<CommandInput
						placeholder="Search models..."
						className="h-9"
					/>
					<CommandList>
						<CommandEmpty>No models found.</CommandEmpty>
						<CommandGroup>
							{availableModels?.map((model) => {
								const isDisabled =
									model.isPremium && !hasGrowthAccess;
								return (
									<CommandItem
										key={model.id}
										value={model.id}
										className={`data-[selected=true]:bg-muted data-[selected=true]:text-foreground ${
											isDisabled
												? "opacity-50 cursor-not-allowed"
												: "cursor-pointer"
										}`}
										onSelect={(selectedValue) => {
											if (isDisabled) {
												return;
											}
											const selectedModel =
												availableModels.find(
													(m) =>
														m.id === selectedValue,
												);
											if (selectedModel) {
												handleModelSelect(
													selectedModel,
												);
											}
										}}
										onMouseEnter={() => {
											setHoveredModel(model);
										}}
										onClick={(e) => {
											if (isDisabled) {
												e.preventDefault();
												e.stopPropagation();
												return;
											}
										}}
									>
										<span className="flex items-center gap-2 flex-1">
											{model.icon && (
												<ModelIcon
													className="h-3 w-3 fill-primary"
													model={
														model.icon as ModelType
													}
												/>
											)}
											<span className="truncate">
												{model.name}
											</span>
										</span>
										{model.isPremium &&
											!hasGrowthAccess && (
												<Tooltip>
													<TooltipTrigger>
														<div className="text-xs px-2 py-1 rounded border border-border bg-background">
															Growth
														</div>
													</TooltipTrigger>
													<TooltipContent>
														<p>
															This model is only
															available to Growth
															plan users.
														</p>
													</TooltipContent>
												</Tooltip>
											)}
									</CommandItem>
								);
							})}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
