"use client";

import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface PurchaseCreditButtonProps {
	packageId: string;
	priceId: string;
	organizationId: string;
	organizationSlug: string;
	credits: number;
	price: number;
	children: React.ReactNode;
	className?: string;
}

export function PurchaseCreditButton({
	packageId,
	priceId,
	organizationId,
	organizationSlug,
	credits,
	price,
	children,
	className,
}: PurchaseCreditButtonProps) {
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();

	const handlePurchase = async () => {
		try {
			setIsLoading(true);

			const response = await fetch("/api/ai/purchase-credits", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					packageId,
					priceId,
					organizationId,
					credits,
					price,
					successUrl: `${window.location.origin}/app/${organizationSlug}/settings/ai/credits?success=true`,
					cancelUrl: `${window.location.origin}/app/${organizationSlug}/settings/ai/purchase?canceled=true`,
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to create checkout session");
			}

			const { checkoutUrl } = await response.json();

			window.location.href = checkoutUrl;
		} catch (error) {
			console.error("Error creating checkout session:", error);
			alert("Failed to start checkout. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Button
			onClick={handlePurchase}
			disabled={isLoading}
			className={className}
		>
			{isLoading ? (
				<>
					<Loader2 className="h-4 w-4 mr-2 animate-spin" />
					Processing...
				</>
			) : (
				children
			)}
		</Button>
	);
}
