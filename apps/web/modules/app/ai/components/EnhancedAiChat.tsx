"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { useChat } from "ai/react";
import { motion } from "framer-motion";
import { ChevronDown, Sparkles } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { useModelSelection } from "../hooks/useModelSelection";
import { resolveModelById } from "../lib/models";
import { ChatMessage } from "./ChatMessage";
import { ChatMessageLoaders } from "./ChatMessageLoaders";
import { CRMPromptInput } from "./PromptInput";
import { Suggestions } from "./Suggestions";

interface EnhancedAiChatProps {
	organizationId: string;
	chatId?: string;
	className?: string;
	onNewChat?: (chatId: string) => void;
	onTitleUpdate?: (chatId: string, title: string) => void;
	initialMessages?: Array<{
		role: "user" | "assistant";
		content: string;
		model?: string;
		createdAt?: string;
		toolCalls?: Array<{
			name: string;
			parameters: any;
			result?: any;
		}>;
		annotations?: Array<{
			type: string;
			data: any;
		}>;
	}>;
}

export function EnhancedAiChat({
	organizationId,
	chatId,
	className,
	onNewChat,
	onTitleUpdate,
	initialMessages = [],
}: EnhancedAiChatProps) {
	const { selectedModelId, selectedModel } = useModelSelection();
	const [isComposing, setIsComposing] = useState(false);
	const [currentModelUsed, setCurrentModelUsed] = useState<string | null>(
		null,
	);
	const [activeTool, setActiveTool] = useState<string | undefined>(undefined);
	const [showScrollButton, setShowScrollButton] = useState(false);
	const [resolvedModels, setResolvedModels] = useState<Map<string, any>>(
		new Map(),
	);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);
	const lastMessageCountRef = useRef(0);
	const lastToolMessageCountRef = useRef(0);

	const {
		messages,
		input,
		handleInputChange,
		handleSubmit,
		isLoading,
		error,
		reload,
		stop,
		setMessages,
	} = useChat({
		api: "/api/ai/data-chat",
		id: chatId,
		body: {
			organizationId,
			selectedModel: selectedModelId,
			chatId,
			tool: activeTool,
		},
		headers: {
			// Removed Content-Type to prevent conflicts with streaming format
		},

		onResponse: async (response: Response) => {
			const newChatId = response.headers.get("X-Chat-ID");
			const modelUsed = response.headers.get("X-Model-Used");

			if (newChatId && newChatId !== chatId) {
				onNewChat?.(newChatId);
			}

			if (modelUsed) {
				setCurrentModelUsed(modelUsed);
			}
		},
		onError: (error: Error) => {
			console.error("[Frontend] Chat error:", error.message);

			if (error.message?.includes("Insufficient credits")) {
				toast.error(
					"You've reached your AI credit limit. Credits reset in 24 hours or you can purchase more credits.",
				);
			} else if (
				error.message?.includes("Rate limit") ||
				error.message?.includes("rate limit") ||
				error.message?.includes("429")
			) {
				toast.error(
					"⚠️ OpenAI rate limit reached. Your query was processed but response generation was interrupted. The conversation has been saved. Please try again in a few minutes.",
				);
				// Auto-reload to show the saved partial message
				setTimeout(() => {
					reload();
				}, 1500);
			} else if (
				error.message?.includes("stream") ||
				error.message?.includes("An error occurred")
			) {
				console.error(
					"[Frontend] Stream parsing error - attempting auto-recovery",
				);
				toast.error(
					"Response generated successfully! Refreshing chat to display the message...",
				);

				// Auto-refresh the messages after a short delay to show the successful response
				// setTimeout(() => {
				//   reload();
				// }, 2000);
			} else {
				toast.error(
					`Failed to send message: ${error.message || "Unknown error"}`,
				);
			}

			setIsComposing(false);
		},
		onFinish: (message: any) => {
			setIsComposing(false);

			if (messages.length === 1 && messages[0]?.content) {
				generateAndUpdateTitle(messages[0]?.content || input);
			}
		},
	});

	const generateAndUpdateTitle = useCallback(
		async (userMessage: string) => {
			if (!chatId || !onTitleUpdate) return;

			try {
				const words = userMessage
					.toLowerCase()
					.replace(/[^\w\s]/g, "") // Remove punctuation
					.split(/\s+/)
					.filter((word) => word.length > 2) // Remove short words
					.filter(
						(word) =>
							![
								"what",
								"are",
								"the",
								"my",
								"can",
								"you",
								"show",
								"me",
								"give",
								"get",
								"how",
								"tell",
							].includes(word),
					); // Remove common words

				const titleWords = words
					.slice(0, 3)
					.map(
						(word) => word.charAt(0).toUpperCase() + word.slice(1),
					);

				if (titleWords.length === 0) {
					const fallbackWords = userMessage.split(/\s+/).slice(0, 3);
					titleWords.push(
						...fallbackWords.map(
							(word) =>
								word.charAt(0).toUpperCase() +
								word.slice(1).toLowerCase(),
						),
					);
				}

				const title = titleWords.join(" ");

				onTitleUpdate(chatId, title);
			} catch (error) {
				console.error("Failed to generate title:", error);
			}
		},
		[chatId, onTitleUpdate],
	);

	// Check if user is at bottom of scroll container
	const checkScrollPosition = useCallback(() => {
		const scrollContainer = scrollContainerRef.current;
		if (!scrollContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const isAtBottom = scrollHeight - scrollTop - clientHeight < 100; // 100px threshold
		setShowScrollButton(!isAtBottom);
	}, []);

	// Add scroll event listener
	useEffect(() => {
		const scrollContainer = scrollContainerRef.current;
		if (!scrollContainer) return;

		const handleScroll = () => {
			checkScrollPosition();
		};

		scrollContainer.addEventListener("scroll", handleScroll, {
			passive: true,
		});

		// Check initial position
		checkScrollPosition();

		return () => {
			scrollContainer.removeEventListener("scroll", handleScroll);
		};
	}, [checkScrollPosition]);

	useEffect(() => {
		if (initialMessages.length > 0) {
			const convertedMessages = initialMessages.map((msg, index) => ({
				id: `initial-${chatId}-${index}`,
				role: msg.role,
				content: msg.content,
				createdAt: msg.createdAt ? new Date(msg.createdAt) : undefined,
				// Include tool calls if they exist
				...(msg.toolCalls &&
					msg.toolCalls.length > 0 && {
						toolCalls: msg.toolCalls,
					}),
				// Include annotations if they exist
				...(msg.annotations &&
					msg.annotations.length > 0 && {
						annotations: msg.annotations,
					}),
				// Include model info if it exists
				...(msg.model && {
					model: msg.model,
				}),
			}));
			setMessages(convertedMessages);
		} else {
			setMessages([]);
		}
	}, [chatId, initialMessages, setMessages]);

	useEffect(() => {
		const scrollContainer = scrollContainerRef.current;
		if (!scrollContainer) return;

		const hasNewMessage = messages.length > lastMessageCountRef.current;
		lastMessageCountRef.current = messages.length;

		if (hasNewMessage) {
			const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
			const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

			if (isNearBottom) {
				setTimeout(() => {
					messagesEndRef.current?.scrollIntoView({
						behavior: "smooth",
						block: "end",
					});
				}, 100);
			}
		}
	}, [messages]);

	useEffect(() => {
		const textarea = textareaRef.current;
		if (textarea) {
			textarea.style.height = "auto";
			textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
		}
	}, [input]);

	const scrollToBottom = useCallback(() => {
		setTimeout(() => {
			messagesEndRef.current?.scrollIntoView({
				behavior: "smooth",
				block: "end",
			});
		}, 100);
	}, []);

	const handleFormSubmit = useCallback(
		async (e?: React.FormEvent | any) => {
			e?.preventDefault?.();
			if (!input.trim() || isLoading) return;

			setIsComposing(true);

			scrollToBottom();

			handleSubmit(e || ({} as React.FormEvent));
		},
		[input, isLoading, handleSubmit, scrollToBottom],
	);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				handleFormSubmit(e as any);
			}
		},
		[handleFormSubmit],
	);

	const handleInputChangeWrapper = useCallback(
		(value: string) => {
			handleInputChange({ target: { value } } as any);
		},
		[handleInputChange],
	);

	const handleSuggestionClick = useCallback(
		(suggestion: string) => {
			handleInputChange({ target: { value: suggestion } } as any);
		},
		[handleInputChange],
	);

	const handleMessageEdit = useCallback(
		(content: string) => {
			handleInputChange({ target: { value: content } } as any);
		},
		[handleInputChange],
	);

	const handleMessageRegenerate = useCallback(() => {
		reload();
	}, [reload]);

	const handleMessageCopy = useCallback(() => {
		toast.success("Message copied to clipboard");
	}, []);

	// Function to resolve model ID asynchronously
	const resolveModelId = useCallback(
		async (modelId: string) => {
			if (resolvedModels.has(modelId)) {
				return resolvedModels.get(modelId);
			}

			try {
				const resolvedModel = await resolveModelById(modelId);
				if (resolvedModel) {
					setResolvedModels(
						(prev) => new Map(prev.set(modelId, resolvedModel)),
					);
					return resolvedModel;
				}
			} catch (error) {
				console.warn(
					"[EnhancedAiChat] Failed to resolve model:",
					modelId,
					error,
				);
			}

			return null;
		},
		[resolvedModels],
	);

	// Effect to pre-resolve model IDs from messages
	useEffect(() => {
		const modelIds = new Set<string>();

		messages.forEach((message: any) => {
			if (message.role === "assistant") {
				const modelId =
					(message as any).model ||
					currentModelUsed ||
					selectedModelId;
				if (modelId && !resolvedModels.has(modelId)) {
					modelIds.add(modelId);
				}
			}
		});

		// Resolve all unique model IDs
		modelIds.forEach(async (modelId) => {
			await resolveModelId(modelId);
		});
	}, [messages, currentModelUsed, selectedModelId, resolveModelId]);

	const displayMessages = useMemo(() => {
		return messages.map((message: any, index: number) => {
			let messageModel: string | undefined;

			if (message.role === "assistant") {
				// Priority: stored model info > streaming model > selected model
				const modelId =
					(message as any).model ||
					currentModelUsed ||
					selectedModelId;

				// Try to get resolved model first
				const resolvedModel = resolvedModels.get(modelId);
				if (resolvedModel) {
					messageModel = resolvedModel.id;
				} else {
					messageModel = modelId;
				}
			}

			let timestamp: Date | undefined;
			if (
				message.createdAt &&
				message.createdAt instanceof Date &&
				!isNaN(message.createdAt.getTime())
			) {
				timestamp = message.createdAt;
			}

			let toolCalls: any[] = [];
			const messageWithTools = message as any;

			if (
				messageWithTools.toolInvocations &&
				Array.isArray(messageWithTools.toolInvocations)
			) {
				toolCalls = messageWithTools.toolInvocations.map(
					(invocation: any) => ({
						name: invocation.toolName,
						parameters: invocation.args,
						result: invocation.result,
					}),
				);
			}

			if (
				messageWithTools.toolCalls &&
				Array.isArray(messageWithTools.toolCalls)
			) {
				toolCalls = messageWithTools.toolCalls.map((call: any) => ({
					name: call.toolName || call.name,
					parameters: call.args || call.parameters,
					result: call.result,
				}));
			}

			return {
				id: message.id,
				role: message.role as "user" | "assistant",
				content: message.content,
				toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
				annotations: (message.annotations || []) as Array<{
					type: string;
					data: any;
				}>,
				timestamp,
				isStreaming:
					isLoading &&
					index === messages.length - 1 &&
					message.role === "assistant",
				model: messageModel,
			};
		});
	}, [
		messages,
		currentModelUsed,
		selectedModelId,
		isLoading,
		resolvedModels,
	]);

	useEffect(() => {
		if (chatId && displayMessages.length > 0) {
			const timer = setTimeout(() => {
				const scrollContainer = scrollContainerRef.current;
				if (scrollContainer) {
					scrollContainer.scrollTop = scrollContainer.scrollHeight;
				}
				setShowScrollButton(false);
			}, 200);

			return () => clearTimeout(timer);
		}
	}, [chatId, displayMessages.length]);

	return (
		<div className={cn("flex flex-col h-full relative", className)}>
			<div ref={scrollContainerRef} className="flex-1 overflow-y-auto">
				<div className="p-4 space-y-6 max-w-3xl mx-auto">
					{displayMessages.length === 0 ? (
						<div className="flex flex-col items-center justify-center h-full">
							<Suggestions
								input={input}
								onSuggestionClick={handleSuggestionClick}
							/>
						</div>
					) : (
						<div className="space-y-6">
							{displayMessages.map(
								(message: any, index: number) => (
									<ChatMessage
										key={message.id}
										message={message}
										isEditable={
											message.role === "user" &&
											index === displayMessages.length - 1
										}
										onEdit={handleMessageEdit}
										onRegenerate={
											message.role === "assistant"
												? handleMessageRegenerate
												: undefined
										}
										onCopy={handleMessageCopy}
									/>
								),
							)}
							{isLoading &&
								!displayMessages.some(
									(m: any) => m.isStreaming,
								) && (
									<div className="flex gap-4 w-full">
										<div className="flex-shrink-0 w-8 h-8 rounded-full bg-accent flex items-center justify-center mt-1">
											<Sparkles className="w-4 h-4 text-accent-foreground" />
										</div>
										<div className="flex-1">
											<ChatMessageLoaders variant="typing" />
										</div>
									</div>
								)}

							<div ref={messagesEndRef} />
						</div>
					)}
				</div>
			</div>

			{/* Scroll to bottom button */}
			{showScrollButton && (
				<motion.div
					initial={{ opacity: 0, scale: 0.8, y: 10 }}
					animate={{ opacity: 1, scale: 1, y: 0 }}
					exit={{ opacity: 0, scale: 0.8, y: 10 }}
					className="absolute bottom-35 left-1/2 transform -translate-x-1/2 z-10"
				>
					<Button
						onClick={scrollToBottom}
						variant="outline"
						size="sm"
						className="rounded-full shadow-lg bg-blue-500/80 backdrop-blur-sm hover:bg-blue-500/90"
					>
						<span className="text-xs">Scroll to bottom</span>
						<ChevronDown className="size-3 ml-1" />
					</Button>
				</motion.div>
			)}

			{error && (
				<motion.div
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					className="px-4 py-2 bg-destructive/10 border border-destructive/20 text-destructive text-sm"
				>
					<div className="flex items-center justify-between">
						<span>Failed to send message: {error.message}</span>
						<Button
							variant="ghost"
							size="sm"
							onClick={() => reload()}
							className="text-destructive hover:text-destructive"
						>
							Retry
						</Button>
					</div>
				</motion.div>
			)}

			<div className="pb-4">
				<CRMPromptInput
					input={input}
					onInputChange={handleInputChangeWrapper}
					onSubmit={handleFormSubmit}
					isLoading={isLoading}
					disabled={isLoading}
					placeholder="Ask about your CRM data, create tasks, or analyze metrics..."
					tool={activeTool}
					onToolChange={setActiveTool}
					supportsTools={true}
				/>
			</div>
		</div>
	);
}

export function ExampleUsage() {
	const [currentChatId, setCurrentChatId] = useState<string>();

	const handleTitleUpdate = useCallback(
		async (chatId: string, title: string) => {
			try {
				const response = await fetch(`/api/ai/chats/${chatId}`, {
					method: "PUT",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({ title }),
				});

				if (!response.ok) {
					throw new Error("Failed to update title");
				}
			} catch (error) {
				console.error("Failed to update chat title:", error);
			}
		},
		[],
	);

	return (
		<div className="h-screen max-w-3xl mx-auto">
			<EnhancedAiChat
				organizationId="your-organization-id"
				chatId={currentChatId}
				onNewChat={(chatId) => {
					setCurrentChatId(chatId);
				}}
				onTitleUpdate={handleTitleUpdate}
			/>
		</div>
	);
}
