import { IconBuilding, IconChartArea, IconHome, IconSquareRoundedCheck, IconUsers } from "@tabler/icons-react";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	Activity,
	BarChart3,
	Building,
	Calendar,
	CheckSquare,
	Search,
	TrendingUp,
	Users,
} from "lucide-react";
import { useState } from "react";

const suggestionGroups = {
	contacts: [
		"Show me my recent contacts added this week",
		"Find contacts in the technology industry",
		"Who are my top contacts by interaction count?",
		"List contacts that haven't been contacted in over 30 days",
	],
	analytics: [
		"What are my analytics for this month?",
		"Show me contact growth trends over the last quarter",
		"How many tasks were completed this week?",
		"Generate a summary of my CRM performance",
	],
	tasks: [
		"What tasks are overdue and need immediate attention?",
		"Create a task to follow up with <PERSON> tomorrow",
		"Show me all high-priority tasks for this week",
		"List tasks related to lead qualification",
	],
	companies: [
		"Find companies in the healthcare sector",
		"Show me companies with the highest deal values",
		"Which companies have we contacted recently?",
		"List companies that need follow-up activities",
	],
};

const tabLabels = {
	contacts: "Contacts",
	analytics: "Analytics",
	tasks: "Tasks",
	companies: "Companies",
	properties: "Properties",
};

const tabIcons = {
	contacts: <IconUsers className="size-4" />,
	analytics: <IconChartArea className="size-4" />,
	tasks: <IconSquareRoundedCheck className="size-4" />,
	companies: <IconBuilding className="size-4" />,
	properties: <IconHome className="size-4" />,
};

interface SuggestionsProps {
	input: string;
	onSuggestionClick: (suggestion: string) => void;
	className?: string;
}

export function Suggestions({
	input,
	onSuggestionClick,
	className,
}: SuggestionsProps) {
	const [activeTab, setActiveTab] =
		useState<keyof typeof suggestionGroups>("contacts");

	return (
		<motion.div
			className={cn(
				"mt-8 px-2 w-full max-w-xl flex flex-col items-center justify-center",
				input && "pointer-events-none",
			)}
			initial={{ opacity: 0, y: 10 }}
			animate={{
				opacity: input ? 0 : 1,
				y: input ? -10 : 0,
			}}
			transition={{ duration: 0.3 }}
		>
			{/* Header */}
			<div className="text-center mb-6">
				<h2 className="text-xl font-semibold mb-2">
					Welcome to your AI Data Assistant
				</h2>
				<p className="text-muted-foreground text-sm">
					Ask questions about your CRM data to get instant insights
					and analysis
				</p>
			</div>

			{/* Tabs */}
			<div className="flex gap-2 mb-4">
				{Object.entries(tabLabels).map(([key, label]) => (
					<button
						key={key}
						className={cn(
							"md:flex-row md:w-auto md:px-6 md:h-8 md:gap-2",
							"rounded-2xl flex flex-col text-sm gap-1 border items-center justify-center w-16 h-16 border-border bg-muted/50 cursor-pointer hover:bg-muted/80 transition-all duration-200",
							activeTab === key &&
								"bg-primary border-primary hover:bg-primary/80 text-primary-foreground",
						)}
						onClick={() =>
							setActiveTab(key as keyof typeof suggestionGroups)
						}
					>
						{tabIcons[key as keyof typeof tabIcons]}
						<span className="text-xs">{label}</span>
					</button>
				))}
			</div>

			{/* Suggestions */}
			<motion.div
				key={activeTab}
				className="flex flex-col w-full"
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.2 }}
			>
				{suggestionGroups[activeTab].map((suggestion, index) => (
					<button
						key={suggestion}
						onClick={() => onSuggestionClick(suggestion)}
						className={cn(
							"text-sm py-4 px-2 cursor-pointer text-left text-foreground/80 transition-colors hover:text-foreground last:border-b-0 border-b border-border/80 hover:bg-muted/30 rounded-md",
						)}
					>
						<div className="flex items-start gap-3">
							<div className="flex-shrink-0 w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
								<span className="text-xs font-medium text-primary">
									{index + 1}
								</span>
							</div>
							<span className="flex-1">{suggestion}</span>
						</div>
					</button>
				))}
			</motion.div>

			{/* Footer tip */}
			<div className="mt-6 text-center">
				<p className="text-xs text-muted-foreground">
					💡 Tip: You can ask about contacts, tasks, analytics,
					companies, or any data insights
				</p>
			</div>
		</motion.div>
	);
}
