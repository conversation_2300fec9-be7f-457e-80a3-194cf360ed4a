"use client";

import {
	IconAt,
	IconBuilding,
	IconCopy,
	IconExternalLink,
	IconMapPin,
	IconUsers,
} from "@tabler/icons-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface CompanyData {
	id?: string;
	name?: string;
	industry?: string;
	location?: string;
	website?: string;
	email?: string;
	logo?: string;
	description?: string;
	contactCount?: number;
	status?: string;
}

interface CompanyCardProps {
	company: CompanyData;
	className?: string;
	onEdit?: (companyId: string) => void;
	onViewContacts?: (companyId: string) => void;
	showActions?: boolean;
}

export function CompanyCard({
	company,
	className,
	onEdit,
	onViewContacts,
	showActions = true,
}: CompanyCardProps) {
	const [isHovered, setIsHovered] = useState(false);

	const getCompanyName = () => {
		return company.name || "Unnamed Company";
	};

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((part) => part.charAt(0))
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	const handleCopy = useCallback((value: string, label: string) => {
		navigator.clipboard.writeText(value);
		toast.success(`${label} copied to clipboard`);
	}, []);

	const handleEdit = useCallback(() => {
		if (company.id) {
			onEdit?.(company.id);
		}
	}, [company.id, onEdit]);

	const handleViewContacts = useCallback(() => {
		if (company.id) {
			onViewContacts?.(company.id);
		}
	}, [company.id, onViewContacts]);

	const companyName = getCompanyName();

	return (
		<motion.div
			initial={{ opacity: 0, y: 10 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className={cn("w-full", className)}
		>
			<Card
				className="hover:shadow-md transition-all duration-200 cursor-pointer"
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
			>
				<CardContent className="p-4">
					<div className="flex items-start gap-3">
						<div className="flex-shrink-0">
							<Avatar className="w-12 h-12">
								<AvatarImage
									src={company.logo}
									alt={companyName}
								/>
								<AvatarFallback className="bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300">
									{getInitials(companyName)}
								</AvatarFallback>
							</Avatar>
						</div>

						<div className="flex-1 min-w-0">
							<div className="space-y-1">
								<h4 className="font-semibold text-sm text-foreground truncate">
									{companyName}
								</h4>
								{company.industry && (
									<p className="text-xs text-muted-foreground truncate capitalize">
										{company.industry.replace(/_/g, " ")}
									</p>
								)}
							</div>

							<div className="mt-3 space-y-2">
								{company.location && (
									<div className="flex items-center gap-2 text-xs">
										<IconMapPin className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<span className="text-muted-foreground truncate">
											{company.location}
										</span>
									</div>
								)}

								{company.email && (
									<div className="flex items-center gap-2 text-xs">
										<IconAt className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<div className="flex items-center gap-1 min-w-0 flex-1">
											<a
												href={`mailto:${company.email}`}
												className="text-blue-600 hover:underline truncate"
												onClick={(e) =>
													e.stopPropagation()
												}
											>
												{company.email}
											</a>
											{isHovered && (
												<Button
													variant="ghost"
													size="sm"
													className="h-4 w-4 p-0 hover:bg-muted/50"
													onClick={(e) => {
														e.stopPropagation();
														handleCopy(
															company.email!,
															"Email",
														);
													}}
												>
													<IconCopy className="w-3 h-3" />
												</Button>
											)}
										</div>
									</div>
								)}

								{company.contactCount !== undefined && (
									<div className="flex items-center gap-2 text-xs">
										<IconUsers className="w-3 h-3 text-muted-foreground flex-shrink-0" />
										<span className="text-muted-foreground">
											{company.contactCount} contact
											{company.contactCount !== 1
												? "s"
												: ""}
										</span>
									</div>
								)}

								{company.description && (
									<div className="text-xs text-muted-foreground line-clamp-2 mt-2">
										{company.description}
									</div>
								)}
							</div>

							<div className="mt-3 flex items-center justify-between">
								<div className="flex items-center gap-2">
									{company.status && (
										<Badge
											status="info"
											className="text-xs"
										>
											{company.status}
										</Badge>
									)}
								</div>

								{showActions && isHovered && (
									<motion.div
										initial={{ opacity: 0, scale: 0.9 }}
										animate={{ opacity: 1, scale: 1 }}
										className="flex items-center gap-1"
									>
										{company.website && (
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-6 w-6 p-0 hover:bg-muted/80"
														onClick={(e) => {
															e.stopPropagation();
															window.open(
																company.website?.startsWith(
																	"http",
																)
																	? company.website
																	: `https://${company.website}`,
																"_blank",
															);
														}}
													>
														<IconExternalLink className="w-3 h-3" />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													Visit website
												</TooltipContent>
											</Tooltip>
										)}

										{company.contactCount &&
											company.contactCount > 0 && (
												<Tooltip>
													<TooltipTrigger asChild>
														<Button
															variant="ghost"
															size="sm"
															className="h-6 w-6 p-0 hover:bg-muted/80"
															onClick={
																handleViewContacts
															}
														>
															<IconUsers className="w-3 h-3" />
														</Button>
													</TooltipTrigger>
													<TooltipContent>
														View contacts
													</TooltipContent>
												</Tooltip>
											)}
									</motion.div>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
