import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery } from "@tanstack/react-query";

export const aiChatListQueryKey = (organizationId?: string) =>
	organizationId
		? (["ai-chat-list", organizationId] as const)
		: (["ai-chat-list"] as const);
export const useAiChatListQuery = (organizationId?: string) =>
	useQuery<Chat[]>({
		queryKey: aiChatListQueryKey(organizationId),
		queryFn: async () => {
			const response = await apiClient.ai.chats.$get({
				query: {
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch AI chat list");
			}

			return response.json() as Promise<Chat[]>;
		},
	});

export const aiChatQueryKey = (id: string) => ["ai-chat", id];
export const useAiChatQuery = (id: string) =>
	useQuery<Chat | null>({
		queryKey: aiChatQuery<PERSON>ey(id),
		queryFn: async () => {
			if (id === "new") {
				return null;
			}

			const response = await apiClient.ai.chats[":id"].$get({
				param: {
					id,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch AI chat");
			}

			return response.json() as Promise<Chat>;
		},
	});

export interface ChatMessage {
	id?: string;
	content: string;
	role: string;
	createdAt: string;
}

export interface Chat {
	id: string;
	title: string | null;
	createdAt: string;
	updatedAt: string;
	userId: string | null;
	organizationId: string | null;
	messages?: ChatMessage[];
}

export const useCreateAiChatMutation = () => {
	return useMutation<
		Chat,
		Error,
		{ title?: string; organizationId?: string }
	>({
		mutationFn: async ({
			title,
			organizationId,
		}: {
			title?: string;
			organizationId?: string;
		}) => {
			const response = await apiClient.ai.chats.$post({
				json: {
					title,
					organizationId: organizationId || "",
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create AI chat");
			}

			return response.json() as unknown as Promise<Chat>;
		},
	});
};

export const updateAiChatMutation = () => {
	return useMutation({
		mutationFn: async ({ id, title }: { id: string; title?: string }) => {
			const response = await apiClient.ai.chats[":id"].$put({
				param: { id },
				json: { title },
			});

			if (!response.ok) {
				throw new Error("Failed to update AI chat");
			}

			return response.json();
		},
	});
};

export const deleteAiChatMutation = () => {
	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.ai.chats[":id"].$delete({
				param: { id },
			});

			if (!response.ok) {
				throw new Error("Failed to delete AI chat");
			}
		},
	});
};
