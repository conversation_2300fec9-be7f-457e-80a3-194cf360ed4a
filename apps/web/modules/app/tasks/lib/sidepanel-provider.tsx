"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import { X } from "lucide-react";
import { createContext, type ReactNode, useContext, useState } from "react";

interface SidePanelContextType {
	open: boolean;
	setOpen: (open: boolean) => void;
	title: string;
	setTitle: (title: string) => void;
	content: ReactNode;
	setContent: (content: ReactNode) => void;
}

const SidePanelContext = createContext<SidePanelContextType | undefined>(
	undefined,
);

export function SidePanelProvider({ children }: { children: ReactNode }) {
	const [open, setOpen] = useState(false);
	const [title, setTitle] = useState("");
	const [content, setContent] = useState<ReactNode>(null);

	return (
		<SidePanelContext.Provider
			value={{
				open,
				setOpen,
				title,
				setTitle,
				content,
				setContent,
			}}
		>
			<div className="relative flex h-[calc(100vh-4rem)]">
				<motion.div
					className="flex-1"
					animate={{
						marginRight: open ? "22rem" : 0,
					}}
					transition={{
						type: "spring",
						bounce: 0,
						duration: 0.3,
					}}
				>
					{children}
				</motion.div>
				<SidePanel />
			</div>
		</SidePanelContext.Provider>
	);
}

export function useSidePanel() {
	const context = useContext(SidePanelContext);
	if (!context) {
		throw new Error("useSidePanel must be used within a SidePanelProvider");
	}
	return context;
}

function SidePanel() {
	const { open, setOpen, title, content } = useSidePanel();

	return (
		<AnimatePresence>
			{open && (
				<motion.div
					initial={{ x: "100%" }}
					animate={{ x: 0 }}
					exit={{ x: "100%" }}
					transition={{
						type: "spring",
						bounce: 0,
						duration: 0.3,
					}}
					className={cn(
						"z-20 flex h-full w-[22rem] flex-col",
						"border-l border-l-zinc-300 bg-white/80 backdrop-blur-sm shadow-lg",
						"focus:outline-hidden dark:border-l-zinc-800 dark:bg-background",
						"absolute right-0 top-0 bottom-0",
					)}
				>
					<div className="flex items-center p-[1.125rem] border-b border-zinc-200 dark:border-zinc-800">
						<p className="text-sm font-medium text-zinc-800 dark:text-zinc-200">
							{title}
						</p>
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setOpen(false)}
							className="ml-auto h-5 w-5"
						>
							<X className="h-3 w-3 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-300 transition-colors" />
						</Button>
					</div>
					<div className="flex-1 overflow-y-auto px-4 py-3">
						{content}
					</div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
