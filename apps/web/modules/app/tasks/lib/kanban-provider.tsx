"use client";

import type { TaskStatus } from "@app/shared/lib/constants";
import React, { createContext, useContext, useState } from "react";

interface KanbanContextType {
	hoveredColumn: TaskStatus | null;
	setHoveredColumn: (column: TaskStatus | null) => void;
	selectedTasks: Set<string>;
	setSelectedTasks: (tasks: Set<string>) => void;
	hiddenStatuses: TaskStatus[];
	setHiddenStatuses: (statuses: TaskStatus[]) => void;
	draggedTask: any | null;
	setDraggedTask: (task: any | null) => void;
	dragOverColumn: TaskStatus | null;
	setDragOverColumn: (column: TaskStatus | null) => void;
	dragOverTask: any | null;
	setDragOverTask: (task: any | null) => void;
	dropPosition: "before" | "after" | null;
	setDropPosition: (position: "before" | "after" | null) => void;
}

const KanbanContext = createContext<KanbanContextType | undefined>(undefined);

export function KanbanProvider({ children }: { children: React.ReactNode }) {
	const [hoveredColumn, setHoveredColumn] = useState<TaskStatus | null>(null);
	const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
	const [hiddenStatuses, setHiddenStatuses] = useState<TaskStatus[]>([]);
	const [draggedTask, setDraggedTask] = useState<any | null>(null);
	const [dragOverColumn, setDragOverColumn] = useState<TaskStatus | null>(
		null,
	);
	const [dragOverTask, setDragOverTask] = useState<any | null>(null);
	const [dropPosition, setDropPosition] = useState<"before" | "after" | null>(
		null,
	);

	return (
		<KanbanContext.Provider
			value={{
				hoveredColumn,
				setHoveredColumn,
				selectedTasks,
				setSelectedTasks,
				hiddenStatuses,
				setHiddenStatuses,
				draggedTask,
				setDraggedTask,
				dragOverColumn,
				setDragOverColumn,
				dragOverTask,
				setDragOverTask,
				dropPosition,
				setDropPosition,
			}}
		>
			{children}
		</KanbanContext.Provider>
	);
}

export function useKanban() {
	const context = useContext(KanbanContext);
	if (context === undefined) {
		throw new Error("useKanban must be used within a KanbanProvider");
	}
	return context;
}
