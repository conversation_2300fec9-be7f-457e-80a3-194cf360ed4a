import type { Task } from "@repo/database/src/zod";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";

export interface CreateTaskPayload {
	title: string;
	description?: string | null;
	dueDate?: string | null;
	status: string;
	priority: string;
	assigneeId?: string | null;
	relatedObjectId?: string | null;
	relatedObjectType?: string | null;
	position?: number | null;
	organizationId: string;
	createdById: string;
}

export async function createTask(payload: CreateTaskPayload): Promise<Task> {
	const safePayload = {
		...payload,
		description: payload.description ?? null,
		dueDate: payload.dueDate ?? null,
		assigneeId: payload.assigneeId ?? null,
		position: payload.position ?? null,
		relatedObjectId: payload.relatedObjectId ?? null,
		relatedObjectType: payload.relatedObjectType ?? null,
		createdById: payload.createdById ?? null,
	};
	const res = await fetch("/api/tasks", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(safePayload),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create task");
	}
	return res.json();
}

export async function fetchTasks(organizationId: string): Promise<Task[]> {
	const res = await fetch(`/api/tasks?organizationId=${organizationId}`);
	if (!res.ok) {
		throw new Error("Failed to fetch tasks");
	}
	return res.json();
}

export async function fetchContactTasks(contactId: string, organizationId: string): Promise<Task[]> {
	const res = await fetch(`/api/tasks?organizationId=${organizationId}&relatedObjectId=${contactId}&relatedObjectType=contact`);
	if (!res.ok) {
		throw new Error("Failed to fetch contact tasks");
	}
	return res.json();
}

export interface UpdateTaskPayload {
	id: string;
	title?: string;
	description?: string | null;
	dueDate?: string | null;
	status?: string;
	priority?: string;
	assigneeId?: string | null;
	relatedObjectId?: string | null;
	relatedObjectType?: string | null;
	position?: number | null;
	createdById?: string | null;
}

export async function updateTask(payload: UpdateTaskPayload): Promise<Task> {
	const { id, ...data } = payload;
	const {
		title,
		description,
		dueDate,
		status,
		priority,
		assigneeId,
		relatedObjectId,
		relatedObjectType,
		position,
		createdById,
	} = data;
	const safePayload = {
		...(title !== undefined && { title }),
		...(description !== undefined && { description: description ?? null }),
		...(dueDate !== undefined && { dueDate: dueDate ?? null }),
		...(status !== undefined && { status }),
		...(priority !== undefined && { priority }),
		...(assigneeId !== undefined && { assigneeId: assigneeId ?? null }),
		...(relatedObjectId !== undefined && { relatedObjectId: relatedObjectId ?? null }),
		...(relatedObjectType !== undefined && { relatedObjectType: relatedObjectType ?? null }),
		...(position !== undefined && { position: position ?? null }),
		...(createdById !== undefined && { createdById: createdById ?? null }),
	};
	const res = await fetch(`/api/tasks/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(safePayload),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update task");
	}
	return res.json();
}

export function useUpdateTask(organizationId: string | undefined) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateTask,
		onMutate: async (updatedTask) => {
			await queryClient.cancelQueries({
				queryKey: ["tasks", organizationId],
			});
			const previousTasks = queryClient.getQueryData([
				"tasks",
				organizationId,
			]);
			queryClient.setQueryData(
				["tasks", organizationId],
				(old: Task[] = []) => {
					return old.map((task) =>
						task.id === updatedTask.id
							? { ...task, ...updatedTask }
							: task,
					);
				},
			);
			return { previousTasks };
		},
		onError: (err, newTask, context) => {
			if (context?.previousTasks) {
				queryClient.setQueryData(
					["tasks", organizationId],
					context.previousTasks,
				);
			}
		},
		onSettled: () => {
			// We will only invalidate if we need fresh data (e.g., for real-time updates - this is not needed for now)
			// The optimistic update should handle most cases
			queryClient.invalidateQueries({
				queryKey: ["tasks", organizationId],
				refetchType: "none", // Don't refetch immediately
			});
			// Also invalidate contact-specific task queries
			queryClient.invalidateQueries({
				queryKey: ["tasks", "contact"],
				refetchType: "none",
			});
		},
	});
}

// Contact-specific task update hook with optimistic updates
export function useUpdateContactTask(contactId: string | undefined, organizationId: string | undefined) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateTask,
		onMutate: async (updatedTask) => {
			// Cancel any outgoing refetches (so they don't overwrite our optimistic update)
			await queryClient.cancelQueries({
				queryKey: ["tasks", "contact", contactId, organizationId],
			});
			await queryClient.cancelQueries({
				queryKey: ["tasks", organizationId],
			});

			// Snapshot the previous value
			const previousContactTasks = queryClient.getQueryData([
				"tasks", "contact", contactId, organizationId,
			]);
			const previousAllTasks = queryClient.getQueryData([
				"tasks", organizationId,
			]);

			// Optimistically update contact tasks
			queryClient.setQueryData(
				["tasks", "contact", contactId, organizationId],
				(old: Task[] = []) => {
					return old.map((task) =>
						task.id === updatedTask.id
							? { ...task, ...updatedTask }
							: task,
					);
				},
			);

			// Also update all tasks cache if it exists
			queryClient.setQueryData(
				["tasks", organizationId],
				(old: Task[] = []) => {
					return old.map((task) =>
						task.id === updatedTask.id
							? { ...task, ...updatedTask }
							: task,
					);
				},
			);

			return { previousContactTasks, previousAllTasks };
		},
		onError: (err, newTask, context) => {
			// If the mutation fails, use the context returned from onMutate to roll back
			if (context?.previousContactTasks) {
				queryClient.setQueryData(
					["tasks", "contact", contactId, organizationId],
					context.previousContactTasks,
				);
			}
			if (context?.previousAllTasks) {
				queryClient.setQueryData(
					["tasks", organizationId],
					context.previousAllTasks,
				);
			}
		},
		onSettled: () => {
			// Always refetch after error or success
			queryClient.invalidateQueries({
				queryKey: ["tasks", "contact", contactId, organizationId],
				refetchType: "none",
			});
			queryClient.invalidateQueries({
				queryKey: ["tasks", organizationId],
				refetchType: "none",
			});
		},
	});
}

export async function deleteTask(id: string): Promise<void> {
	const res = await fetch(`/api/tasks/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete task");
	}
}

export function useDeleteTask(organizationId: string | undefined) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: deleteTask,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["tasks", organizationId],
			});
			// Also invalidate contact-specific task queries
			queryClient.invalidateQueries({
				queryKey: ["tasks", "contact"],
			});
		},
	});
}

// Contact-specific task delete hook with optimistic updates
export function useDeleteContactTask(contactId: string | undefined, organizationId: string | undefined) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteTask,
		onMutate: async (taskId: string) => {
			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["tasks", "contact", contactId, organizationId],
			});
			await queryClient.cancelQueries({
				queryKey: ["tasks", organizationId],
			});

			// Snapshot the previous value
			const previousContactTasks = queryClient.getQueryData([
				"tasks", "contact", contactId, organizationId,
			]);
			const previousAllTasks = queryClient.getQueryData([
				"tasks", organizationId,
			]);

			// Optimistically remove the task
			queryClient.setQueryData(
				["tasks", "contact", contactId, organizationId],
				(old: Task[] = []) => old.filter((task) => task.id !== taskId),
			);

			queryClient.setQueryData(
				["tasks", organizationId],
				(old: Task[] = []) => old.filter((task) => task.id !== taskId),
			);

			return { previousContactTasks, previousAllTasks };
		},
		onError: (err, taskId, context) => {
			// Roll back on error
			if (context?.previousContactTasks) {
				queryClient.setQueryData(
					["tasks", "contact", contactId, organizationId],
					context.previousContactTasks,
				);
			}
			if (context?.previousAllTasks) {
				queryClient.setQueryData(
					["tasks", organizationId],
					context.previousAllTasks,
				);
			}
		},
		onSettled: () => {
			// Always refetch after error or success
			queryClient.invalidateQueries({
				queryKey: ["tasks", "contact", contactId, organizationId],
				refetchType: "none",
			});
			queryClient.invalidateQueries({
				queryKey: ["tasks", organizationId],
				refetchType: "none",
			});
		},
	});
}

export function useContactTasks(contactId: string | undefined, organizationId: string | undefined) {
	return useQuery<Task[]>({
		queryKey: ["tasks", "contact", contactId, organizationId],
		queryFn: () => 
			contactId && organizationId 
				? fetchContactTasks(contactId, organizationId)
				: Promise.resolve([]),
		enabled: !!contactId && !!organizationId,
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		refetchOnWindowFocus: false,
		refetchOnMount: false,
	});
}

// Helper function to normalize MongoDB ObjectId to string
export function normalizeTaskId(id: unknown): string {
	if (typeof id === 'string') return id;
	
	// Handle MongoDB ObjectId format: { $oid: "..." }
	if (typeof id === 'object' && id !== null && '$oid' in id) {
		return (id as { $oid: string }).$oid;
	}
	
	// Handle MongoDB _id format directly
	if (typeof id === 'object' && id !== null && '_id' in id) {
		const _id = (id as { _id: unknown })._id;
		if (typeof _id === 'object' && _id !== null && '$oid' in _id) {
			return (_id as { $oid: string }).$oid;
		}
		return String(_id);
	}
	
	return String(id);
}

// Helper function to normalize MongoDB date format
function normalizeDate(dateValue: any): Date | null {
	if (!dateValue) return null;
	
	// Handle MongoDB date format: { $date: "2025-07-03T00:49:20.138Z" }
	if (typeof dateValue === 'object' && dateValue !== null && '$date' in dateValue) {
		const parsed = new Date(dateValue.$date);
		return !isNaN(parsed.getTime()) ? parsed : null;
	}
	
	// Handle regular date parsing
	if (typeof dateValue === 'string' || dateValue instanceof Date) {
		const parsed = new Date(dateValue);
		return !isNaN(parsed.getTime()) ? parsed : null;
	}
	
	return null;
}

// Helper function to normalize task data
export function normalizeTask(task: any): Task {
	// Make a copy to avoid mutating the original
	const normalizedTask = { ...task };
	
	// Normalize ID fields
	normalizedTask.id = normalizeTaskId(task.id);
	normalizedTask.createdById = task.createdById ? normalizeTaskId(task.createdById) : null;
	normalizedTask.assigneeId = task.assigneeId ? normalizeTaskId(task.assigneeId) : null;
	normalizedTask.relatedObjectId = task.relatedObjectId ? normalizeTaskId(task.relatedObjectId) : null;
	normalizedTask.organizationId = task.organizationId ? normalizeTaskId(task.organizationId) : null;
	
	// Normalize status to lowercase for consistent filtering
	normalizedTask.status = typeof task.status === "string" ? task.status.toLowerCase() : task.status;
	
	// Normalize date fields
	if (task.createdAt) normalizedTask.createdAt = normalizeDate(task.createdAt) || task.createdAt;
	if (task.updatedAt) normalizedTask.updatedAt = normalizeDate(task.updatedAt) || task.updatedAt;
	if (task.dueDate) normalizedTask.dueDate = normalizeDate(task.dueDate) || task.dueDate;
	
	return normalizedTask as Task;
}

// Quick status update hook specifically for contact tasks with minimal payload
export function useQuickUpdateContactTask(contactId: string | undefined, organizationId: string | undefined) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ taskId, status, priority }: { taskId: string; status?: string; priority?: string }) => {
			const normalizedId = normalizeTaskId(taskId);
			return updateTask({ 
				id: normalizedId, 
				...(status !== undefined && { status: status.toLowerCase() }),
				...(priority !== undefined && { priority: priority.toLowerCase() })
			});
		},
		onMutate: async ({ taskId, status, priority }) => {
			const normalizedId = normalizeTaskId(taskId);
			const normalizedStatus = status?.toLowerCase();
			const normalizedPriority = priority?.toLowerCase();

			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["tasks", "contact", contactId, organizationId],
			});
			await queryClient.cancelQueries({
				queryKey: ["tasks", organizationId],
			});

			// Snapshot the previous values
			const previousContactTasks = queryClient.getQueryData([
				"tasks", "contact", contactId, organizationId,
			]);
			const previousAllTasks = queryClient.getQueryData([
				"tasks", organizationId,
			]);

			// Optimistically update contact tasks
			queryClient.setQueryData(
				["tasks", "contact", contactId, organizationId],
				(old: Task[] = []) => {
					return old.map((task) =>
						normalizeTaskId(task.id) === normalizedId
							? { 
									...task, 
									...(normalizedStatus !== undefined && { status: normalizedStatus }),
									...(normalizedPriority !== undefined && { priority: normalizedPriority }),
									updatedAt: new Date() 
								}
							: task,
					);
				},
			);

			// Also update all tasks cache if it exists
			queryClient.setQueryData(
				["tasks", organizationId],
				(old: Task[] = []) => {
					return old.map((task) =>
						normalizeTaskId(task.id) === normalizedId
							? { 
									...task, 
									...(normalizedStatus !== undefined && { status: normalizedStatus }),
									...(normalizedPriority !== undefined && { priority: normalizedPriority }),
									updatedAt: new Date() 
								}
							: task,
					);
				},
			);

			return { previousContactTasks, previousAllTasks };
		},
		onError: (err, variables, context) => {
			// Roll back on error
			if (context?.previousContactTasks) {
				queryClient.setQueryData(
					["tasks", "contact", contactId, organizationId],
					context.previousContactTasks,
				);
			}
			if (context?.previousAllTasks) {
				queryClient.setQueryData(
					["tasks", organizationId],
					context.previousAllTasks,
				);
			}
		},
		onSettled: () => {
			// Refresh queries to ensure consistency
			queryClient.invalidateQueries({
				queryKey: ["tasks", "contact", contactId, organizationId],
				refetchType: "none",
			});
			queryClient.invalidateQueries({
				queryKey: ["tasks", organizationId],
				refetchType: "none",
			});
		},
	});
}
