import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useColumnPreferences(organizationId?: string) {
	return useQuery({
		queryKey: ["columnPreferences", organizationId],
		queryFn: async () => {
			if (!organizationId) return [];
			const res = await fetch(
				`/api/column-preferences?organizationId=${organizationId}`,
			);
			if (!res.ok) throw new Error("Failed to fetch column preferences");
			return res.json();
		},
		enabled: !!organizationId,
	});
}

export function useColumnPreference(organizationId?: string, column?: string) {
	return useQuery({
		queryKey: ["columnPreference", organizationId, column],
		queryFn: async () => {
			if (!organizationId || !column) return null;
			const params = new URLSearchParams({ organizationId, column });
			const res = await fetch(
				`/api/column-preferences/single?${params.toString()}`,
			);
			if (!res.ok) throw new Error("Failed to fetch column preference");
			return res.json();
		},
		enabled: !!organizationId && !!column,
	});
}

export function useUpsertColumnPreference() {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: async (data: {
			organizationId: string;
			column: string;
			trackTimeInStatus?: boolean;
			showConfetti?: boolean;
			hidden?: boolean;
			targetTimeInStatus?: number | null;
		}) => {
			const res = await fetch("/api/column-preferences", {
				method: "PATCH",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data),
			});
			if (!res.ok) throw new Error("Failed to upsert column preference");
			return res.json();
		},
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["columnPreferences", variables.organizationId],
			});
			queryClient.invalidateQueries({
				queryKey: [
					"columnPreference",
					variables.organizationId,
					variables.column,
				],
			});
		},
	});
}
