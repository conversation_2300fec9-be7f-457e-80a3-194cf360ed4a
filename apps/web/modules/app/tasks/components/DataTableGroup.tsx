import {
	IconAt,
	IconCalendarTime,
	IconClock,
	IconProgress,
	IconSquareRoundedCheckFilled,
	IconUserCircle,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import React from "react";

type GroupingOption = {
	value: string;
	label: string;
	icon: React.ComponentType<any>;
};

const groupingOptions: GroupingOption[] = [
	{ value: "dueDate", label: "Due date", icon: IconCalendarTime },
	{ value: "assignee", label: "Assignee", icon: IconAt },
	{ value: "createdAt", label: "Created At", icon: IconClock },
	{ value: "createdBy", label: "Created by", icon: IconUserCircle },
	{ value: "status", label: "Status", icon: IconProgress },
];

interface DataTableGroupProps {
	groupBy: string;
	onGroupByChange: (value: string) => void;
	user: any;
}

export function DataTableGroup({
	groupBy,
	onGroupByChange,
	user,
}: DataTableGroupProps) {
	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="outline"
					size="sm"
					className="h-8 gap-2 rounded-lg flex items-center justify-start"
				>
					{(() => {
						const selectedOption = groupingOptions.find(
							(option) => option.value === groupBy,
						);
						return (
							<>
								{selectedOption?.icon && (
									<selectedOption.icon className="size-4 text-muted-foreground" />
								)}
								<span className="text-xs font-mono">
									{selectedOption?.label}
								</span>
							</>
						);
					})()}
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="start" className="w-70">
				{groupingOptions.map((option) => (
					<DropdownMenuItem
						key={option.value}
						onClick={() => onGroupByChange(option.value)}
						className="gap-2"
					>
						<option.icon className="size-4 text-muted-foreground" />
						<span
							className={
								groupBy === option.value ? "font-medium" : ""
							}
						>
							{option.label}
						</span>
						{groupBy === option.value && (
							<span className="ml-auto">
								<IconSquareRoundedCheckFilled className="text-blue-400 size-4" />
							</span>
						)}
					</DropdownMenuItem>
				))}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
