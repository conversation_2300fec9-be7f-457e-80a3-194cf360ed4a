import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	clearTaskPreferences,
	getTaskPreferences,
} from "@app/tasks/lib/preferences";
import type { Table } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import Filters, {
	AnimateChangeInHeight,
	DueDate,
	type Filter,
	FilterOperator,
	FilterType,
	filterViewOptions,
	filterViewToFilterOptions,
} from "@ui/components/filters";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { ListFilter } from "lucide-react";
import React from "react";
import { v4 as uuidv4 } from "uuid";

// import { UserAvatar } from "@app/shared/components/UserAvatar"; // Uncomment and use in the future when Filters supports richer objects

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
	groupBy: string;
	onGroupByChange: (value: string) => void;
	filters: Filter[];
	setFilters: React.Dispatch<React.SetStateAction<Filter[]>>;
}

// Helper to get initials from a name
function getInitials(name: string) {
	return name
		.split(" ")
		.map((n) => n[0])
		.join("")
		.toUpperCase();
}

// Add this mapping at the top, after imports
const filterTypeToColumnId: Record<FilterType, string> = {
	[FilterType.STATUS]: "status",
	[FilterType.ASSIGNEE]: "assignee",
	[FilterType.PRIORITY]: "priority",
	[FilterType.DUE_DATE]: "dueDate",
	[FilterType.CREATED_DATE]: "createdAt",
	[FilterType.UPDATED_DATE]: "updatedAt",
	[FilterType.TAG]: "tags",
};

export function TableToolbar<TData>({
	table,
	groupBy,
	onGroupByChange,
	filters,
	setFilters,
}: DataTableToolbarProps<TData>) {
	const { activeOrganization } = useActiveOrganization();
	const members = activeOrganization?.members || [];

	const memberOptions = [
		{
			id: "no-assignee",
			name: "No assignee",
			image: undefined,
			initials: "NA",
		},
		...members.map((member: any) => ({
			id: member.user.id,
			name: member.user.name,
			image: member.user.image,
			initials: getInitials(member.user.name),
		})),
	];

	const isFiltered = table.getState().columnFilters.length > 0;
	const sorting = table.getState().sorting;
	const [open, setOpen] = React.useState(false);
	const [selectedView, setSelectedView] = React.useState<FilterType | null>(
		null,
	);
	const [commandInput, setCommandInput] = React.useState("");
	const commandInputRef = React.useRef<HTMLInputElement>(null);

	React.useEffect(() => {
		table.setColumnFilters(
			filters.map((filter) => ({
				id: filterTypeToColumnId[filter.type],
				value: filter.value,
			})),
		);
	}, [filters, table]);

	const handleReset = React.useCallback(() => {
		table.resetColumnFilters();
		table.resetSorting();
		onGroupByChange("status");
		clearTaskPreferences();
	}, [table, onGroupByChange]);

	// const shouldShowReset = React.useMemo(() => {
	// 	const preferences = getTaskPreferences();

	// 	// Check if sorting differs from preferences
	// 	const currentSorting = sorting[0];
	// 	const sortingDiffers =
	// 		(currentSorting &&
	// 			(currentSorting.id !== preferences.sortBy ||
	// 				currentSorting.desc !==
	// 					(preferences.sortOrder === "desc"))) ||
	// 		(!currentSorting && preferences.sortBy);

	// 	// Check if grouping differs from preferences
	// 	const groupingDiffers = groupBy !== preferences.groupBy;

	// 	// Check if filters differ from preferences
	// 	let filtersDiffer = false;
	// 	let missingPrefFilter = false;
	// 	if (preferences.filters) {
	// 		filtersDiffer = table.getState().columnFilters.some((filter) => {
	// 			const prefValue =
	// 				preferences.filters?.[
	// 					filter.id as keyof typeof preferences.filters
	// 				];
	// 			if (Array.isArray(prefValue)) {
	// 				return (
	// 					JSON.stringify(filter.value) !==
	// 					JSON.stringify(prefValue)
	// 				);
	// 			}
	// 			return filter.value !== prefValue;
	// 		});

	// 		// Also check if there are filters in preferences that are not present in current filters
	// 		const currentFilterIds = table
	// 			.getState()
	// 			.columnFilters.map((f) => f.id);
	// 		missingPrefFilter = Object.keys(preferences.filters).some((key) => {
	// 			const prefValue =
	// 				preferences.filters?.[
	// 					key as keyof typeof preferences.filters
	// 				];
	// 			if (Array.isArray(prefValue)) {
	// 				return (
	// 					prefValue.length > 0 && !currentFilterIds.includes(key)
	// 				);
	// 			}
	// 			return prefValue && !currentFilterIds.includes(key);
	// 		});
	// 	}

	// 	return (
	// 		sortingDiffers ||
	// 		groupingDiffers ||
	// 		filtersDiffer ||
	// 		missingPrefFilter
	// 	);
	// }, [sorting, groupBy, table]);

	const filterOptions = {
		title: [],
		description: [],
		status: Array.from(
			table.getColumn("status")?.getFacetedUniqueValues()?.keys() ?? [],
		),
		priority: Array.from(
			table.getColumn("priority")?.getFacetedUniqueValues().keys() ?? [],
		),
		dueDate: Array.from(
			table.getColumn("dueDate")?.getFacetedUniqueValues().keys() ?? [],
		),
		assignee: memberOptions,
		createdBy: memberOptions,
		...(table.getAllColumns().some((col) => col.id === "createdAt") && {
			createdAt: Array.from(
				table.getColumn("createdAt")?.getFacetedUniqueValues().keys() ??
					[],
			),
		}),
		...(table.getAllColumns().some((col) => col.id === "updatedAt") && {
			updatedAt: Array.from(
				table.getColumn("updatedAt")?.getFacetedUniqueValues().keys() ??
					[],
			),
		}),
	};

	return (
		<div className="flex items-center justify-between">
			<div className="flex flex-1 items-center">
				<Input
					placeholder="Filter tasks..."
					value={
						(table
							.getColumn("title")
							?.getFilterValue() as string) ?? ""
					}
					onChange={(event) =>
						table
							.getColumn("title")
							?.setFilterValue(event.target.value)
					}
					className="h-8 w-[150px] lg:w-[250px] text-xs"
				/>
				{/* <Separator
					orientation="vertical"
					className="mx-4 data-[orientation=vertical]:h-4"
				/> */}
				<div
					className={cn(
						"flex flex-1 items-center",
						filters.length > 0 && "space-x-2",
					)}
				>
					{/* <Filters
						filters={filters}
						setFilters={setFilters}
						filterOptions={filterOptions}
					/> */}
					{/* {filters.filter((filter) => filter.value?.length > 0)
						.length > 0 && (
						<Button
							variant="ghost"
							size="sm"
							className="transition group h-6 text-xs items-center rounded-sm"
							onClick={() => setFilters([])}
						>
							Clear
						</Button>
					)} */}
					{/* <Popover
						open={open}
						onOpenChange={(open) => {
							setOpen(open);
							if (!open) {
								setTimeout(() => {
									setSelectedView(null);
									setCommandInput("");
								}, 200);
							}
						}}
					>
						<PopoverTrigger asChild>
							<Button
								size="sm"
								className={cn(
									"transition group text-xs rounded-sm flex gap-1.5 items-center border !border-border !bg-muted/50 hover:!border hover:!border-border hover:!bg-muted/70",
									filters.length > 0 && "w-4",
								)}
							>
								<ListFilter className="!mr-0 size-4 shrink-0 transition-all text-muted-foreground group-hover:text-primary" />
								{filters.length === 0 && "Filter"}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-[200px] p-0">
							<AnimateChangeInHeight>
								<Command>
									<CommandInput
										placeholder={
											selectedView
												? selectedView
												: "Filter..."
										}
										className="h-9"
										value={commandInput}
										onInputCapture={(e) => {
											setCommandInput(
												e.currentTarget.value,
											);
										}}
										ref={commandInputRef}
									/>
									<CommandList>
										<CommandEmpty>
											No results found.
										</CommandEmpty>
										{selectedView ? (
											<CommandGroup>
												{filterViewToFilterOptions[
													selectedView
												].map((filter) => (
													<CommandItem
														className="group text-muted-foreground flex gap-2 items-center"
														key={filter.name}
														value={filter.name}
														onSelect={(
															currentValue,
														) => {
															setFilters(
																(prev) => [
																	...prev,
																	{
																		id: uuidv4(),
																		type: selectedView,
																		operator:
																			(selectedView as string) ===
																				"due_date" &&
																			currentValue !==
																				DueDate.IN_THE_PAST
																				? FilterOperator.BEFORE
																				: FilterOperator.IS,
																		value: [
																			currentValue,
																		],
																	},
																],
															);
															setTimeout(() => {
																setSelectedView(
																	null,
																);
																setCommandInput(
																	"",
																);
															}, 200);
															setOpen(false);
														}}
													>
														{filter.icon}
														<span className="text-accent-foreground">
															{filter.name}
														</span>
														{filter.label && (
															<span className="text-muted-foreground text-xs ml-auto">
																{filter.label}
															</span>
														)}
													</CommandItem>
												))}
											</CommandGroup>
										) : (
											filterViewOptions.map(
												(group, index) => (
													<React.Fragment key={index}>
														<CommandGroup>
															{group.map(
																(filter) => (
																	<CommandItem
																		className="group text-muted-foreground flex gap-2 items-center"
																		key={
																			filter.name
																		}
																		value={
																			filter.name
																		}
																		onSelect={(
																			currentValue,
																		) => {
																			setSelectedView(
																				currentValue as FilterType,
																			);
																			setCommandInput(
																				"",
																			);
																			commandInputRef.current?.focus();
																		}}
																	>
																		{
																			filter.icon
																		}
																		<span className="text-accent-foreground">
																			{
																				filter.name
																			}
																		</span>
																	</CommandItem>
																),
															)}
														</CommandGroup>
														{index <
															filterViewOptions.length -
																1 && (
															<CommandSeparator />
														)}
													</React.Fragment>
												),
											)
										)}
									</CommandList>
								</Command>
							</AnimateChangeInHeight>
						</PopoverContent>
					</Popover> */}
					{/* TODO: Add reset button */}
					{/* {shouldShowReset && (
						<Button
							variant="ghost"
							onClick={handleReset}
							className="h-7 text-xs border !border-transparent !bg-transparent hover:!border hover:!border-border hover:!bg-muted/50"
						>
							Reset
						</Button>
					)} */}
				</div>
			</div>
		</div>
	);
}
