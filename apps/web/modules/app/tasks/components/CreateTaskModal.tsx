"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	TASK_PRIORITY,
	TASK_STATUS,
	type TaskPriority,
	type TaskStatus,
} from "@app/shared/lib/constants";
import { RelatedObjectSelector } from "@app/shared/components/RelatedObjectSelector";
import { createTask, updateTask } from "@app/tasks/lib/api";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconAt,
	IconCalendar,
	IconSquareRoundedCheck,
} from "@tabler/icons-react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import { DatePicker } from "@ui/components/date-picker";
import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import { FormField, FormItem } from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";


interface TaskToEdit {
	id: any;
	title: string;
	description?: string | null;
	status: TaskStatus;
	priority: TaskPriority;
	dueDate?: Date | string | number | null;
	assignee?: { id: any; name: string } | null;
	related?: { 
		id: string; 
		name: string; 
		recordType: "contact" | "company" | "property";
	} | null;
}

interface CreateTaskModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	taskToEdit?: TaskToEdit;
	defaultStatus?: TaskStatus;
	relatedObject?: {
		id: string;
		name: string;
		recordType: "contact" | "company" | "property";
	};
}

// --- Helpers ---
function getIdString(id: any): string | undefined {
	if (!id) return undefined;
	if (typeof id === "string") return id;
	if (typeof id === "object" && "$oid" in id) return id.$oid;
	if (typeof id === "object" && "id" in id) return id.id;
	return undefined;
}



const taskSchema = z.object({
	title: z.string().min(1, "Title is required"),
	description: z.string().optional(),
	dueDate: z.date().optional(),
	status: z
		.enum(["backlog", "todo", "in_progress", "review", "done"])
		.default("todo"),
	priority: z
		.enum(["no_priority", "urgent", "high", "medium", "low"])
		.default("no_priority"),
	assignee: z
		.object({
			id: z.custom<any>(),
			name: z.string(),
		})
		.optional()
		.nullable(),
	related: z
		.object({
			id: z.string(),
			name: z.string(),
			recordType: z.enum(["contact", "company", "property"]),
		})
		.optional()
		.nullable(),
});

type TaskFormData = z.infer<typeof taskSchema>;

const defaultFormValues: TaskFormData = {
	title: "",
	description: "",
	status: "todo",
	priority: "no_priority",
	assignee: null,
	related: null,
};

// --- Subcomponents ---
function AssigneeButton({ assignee, user }: { assignee: any; user: any }) {
	const assigneeId = getIdString(assignee);
	const userId = getIdString(user?.id);

	if (assigneeId && userId && assigneeId === userId) {
		return (
			<div className="flex items-center gap-2 text-muted-foreground">
				<IconAt className="h-4 w-4" />
				<span>Assigned to you</span>
			</div>
		);
	}
	if (assignee?.name) {
		return (
			<div className="flex items-center gap-2">
				<IconAt className="h-4 w-4" />
				<span>Assigned to {assignee.name}</span>
			</div>
		);
	}
	return (
		<div className="flex items-center gap-2 text-muted-foreground">
			<IconAt className="h-4 w-4" />
			Unassigned
		</div>
	);
}



export function CreateTaskModal({
	open,
	onOpenChange,
	taskToEdit,
	defaultStatus,
	relatedObject,
}: CreateTaskModalProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	const [isSubmitting, setIsSubmitting] = useState(false);
	const [assigneePopoverOpen, setAssigneePopoverOpen] = useState(false);
	const [statusOpen, setStatusOpen] = useState(false);
	const [priorityOpen, setPriorityOpen] = useState(false);
	const [searchTermUsers, setSearchTermUsers] = useState("");

	const defaultAssignee = user ? { id: user.id, name: user.name } : null;

	const form = useForm<TaskFormData>({
		resolver: zodResolver(taskSchema),
		defaultValues: { ...defaultFormValues, assignee: defaultAssignee },
	});

	React.useEffect(() => {
		if (!open) {
			form.reset({ ...defaultFormValues, assignee: defaultAssignee });
			setSearchTermUsers("");
			return;
		}

		if (taskToEdit) {
			let dueDate: Date | undefined;
			if (taskToEdit.dueDate) {
				const parsed = typeof taskToEdit.dueDate === 'object' && '$date' in (taskToEdit.dueDate as any)
					? new Date((taskToEdit.dueDate as any).$date)
					: typeof taskToEdit.dueDate === "string" || typeof taskToEdit.dueDate === "number"
						? new Date(taskToEdit.dueDate)
						: taskToEdit.dueDate;
				dueDate = parsed instanceof Date && !Number.isNaN(parsed.getTime())
					? parsed
					: undefined;
			}

			// Handle both the formatted TaskToEdit object and raw Task objects
			const rawTask = taskToEdit as any;
			
			form.reset({
				title: taskToEdit.title,
				description: taskToEdit.description || "",
				status: taskToEdit.status,
				priority: taskToEdit.priority,
				dueDate,
				assignee: taskToEdit.assignee || (rawTask.assignee ? {
					id: rawTask.assignee.id,
					name: rawTask.assignee.name,
				} : null),
				related: (() => {
					// Handle direct related object from taskToEdit
					if (taskToEdit.related) {
						return taskToEdit.related;
					}
					
					// Handle raw task data conversion
					if (rawTask.relatedObjectId && rawTask.relatedObjectType) {
						const recordType = rawTask.relatedObjectType as "contact" | "company" | "property";
						if (["contact", "company", "property"].includes(recordType)) {
							return {
								id: rawTask.relatedObjectId,
								name: rawTask.relatedObject?.name || "Unnamed Object",
								recordType,
							};
						}
					}
					
					return null;
				})(),
			});
		} else {
			form.reset({
				...defaultFormValues,
				status: defaultStatus || defaultFormValues.status,
				assignee: defaultAssignee,
				related: relatedObject || null,
			});
		}
	}, [open, taskToEdit, form, defaultStatus, user, relatedObject]);

	// --- Handlers ---
	async function onSubmit(formData: TaskFormData) {
		try {
			setIsSubmitting(true);
			const dueDateValue =
				formData.dueDate &&
				formData.dueDate instanceof Date &&
				!Number.isNaN(formData.dueDate.getTime())
					? formData.dueDate.toISOString()
					: null;
			if (taskToEdit) {
				const taskId = getIdString(taskToEdit.id) || taskToEdit.id;
				const payload = {
					id: taskId,
					...formData,
					dueDate: dueDateValue,
					assigneeId: formData.assignee ? formData.assignee.id : null,
					relatedObjectId: formData.related?.id || null,
					relatedObjectType: formData.related?.recordType || null,
					position: undefined,
					organizationId: activeOrganization?.id ?? "",
					createdById: user?.id ?? "",
				};
				await updateTask(payload);
				queryClient.setQueryData(
					["tasks", activeOrganization?.id],
					(old: any[] = []) =>
						old.map((task) =>
							task.id === payload.id
								? { ...task, ...payload }
								: task,
						),
				);
				if (activeOrganization?.id) {
					await queryClient.invalidateQueries({
						queryKey: ["tasks", activeOrganization.id],
					});
					// Also invalidate contact-specific task queries
					await queryClient.invalidateQueries({
						queryKey: ["tasks", "contact"],
					});
				}
				toast.success("Task updated successfully");
				onOpenChange(false);
			} else {
				if (!activeOrganization?.id)
					throw new Error("No active organization selected");
				const payload = {
					title: formData.title,
					description: formData.description,
					dueDate: dueDateValue,
					status: formData.status,
					priority: formData.priority,
					assigneeId: formData.assignee ? formData.assignee.id : null,
					relatedObjectId: formData.related?.id || null,
					relatedObjectType: formData.related?.recordType || null,
					position: undefined,
					organizationId: activeOrganization.id,
					createdById: user?.id ?? "",
				};

				const optimisticTask = {
					id: `temp-${Date.now()}`,
					...payload,
					createdAt: new Date(),
					updatedAt: new Date(),
					assignee: formData.assignee ? {
						id: formData.assignee.id,
						name: formData.assignee.name,
						image: null,
					} : null,
					createdBy: user ? {
						id: user.id,
						name: user.name,
						image: user.image,
					} : null,
					relatedObject: formData.related ? {
						id: formData.related.id,
						name: formData.related.name,
						type: formData.related.recordType,
					} : null,
				};
				
				queryClient.setQueryData(
					["tasks", activeOrganization.id],
					(old: any[] = []) => [optimisticTask, ...old],
				);
				
				try {
					const result = await createTask(payload);
					queryClient.setQueryData(
						["tasks", activeOrganization.id],
						(old: any[] = []) =>
							old.map((task) =>
								task.id === optimisticTask.id ? result : task,
							),
					);
					// Invalidate with a slight delay to ensure server has committed the task
					// Use refetchType: "none" to avoid immediate refetch race condition
					setTimeout(() => {
						queryClient.invalidateQueries({
							queryKey: ["tasks", activeOrganization.id],
							refetchType: "none", // Don't refetch immediately
						});
						// Also invalidate contact-specific task queries
						queryClient.invalidateQueries({
							queryKey: ["tasks", "contact"],
							refetchType: "none",
						});
					}, 100);
					toast.success("Task created successfully");
					onOpenChange(false);
				} catch (error) {
					queryClient.setQueryData(
						["tasks", activeOrganization.id],
						(old: any[] = []) =>
							old.filter((task) => task.id !== optimisticTask.id),
					);
					throw error;
				}
			}
		} catch (error: any) {
			toast.error(error?.message || "Failed to save task");
		} finally {
			setIsSubmitting(false);
		}
	}

	// --- Derived Data ---
	const members = activeOrganization?.members || [];
	const filteredMembers = members.filter((member) => {
		if (!searchTermUsers) return true;
		const name = member.user?.name || "";
		return name.toLowerCase().includes(searchTermUsers.toLowerCase());
	});

	// --- Render ---
	return (
		<StandardizedModal
			open={open}
			onOpenChange={onOpenChange}
			title={taskToEdit ? "Edit Task" : "Create Task"}
			description={taskToEdit ? "Edit an existing task" : "Create a new task"}
			icon={<IconSquareRoundedCheck className="h-5 w-5" />}
			footer={
				<StandardizedModalFooter>
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={() => onOpenChange(false)}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						type="submit"
						variant="primary"
						size="sm"
						disabled={isSubmitting}
						onClick={form.handleSubmit(onSubmit)}
					>
						{isSubmitting ? (
							<div className="flex items-center gap-2">
								<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
								<span>
									{taskToEdit
										? "Updating..."
										: "Creating..."}
								</span>
							</div>
						) : taskToEdit ? (
							"Update Task"
						) : (
							"Create Task"
						)}
					</Button>
				</StandardizedModalFooter>
			}
		>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="flex flex-col w-full"
			>
					<div className="flex flex-col px-2 pt-2 pb-4">
						<Input
							{...form.register("title")}
							autoFocus
							className="shadow-none selection:bg-transparent dark:bg-transparent bg-transparent file:bg-transparent ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full font-semibold border-none rounded-2xl text-xl md:text-xl"
							placeholder="Title"
						/>
						{form.formState.errors.title && (
							<span className="text-red-500 text-sm mt-1">
								{form.formState.errors.title.message}
							</span>
						)}
						<Textarea
							{...form.register("description")}
							className="shadow-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full border-none rounded-none p-3 bg-transparent dark:bg-transparent text-muted-foreground text-md md:text-md mt-2"
							placeholder="Add a description..."
							style={{ resize: "none" }}
						/>
					</div>
					<div className="flex flex-row items-center px-4 pb-2 gap-x-2">
						{/* Status */}
						<Popover open={statusOpen} onOpenChange={setStatusOpen}>
							<PopoverTrigger asChild>
								<Button
									type="button"
									variant="ghost"
									size="sm"
								>
									<div className="flex items-center gap-2">
										{TASK_STATUS.find(
											(st) =>
												st.value ===
												form.watch("status"),
										)?.icon && (
											<div
												className={cn(
													"h-4 w-4",
													TASK_STATUS.find(
														(st) =>
															st.value ===
															form.watch(
																"status",
															),
													)?.color,
												)}
											>
												{React.createElement(
													TASK_STATUS.find(
														(st) =>
															st.value ===
															form.watch(
																"status",
															),
													)!.icon,
													{
														className: cn(
															"h-4 w-4",
															TASK_STATUS.find(
																(st) =>
																	st.value ===
																	form.watch(
																		"status",
																	),
															)?.color,
														),
													},
												)}
											</div>
										)}
										<span>
											{TASK_STATUS.find(
												(st) =>
													st.value ===
													form.watch("status"),
											)?.label || "Status"}
										</span>
									</div>
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-fit p-0">
								<Command>
									<CommandInput placeholder="Search status..." />
									<CommandList>
										<CommandEmpty>
											No status found.
										</CommandEmpty>
										<CommandGroup>
											{TASK_STATUS.map((st) => (
												<CommandItem
													key={st.value}
													value={st.value}
													onSelect={() => {
														form.setValue(
															"status",
															st.value as TaskFormData["status"],
														);
														setStatusOpen(false);
													}}
												>
													{React.createElement(
														st.icon,
														{
															className: cn(
																"h-4 w-4 mr-2",
																st.color,
															),
														},
													)}
													{st.label}
												</CommandItem>
											))}
										</CommandGroup>
									</CommandList>
								</Command>
							</PopoverContent>
						</Popover>
						{/* Priority */}
						<Popover
							open={priorityOpen}
							onOpenChange={setPriorityOpen}
						>
							<PopoverTrigger asChild>
								<Button
									type="button"
									variant="ghost"
									size="sm"
								>
									<div className="flex items-center gap-2">
										{TASK_PRIORITY.find(
											(pr) =>
												pr.value ===
												form.watch("priority"),
										)?.icon &&
											React.createElement(
												TASK_PRIORITY.find(
													(pr) =>
														pr.value ===
														form.watch("priority"),
												)!.icon,
												{
													className: cn(
														"h-4 w-4",
														TASK_PRIORITY.find(
															(pr) =>
																pr.value ===
																form.watch(
																	"priority",
																),
														)?.color,
													),
												},
											)}
										<span
											className={cn(
												form.watch("priority") ===
													"no_priority" &&
													"text-muted-foreground",
											)}
										>
											{TASK_PRIORITY.find(
												(pr) =>
													pr.value ===
													form.watch("priority"),
											)?.label || "Priority"}
										</span>
									</div>
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-fit p-0">
								<Command>
									<CommandInput placeholder="Search priority..." />
									<CommandList>
										<CommandEmpty>
											No priority found.
										</CommandEmpty>
										<CommandGroup>
											{TASK_PRIORITY.map((pr) => (
												<CommandItem
													key={pr.value}
													value={pr.value}
													onSelect={() => {
														form.setValue(
															"priority",
															pr.value as TaskFormData["priority"],
														);
														setPriorityOpen(false);
													}}
												>
													{React.createElement(
														pr.icon,
														{
															className: cn(
																"h-4 w-4 mr-2",
																pr.color,
															),
														},
													)}
													{pr.label}
												</CommandItem>
											))}
										</CommandGroup>
									</CommandList>
								</Command>
							</PopoverContent>
						</Popover>
						{/* Due Date */}
						<FormField
							control={form.control}
							name="dueDate"
							render={({ field }) => (
								<FormItem>
									<Popover>
										<PopoverTrigger asChild>
											<Button
												type="button"
												variant="ghost"
												size="sm"
											>
												{(() => {
													return null;
												})()}
												{field.value &&
												field.value instanceof Date &&
												!Number.isNaN(
													field.value.getTime(),
												) ? (
													<div className="flex items-center gap-2">
														<IconCalendar className="h-4 w-4" />
														{format(
															field.value,
															"MMM d, yyyy, h:mm a",
														)}
													</div>
												) : (
													<div className="flex items-center gap-2 text-muted-foreground">
														<IconCalendar className="h-4 w-4" />
														Due date
													</div>
												)}
											</Button>
										</PopoverTrigger>
										<PopoverContent
											className="w-[600px] p-0 rounded-2xl"
											align="start"
										>
											<DatePicker
												value={field.value}
												onChange={field.onChange}
											/>
										</PopoverContent>
									</Popover>
								</FormItem>
							)}
						/>
						{/* Assignee */}
						<Popover
							open={assigneePopoverOpen}
							onOpenChange={setAssigneePopoverOpen}
						>
							<PopoverTrigger asChild>
								<Button
									type="button"
									variant="ghost"
									size="sm"
								>
									<AssigneeButton
										assignee={form.watch("assignee")}
										user={user}
									/>
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-fit p-0">
								<Command>
									<CommandInput
										placeholder="Search users..."
										value={searchTermUsers}
										onValueChange={setSearchTermUsers}
									/>
									<CommandList>
										<CommandEmpty>
											No users found.
										</CommandEmpty>
										<CommandGroup>
											<CommandItem
												value="unassign"
												onSelect={() => {
													form.setValue(
														"assignee",
														null,
													);
													setAssigneePopoverOpen(
														false,
													);
												}}
											>
												<IconAt className="h-4 w-4 mr-2" />
												Unassign
											</CommandItem>
											<CommandSeparator className="my-1" />
											{filteredMembers.map((member) => (
												<CommandItem
													key={member.id}
													value={member.id}
													onSelect={() => {
														form.setValue(
															"assignee",
															{
																id: member.id,
																name:
																	member.user
																		?.name ||
																	"Unnamed User",
															},
														);
														setAssigneePopoverOpen(
															false,
														);
													}}
												>
													<UserAvatar
														name={
															member.user?.name ||
															"Unnamed User"
														}
														avatarUrl={
															member.user?.image
														}
														className="size-5"
													/>
													<span className="ml-2">
														{member.user?.name ||
															"Unnamed User"}
													</span>
												</CommandItem>
											))}
										</CommandGroup>
									</CommandList>
								</Command>
							</PopoverContent>
						</Popover>
						{/* Related Object */}
						<RelatedObjectSelector
							value={(() => {
								const related = form.watch("related");
								if (!related?.id || !related?.name || !related?.recordType) return null;
								return {
									id: related.id,
									name: related.name,
									recordType: related.recordType as "contact" | "company" | "property",
								};
							})()}
							onValueChange={(relatedObject) => {
								form.setValue("related", relatedObject ? {
									id: relatedObject.id,
									name: relatedObject.name,
									recordType: relatedObject.recordType,
								} : null);
							}}
							displayMode="full"
							size="lg"
							className="!text-xs"
						/>
					</div>
				</form>
		</StandardizedModal>
	);
}
