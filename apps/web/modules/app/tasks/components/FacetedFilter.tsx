"use client";

import {
	IconAntennaBars5,
	IconProgress,
	IconSquareRoundedCheckFilled,
} from "@tabler/icons-react";
import type { Column } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { CheckIcon, PlusCircle } from "lucide-react";
import type { ComponentType } from "react";
import * as React from "react";

interface DataTableFacetedFilterProps<TData, TValue> {
	column?: Column<TData, TValue>;
	title?: string;
	options: ReadonlyArray<{
		label: string;
		value: string;
		icon?: ComponentType<{ className?: string }>;
		color?: string;
	}>;
}

const getFilterIcon = (title?: string) => {
	switch (title) {
		case "Status":
			return IconProgress;
		case "Priority":
			return IconAntennaBars5;
		default:
			return PlusCircle;
	}
};

export function DataTableFacetedFilter<TData, TValue>({
	column,
	title,
	options,
}: DataTableFacetedFilterProps<TData, TValue>) {
	const facets = column?.getFacetedUniqueValues();
	const selectedValues = new Set(column?.getFilterValue() as string[]);
	const FilterIcon = getFilterIcon(title);

	const getFacetCount = (value: string) => {
		if (!facets) return undefined;
		const count = facets.get(value);
		return typeof count === "number" ? count : undefined;
	};

	const handleSelect = (optionValue: string) => {
		const updatedSelectedValues = new Set(selectedValues);
		if (updatedSelectedValues.has(optionValue)) {
			updatedSelectedValues.delete(optionValue);
		} else {
			updatedSelectedValues.add(optionValue);
		}

		const filterValues = Array.from(updatedSelectedValues);
		column?.setFilterValue(filterValues.length ? filterValues : undefined);
	};

	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					size="sm"
					className="h-8 border-dashed rounded-lg"
				>
					<FilterIcon className="h-4 w-4" />
					{title}
					{selectedValues?.size > 0 && (
						<>
							<div className="hidden space-x-1 lg:flex">
								{selectedValues.size > 2 ? (
									<span className="ml-1 text-xs text-muted-foreground font-mono">
										{selectedValues.size} selected
									</span>
								) : (
									options
										.filter((option) =>
											selectedValues.has(option.value),
										)
										.map((option) => (
											<span
												key={option.value}
												className="ml-1 text-xs text-muted-foreground font-mono"
											>
												{option.label}
											</span>
										))
								)}
							</div>
						</>
					)}
				</Button>
			</PopoverTrigger>
			<PopoverContent
				className="w-[200px] p-0 bg-zinc-100 dark:bg-sidebar"
				align="start"
			>
				<Command className="bg-zinc-100 dark:bg-sidebar">
					<CommandInput placeholder={title} />
					<CommandList className="!h-fit">
						<CommandEmpty>No results found.</CommandEmpty>
						<CommandGroup>
							{options.map((option) => {
								const isSelected = selectedValues.has(
									option.value,
								);
								return (
									<CommandItem
										key={option.value}
										onSelect={() =>
											handleSelect(option.value)
										}
									>
										{option.icon && (
											<option.icon
												className={cn(
													"mr-2 h-4 w-4 text-muted-foreground",
													option.color,
												)}
											/>
										)}
										<span>{option.label}</span>
										<div
											className={cn(
												"ml-auto flex h-4 w-4 items-center justify-center gap-2",
											)}
										>
											{getFacetCount(option.value) !==
												undefined && (
												<span className="flex h-4 w-4 items-center justify-center font-mono text-xs">
													{getFacetCount(
														option.value,
													)}
												</span>
											)}
											{isSelected && (
												<IconSquareRoundedCheckFilled
													className={cn(
														"size-4 text-blue-400",
													)}
												/>
											)}
										</div>
									</CommandItem>
								);
							})}
						</CommandGroup>
						{selectedValues.size > 0 && (
							<>
								<CommandSeparator />
								<CommandGroup>
									<CommandItem
										onSelect={() =>
											column?.setFilterValue(undefined)
										}
										className="justify-center text-center"
									>
										Clear filters
									</CommandItem>
								</CommandGroup>
							</>
						)}
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
