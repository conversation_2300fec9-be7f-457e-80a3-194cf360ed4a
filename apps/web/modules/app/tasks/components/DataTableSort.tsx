import {
	IconAntennaBars5,
	IconArrowsSort,
	IconAt,
	IconCalendar,
	IconCalendarTime,
	IconClock,
	IconId,
	IconLayoutColumns,
	IconLayoutList,
	IconLetterTSmall,
	IconProgress,
	IconQuestionMark,
	IconSquareRoundedCheckFilled,
	IconTableOptions,
} from "@tabler/icons-react";
import type { Table } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import React from "react";

const sortOptions = [
	{ value: "title", label: "Title", icon: IconLetterTSmall },
	{ value: "status", label: "Status", icon: IconProgress },
	{ value: "priority", label: "Priority", icon: IconAntennaBars5 },
	{ value: "dueDate", label: "Date Due", icon: IconCalendarTime },
	{ value: "related", label: "Related", icon: IconId },
	{ value: "assignee", label: "Assigned to", icon: IconAt },
	{ value: "createdAt", label: "Created", icon: IconClock },
];

interface DataTableSortProps<TData> {
	table: Table<TData>;
}

export function DataTableSort<TData>({ table }: DataTableSortProps<TData>) {
	const [sortBy, setSortBy] = React.useState<string | undefined>();
	const [sortOrder, setSortOrder] = React.useState<"asc" | "desc">("asc");

	// Update local state when table sorting changes
	const sortingState = table.getState().sorting[0];
	React.useEffect(() => {
		if (sortingState) {
			setSortBy(sortingState.id);
			setSortOrder(sortingState.desc ? "desc" : "asc");
		} else {
			setSortBy(undefined);
			setSortOrder("asc");
		}
	}, [sortingState]);

	const handleSort = (value: string) => {
		const column = table.getColumn(value);
		if (!column) {
			console.warn(`Column with id '${value}' does not exist.`);
			return;
		}

		if (sortBy === value) {
			// If clicking the same column, toggle the sort order
			const newOrder = sortOrder === "asc" ? "desc" : "asc";
			column.toggleSorting(newOrder === "desc");
			setSortOrder(newOrder);
		} else {
			// If clicking a different column, use the current sort order
			column.toggleSorting(sortOrder === "desc");
			setSortBy(value);
		}
	};

	const currentSortOption = sortOptions.find((opt) => opt.value === sortBy);

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="outline"
					size="sm"
					className="h-8 gap-2 rounded-lg flex items-center justify-start"
				>
					{currentSortOption ? (
						<>
							{currentSortOption.icon && (
								<currentSortOption.icon className="h-4 w-4 text-muted-foreground" />
							)}
							<span className="text-xs font-mono">
								{currentSortOption.label}
							</span>
							<span className="ml-1 text-xs bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
								{sortOrder === "asc" ? "asc" : "desc"}
							</span>
						</>
					) : (
						"Sort"
					)}
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="start" className="w-70 space-y-0.5">
				{sortOptions.map((option) => {
					const Icon = option.icon;
					const isActive = sortBy === option.value;
					return (
						<DropdownMenuItem
							key={option.value}
							onClick={() => handleSort(option.value)}
							className="flex items-center gap-2"
						>
							{Icon && (
								<Icon className="h-4 w-4 text-muted-foreground" />
							)}
							<span>{option.label}</span>
							{isActive && (
								<span className="ml-auto">
									<IconSquareRoundedCheckFilled className="text-blue-400 size-4" />
								</span>
							)}
						</DropdownMenuItem>
					);
				})}
				<DropdownMenuSeparator />
				<DropdownMenuItem
					onClick={() => {
						if (!sortBy) return; // Don't do anything if no column is selected
						const column = table.getColumn(sortBy);
						if (column) {
							column.toggleSorting(false);
							setSortOrder("asc");
						}
					}}
					className={cn(
						"flex items-center gap-2 text-muted-foreground",
						sortOrder === "asc" && "bg-accent",
						!sortBy && "opacity-50 cursor-not-allowed", // Disable if no column selected
					)}
				>
					<IconArrowsSort className="size-4 rotate-0" />
					<span className="text-primary">Ascending</span>
					{sortOrder === "asc" && sortBy && (
						<span className="ml-auto">
							<IconSquareRoundedCheckFilled className="text-blue-400 size-4" />
						</span>
					)}
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => {
						if (!sortBy) return; // Don't do anything if no column is selected
						const column = table.getColumn(sortBy);
						if (column) {
							column.toggleSorting(true);
							setSortOrder("desc");
						}
					}}
					className={cn(
						"flex items-center gap-2 text-muted-foreground",
						sortOrder === "desc" && "bg-accent",
						!sortBy && "opacity-50 cursor-not-allowed", // Disable if no column selected
					)}
				>
					<IconArrowsSort className="size-4 rotate-180" />
					<span className="text-primary">Descending</span>
					{sortOrder === "desc" && sortBy && (
						<span className="ml-auto">
							<IconSquareRoundedCheckFilled className="text-blue-400 size-4" />
						</span>
					)}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
