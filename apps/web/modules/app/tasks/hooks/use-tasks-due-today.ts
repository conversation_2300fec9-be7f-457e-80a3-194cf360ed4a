import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useQuery } from "@tanstack/react-query";
import { fetchTasks } from "../lib/api";
import { startOfToday, endOfToday, isWithinInterval } from "date-fns";

export function useTasksDueToday() {
  const { activeOrganization } = useActiveOrganization();

  return useQuery({
    queryKey: ["tasks", activeOrganization?.id, "due-today"],
    queryFn: async () => {
      if (!activeOrganization?.id) return [];
      const tasks = await fetchTasks(activeOrganization.id);
      
      const today = startOfToday();
      const todayEnd = endOfToday();

      return tasks.filter(task => {
        if (!task.dueDate) return false;
        const dueDate = new Date(task.dueDate);
        return isWithinInterval(dueDate, { start: today, end: todayEnd });
      });
    },
    enabled: !!activeOrganization?.id,
  });
} 