import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// INTERFACES
export interface CustomFieldDefinition {
	id: string;
	name: string;
	label: string;
	type: string;
	objectType: string;
	organizationId: string;
	options?: {
		choices?: Array<{
			label: string;
			value: string;
			color?: string;
			trackTime?: boolean;
			showConfetti?: boolean;
			targetTime?: number;
			targetTimeUnit?: string;
		}>;
	};
	isRequired: boolean;
	position: number;
	isSystem: boolean;
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
}

export interface CreateCustomFieldDefinitionPayload {
	name: string;
	label: string;
	type: string;
	objectType: string;
	organizationId: string;
	options?: {
		choices?: Array<{
			label: string;
			value: string;
			color?: string;
			trackTime?: boolean;
			showConfetti?: boolean;
			targetTime?: number;
			targetTimeUnit?: string;
		}>;
	};
	isRequired?: boolean;
	position?: number;
}

export interface UpdateCustomFieldDefinitionPayload {
	name?: string;
	label?: string;
	type?: string;
	options?: {
		choices?: Array<{
			label: string;
			value: string;
			color?: string;
			trackTime?: boolean;
			showConfetti?: boolean;
			targetTime?: number;
			targetTimeUnit?: string;
		}>;
	};
	isRequired?: boolean;
	position?: number;
}

// FETCH CUSTOM FIELD DEFINITIONS FOR AN OBJECT TYPE
export async function fetchCustomFieldDefinitions(
	objectType: string,
	organizationId?: string,
): Promise<CustomFieldDefinition[]> {
	const params = new URLSearchParams();
	if (organizationId) {
		params.append("organizationId", organizationId);
	}

	const queryString = params.toString();
	const url = `/api/custom-field-definitions/${objectType}${queryString ? `?${queryString}` : ""}`;

	const res = await fetch(url, {
		method: "GET",
		credentials: "include",
	});

	if (!res.ok) {
		throw new Error("Failed to fetch custom field definitions");
	}

	return res.json();
}

// FETCH A SPECIFIC CUSTOM FIELD DEFINITION
export async function fetchCustomFieldDefinition(
	id: string,
): Promise<CustomFieldDefinition> {
	const res = await fetch(`/api/custom-field-definitions/definition/${id}`, {
		method: "GET",
		credentials: "include",
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(
			error.error || "Failed to fetch custom field definition",
		);
	}

	return res.json();
}

// CREATE A NEW CUSTOM FIELD DEFINITION
export async function createCustomFieldDefinition(
	payload: CreateCustomFieldDefinitionPayload,
): Promise<CustomFieldDefinition> {
	const res = await fetch("/api/custom-field-definitions", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		credentials: "include",
		body: JSON.stringify(payload),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(
			error.error || "Failed to create custom field definition",
		);
	}

	return res.json();
}

// UPDATE A CUSTOM FIELD DEFINITION
export async function updateCustomFieldDefinition(
	id: string,
	payload: UpdateCustomFieldDefinitionPayload,
): Promise<CustomFieldDefinition> {
	const res = await fetch(`/api/custom-field-definitions/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		credentials: "include",
		body: JSON.stringify(payload),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(
			error.error || "Failed to update custom field definition",
		);
	}

	return res.json();
}

// UPSERT A CUSTOM FIELD DEFINITION (CREATE OR UPDATE)
export async function upsertCustomFieldDefinition(
	payload: CreateCustomFieldDefinitionPayload,
): Promise<CustomFieldDefinition> {
	const res = await fetch("/api/custom-field-definitions/upsert", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		credentials: "include",
		body: JSON.stringify(payload),
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(
			error.error || "Failed to upsert custom field definition",
		);
	}

	return res.json();
}

// DELETE A CUSTOM FIELD DEFINITION
export async function deleteCustomFieldDefinition(id: string): Promise<void> {
	const res = await fetch(`/api/custom-field-definitions/${id}`, {
		method: "DELETE",
		credentials: "include",
	});

	if (!res.ok) {
		const error = await res.json();
		throw new Error(
			error.error || "Failed to delete custom field definition",
		);
	}
}

// REACT QUERY HOOKS

export function useCustomFieldDefinitions(
	objectType: string,
	organizationId?: string,
	enabled = true,
) {
	return useQuery({
		queryKey: ["customFieldDefinitions", objectType, organizationId],
		queryFn: () => fetchCustomFieldDefinitions(objectType, organizationId),
		enabled: !!objectType && enabled,
		staleTime: 1000 * 30, // 30 seconds - more reactive to changes
	});
}

export function useCustomFieldDefinition(id: string | undefined) {
	return useQuery({
		queryKey: ["customFieldDefinition", id],
		queryFn: () => fetchCustomFieldDefinition(id!),
		enabled: !!id,
		staleTime: 1000 * 30, // 30 seconds - more reactive to changes
	});
}

export function useCreateCustomFieldDefinition() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createCustomFieldDefinition,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: [
					"customFieldDefinitions",
					data.objectType,
					data.organizationId,
				],
			});
		},
	});
}

export function useUpdateCustomFieldDefinition() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			id,
			payload,
		}: {
			id: string;
			payload: UpdateCustomFieldDefinitionPayload;
		}) => updateCustomFieldDefinition(id, payload),
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: [
					"customFieldDefinitions",
					data.objectType,
					data.organizationId,
				],
			});
			queryClient.invalidateQueries({
				queryKey: ["customFieldDefinition", data.id],
			});
		},
	});
}

export function useUpsertCustomFieldDefinition() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: upsertCustomFieldDefinition,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: [
					"customFieldDefinitions",
					data.objectType,
					data.organizationId,
				],
			});
			queryClient.invalidateQueries({
				queryKey: ["customFieldDefinition", data.id],
			});
		},
	});
}

export function useDeleteCustomFieldDefinition() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteCustomFieldDefinition,
		onSuccess: (_, id) => {
			// Invalidate all custom field definitions queries since we don't have objectType/organizationId in the response
			queryClient.invalidateQueries({
				queryKey: ["customFieldDefinitions"],
			});
			// Also invalidate the specific definition
			queryClient.invalidateQueries({
				queryKey: ["customFieldDefinition", id],
			});
		},
	});
}

// QUERY KEYS (for consistency)
export const customFieldDefinitionsQueryKey = (
	objectType: string,
	organizationId?: string,
) => ["customFieldDefinitions", objectType, organizationId];

export const customFieldDefinitionQueryKey = (id: string) => [
	"customFieldDefinition",
	id,
];
