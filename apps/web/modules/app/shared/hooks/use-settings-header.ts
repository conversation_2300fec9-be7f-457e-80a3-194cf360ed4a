"use client";

import React, { useEffect, useState } from "react";

interface HeaderState {
	title: string;
	subtitle?: string;
}

// Simple store for the current header
let currentHeader: HeaderState = { title: "Settings", subtitle: undefined };
let listeners: (() => void)[] = [];

export function useSettingsHeader(title: string, subtitle?: string) {
	useEffect(() => {
		currentHeader = { title, subtitle };
		listeners.forEach(listener => listener());
	}, [title, subtitle]);
}

export function useCurrentHeader() {
	const [header, setHeader] = useState<HeaderState>(currentHeader);

	useEffect(() => {
		const listener = () => setHeader({ ...currentHeader });
		listeners.push(listener);
		return () => {
			listeners = listeners.filter(l => l !== listener);
		};
	}, []);

	return header;
} 