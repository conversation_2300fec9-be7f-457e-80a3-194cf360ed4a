import { useSearchParams } from "next/navigation";
import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";

export interface RecordNavigationInfo {
  currentIndex: number;
  totalRecords: number;
  viewId?: string;
  viewName?: string;
  hasPrevious: boolean;
  hasNext: boolean;
  navigateToPrevious: () => void;
  navigateToNext: () => void;
}

interface NavigationResponse {
  previousId: string | null;
  nextId: string | null;
  totalRecords: number;
  currentIndex: number;
}

export function useRecordNavigation(
  objectType: string,
  currentId: string,
  organizationId: string
): RecordNavigationInfo {
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();

  // Get navigation params from URL
  const viewId = searchParams.get("viewId") || undefined;
  const viewName = searchParams.get("viewName") || undefined;
  
  // Try to get navigation info from URL params first (more efficient and accurate)
  const urlIndex = searchParams.get("index");
  const urlTotal = searchParams.get("total");
  
  // If we have URL params with the filtered info, use them directly
  const hasUrlNavigation = urlIndex !== null && urlTotal !== null;
  
  const currentIndex = hasUrlNavigation ? parseInt(urlIndex, 10) : 0;
  const totalRecords = hasUrlNavigation ? parseInt(urlTotal, 10) : 0;

  // Build query parameters including filters (only used as fallback)
  const queryParams = new URLSearchParams({
    objectType,
    currentId,
    organizationId,
    ...(viewId ? { viewId } : {})
  });

  // Add all current filter parameters from URL
  searchParams.forEach((value, key) => {
    // Skip navigation-specific params and system params
    if (!["viewId", "viewName", "index", "total"].includes(key) && value) {
      queryParams.set(key, value);
    }
  });

  // Fetch from API for navigation (to get previous/next record IDs)
  // Even when we have URL params for counts, we still need this for actual navigation
  const { data: navigationData } = useQuery<NavigationResponse>({
    queryKey: ["recordNavigation", objectType, currentId, viewId, organizationId, searchParams.toString()],
    queryFn: async () => {
      const response = await fetch(`/api/navigation/getRecordIds?${queryParams.toString()}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch record navigation data");
      }

      const data: NavigationResponse = await response.json();
      return data;
    },
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });

  // Calculate navigation state - prefer URL params over API data
  const finalCurrentIndex = hasUrlNavigation ? currentIndex : (navigationData?.currentIndex || 0);
  const finalTotalRecords = hasUrlNavigation ? totalRecords : (navigationData?.totalRecords || 0);
  
  const hasPrevious = hasUrlNavigation 
    ? currentIndex > 0 
    : Boolean(navigationData?.previousId);
  const hasNext = hasUrlNavigation 
    ? currentIndex < totalRecords - 1 
    : Boolean(navigationData?.nextId);

  // Navigation functions
  const navigateToPrevious = useCallback(() => {
    if (!hasPrevious) return;

    if (hasUrlNavigation && currentIndex > 0) {
      // For URL-based navigation, we need API data to get actual record IDs
      // Fall back to API navigation for now
      if (!navigationData?.previousId) return;
    }

    if (!navigationData?.previousId) return;

    // Get the organization slug from the URL
    const orgSlug = window.location.pathname.split('/')[2];

    // Build new search params with updated index
    const newParams = new URLSearchParams(searchParams.toString());
    if (hasUrlNavigation && currentIndex > 0) {
      newParams.set('index', (currentIndex - 1).toString());
    }

    // Navigate to the new URL
    const url = `/app/${orgSlug}/${objectType}/${navigationData.previousId}?${newParams.toString()}`;
    router.push(url);
  }, [hasPrevious, hasUrlNavigation, currentIndex, navigationData, searchParams, router, objectType]);

  const navigateToNext = useCallback(() => {
    if (!hasNext) return;

    if (hasUrlNavigation && currentIndex < totalRecords - 1) {
      // For URL-based navigation, we need API data to get actual record IDs
      // Fall back to API navigation for now
      if (!navigationData?.nextId) return;
    }

    if (!navigationData?.nextId) return;

    // Get the organization slug from the URL
    const orgSlug = window.location.pathname.split('/')[2];

    // Build new search params with updated index
    const newParams = new URLSearchParams(searchParams.toString());
    if (hasUrlNavigation && currentIndex < totalRecords - 1) {
      newParams.set('index', (currentIndex + 1).toString());
    }

    // Navigate to the new URL
    const url = `/app/${orgSlug}/${objectType}/${navigationData.nextId}?${newParams.toString()}`;
    router.push(url);
  }, [hasNext, hasUrlNavigation, currentIndex, totalRecords, navigationData, searchParams, router, objectType]);

  return {
    currentIndex: finalCurrentIndex,
    totalRecords: finalTotalRecords,
    viewId,
    viewName,
    hasPrevious,
    hasNext,
    navigateToPrevious,
    navigateToNext
  };
} 