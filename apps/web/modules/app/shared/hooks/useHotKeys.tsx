import { useEffect } from "react";

type HotkeyCallback = (e: KeyboardEvent) => void;

interface HotkeyConfig {
	key: string;
	callback: HotkeyCallback;
	ctrlKey?: boolean;
	altKey?: boolean;
	shiftKey?: boolean;
	metaKey?: boolean;
	// Prevent triggering when typing in input fields
	ignoreInputs?: boolean;
	// Prevent triggering when these modifiers are pressed
	preventModifiers?: {
		ctrlKey?: boolean;
		altKey?: boolean;
		shiftKey?: boolean;
		metaKey?: boolean;
	};
}

// Helper to check if we should ignore the event based on the target element
function shouldIgnoreTarget(target: HTMLElement): boolean {
	// Get the tag name in uppercase for consistent comparison
	const tagName = target.tagName.toUpperCase();
	
	// Check for common input elements
	if (["INPUT", "TEXTAREA", "SELECT"].includes(tagName)) {
		return true;
	}
	
	// Check for contenteditable elements
	if (target.getAttribute("contenteditable") === "true") {
		return true;
	}
	
	// Check for role="textbox" elements
	if (target.getAttribute("role") === "textbox") {
		return true;
	}
	
	// Check if element is within a modal or dialog
	if (target.closest('[role="dialog"]') || target.closest('.modal')) {
		return true;
	}
	
	// Check for rich text editors (common class names and data attributes)
	if (
		target.closest('.ProseMirror') || // TipTap
		target.closest('.ql-editor') || // Quill
		target.closest('[data-slate-editor]') || // Slate
		target.closest('.tox-edit-area') // TinyMCE
	) {
		return true;
	}
	
	return false;
}

export function useHotkeys(configs: HotkeyConfig[]) {
	useEffect(() => {
		const handler = (e: KeyboardEvent) => {
			const target = e.target as HTMLElement;
			
			// First check if we should ignore this target
			if (shouldIgnoreTarget(target)) {
				return;
			}

			for (const config of configs) {
				const keyMatch = e.key.toLowerCase() === config.key.toLowerCase();
				const ctrlMatch = !config.ctrlKey || e.ctrlKey;
				const altMatch = !config.altKey || e.altKey;
				const shiftMatch = !config.shiftKey || e.shiftKey;
				const metaMatch = !config.metaKey || e.metaKey;
				const ignoreInputs = config.ignoreInputs ?? true;

				// Check if any prevented modifiers are pressed
				const preventModifiers = config.preventModifiers ?? {};
				const hasPreventedModifier =
					(preventModifiers.ctrlKey && e.ctrlKey) ||
					(preventModifiers.altKey && e.altKey) ||
					(preventModifiers.shiftKey && e.shiftKey) ||
					(preventModifiers.metaKey && e.metaKey);

				if (
					keyMatch &&
					ctrlMatch &&
					altMatch &&
					shiftMatch &&
					metaMatch &&
					!hasPreventedModifier &&
					(!ignoreInputs || !shouldIgnoreTarget(target))
				) {
					e.preventDefault();
					config.callback(e);
					break;
				}
			}
		};

		window.addEventListener("keydown", handler);
		return () => window.removeEventListener("keydown", handler);
	}, [configs]);
}
