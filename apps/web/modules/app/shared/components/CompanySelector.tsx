"use client";

import {
	IconBuilding,
	IconChevronDown,
	IconPlus,
	IconX,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import * as React from "react";
import { useState } from "react";

interface Company {
	id: string;
	name: string;
	website?: string;
	logo?: string;
	domain?: string;
}

interface CompanySelectorProps {
	value?: Company | null;
	onValueChange: (company: Company | null) => void;
	organizationId: string;
	placeholder?: string;
	className?: string;
	disabled?: boolean;
	autoOpen?: boolean;
	direct?: boolean; // Show command interface directly without popover/button
}

async function searchCompanies(
	query: string,
	organizationId: string,
): Promise<Company[]> {
	if (!query.trim()) return [];

	try {
		const response = await fetch(
			`/api/objects/companies?search=${encodeURIComponent(query)}&organizationId=${organizationId}&limit=10`,
		);

		if (!response.ok) {
			console.warn("Companies search API not available");
			return [];
		}

		const result = await response.json();
		return (result.companies || []).map((company: any) => ({
			id: company.id,
			name: company.name,
			website: company.website,
			logo: company.logo,
			domain: company.website,
		}));
	} catch (error) {
		console.warn("Error searching companies:", error);
		return [];
	}
}

function CompanyLogo({
	company,
	size = "sm",
}: {
	company: Company;
	size?: "sm" | "md";
}) {
	const sizeClasses = size === "md" ? "w-6 h-6" : "w-4 h-4";

	if (company.logo) {
		return (
			<img
				src={company.logo}
				alt={`${company.name} logo`}
				className={cn("rounded object-cover", sizeClasses)}
			/>
		);
	}

	return (
		<IconBuilding className={cn("text-muted-foreground", sizeClasses)} />
	);
}

function CompanyItem({
	company,
	onSelect,
	isSelected = false,
}: {
	company: Company;
	onSelect: () => void;
	isSelected?: boolean;
}) {
	return (
		<CommandItem
			onSelect={onSelect}
			className={cn(
				"flex items-center gap-3 p-3 cursor-pointer",
				isSelected && "bg-accent",
			)}
		>
			<CompanyLogo company={company} />
			<div className="flex flex-col min-w-0 flex-1">
				<span className="font-medium text-foreground truncate">
					{company.name}
				</span>
				{company.domain && (
					<span className="text-xs text-muted-foreground truncate">
						{company.domain}
					</span>
				)}
			</div>
			<div className="flex items-center gap-2">
				<Button
					size="sm"
					variant="ghost"
					className="h-6 w-6 p-0 text-blue-400 hover:text-blue-300"
					onClick={(e) => {
						e.stopPropagation();
						onSelect();
					}}
				>
					<IconBuilding className="h-3 w-3" />
				</Button>
			</div>
		</CommandItem>
	);
}

export function CompanySelector({
	value,
	onValueChange,
	organizationId,
	placeholder = "Find a company...",
	className,
	disabled = false,
	autoOpen = false,
	direct = false,
}: CompanySelectorProps) {
	const [open, setOpen] = useState(autoOpen);
	const [searchTerm, setSearchTerm] = useState("");

	// Auto-open when autoOpen prop is true (only for popover mode)
	React.useEffect(() => {
		if (autoOpen && !direct) {
			setOpen(true);
			// Focus the search input after a brief delay to ensure the popover is rendered
			setTimeout(() => {
				const searchInput = document.querySelector(
					"[cmdk-input]",
				) as HTMLInputElement;
				if (searchInput) {
					searchInput.focus();
				}
			}, 50);
		}
	}, [autoOpen, direct]);

	const { data: companies = [] } = useQuery({
		queryKey: ["companies", "search", searchTerm, organizationId],
		queryFn: () => searchCompanies(searchTerm, organizationId),
		enabled: searchTerm.length >= 2 && !!organizationId && !disabled,
		staleTime: 30000,
	});

	const handleSelect = (company: Company) => {
		onValueChange(company);
		if (!direct) {
			setOpen(false);
		}
		setSearchTerm("");
	};

	const handleRemove = (e: React.MouseEvent) => {
		e.stopPropagation();
		onValueChange(null);
	};

	const handleCreateCompany = () => {
		// TODO: Open create company modal/form
		if (!direct) {
			setOpen(false);
		}
	};

	// Render command interface directly for inline editing
	if (direct) {
		return (
			<div className={cn("w-full", className)}>
				<Command className="border border-border rounded-xl bg-sidebar">
					<CommandInput
						placeholder={placeholder}
						value={searchTerm}
						onValueChange={setSearchTerm}
						className="border-0 focus:ring-0"
						autoFocus
					/>
					<CommandList className="max-h-[400px]">
						<CommandEmpty>
							{searchTerm.length > 0
								? "No companies found."
								: "Type to search companies..."}
						</CommandEmpty>

						{/* Companies */}
						{companies.length > 0 && (
							<>
								<CommandGroup>
									{companies.map((company) => (
										<CompanyItem
											key={company.id}
											company={company}
											onSelect={() =>
												handleSelect(company)
											}
											isSelected={
												value?.id === company.id
											}
										/>
									))}
								</CommandGroup>
								<CommandSeparator />
							</>
						)}

						{/* Create Company Option */}
						<CommandSeparator />
						<CommandGroup>
							<CommandItem
								onSelect={handleCreateCompany}
								className="flex items-center gap-2 p-3 cursor-pointer"
							>
								<IconPlus className="h-4 w-4" />
								<span>Create Company</span>
							</CommandItem>
						</CommandGroup>
					</CommandList>
				</Command>
			</div>
		);
	}

	// Original popover version for non-direct usage
	return (
		<div className={cn("w-full", className)}>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<Button
						variant="outline"
						role="combobox"
						aria-expanded={open}
						disabled={disabled}
						className={cn(
							"w-full justify-between bg-transparent hover:!bg-secondary/10 border-input",
							"hover:bg-accent hover:text-accent-foreground",
							"focus-visible:ring-2 focus-visible:ring-ring",
							value && "border-primary",
						)}
					>
						{value ? (
							<div className="flex items-center gap-2 min-w-0 flex-1">
								<CompanyLogo company={value} />
								<span className="truncate">{value.name}</span>
								<Button
									size="sm"
									variant="ghost"
									className="h-4 w-4 p-0 ml-auto hover:bg-destructive hover:text-destructive-foreground"
									onClick={handleRemove}
								>
									<IconX className="h-3 w-3" />
								</Button>
							</div>
						) : (
							<>
								<span className="text-muted-foreground">
									{placeholder}
								</span>
								<IconChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
							</>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent
					className="w-[var(--radix-popover-trigger-width)] p-0 bg-popover border-border"
					align="start"
				>
					<Command className="!border-0">
						<CommandInput
							placeholder={placeholder}
							value={searchTerm}
							onValueChange={setSearchTerm}
							className="border-0 focus:ring-0"
						/>
						<CommandList className="max-h-[400px]">
							<CommandEmpty>
								{searchTerm.length > 0
									? "No companies found."
									: "Type to search companies..."}
							</CommandEmpty>

							{/* Companies */}
							{companies.length > 0 && (
								<>
									<CommandGroup>
										{companies.map((company) => (
											<CompanyItem
												key={company.id}
												company={company}
												onSelect={() =>
													handleSelect(company)
												}
												isSelected={
													value?.id === company.id
												}
											/>
										))}
									</CommandGroup>
									<CommandSeparator />
								</>
							)}

							{/* Create Company Option */}
							<CommandSeparator />
							<CommandGroup>
								<CommandItem
									onSelect={handleCreateCompany}
									className="flex items-center gap-2 p-3 cursor-pointer"
								>
									<IconPlus className="h-4 w-4" />
									<span>Create Company</span>
								</CommandItem>
							</CommandGroup>
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>
		</div>
	);
}
