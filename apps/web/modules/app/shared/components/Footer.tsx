import { LocaleLink } from "@i18n/routing";
import { cn } from "@ui/lib";

export function Footer() {
	return (
		<footer
			className={cn(
				"container max-w-6xl py-6 text-center text-foreground/60 text-xs space-x-4",
			)}
		>
			<span>
				© {new Date().getFullYear()} Relio. All rights reserved.
			</span>
			<LocaleLink
				className="hover:underline"
				href="/legal/privacy-policy"
			>
				Privacy policy
			</LocaleLink>
			<LocaleLink className="hover:underline" href="/legal/terms">
				Terms and conditions
			</LocaleLink>
		</footer>
	);
}
