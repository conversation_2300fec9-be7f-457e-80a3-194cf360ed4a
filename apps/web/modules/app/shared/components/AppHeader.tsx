"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useDeletePin, usePins, useUpdatePinOrder } from "@app/pins/lib/api";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	horizontalListSortingStrategy,
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { User } from "@repo/database";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { UserAvatar } from "@shared/components/UserAvatar";
import { IconGripVertical, IconX } from "@tabler/icons-react";
import { AnimatedSidebarTrigger } from "@ui/components/animated-sidebar-trigger";
import { Button } from "@ui/components/button";
import { Separator } from "@ui/components/separator";
import { Skeleton } from "@ui/components/skeleton";
import { useSidebar } from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";

interface SortableTabProps {
	pin: {
		id: string;
		name: string;
		image: string;
		objectId: string;
		objectType: string;
	};
	onRemove: () => void;
	onClick: () => void;
}

function SortableTab({ pin, onRemove, onClick }: SortableTabProps) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: pin.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	const pathname = usePathname();
	const isActive = pathname.includes(pin.objectId);

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={cn(
				"relative flex items-center gap-1 px-1 py-1 bg-secondary/30 border border-border rounded-lg text-sm",
				"hover:bg-muted/50 transition-colors cursor-pointer group/tab max-w-48 flex-shrink-0 overflow-hidden",
				isDragging && "opacity-50 shadow-lg z-50",
				isActive && "border-blue-500",
			)}
		>
			<span
				onClick={onClick}
				className={cn(
					"flex-1 min-w-0 flex items-center gap-1 cursor-pointer transition-all duration-150",
					"group-hover/tab:max-w-[calc(100%-1rem)]",
				)}
				title={pin.name}
			>
				<span className="relative w-4 h-4 flex-shrink-0">
					{/* ContactAvatar (visible when not hovered) */}
					<span
						className={cn(
							"absolute inset-0 transition-opacity duration-150 pointer-events-auto",
							"opacity-100 group-hover/tab:opacity-0 group-hover/tab:pointer-events-none",
						)}
					>
						{pin.objectType === "contact" || pin.objectType === "contacts" ? (
						<ContactAvatar
							name={pin.name}
							avatarUrl={pin.image}
							className="w-4 h-4"
						/>
						) : (
							<PropertyAvatar
								name={pin.name}
								avatarUrl={pin.image}
								className="w-4 h-4"
							/>
						)}
					</span>
					{/* Grip icon (visible on hover) */}
					<span
						{...attributes}
						{...listeners}
						className={cn(
							"absolute inset-0 flex items-center justify-center transition-opacity duration-150 cursor-grab active:cursor-grabbing pointer-events-none",
							"opacity-0 group-hover/tab:opacity-100 group-hover/tab:pointer-events-auto",
						)}
					>
						<IconGripVertical className="w-3 h-3" />
					</span>
				</span>
				<span className="text-md overflow-hidden text-ellipsis whitespace-nowrap">
					{pin.name}
				</span>
			</span>

			{/* X button - slide in from right on hover */}
			<div className="absolute top-0 right-0 bottom-0 flex items-center h-full pr-1">
				<Button
					variant="ghost"
					size="sm"
					className={cn(
						"h-5 w-5 p-0 !rounded-sm text-muted-foreground hover:text-destructive hover:bg-destructive/20 transition-all duration-150",
						"translate-x-full opacity-0 group-hover/tab:translate-x-0 group-hover/tab:opacity-100",
					)}
					onClick={(e) => {
						e.stopPropagation();
						onRemove();
					}}
					aria-label="Remove pin"
				>
					<IconX className="w-3 h-3" />
				</Button>
			</div>
		</div>
	);
}

export function AppHeader({
	user,
	organizationId: propOrganizationId,
	organiationSlug,
}: {
	user: User;
	organizationId?: string;
	organiationSlug?: string;
}) {
	const { open, toggleSidebar } = useSidebar();
	const router = useRouter();
	const { activeOrganization, loaded: organizationLoaded } = useActiveOrganization();

	// Use active organization from context if available, fallback to prop
	const organizationId = activeOrganization?.id || propOrganizationId;
	const organizationSlug = activeOrganization?.slug || organiationSlug;

	// Only fetch pins when we have an organization and the context is loaded
	const shouldFetchPins = organizationLoaded && !!organizationId;
	
	// Fetch pins for the current organization
	const { data: pins = [], isLoading, error } = usePins(
		shouldFetchPins ? organizationId : undefined
	);
	const deletePinMutation = useDeletePin(organizationId);
	const updatePinOrderMutation = useUpdatePinOrder(organizationId);

	// Local state for optimistic sorting - use a stable reference
	const [sortedPins, setSortedPins] = useState<typeof pins>([]);

	// Update sorted pins when pins data changes - stable dependency array
	useEffect(() => {
		if (pins.length > 0 || sortedPins.length !== pins.length) {
			setSortedPins([...pins]);
		}
	}, [pins.length]);

	// Drag and drop sensors
	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	function handleDragEnd(event: DragEndEvent) {
		const { active, over } = event;

		if (active.id !== over?.id) {
			setSortedPins((items) => {
				const oldIndex = items.findIndex(
					(item) => item.id === active.id,
				);
				const newIndex = items.findIndex(
					(item) => item.id === over?.id,
				);
				const newOrder = arrayMove(items, oldIndex, newIndex);

				// Persist new order if changed
				const oldIds = items.map((p) => p.id);
				const newIds = newOrder.map((p) => p.id);
				const changed = oldIds.some((id, idx) => id !== newIds[idx]);
				if (changed && organizationId) {
					updatePinOrderMutation.mutate({
						organizationId,
						pinIds: newIds,
					});
				}

				return newOrder;
			});
		}
	}

	function handlePinClick(pin: (typeof pins)[0]) {
		router.push(`/app/${organizationSlug}/${pin.objectType}/${pin.objectId}`);
	}

	function handlePinRemove(pin: (typeof pins)[0]) {
		if (!organizationId) return;

		deletePinMutation.mutate({
			objectId: pin.objectId,
			objectType: pin.objectType,
			organizationId,
		});
	}

	const renderPinsContent = () => {
		// Show loading state when organization context is not loaded yet
		if (!organizationLoaded) {
			return (
				<div className="flex items-center gap-2">
					<Skeleton className="h-4 w-20" />
					<Skeleton className="h-4 w-16" />
				</div>
			);
		}

		// Show loading state when pins are being fetched
		if (isLoading && shouldFetchPins) {
			return (
				<div className="text-sm text-muted-foreground">
					Loading pins...
				</div>
			);
		}

		// Show error state (but filter out the "No active organization" error during login)
		if (error && organizationLoaded && organizationId) {
			return (
				<div className="text-sm text-red-500">
					Error loading pins: {error.message}
				</div>
			);
		}

		// Show pins if we have them
		if (sortedPins.length > 0 && organizationId) {
			return (
				<DndContext
					sensors={sensors}
					collisionDetection={closestCenter}
					onDragEnd={handleDragEnd}
				>
					<SortableContext
						items={sortedPins.map((pin) => pin.id)}
						strategy={horizontalListSortingStrategy}
					>
						<div className="flex items-center gap-2 overflow-x-auto scrollbar-hide">
							{sortedPins.map((pin) => (
								<SortableTab
									key={pin.id}
									pin={pin as any}
									onClick={() => {
										handlePinClick(pin);
									}}
									onRemove={() =>
										handlePinRemove(pin)
									}
								/>
							))}
						</div>
					</SortableContext>
				</DndContext>
			);
		}

		// Show appropriate message based on organization state
		if (!organizationId && organizationLoaded) {
			return (
				<div className="text-sm text-muted-foreground">
					Select an organization to see pinned records
				</div>
			);
		}

		// Default empty state
		return <div />;
	};

	return (
		<header className="flex h-13 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 border-b bg-sidebar">
			<div className="flex items-center gap-2 px-4 flex-1 min-w-0">
				<AnimatedSidebarTrigger
					className="-ml-1"
					expanded={open}
					onClick={toggleSidebar}
					animation="fade"
				/>
				<Separator
					orientation="vertical"
					className="mr-2 data-[orientation=vertical]:h-4"
				/>

				{/* Pinned Records */}
				<div className="flex-1 min-w-0">
					{renderPinsContent()}
				</div>
			</div>
			<div className="flex items-center px-4">
				<UserAvatar name={user.name} avatarUrl={user.image} className="h-6 w-6" />
			</div>
		</header>
	);
}
