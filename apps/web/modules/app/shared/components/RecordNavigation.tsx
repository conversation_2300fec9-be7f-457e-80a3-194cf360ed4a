import { Button } from "@ui/components/button";
import { IconChevronUp, IconChevronDown } from "@tabler/icons-react";
import { useRecordNavigation } from "../hooks/use-record-navigation";
import { cn } from "@ui/lib";
import { useEffect } from "react";

interface RecordNavigationProps {
  objectType: string;
  recordId: string;
  organizationId: string;
  className?: string;
  viewName?: string;
}

export function RecordNavigation({ objectType, recordId, organizationId, className, viewName }: RecordNavigationProps) {
  const {
    currentIndex,
    totalRecords,
    hasPrevious,
    hasNext,
    navigateToPrevious,
    navigateToNext,
  } = useRecordNavigation(objectType, recordId, organizationId);

  // Add keyboard navigation
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      // Ignore if user is typing in an input or textarea
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Ignore if user is holding modifier keys
      if (event.ctrlKey || event.metaKey || event.altKey) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case 'k':
          event.preventDefault();
          if (hasPrevious) {
            navigateToPrevious();
          }
          break;
        case 'j':
          event.preventDefault();
          if (hasNext) {
            navigateToNext();
          }
          break;
      }
    }

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [hasPrevious, hasNext, navigateToPrevious, navigateToNext]);

  if (totalRecords <= 1) return null;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-1">
        <span className="text-sm text-muted-foreground font-mono">
          {currentIndex + 1}
        </span>
        <span className="text-sm text-muted-foreground">
          of
        </span>
        <span className="text-sm text-muted-foreground font-mono">
          {totalRecords}
        </span>
      </div>
      {viewName && (
        <span className="text-sm text-muted-foreground">
          in {viewName}
        </span>
      )}
      <div className="flex items-center gap-1">
        <Button
          variant="relio"
          size="icon"
          disabled={!hasPrevious}
          onClick={navigateToPrevious}
          className={cn(
            "h-8 w-8 relative group transition-colors",
            !hasPrevious && "opacity-50 cursor-not-allowed",
            hasPrevious && "hover:bg-muted/80"
          )}
          tooltip={{
            content: "Previous record",
            shortcut: "K",
            type: "shortcut"
          }}
        >
          <IconChevronUp className="h-5 w-5" />
        </Button>

        <Button
          variant="relio"
          size="icon"
          disabled={!hasNext}
          onClick={navigateToNext}
          className={cn(
            "h-8 w-8 relative group transition-colors",
            !hasNext && "opacity-50 cursor-not-allowed",
            hasNext && "hover:bg-muted/80"
          )}
          tooltip={{
            content: "Next record",
            shortcut: "J",
            type: "shortcut"
          }}
        >
          <IconChevronDown className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
} 