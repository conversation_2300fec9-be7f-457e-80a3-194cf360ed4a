"use client";

import Notifications from "@app/notifications/components/Notifications";
import {
	type Icon,
	IconDashboard,
	IconNote,
	IconSparkles,
	IconSquareRoundedCheck,
} from "@tabler/icons-react";
import {
	SidebarGroup,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo } from "react";
import { useTasksDueToday } from "@app/tasks/hooks/use-tasks-due-today";
import { NumberBadge } from "@ui/components/badge";

export interface NavItem {
	title: string;
	href?: string;
	disabled?: boolean;
	external?: boolean;
	icon?: Icon;
	label?: string;
	description?: string;
	recordType?: string;
	notification?: number;
	items?: NavItem[];
}

export const navItems: NavItem[] = [
	{
		title: "Dashboard",
		href: "/",
		icon: IconDashboard,
		label: "Dashboard",
	},
	{
		title: "Tasks",
		href: "/tasks",
		icon: IconSquareRoundedCheck,
		label: "tasks",
		notification: 0,
	},
	{
		title: "Notes",
		href: "/notes",
		icon: IconNote,
		label: "notes",
	},
	{
		title: "Chat",
		href: "/chat",
		icon: IconSparkles,
		label: "ai",
	},
	// {
	//   title: 'Emails',
	//   href: '/emails',
	//   icon: 'emails',
	//   label: 'emails',
	//   notification: 0
	// }
];

const addSlugToNavItems = (navItems: NavItem[], slug: string): NavItem[] => {
	return navItems.map((item) => ({
		...item,
		href: `/app/${slug}${item.href}`,
	}));
};

export function NavMain({ slug }: { slug: string }) {
	const pathname = usePathname();
	const itemsWithSlug = useMemo(
		() => addSlugToNavItems(navItems, slug),
		[slug],
	);
	const sidebar = useSidebar();
	const isCollapsed = sidebar?.state === "collapsed";
	const { data: tasksDueToday = [] } = useTasksDueToday();

	return (
		<SidebarGroup>
			<SidebarMenu>
				<Notifications />
				{itemsWithSlug.map((item) => {
					const normalize = (path: string) => path.replace(/\/$/, "");
					const isDashboard = item.title === "Dashboard";
					const isActive = isDashboard
						? normalize(pathname) === normalize(item.href as string)
						: pathname.startsWith(item.href as string);

					return (
						<Link key={item.title} href={item.href || "#"}>
							<SidebarMenuItem
								className={cn(
									"w-full",
									isCollapsed && "justify-center",
								)}
							>
								<SidebarMenuButton
									tooltip={item.title}
									className={cn(
										"transition-colors duration-200",
										isActive &&
											"!border !border-border !bg-muted/50",
										"hover:bg-accent hover:text-accent-foreground",
										"w-full flex items-center justify-between",
										isCollapsed &&
											"justify-center p-0 h-9 w-9",
									)}
								>
									<div className={cn("flex items-center gap-2", isCollapsed && "justify-center w-full")}>
										{item.icon && (
											<div className="relative">
												<item.icon
													className={cn(
														"size-4 text-muted-foreground",
														isActive &&
															"text-accent-foreground",
														isCollapsed && "mx-auto",
													)}
												/>
												{isCollapsed && item.title === "Tasks" && tasksDueToday.length > 0 && (
													<div className="font-mono absolute -top-1.5 -right-1.5 size-3 rounded border text-[10px] font-medium flex items-center justify-center bg-blue-200 dark:bg-blue-700 border-blue-300 dark:border-blue-600 text-blue-900 dark:text-blue-100">
														{tasksDueToday.length}
													</div>
												)}
											</div>
										)}
										{!isCollapsed && <span>{item.title}</span>}
									</div>
									{!isCollapsed && item.title === "Tasks" && tasksDueToday.length > 0 && (
										<NumberBadge number={tasksDueToday.length} color="blue" />
									)}
								</SidebarMenuButton>
							</SidebarMenuItem>
						</Link>
					);
				})}
			</SidebarMenu>
		</SidebarGroup>
	);
}
