"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { OrganizationLogo } from "@app/organizations/components/OrganizationLogo";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useOrganizationListQuery } from "@app/organizations/lib/api";
import { ActivePlanBadge } from "@app/payments/components/ActivePlanBadge";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { clearCache } from "@shared/lib/cache";
import {
	IconBuildingSkyscraper,
	IconDeviceDesktop,
	IconLogout,
	IconMoon,
	IconPlus,
	IconSquareRoundedCheckFilled,
	IconSun,
	IconUserCircle,
} from "@tabler/icons-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import * as React from "react";

export function OrgSwitcher() {
	const { user } = useSession();

	const { isMobile, state } = useSidebar();
	const isCollapsed = state === "collapsed";
	const t = useTranslations();
	const { activeOrganization, setActiveOrganization } =
		useActiveOrganization();
	const { data: allOrganizations } = useOrganizationListQuery();
	const { theme, setTheme } = useTheme();

	const onLogout = () => {
		authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					window.location.href = new URL(
						config.auth.redirectAfterLogout,
						window.location.origin,
					).toString();
				},
			},
		});
	};

	return (
		<SidebarMenu>
			<SidebarMenuItem>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<SidebarMenuButton
							size="md"
							className={cn(
								"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
								isCollapsed &&
									"flex items-center justify-center p-2",
							)}
						>
							<OrganizationLogo
								name={activeOrganization?.name ?? ""}
								logoUrl={activeOrganization?.logo}
								className={cn(
									"size-5",
									isCollapsed ? "block" : "hidden sm:block",
									"shrink-0",
								)}
							/>
							{!isCollapsed && (
								<>
									<div className="grid flex-1 text-left text-sm leading-tight">
										<span className="truncate font-medium">
											{activeOrganization?.name}
										</span>
									</div>
									<ActivePlanBadge
										organizationId={
											activeOrganization?.id ?? ""
										}
									/>
								</>
							)}
						</SidebarMenuButton>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="w-(--radix-dropdown-menu-trigger-width) min-w-72 rounded-lg !ml-2 space-y-1"
						side={isMobile ? "bottom" : "bottom"}
						align="end"
						sideOffset={4}
					>
						<DropdownMenuGroup className="space-y-1">
							{allOrganizations
								?.filter((organization) => {
									const userInOrganization =
										organization.metadata?.members?.find(
											(u: any) => u.id === user?.id,
										);
									return (
										userInOrganization?.status !==
										"suspended"
									);
								})
								?.sort((a, b) =>
									a.id === activeOrganization?.id
										? -1
										: b.id === activeOrganization?.id
											? 1
											: 0,
								)
								.map((organization) => (
									<DropdownMenuItem
										key={organization.id}
										onClick={async () => {
											await setActiveOrganization(
												organization.id,
											);
										}}
									>
										<div className="flex items-center justify-between w-full">
											<div className="flex items-center gap-x-2 w-full">
												<OrganizationLogo
													name={organization.name}
													logoUrl={organization.logo}
													className="hidden size-5 sm:block ml-1"
												/>
												<span key={organization.id}>
													{organization.name}
												</span>
											</div>
											{activeOrganization?.id ===
												organization.id && (
												<IconSquareRoundedCheckFilled className="mr-auto size-4 text-blue-400" />
											)}
										</div>
									</DropdownMenuItem>
								))}

							<DropdownMenuItem
								className="group"
								icon={<IconPlus className="size-4" />}
							>
								<Link href="/app/new-organization">
									{t(
										"organizations.organizationSelect.createNewOrganization",
									)}
								</Link>
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								icon={<IconUserCircle className="size-4" />}
							>
								<Link
									href={`/app/${activeOrganization?.slug}/settings/account`}
								>
									<span>
										{t(
											"organizations.organizationSelect.accountSettings",
										)}
									</span>
								</Link>
							</DropdownMenuItem>
							<DropdownMenuItem
								icon={
									<IconBuildingSkyscraper className="size-4" />
								}
							>
								<Link
									href={`/app/${activeOrganization?.slug}/settings/organization`}
								>
									<span>
										{t(
											"organizations.organizationSelect.organizationSettings",
										)}
									</span>
								</Link>
							</DropdownMenuItem>
							<DropdownMenuSub>
								<DropdownMenuSubTrigger
									icon={<IconSun className="size-4" />}
								>
									<div className="flex items-center justify-between w-full">
										<span>{t("common.theme.title")}</span>
										<span className="font-mono text-[10px] text-muted-foreground pr-2">
											{theme}
										</span>
									</div>
								</DropdownMenuSubTrigger>
								<DropdownMenuSubContent>
									<DropdownMenuItem
										onClick={() => setTheme("light")}
									>
										<IconSun className="size-4 mr-2" />
										{t("common.theme.light")}
										{theme === "light" && (
											<IconSquareRoundedCheckFilled className="ml-auto size-4 text-blue-400" />
										)}
									</DropdownMenuItem>
									<DropdownMenuItem
										onClick={() => setTheme("dark")}
									>
										<IconMoon className="size-4 mr-2" />
										{t("common.theme.dark")}
										{theme === "dark" && (
											<IconSquareRoundedCheckFilled className="ml-auto size-4 text-blue-400" />
										)}
									</DropdownMenuItem>
									<DropdownMenuItem
										onClick={() => setTheme("system")}
									>
										<IconDeviceDesktop className="size-4 mr-2" />
										{t("common.theme.system")}
										{theme === "system" && (
											<IconSquareRoundedCheckFilled className="ml-auto size-4 text-blue-400" />
										)}
									</DropdownMenuItem>
								</DropdownMenuSubContent>
							</DropdownMenuSub>

							<DropdownMenuSeparator />

							<DropdownMenuItem
								onClick={onLogout}
								icon={<IconLogout className="size-4" />}
							>
								{t("app.userMenu.logout")}
							</DropdownMenuItem>
						</DropdownMenuGroup>
					</DropdownMenuContent>
				</DropdownMenu>
			</SidebarMenuItem>
		</SidebarMenu>
	);
}
