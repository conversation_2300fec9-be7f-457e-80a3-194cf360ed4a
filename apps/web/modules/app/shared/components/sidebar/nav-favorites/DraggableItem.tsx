import { useSortable } from "@dnd-kit/sortable";
import { cn } from "@ui/lib";
import React from "react";
import type { FavoriteItem } from "./types";

export function DraggableItem({
	item,
	children,
}: {
	item: FavoriteItem;
	children: React.ReactNode;
}) {
	const { attributes, listeners, setNodeRef, isDragging } = useSortable({
		id: item.id,
		data: {
			type: "favorite",
			item,
		},
	});

	return (
		<div
			ref={setNodeRef}
			{...attributes}
			{...listeners}
			className={cn("transition-opacity", isDragging && "opacity-50")}
		>
			{children}
		</div>
	);
}
