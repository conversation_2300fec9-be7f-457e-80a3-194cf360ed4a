import { useSortable } from "@dnd-kit/sortable";
import { cn } from "@ui/lib";
import React from "react";
import type { FavoriteFolder } from "./types";

export function DraggableFolder({
	folder,
	children,
}: {
	folder: FavoriteFolder;
	children: React.ReactNode;
}) {
	const { attributes, listeners, setNodeRef, isDragging } = useSortable({
		id: folder.id,
		data: {
			type: "folder",
			item: folder,
		},
	});

	return (
		<div
			ref={setNodeRef}
			{...attributes}
			{...listeners}
			className={cn("transition-opacity", isDragging && "opacity-50")}
		>
			{children}
		</div>
	);
}
