import React, { useState } from "react";
import { useUpdateNote } from "@app/notes/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconNote } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	EmojiPicker,
	EmojiPickerContent,
	EmojiPickerFooter,
	EmojiPickerSearch,
} from "@shared/components/EmojiPicker";

interface NoteIconSelectorProps {
	noteId: string;
	currentIcon?: string | null;
	className?: string;
	size?: "sm" | "md" | "lg";
}

export const NoteIconSelector = ({
	noteId,
	currentIcon,
	className,
	size = "sm",
}: NoteIconSelectorProps): JSX.Element => {
	const [isOpen, setIsOpen] = useState(false);
	const { activeOrganization } = useActiveOrganization();
	const updateNote = useUpdateNote(activeOrganization?.id);

	const handleIconSelect = (emoji: string) => {
		setIsOpen(false);
		updateNote.mutate({
			id: noteId,
			icon: emoji,
			organizationId: activeOrganization?.id || "",
		});
	};

	const handleRemoveIcon = () => {
		setIsOpen(false);
		updateNote.mutate({
			id: noteId,
			icon: "",
			organizationId: activeOrganization?.id || "",
		});
	};

	const iconSizeClass = {
		sm: "text-xs",
		md: "text-sm", 
		lg: "text-base",
	}[size];

	const buttonSizeClass = {
		sm: "h-4 w-4",
		md: "h-6 w-6",
		lg: "h-8 w-8", 
	}[size];

	return (
		<Popover open={isOpen} onOpenChange={setIsOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="ghost"
					size="sm"
					className={`${buttonSizeClass} p-0 hover:bg-transparent ${className}`}
				>
					{currentIcon ? (
						<span className={iconSizeClass}>
							{currentIcon}
						</span>
					) : (
						<IconNote className={`${buttonSizeClass} text-muted-foreground`} />
					)}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-fit p-0" side="bottom" align="start">
				<EmojiPicker
					className="h-[342px]"
					onEmojiSelect={({ emoji }) => handleIconSelect(emoji)}
				>
					<EmojiPickerSearch />
					<EmojiPickerContent />
					<EmojiPickerFooter />
				</EmojiPicker>
				{currentIcon && (
					<div className="p-2 border-t">
						<Button
							variant="outline"
							size="sm"
							onClick={handleRemoveIcon}
							className="w-full text-xs"
						>
							Remove icon
						</Button>
					</div>
				)}
			</PopoverContent>
		</Popover>
	);
}; 