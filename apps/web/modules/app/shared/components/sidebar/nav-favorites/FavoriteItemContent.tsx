import React, { useState } from "react";
import { objectItems } from "@app/organizations/components/objects/constants";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { StatusSelector } from "@app/shared/components/StatusSelector";
import { PrioritySelector } from "@app/shared/components/PrioritySelector";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import { useUpdateTask } from "@app/tasks/lib/api";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import {
	IconBuilding,
	IconHome,
	IconLayoutKanban,
	IconMapPin2,
	IconNote,
	IconSquareRoundedCheck,
	IconStarOff,
	IconTable,
	IconUser,
} from "@tabler/icons-react";
import { useQueryClient } from "@tanstack/react-query";
import { Avatar } from "@ui/components/avatar";
import {
	SidebarMenuAction,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import Link from "next/link";
import type { FavoriteItem } from "./types";
import { ObjectType } from "@repo/database";
import { NoteIconSelector } from "./NoteIconSelector";

interface FavoriteItemContentProps {
	item: FavoriteItem;
	onHover: (id: string | null) => void;
	isHovered: boolean;
	toggleFavorite: any;
	updateItem: any;
}

function renderIcon(icon: any, props: any = {}) {
	if (!icon) return null;
	if (React.isValidElement(icon)) {
		return React.cloneElement(icon, props);
	}
	if (typeof icon === "function") {
		return React.createElement(icon, props);
	}
	return null;
}

export const FavoriteItemContent = ({
	item,
	onHover,
	isHovered,
	toggleFavorite,
	updateItem,
}: FavoriteItemContentProps) => {
	const [isLocallyRemoved, setIsLocallyRemoved] = useState(false);
	const queryClient = useQueryClient();
	const { openCreateTask, openEditTask } = useTasks();
	const sidebar = useSidebar();
	const isCollapsed = sidebar?.state === "collapsed";
	const { activeOrganization } = useActiveOrganization();
	const updateTaskMutation = useUpdateTask(activeOrganization?.id);

	if (isLocallyRemoved) return null;

	let task: any = null;
	if (item.objectId && item.objectType === "task") {
		const orgId = item.organizationId;
		const tasks = queryClient.getQueryData<any[]>(["tasks", orgId]);
		if (tasks) {
			task = tasks.find((t) => t.id === item.objectId);
		}
	}

	let viewData: any = null;
	if (item.objectType === "view") {
		viewData = item.viewData;
	}

	// Get note data for icons
	let noteData: any = null;
	if (item.objectId && item.objectType === "note") {
		const orgId = item.organizationId;
		const notes = queryClient.getQueryData<any[]>(["notes", orgId]);
		if (notes) {
			noteData = notes.find((n) => n && (n.id || (n as any)._id) === item.objectId);
		}
	}

	// Handle task status change
	const handleStatusChange = (taskId: string, status: string) => {
		updateTaskMutation.mutate({ id: taskId, status });
	};

	// Handle task priority change
	const handlePriorityChange = (taskId: string, priority: string) => {
		updateTaskMutation.mutate({ id: taskId, priority });
	};

	if (item.objectType === "task") {
		if (isCollapsed) {
			return (
				<SidebarMenuItem className="flex justify-center">
					<SidebarMenuButton
						tooltip={task?.title ?? item.name}
						className={cn(
							"transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0",
						)}
						asChild
					>
						<div
							className="flex items-center justify-center w-full cursor-pointer"
							onClick={() => task && openEditTask(task)}
						>
							{task ? (
								<StatusSelector
									status={task.status}
									taskId={task.id}
									onStatusChange={handleStatusChange}
									readonly={false}
								/>
							) : (
								<div className="size-4 animate-spin border-2 border-zinc-400 border-t-transparent rounded-full" />
							)}
						</div>
					</SidebarMenuButton>
				</SidebarMenuItem>
			);
		}

		return (
			<SidebarMenuItem
				onMouseEnter={() => onHover(item.id)}
				onMouseLeave={() => onHover(null)}
			>
				<SidebarMenuButton tooltip={task?.title || item.name} asChild>
					<div
						className="flex items-center gap-2 cursor-pointer"
						onClick={() => task && openEditTask(task)}
					>
						<Avatar className="h-4 w-4 rounded-md">
							<div className="h-full w-full rounded-md flex items-center justify-center">
								{task ? (
									<StatusSelector
										status={task.status}
										taskId={task.id}
										onStatusChange={handleStatusChange}
										readonly={false}
									/>
								) : (
									<div className="h-4 w-4 animate-spin border-2 border-zinc-400 border-t-transparent rounded-full" />
								)}
							</div>
						</Avatar>
						<Avatar className="h-4 w-4 rounded-md">
							<div className="h-full w-full flex items-center justify-center">
								{task ? (
									<PrioritySelector
										priority={task.priority}
										taskId={task.id}
										onPriorityChange={handlePriorityChange}
									/>
								) : (
									<div className="h-4 w-4 animate-spin border-2 border-zinc-400 border-t-transparent rounded-full" />
								)}
							</div>
						</Avatar>
						<span className="text-sm">
							{task?.title || item.name}
						</span>
					</div>
				</SidebarMenuButton>
				{isHovered && (
					<SidebarMenuAction
						onClick={async (e) => {
							e.preventDefault();
							if (item.objectId) {
								setIsLocallyRemoved(true);
								try {
									await toggleFavorite({
										objectId: item.objectId,
										objectType: item.objectType,
										organizationId: item.organizationId,
									});
								} catch (err) {
									setIsLocallyRemoved(false);
								}
							}
						}}
					>
						<IconStarOff className="cursor-pointer text-muted-foreground hover:text-accent-foreground h-4 w-4" />
					</SidebarMenuAction>
				)}
			</SidebarMenuItem>
		);
	}

	// Handle property favorites
	if (item.objectType === "property") {
		if (isCollapsed) {
			return (
				<SidebarMenuItem className="flex justify-center">
					<SidebarMenuButton
						tooltip={item.name}
						className={cn(
							"transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0",
						)}
						asChild
					>
						<Link
							href={`/app/${activeOrganization?.slug}/properties/${item.objectId}`}
							className="flex items-center justify-center w-full"
						>
							<PropertyAvatar name={item.name} className="h-4 w-4" />
						</Link>
					</SidebarMenuButton>
				</SidebarMenuItem>
			);
		}

		return (
			<SidebarMenuItem
				onMouseEnter={() => onHover(item.id)}
				onMouseLeave={() => onHover(null)}
			>
				<SidebarMenuButton tooltip={item.name} asChild>
					<Link
						href={`/app/${activeOrganization?.slug}/properties/${item.objectId}`}
						className="flex items-center gap-2"
					>
						<PropertyAvatar name={item.name} className="h-4 w-4" />
						<span className="text-sm truncate">
							{item.name}
						</span>
					</Link>
				</SidebarMenuButton>
				{isHovered && (
					<SidebarMenuAction
						onClick={async (e) => {
							e.preventDefault();
							if (item.objectId) {
								setIsLocallyRemoved(true);
								try {
									await toggleFavorite({
										objectId: item.objectId,
										objectType: item.objectType,
										organizationId: item.organizationId,
									});
								} catch (err) {
									setIsLocallyRemoved(false);
								}
							}
						}}
					>
						<IconStarOff className="cursor-pointer text-muted-foreground hover:text-accent-foreground h-4 w-4" />
					</SidebarMenuAction>
				)}
			</SidebarMenuItem>
		);
	}

	// Handle note favorites
	if (item.objectType === "note") {
		const noteId = item.objectId;
		const currentIcon = noteData?.icon || item.noteIcon;

		if (isCollapsed) {
			return (
				<SidebarMenuItem className="flex justify-center">
					<SidebarMenuButton
						tooltip={item.name}
						className={cn(
							"transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0",
						)}
						asChild
					>
						<Link
							href={`/app/${activeOrganization?.slug}/notes/${noteId}`}
							className="flex items-center justify-center w-full relative"
						>
							<div className="absolute inset-0 flex items-center justify-center">
								<NoteIconSelector
									noteId={noteId}
									currentIcon={currentIcon}
									size="sm"
									className="hover:bg-accent/20 rounded"
								/>
							</div>
						</Link>
					</SidebarMenuButton>
				</SidebarMenuItem>
			);
		}

		return (
			<SidebarMenuItem
				onMouseEnter={() => onHover(item.id)}
				onMouseLeave={() => onHover(null)}
			>
				<SidebarMenuButton tooltip={item.name} asChild>
					<Link
						href={`/app/${activeOrganization?.slug}/notes/${noteId}`}
						className="flex items-center gap-2"
					>
						<div onClick={(e) => e.preventDefault()}>
							<NoteIconSelector
								noteId={noteId}
								currentIcon={currentIcon}
								size="sm"
								className="hover:bg-accent/20 rounded"
							/>
						</div>
						<span className="text-sm">{item.name}</span>
					</Link>
				</SidebarMenuButton>
				{isHovered && (
					<SidebarMenuAction
						onClick={async (e) => {
							e.preventDefault();
							if (item.objectId) {
								setIsLocallyRemoved(true);
								try {
									await toggleFavorite({
										objectId: item.objectId,
										objectType: item.objectType,
										organizationId: item.organizationId,
									});
								} catch (err) {
									setIsLocallyRemoved(false);
								}
							}
						}}
					>
						<IconStarOff className="cursor-pointer text-muted-foreground hover:text-accent-foreground h-4 w-4" />
					</SidebarMenuAction>
				)}
			</SidebarMenuItem>
		);
	}

	// Handle contact favorites
	if (item.objectType === "contact") {
		const contactId = item.objectId;

		if (isCollapsed) {
			return (
				<SidebarMenuItem className="flex justify-center">
					<SidebarMenuButton
						tooltip={item.name}
						className={cn(
							"transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0",
						)}
						asChild
					>
						<Link
							href={`/app/${activeOrganization?.slug}/contact/${contactId}`}
							className="flex items-center justify-center w-full gap-2"
						>
							{item.contactData ? (
								<ContactAvatar
									name={item.name}
									avatarUrl={item.contactData.image}
									className="h-4 w-4"
								/>
							) : (
								renderIcon(item.icon || IconSquareRoundedCheck, { className: "size-4" })
							)}
						</Link>
					</SidebarMenuButton>
				</SidebarMenuItem>
			);
		}

		return (
			<SidebarMenuItem
				onMouseEnter={() => onHover(item.id)}
				onMouseLeave={() => onHover(null)}
			>
				<SidebarMenuButton
					tooltip={item.name}
					asChild
					className="gap-2"
				>
					<Link
						href={`/app/${activeOrganization?.slug}/contact/${contactId}`}
					>
						<div>
							{item.contactData ? (
								<ContactAvatar
									name={item.name}
									avatarUrl={item.contactData.image}
									className="h-4 w-4"
								/>
							) : (
								<Avatar className="h-4 w-4 rounded-md">
									<div className="h-full w-full rounded-md flex items-center justify-center">
										{renderIcon(item.icon || IconSquareRoundedCheck, { className: "h-4 w-4" })}
									</div>
								</Avatar>
							)}
						</div>
						<span className="text-sm">{item.name}</span>
					</Link>
				</SidebarMenuButton>
				{isHovered && (
					<SidebarMenuAction
						onClick={async (e) => {
							e.preventDefault();
							if (item.objectId) {
								setIsLocallyRemoved(true);
								try {
									await toggleFavorite({
										objectId: item.objectId,
										objectType: item.objectType,
										organizationId: item.organizationId,
									});
								} catch (err) {
									setIsLocallyRemoved(false);
								}
							}
						}}
					>
						<IconStarOff className="cursor-pointer text-muted-foreground hover:text-accent-foreground h-4 w-4" />
					</SidebarMenuAction>
				)}
			</SidebarMenuItem>
		);
	}

	// Handle view favorites
	if (item.objectType === "view") {
		const viewId = item.objectId;

		const getObjectConfig = (objectType: string) => {
			return (
				objectItems.find((obj) => obj.url === objectType) ||
				objectItems[0]
			);
		};

		const objectConfig = getObjectConfig(viewData?.objectType || "contacts");

		// For map views, use the viewIcon and viewColor from the properties object config
		const isMapView = viewData?.viewType === "map";
		const propertiesConfig = getObjectConfig("properties");
		const mainIcon = viewData?.viewType === "map" ? IconHome : objectConfig.icon;
		const mainClassName = viewData?.viewType === "map" ? "bg-orange-500 p-0.5 !rounded-sm" : objectConfig.className;

		// Fix overlay icon and color logic
		const overlayIcon = isMapView ? propertiesConfig.viewIcon : 
						   viewData?.viewType === "kanban" ? IconLayoutKanban : 
						   IconTable;

		const overlayColor = isMapView ? propertiesConfig.viewColor : 
						    viewData?.viewType === "kanban" ? "bg-orange-500" : 
						    "bg-emerald-500";

		if (isCollapsed) {
			return (
				<SidebarMenuItem className="flex justify-center">
					<SidebarMenuButton
						tooltip={item.name}
						className={cn(
							"transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0",
						)}
						asChild
					>
						<Link
							href={
								viewData &&
								viewData.objectType &&
								activeOrganization?.slug
									? `/app/${activeOrganization.slug}/${viewData.objectType}/view/${viewId}`
									: viewId && activeOrganization?.slug
										? `/app/${activeOrganization.slug}/contacts/view/${viewId}`
										: "#"
							}
							className="flex items-center justify-center w-full relative"
						>
							<div
								className={cn(
									mainClassName,
									"flex items-center justify-center rounded-sm w-4 h-4",
								)}
							>
								{mainIcon && React.createElement(mainIcon, { className: "h-3 w-3 text-white" })}
							</div>
							{/* Small overlay with view type icon */}
							<div
								className={cn(
									"absolute -bottom-1 -right-1 rounded-sm p-0.5 border border-border",
									overlayColor
								)}
							>
								{overlayIcon && React.createElement(overlayIcon, { className: "h-2 w-2 text-white" })}
							</div>
						</Link>
					</SidebarMenuButton>
				</SidebarMenuItem>
			);
		}

		// Get object type display name
		const getObjectTypeDisplayName = (objectType: string) => {
			switch (objectType) {
				case "contact":
					return "Contacts";
				case "company":
					return "Companies";
				case "property":
					return "Properties";
				default:
					return objectType;
			}
		};

		// Get view type display name
		const getViewTypeDisplayName = (viewType: string) => {
			switch (viewType) {
				case "table":
					return "Table";
				case "kanban":
					return "Kanban";
				case "map":
					return "Map";
				default:
					return "Table";
			}
		};

		return (
			<SidebarMenuItem
				onMouseEnter={() => onHover(item.id)}
				onMouseLeave={() => onHover(null)}
			>
				<SidebarMenuButton
					tooltip={`${getViewTypeDisplayName(viewData?.viewType || "table")} view for ${getObjectTypeDisplayName(viewData?.objectType || "")}`}
					asChild
					className="gap-1"
				>
					<Link
						href={
							viewData &&
							viewData.objectType &&
							activeOrganization?.slug
								? `/app/${activeOrganization.slug}/${viewData.objectType}/view/${viewId}`
								: viewId && activeOrganization?.slug
									? `/app/${activeOrganization.slug}/contacts/view/${viewId}`
									: "#"
						}
						className="flex items-center gap-2 w-full"
					>
						<div className="relative flex-shrink-0">
							<div
								className={cn(
									mainClassName,
									"flex items-center justify-center rounded-sm w-4 h-4",
								)}
							>
								{mainIcon && React.createElement(mainIcon, { className: "h-3 w-3 text-white" })}
							</div>
							<div
								className={cn(
									"absolute -bottom-1.25 -right-1.25 rounded-sm p-0.5 border border-border",
									overlayColor
								)}
							>
								{overlayIcon && React.createElement(overlayIcon, { className: "h-2 w-2 text-white" })}
							</div>
						</div>
						<div className="flex items-center min-w-0 flex-1 gap-1">
							<span className="text-sm font-medium text-foreground">
								{getViewTypeDisplayName(viewData?.viewType || "table")}
							</span>
							<span className="text-sm text-muted-foreground truncate">
								{getObjectTypeDisplayName(viewData?.name || "")}
							</span>
						</div>
					</Link>
				</SidebarMenuButton>
				{isHovered && (
					<SidebarMenuAction
						onClick={async (e) => {
							e.preventDefault();
							if (item.objectId) {
								setIsLocallyRemoved(true);
								try {
									await toggleFavorite({
										objectId: item.objectId,
										objectType: item.objectType,
										organizationId: item.organizationId,
									});
								} catch (err) {
									setIsLocallyRemoved(false);
								}
							}
						}}
					>
						<IconStarOff className="cursor-pointer text-muted-foreground hover:text-accent-foreground h-4 w-4" />
					</SidebarMenuAction>
				)}
			</SidebarMenuItem>
		);
	}

	// Default rendering for non-task favorites
	if (isCollapsed) {
		// Only show the icon, centered, with NavMain hover/size
		return (
			<SidebarMenuItem className="flex justify-center">
				<SidebarMenuButton
					tooltip={item.name}
					className={cn(
						"transition-colors duration-200 hover:bg-accent hover:text-accent-foreground flex items-center justify-center h-9 w-9 p-0",
					)}
					asChild
				>
					<Link
						href={`/${item.id}`}
						className="flex items-center justify-center w-full"
					>
						<IconSquareRoundedCheck className="size-4" />
					</Link>
				</SidebarMenuButton>
			</SidebarMenuItem>
		);
	}

	return (
		<SidebarMenuItem
			onMouseEnter={() => onHover(item.id)}
			onMouseLeave={() => onHover(null)}
			className="hover:bg-zinc-800/50 rounded-md"
		>
			<SidebarMenuButton tooltip={item.name} asChild>
				<Link href={`/${item.id}`}>
					<Avatar className="h-4 w-4 rounded-md">
						<div className="h-full w-full bg-zinc-100 dark:bg-zinc-800 rounded-md flex items-center justify-center">
							<IconSquareRoundedCheck className="h-4 w-4" />
						</div>
					</Avatar>
					<span className="text-sm">{item.name}</span>
				</Link>
			</SidebarMenuButton>
			{isHovered && (
				<SidebarMenuAction
					onClick={async (e) => {
						e.preventDefault();
						if (item.objectId) {
							setIsLocallyRemoved(true);
							try {
								await toggleFavorite({
									objectId: item.objectId,
									objectType: item.objectType,
									organizationId: item.organizationId,
								});
							} catch (err) {
								setIsLocallyRemoved(false); // Rollback if error
							}
						}
					}}
				>
					<IconStarOff className="text-muted-foreground hover:text-accent-foreground h-4 w-4" />
				</SidebarMenuAction>
			)}
		</SidebarMenuItem>
	);
};
