"use client";

import { ShortcutDisplay } from "@app/shared/components/ShortcutDisplay";
import { Button } from "@ui/components/button";
import { useSidebar } from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import { SearchIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import * as React from "react";

interface CommandButtonProps {
	setOpen: (open: boolean) => void;
}

export function CommandButton({ setOpen }: CommandButtonProps) {
	const t = useTranslations("app");
	const { state } = useSidebar();
	const [mounted, setMounted] = React.useState(false);
	const isCollapsed = mounted ? state === "collapsed" : true;

	React.useEffect(() => {
		setMounted(true);
	}, []);

	return (
		<Button
			size={isCollapsed ? "icon" : "sm"}
			variant={isCollapsed ? "outline" : "ghost"}
			className={cn(
				"relative text-sm text-muted-foreground",
				isCollapsed
					? "h-8 w-8 p-0 flex items-center justify-center hover:bg-muted/50"
					: "w-full justify-start border border-input bg-transparent hover:bg-muted/50",
			)}
			onClick={() => setOpen(true)}
		>
			{isCollapsed ? (
				<SearchIcon className="size-4" />
			) : (
				<>
					<span className="hidden lg:inline-flex">
						{t("command.quickActions")}
					</span>
					<span className="inline-flex lg:hidden">
						{t("command.quickSearch")}
					</span>
					<kbd className="absolute right-1 top-[50%] translate-y-[-50%] px-1.5 py-0.5 text-xs font-medium bg-zinc-200 dark:bg-zinc-800 text-zinc-400 rounded-sm border border-zinc-100 dark:border-zinc-700">
						<ShortcutDisplay />K
					</kbd>
				</>
			)}
		</Button>
	);
}
