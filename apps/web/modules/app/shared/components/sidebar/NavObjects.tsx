"use client";

import { objectItems } from "@app/organizations/components/objects/constants";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@radix-ui/react-collapsible";
import { IconChevronDown, IconCube, IconCubePlus } from "@tabler/icons-react";
import {
	SidebarGroup,
	SidebarGroupLabel,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

interface NavObjectsProps {
	organizationSlug: string;
}

export function NavObjects({ organizationSlug }: NavObjectsProps) {
	const pathname = usePathname();
	const sidebar = useSidebar();
	const { activeOrganizationUserRole } = useActiveOrganization();
	const isCollapsed = sidebar?.state === "collapsed";
	const [isObjectsHovered, setIsObjectsHovered] = useState(false);

	// Only owners can create new objects
	const canCreateObjects = activeOrganizationUserRole === "owner";

	const handleCreateObject = () => {
		// TODO: Implement create object logic
		console.warn("Create new object");
	};

	return (
		<Collapsible defaultOpen className="group/collapsible">
			<SidebarGroup className={cn(isCollapsed && "!mt-6")}>
				<SidebarGroupLabel
					className="group-data-[collapsible=icon]:opacity-100 !p-0"
					onMouseEnter={() => setIsObjectsHovered(true)}
					onMouseLeave={() => setIsObjectsHovered(false)}
				>
					<CollapsibleTrigger
						className={cn(
							"h-8 flex items-center px-2 rounded-md w-full cursor-pointer mb-1",
							"hover:!bg-muted/50 hover:!text-accent-foreground !border !border-transparent hover:!border-border",
						)}
					>
						<div className="flex items-center justify-between w-full">
							{isCollapsed ? (
								<IconCube className="h-4 w-4" />
							) : (
								<div className="flex items-center gap-2">
									<IconChevronDown className="h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
									<span className="text-xs">Objects</span>
								</div>
							)}
							{isObjectsHovered &&
								!isCollapsed &&
								canCreateObjects && (
									<IconCubePlus
										className="h-4 w-4 text-muted-foreground hover:text-accent-foreground cursor-pointer"
										onClick={(e) => {
											e.stopPropagation();
											handleCreateObject();
										}}
									/>
								)}
						</div>
					</CollapsibleTrigger>
				</SidebarGroupLabel>

				<CollapsibleContent className="overflow-hidden data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up">
					<SidebarMenu>
						{objectItems.map((object) => {
							const isActive = pathname.includes(
								`/${organizationSlug}/${object.url}`,
							);
							const Icon = object.icon;
							return (
								<SidebarMenuItem key={object.title}>
									<SidebarMenuButton
										asChild
										tooltip={object.title}
										isActive={isActive}
										className={cn(
											"transition-colors duration-200",
											isActive &&
												"bg-accent text-accent-foreground",
										)}
									>
										<Link
											href={`/app/${organizationSlug}/${object.url}`}
										>
											<div
												className={cn(
													object.className,
													"flex items-center justify-center rounded-sm",
													isCollapsed
														? "w-4 h-4"
														: "w-4 h-4",
												)}
											>
												<Icon className="h-3 w-3 text-white" />
											</div>
											<span>{object.title}</span>
										</Link>
									</SidebarMenuButton>
								</SidebarMenuItem>
							);
						})}
					</SidebarMenu>
				</CollapsibleContent>
			</SidebarGroup>
		</Collapsible>
	);
}
