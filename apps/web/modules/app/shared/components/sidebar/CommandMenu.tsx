"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	ContactDetailPanel,
	NoteDetailPanel,
	type SearchResultDetails,
	TaskDetailPanel,
	UserDetailPanel,
	PropertyDetailPanel,
	useSearch,
	TaskSearchResultDetails,
	NoteSearchResultDetails,
	UserSearchResultDetails,
	PropertySearchResultDetails,
	ContactSearchResultDetails,
} from "@app/search";
import { CommandShortcutDisplay } from "@app/shared/components/sidebar/CommandShortcutDisplay";
import {
	type CommandItem as CommandItemType,
	useCommandGroups,
} from "@app/shared/lib/command-menu";
import { TASK_STATUS } from "@app/shared/lib/constants";
import { CreateTaskModal } from "@app/tasks/components/CreateTaskModal";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import {
	IconArrowDown,
	IconArrowUp,
	IconNote,
	IconSquareRoundedCheck,
	IconUser,
	IconBuilding,
} from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import {
	Command,
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import * as React from "react";
import { CommandButton } from "./CommandButton";
import { ObjectType } from "@repo/database";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";

export function CommandMenu() {
	const router = useRouter();
	const commandGroups = useCommandGroups();
	const [open, setOpen] = React.useState(false);
	const [selectedCommand, setSelectedCommand] =
		React.useState<CommandItemType | null>(null);
	const [selectedSearchResult, setSelectedSearchResult] =
		React.useState<SearchResultDetails | null>(null);
	const [inputValue, setInputValue] = React.useState("");
	const [searchQuery, setSearchQuery] = React.useState("");
	const [taskModalOpen, setTaskModalOpen] = React.useState(false);
	const [taskToEdit, setTaskToEdit] = React.useState<any>(null);
	const [lastSelectedItem, setLastSelectedItem] = React.useState<
		string | null
	>(null);
	const commandListRef = React.useRef<HTMLDivElement>(null);
	const inputRef = React.useRef<HTMLInputElement>(null);
	const t = useTranslations("app.command");
	const { activeOrganization } = useActiveOrganization();

	const shouldSearch = searchQuery.length > 2 && !!activeOrganization?.id;
	const {
		data: searchResults,
		isLoading: searchLoading,
		error: searchError,
	} = useSearch(
		{
			query: searchQuery,
			organizationId: activeOrganization?.id || "",
			type: "all",
			limit: 10,
		},
		shouldSearch,
	);

	// Handle input changes directly from the input element
	const handleInputChange = React.useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const value = e.target.value;
			setSearchQuery(value);
			setInputValue(value);

			// Clear selected search result when search query changes or becomes too short
			if (value.length <= 2) {
				setSelectedSearchResult(null);
				setLastSelectedItem(null);
			}
		},
		[],
	);

	React.useEffect(() => {
		const down = (e: KeyboardEvent) => {
			if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
				e.preventDefault();
				setOpen((open) => !open);
			}
		};
		document.addEventListener("keydown", down);
		return () => document.removeEventListener("keydown", down);
	}, []);

	React.useEffect(() => {
		if (!open || !commandListRef.current) return;

		const observer = new MutationObserver((mutations) => {
			for (const mutation of mutations) {
				if (
					mutation.type === "attributes" &&
					mutation.attributeName === "data-selected"
				) {
					const selectedElement =
						commandListRef.current?.querySelector(
							'[data-selected="true"]',
						) as HTMLElement;
					if (selectedElement) {
						const value =
							selectedElement.getAttribute("data-value");

						// Check if this is a search result (starts with "search:")
						if (value?.startsWith("search:")) {
							if (searchResults?.results) {
								// Extract the ID from the unique value format: "search:id:title"
								const parts = value.split(":");
								const resultId = parts[1];
								const searchResult = searchResults.results.find(
									(result) => result.id === resultId,
								);
								if (searchResult) {
									const { icon: TaskIcon } =
										searchResult.type === "task"
											? getTaskStatusIcon(
													searchResult.status,
												)
											: searchResult.type === "contact"
												? { icon: IconUser }
												: searchResult.type === "user"
												? { icon: IconUser }
												: searchResult.type === "property"
												? { icon: IconBuilding }
												: { icon: IconNote };
									setSelectedCommand({
										icon: TaskIcon,
										label: searchResult.title,
										href: searchResult.url,
										buttonText: "Open",
									});
									setLastSelectedItem(searchResult.id);
									setSelectedSearchResult({
										id: searchResult.id,
										title: searchResult.title,
										type: searchResult.type as "task" | "note" | "contact" | "user" | "property",
										url: searchResult.url,
										content: searchResult.content,
										description: searchResult.description,
										status: searchResult.status,
										icon: searchResult.icon,
										createdAt: formatDate(
											searchResult.createdAt,
										),
										updatedAt: formatDate(
											searchResult.updatedAt,
										),
										priority: searchResult.priority,
										assigneeId: searchResult.assignee?.id,
										assigneeName:
											searchResult.assignee?.name,
										assigneeAvatarUrl:
											searchResult.assignee?.image,
										createdById: searchResult.createdBy?.id,
										createdByName:
											searchResult.createdBy?.name,
										// Note-specific fields
										isPublished: searchResult.isPublished,
										coverImage: searchResult.coverImage,
										objectId: searchResult.objectId,
										objectType: searchResult.objectType,
										// Contact-specific fields
										firstName: searchResult.firstName,
										lastName: searchResult.lastName,
										name: searchResult.name || searchResult.title,
										email: searchResult.email,
										phone: searchResult.phone,
										jobTitle: searchResult.jobTitle,
										company: searchResult.company,
										avatarUrl: searchResult.avatarUrl,
										// User-specific fields
										role: searchResult.role,
										username: searchResult.username,
										memberSince: searchResult.memberSince ? formatDate(searchResult.memberSince) : undefined,
										// Property-specific fields
										propertyType: searchResult.propertyType,
										market: searchResult.market,
										location: searchResult.location,
									});
									return;
								}
							}
						} else {
							// Find the command that matches this value (regular commands)
							for (const group of commandGroups) {
								const command = group.items.find(
									(item) => item.label === value,
								);
								if (command) {
									setSelectedCommand(command);
									setSelectedSearchResult(null);
									setLastSelectedItem(command.label);
									return;
								}
							}
						}
					}
				}
			}
		});

		observer.observe(commandListRef.current, {
			attributes: true,
			subtree: true,
			attributeFilter: ["data-selected"],
		});

		return () => observer.disconnect();
	}, [open, commandGroups, searchResults]);

	const handleEditTask = React.useCallback((task: SearchResultDetails) => {
		if (task.type === "task") {
			const taskData = {
				id: task.id,
				title: task.title,
				description: task.description || null,
				status: task.status as any,
				priority: task.priority as any,
				dueDate: null,
				assignee: task.assigneeName
					? {
							id: task.assigneeId,
							name: task.assigneeName,
						}
					: null,
				related: null,
			};
			setTaskToEdit(taskData);
			setTaskModalOpen(true);
			// Close the command menu
			setOpen(false);
		}
	}, []);

	const runCommand = React.useCallback(
		(command: CommandItemType) => {
			setOpen(false);

			if (command.isExternal && command.href) {
				window.open(command.href, "_blank");
			} else if (command.href) {
				router.push(command.href);
			} else if (command.action) {
				command.action();
			}
		},
		[router],
	);

	const getTaskStatusIcon = (status?: string) => {
		const taskStatus = TASK_STATUS.find((s) => s.value === status);
		return {
			icon: taskStatus?.icon || IconSquareRoundedCheck,
			color: taskStatus?.color || "text-zinc-500",
		};
	};

	const formatDate = (
		date: string | Date | undefined,
	): string | undefined => {
		if (!date) return undefined;
		if (typeof date === "string") return date;
		return date.toISOString();
	};

	const handleTaskModalClose = (open: boolean) => {
		setTaskModalOpen(open);
		if (!open) {
			setTaskToEdit(null);
		}
	};

	const handleItemSelect = (
		itemId: string,
		command: CommandItemType,
		searchResult?: SearchResultDetails,
	) => {
		// If this item is already selected, open it
		if (lastSelectedItem === itemId) {
			runCommand(command);
			return;
		}

		// First selection - show details and set as selected
		setLastSelectedItem(itemId);
		setSelectedCommand(command);

		if (searchResult) {
			setSelectedSearchResult(searchResult);
		} else {
			setSelectedSearchResult(null);
		}
	};

	const resultType = (type: ObjectType | "user") => {
		if (type === "task") {
			return (
				<Badge className="flex items-center gap-1 text-xs !px-2 !py-0.5">
					<IconSquareRoundedCheck className={"h-3 w-3"} />
					Task
				</Badge>
			);
		}
		if (type === "note") {
			return (
				<Badge className="flex items-center gap-1 text-xs !px-2 !py-0.5">
					<IconNote className="h-4 w-4" />
					Note
				</Badge>
			);
		}
		if (type === "user") {
			return (
				<Badge className="flex items-center gap-1 text-xs !px-2 !py-0.5">
					<IconUser className="h-4 w-4" />
					User
				</Badge>
			);
		}
		if (type === "property") {
			return (
				<Badge className="flex items-center gap-1 text-xs !px-2 !py-0.5">
					<IconBuilding className="h-4 w-4" />
					Property
				</Badge>
			);
		}
		return (
			<Badge className="flex items-center gap-1 text-xs !px-2 !py-0.5">
				<IconUser className="h-4 w-4" />
				Contact
			</Badge>
		);
	};

	return (
		<>
			<CommandButton setOpen={setOpen} />
			<CommandDialog
				open={open}
				onOpenChange={(newOpen) => {
					setOpen(newOpen);
					if (!newOpen) {
						setSelectedSearchResult(null);
						setSearchQuery("");
						setInputValue("");
						setLastSelectedItem(null);
					}
				}}
				className={`!rounded-2xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 m-0 bg-clip-padding overflow-hidden ${selectedSearchResult && shouldSearch ? "sm:max-w-6xl" : "sm:max-w-3xl"}`}
			>
				<div className="flex h-full">
					<div className="flex-1">
						<Command
							value={inputValue}
							onValueChange={setInputValue}
							shouldFilter={shouldSearch ? false : true}
						>
							<CommandInput
								ref={inputRef}
								placeholder={t("commandOrSearch")}
								onInput={handleInputChange}
							/>
							<div
								className={`flex ${selectedSearchResult && shouldSearch ? "" : ""}`}
							>
								<CommandList
									ref={commandListRef}
									className={`max-h-[450px] min-h-[450px] overflow-y-auto no-scrollbar bg-secondary/20 px-1 pb-16 ${selectedSearchResult && shouldSearch ? "flex-1" : "w-full"}`}
								>
									<CommandEmpty>
										{shouldSearch && searchLoading
											? "Searching..."
											: shouldSearch &&
													searchResults?.results
														?.length === 0
												? "No search results found."
												: "No results found."}
									</CommandEmpty>

									{/* Show regular command groups only when not searching or no search results */}
									{(!shouldSearch || (shouldSearch && searchResults?.results?.length === 0)) && 
										commandGroups.map((group, index) => (
											<React.Fragment key={group.heading}>
												{index > 0 && <CommandSeparator />}
												<CommandGroup
													heading={group.heading}
													className="[&_[cmdk-group-heading]]:font-mono [&_[cmdk-group-heading]]:uppercase [&_[cmdk-group-heading]]:text-[10px]"
												>
													{group.items.map((item) => (
														<CommandItem
															key={item.label}
															onSelect={() =>
																handleItemSelect(
																	item.label,
																	item,
																)
															}
															value={item.label}
															data-value={item.label}
															className="flex items-center justify-between"
														>
															<div className="flex items-center gap-2">
																<div className="p-1 rounded-md bg-muted border border-border dark:border-zinc-700">
																	<item.icon className="h-4 w-4" />
																</div>
																<span>
																	{item.label}
																</span>
															</div>
															<div className="flex items-center gap-2">
																{item.shortcut && (
																	<CommandShortcutDisplay
																		shortcut={
																			item.shortcut
																		}
																	/>
																)}
															</div>
														</CommandItem>
													))}
												</CommandGroup>
											</React.Fragment>
										))}

									{/* Show search results only when searching */}
									{shouldSearch &&
										searchResults &&
										searchResults.results.length > 0 && (
											<>
												<CommandSeparator />
												<CommandGroup
													heading={`Search Results (${searchResults.pagination.total})`}
													className="[&_[cmdk-group-heading]]:font-mono [&_[cmdk-group-heading]]:uppercase [&_[cmdk-group-heading]]:text-[10px] cursor-default"
												>
													{(() => {
														return searchResults.results.map(
															(result) => {
																const {
																	icon: TaskIcon,
																	color,
																} =
																	result.type ===
																	"task"
																		? getTaskStatusIcon(
																				result.status,
																			)
																		: result.type ===
																				"contact"
																			? {
																					icon: IconUser,
																					color: "text-blue-500",
																				}
																			: result.type ===
																				"user"
																			? {
																					icon: IconUser,
																					color: "text-green-500",
																				}
																			: result.type ===
																				"property"
																			? {
																					icon: IconBuilding,
																					color: "text-orange-500",
																				}
																			: {
																					icon: IconNote,
																					color: "",
																				};

																// Create different commands for tasks, notes, contacts, and properties
																const searchCommand =
																	result.type ===
																	"task"
																		? {
																				icon: TaskIcon,
																				label: result.title,
																				action: () => {
																					const taskData =
																						{
																							id: result.id,
																							title: result.title,
																							description:
																								result.description ||
																								null,
																							status: result.status as any,
																							priority:
																								result.priority as any,
																							dueDate:
																								null,
																							assignee:
																								result
																									.assignee
																									?.name
																									? {
																											id: result
																												.assignee
																												.id,
																											name: result
																												.assignee
																												.name,
																									}
																								: null,
																							related:
																								null,
																						};
																					setTaskToEdit(
																						taskData,
																					);
																					setTaskModalOpen(
																						true,
																					);
																				},
																				buttonText:
																					"Edit",
																		}
																	: {
																				icon: TaskIcon,
																				label: result.title,
																				href: result.url,
																				buttonText:
																					"Open",
																		};

																const uniqueValue = `search:${result.id}:${result.title}`;
																return (
																	<CommandItem
																		key={
																			result.id
																		}
																		onSelect={() => {
																			const searchResultDetails: SearchResultDetails =
																				{
																					id: result.id,
																					title: result.title,
																					type: result.type as
																						| "task"
																						| "note"
																						| "contact"
																						| "user"
																						| "property",
																					url: result.url,
																					content:
																						result.content,
																					description:
																						result.description,
																					status: result.status,
																					icon: result.icon,
																					createdAt:
																						formatDate(
																							result.createdAt,
																						),
																					updatedAt:
																						formatDate(
																							result.updatedAt,
																						),
																					priority:
																						result.priority,
																					assigneeId:
																						result
																							.assignee
																							?.id,
																					assigneeName:
																						result
																							.assignee
																							?.name,
																					assigneeAvatarUrl:
																						result
																							.assignee
																							?.image,
																					createdById:
																						result
																							.createdBy
																							?.id,
																					createdByName:
																						result
																							.createdBy
																							?.name,
																					isPublished:
																						result.isPublished,
																					coverImage:
																						result.coverImage,
																					objectId:
																						result.objectId,
																					objectType:
																						result.objectType,
																					// Contact-specific fields
																					firstName:
																						result.firstName,
																					lastName:
																						result.lastName,
																					name:
																						result.name ||
																						result.title,
																					email: result.email,
																					phone: result.phone,
																					jobTitle:
																						result.jobTitle,
																					company:
																						result.company,
																					avatarUrl:
																						result.avatarUrl,
																					// User-specific fields
																					role: result.role,
																					username: result.username,
																					memberSince: result.memberSince ? formatDate(result.memberSince) : undefined,
																					// Property-specific fields
																					propertyType: result.propertyType,
																					market: result.market,
																					location: result.location,
																				};
																			handleItemSelect(
																				result.id,
																				searchCommand,
																				searchResultDetails,
																			);
																		}}
																		value={
																			uniqueValue
																		}
																		data-value={
																			uniqueValue
																		}
																		className="flex items-center justify-between"
																	>
																		<div className="flex items-center justify-between w-full gap-2">
																			<div className="flex items-center gap-2">
																				<div>
																					{result.type ===
																					"task" ? (
																						<TaskIcon
																							className={`h-4 w-4 ${color}`}
																						/>
																					) : result.type ===
																						"contact" ? (
																						<ContactAvatar
																							name={
																								result.name ||
																								result.title
																							}
																							avatarUrl={
																								result.avatarUrl
																							}
																							className="h-4 w-4"
																						/>
																					) : result.type ===
																						"user" ? (
																						<ContactAvatar
																							name={
																								result.name ||
																								result.title
																							}
																							avatarUrl={
																								result.avatarUrl
																							}
																							className="h-4 w-4"
																						/>
																					) : result.type ===
																						"property" ? (
																						<PropertyAvatar
																							name={result.title}
																							avatarUrl={result.avatarUrl}
																							className={`h-4 w-4 ${color}`}
																						/>
																					) : result.icon ? (
																						<span className="text-base">{result.icon}</span>
																					) : (
																						<IconNote className="h-4 w-4" />
																					)}
																				</div>
																				<div className="flex flex-col items-start">
																					<span className="text-sm">
																						{
																							result.title
																						}
																					</span>
																				</div>
																			</div>
																			<div className="flex items-center gap-2">
																				{resultType(
																					result.type,
																				)}
																			</div>
																		</div>
																	</CommandItem>
																);
															},
														);
													})()}
												</CommandGroup>
											</>
										)}
								</CommandList>

								{selectedSearchResult &&
									shouldSearch &&
									(selectedSearchResult.type === "task" ? (
										<TaskDetailPanel
											task={selectedSearchResult as TaskSearchResultDetails}
											onNavigate={() => setOpen(false)}
											onEdit={handleEditTask as any}
										/>
									) : selectedSearchResult.type === "note" ? (
										<NoteDetailPanel
											note={selectedSearchResult as NoteSearchResultDetails}
											onNavigate={() => setOpen(false)}
										/>
									) : selectedSearchResult.type === "user" ? (
										<UserDetailPanel
											user={selectedSearchResult as UserSearchResultDetails}
											onNavigate={() => setOpen(false)}
										/>
									) : selectedSearchResult.type === "property" ? (
										<PropertyDetailPanel
											property={selectedSearchResult as PropertySearchResultDetails}
											onNavigate={() => setOpen(false)}
										/>
									) : (
										<ContactDetailPanel
											contact={selectedSearchResult as ContactSearchResultDetails}
											onNavigate={() => setOpen(false)}
										/>
									))}
							</div>
						</Command>
						<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-between gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
							<div className="flex items-center gap-2">
								<div className="flex items-center gap-1">
									<div className="border bg-sidebar/50 border-input rounded-md p-1">
										<IconArrowUp className="h-3 w-3 text-muted-foreground" />
									</div>
									<div className="border bg-sidebar/50 border-input rounded-md p-1">
										<IconArrowDown className="h-3 w-3 text-muted-foreground" />
									</div>
								</div>
								<span className="text-xs text-muted-foreground">
									to navigate
								</span>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded-md">
									{selectedCommand?.buttonText || "Select"}
								</span>
							</div>
						</div>
					</div>
				</div>
			</CommandDialog>
			<CreateTaskModal
				open={taskModalOpen}
				onOpenChange={handleTaskModalClose}
				taskToEdit={taskToEdit}
			/>
		</>
	);
}

