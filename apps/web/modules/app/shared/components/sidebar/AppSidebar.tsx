"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { CommandMenu } from "@app/shared/components/sidebar/CommandMenu";
import { NavFavorites } from "@app/shared/components/sidebar/NavFavorites";
import { NavMain } from "@app/shared/components/sidebar/NavMain";
import { NavObjects } from "@app/shared/components/sidebar/NavObjects";
import type { Organization } from "@repo/database";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { getCookie, setCookie } from "@repo/utils/lib/cookie";
import {
	IconBan,
	IconBell,
	IconChevronLeft,
	IconCreditCard,
	IconCreditCardPay,
	IconExternalLink,
	IconInbox,
	IconKey,
	IconLock,
	IconMail,
	IconMailBolt,
	IconMailbox,
	IconMailForward,
	IconPalette,
	IconPhoto,
	IconSearch,
	IconSettings,
	IconShare,
	IconTag,
	IconUser,
	IconUsers,
} from "@tabler/icons-react";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarRail,
	SidebarSeparator,
	useSidebar,
} from "@ui/components/sidebar";
import { cn } from "@ui/lib";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import * as React from "react";
import { changelog } from "../../../../homepage/changelog/data";
import {
	InfoCard,
	InfoCardAction,
	InfoCardContent,
	InfoCardDescription,
	InfoCardDismiss,
	InfoCardFooter,
	InfoCardMedia,
	InfoCardTitle,
} from "./InfoCard";
import { OrgSwitcher } from "./OrgSwitcher";

interface NavItem {
	title: string;
	url?: string;
	icon?: typeof IconSettings;
	onClick?: () => void;
	subItems?: NavSubItem[];
}

interface NavSubItem {
	title: string;
	url: string;
	icon?: typeof IconSettings;
}

interface NavSection {
	heading: string;
	items: NavItem[];
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
	messages: {
		searchSettings: string;
		account: {
			heading: string;
			title: string;
			changeEmail: string;
			changePassword: string;
			deleteAccount: string;
			notifications: string;
			emailNotifications: string;
			emailCalendarAccounts: {
				title: string;
				connectedAccounts: string;
				forwardingAddress: string;
				shareAccess: string;
				watermark: string;
				blocklist: string;
			};
		};
		ai: {
			heading: string;
			title: string;
			credits: string;
			usage: string;
			purchase: string;
			settings: string;
		};
		data: {
			heading: string;
			tags: string;
			managePermissions: string;
		};
		organization: {
			heading: string;
			general: string;
			members: string;
			billing: string;
			plans: string;
			paymentMethods: string;
		};
		security: {
			heading: string;
			title: string;
		};
	};
	organization: Organization | null;
}

const getSettingsNav = (
	messages: AppSidebarProps["messages"],
	userIsOrganizationAdmin: boolean = false,
): NavSection[] => {
	const sections: NavSection[] = [
		// Account section - always visible to all users
		{
			heading: messages.account.heading,
			items: [
				{
					title: messages.account.title,
					url: "account",
					icon: IconUser,
					subItems: [
						{
							title: messages.account.changeEmail,
							url: "account/email",
							icon: IconMail,
						},
						{
							title: messages.account.changePassword,
							url: "account/password",
							icon: IconKey,
						},
					],
				},
				{
					title: "Appearance",
					url: "account/appearance",
					icon: IconPalette,
				},
				{
					title: messages.account.emailCalendarAccounts.title,
					url: "account/email-calendar",
					icon: IconMailBolt,
					subItems: [
						{
							title: messages.account.emailCalendarAccounts
								.connectedAccounts,
							url: "account/email-calendar/connected-accounts",
							icon: IconMailbox,
						},
						{
							title: messages.account.emailCalendarAccounts
								.forwardingAddress,
							url: "account/email-calendar/forwarding",
							icon: IconMailForward,
						},
						{
							title: messages.account.emailCalendarAccounts
								.shareAccess,
							url: "account/email-calendar/access",
							icon: IconShare,
						},
						{
							title: messages.account.emailCalendarAccounts.watermark,
							url: "account/email-calendar/watermark",
							icon: IconPhoto,
						},
						{
							title: messages.account.emailCalendarAccounts.blocklist,
							url: "account/email-calendar/blocklist",
							icon: IconBan,
						},
					],
				},
				{
					title: messages.security.title,
					url: "account/security",
					icon: IconLock,
				},
				{
					title: messages.account.notifications,
					url: "account/notifications",
					icon: IconBell,
					subItems: [
						{
							title: messages.account.emailNotifications,
							url: "account/notifications",
							icon: IconBell,
						},
					],
				},
			],
		},
		// AI section - visible to all organization members
		{
			heading: messages.ai.heading,
			items: [
				{
					title: messages.ai.title,
					url: "ai",
					icon: IconSettings,
					subItems: [
						{
							title: messages.ai.credits,
							url: "ai/credits",
							icon: IconCreditCard,
						},
						{
							title: messages.ai.usage,
							url: "ai/usage",
							icon: IconInbox,
						},
						{
							title: messages.ai.purchase,
							url: "ai/purchase",
							icon: IconCreditCardPay,
						},
					],
				},
			],
		},
		// Data Management section - visible to all organization members
		{
			heading: messages.data.heading,
			items: [
				{
					title: messages.data.tags,
					url: "data/tags",
					icon: IconTag,
				},
			],
		},
	];

	// Organization section - only visible to admins and owners
	if (userIsOrganizationAdmin) {
		sections.push({
			heading: messages.organization.heading,
			items: [
				{
					title: messages.organization.general,
					url: "organization",
					icon: IconSettings,
				},
				{
					title: messages.organization.members,
					url: "organization/members",
					icon: IconUsers,
				},
				{
					title: messages.organization.billing,
					url: "organization/billing",
					icon: IconCreditCardPay,
					subItems: [
						{
							title: messages.organization.plans,
							url: "organization/billing/plans",
							icon: IconCreditCard,
						},
						{
							title: messages.organization.paymentMethods,
							url: "organization/billing/payment",
							icon: IconCreditCardPay,
						},
					],
				},
			],
		});
	}

	return sections;
};

export function AppSidebar({
	messages,
	organization,
	...props
}: AppSidebarProps) {
	const router = useRouter();
	const pathname = usePathname();
	const { user } = useSession();
	const [searchQuery, setSearchQuery] = React.useState("");
	const [mounted, setMounted] = React.useState(false);
	const isSettingsPage = pathname?.includes("/settings");
	const latestChangelog = changelog[0];
	const [showChangelogCard, setShowChangelogCard] = React.useState(false);
	const [isHovering, setIsHovering] = React.useState(false);
	const sidebar = useSidebar();
	const isCollapsed = sidebar?.state === "collapsed";
	const t = useTranslations();

	// Calculate user's admin status for the current organization
	const userIsOrganizationAdmin = React.useMemo(() => {
		if (!organization || !user) return false;
		return isOrganizationAdmin(organization as any, user);
	}, [organization, user]);

	React.useEffect(() => {
		setMounted(true);
		if (!latestChangelog) return;
		if (typeof window !== "undefined") {
			const dismissedVersion = getCookie("changelog_dismissed");
			setShowChangelogCard(dismissedVersion !== latestChangelog.version);
		}
	}, [latestChangelog?.version]);

	const filterItems = (items: NavItem[]) => {
		if (!searchQuery.trim()) return items;
		return items.filter((item) => {
			const matchesSearch = item.title
				.toLowerCase()
				.includes(searchQuery.toLowerCase());
			const hasMatchingSubItems = item.subItems?.some((subItem) =>
				subItem.title.toLowerCase().includes(searchQuery.toLowerCase()),
			);
			return matchesSearch || hasMatchingSubItems;
		});
	};

	const renderSettingsNav = (sections: NavSection[]) => {
		return sections.map((section) => (
			<React.Fragment key={section.heading}>
				<div className="px-2 pt-4 pb-2">
					<h3 className="text-xs font-normal text-gray-500">
						{section.heading}
					</h3>
				</div>
				{filterItems(section.items).map((item) => (
					<SidebarMenuItem key={`menu-${item.url}`}>
						<SidebarMenuButton
							onClick={() =>
								router.push(
									`/app/${organization?.slug}/settings/${item.url}`,
								)
							}
							className={
								pathname ===
								`/app/${organization?.slug}/settings/${item.url}`
									? "bg-muted/50 text-sidebar-accent-foreground !border !border-border"
									: ""
							}
						>
							{item.icon && <item.icon className="h-4 w-4" />}
							<span>{item.title}</span>
						</SidebarMenuButton>
						{item.subItems &&
							item.subItems.length > 0 &&
							searchQuery.trim() && (
								<SidebarMenuSub>
									{item.subItems
										.filter((subItem) =>
											subItem.title
												.toLowerCase()
												.includes(
													searchQuery.toLowerCase(),
												),
										)
										.map((subItem) => (
											<SidebarMenuItem
												key={`sub-${item.url}-${subItem.url}`}
											>
												<SidebarMenuButton
													onClick={() =>
														router.push(
															`/app/${organization?.slug}/settings/${subItem.url}`,
														)
													}
													className={
														pathname ===
														`/app/${organization?.slug}/settings/${subItem.url}`
															? "bg-sidebar-accent"
															: ""
													}
												>
													<span>{subItem.title}</span>
												</SidebarMenuButton>
											</SidebarMenuItem>
										))}
								</SidebarMenuSub>
							)}
					</SidebarMenuItem>
				))}
			</React.Fragment>
		));
	};

	function handleDismiss() {
		if (latestChangelog) {
			setCookie("changelog_dismissed", latestChangelog.version, 365);
			setShowChangelogCard(false);
		}
	}

	if (isSettingsPage) {
		return (
			<Sidebar
				{...props}
				variant="sidebar"
				collapsible="none"
				className="h-screen border-r"
			>
				<SidebarHeader>
					<SidebarMenu>
						<SidebarMenuItem>
							<SidebarMenuButton asChild>
								<Link href={`/app/${organization?.slug}`}>
									<IconChevronLeft className="h-4 w-4" />
									<span>Settings</span>
								</Link>
							</SidebarMenuButton>
						</SidebarMenuItem>
						<Separator />
						<SidebarMenuItem>
							<div className="pt-2">
								<div className="relative">
									<IconSearch className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
									<Input
										id="search-settings"
										name="search-settings"
										type="search"
										autoComplete="off"
										data-lpignore="true"
										data-form-type="other"
										placeholder={messages.searchSettings}
										value={searchQuery}
										onChange={(e) =>
											setSearchQuery(e.target.value)
										}
										className="pl-9 bg-transparent text-xs"
									/>
								</div>
							</div>
						</SidebarMenuItem>
					</SidebarMenu>
				</SidebarHeader>
				<SidebarContent>
					<SidebarMenu className="px-2">
						{renderSettingsNav(getSettingsNav(messages, userIsOrganizationAdmin))}
					</SidebarMenu>
				</SidebarContent>
			</Sidebar>
		);
	}

	// Regular sidebar
	return (
		<Sidebar
			{...props}
			variant="sidebar"
			collapsible="icon"
			// className="h-screen"
			data-state={mounted ? undefined : "collapsed"}
		>
			<SidebarHeader>
				<OrgSwitcher />
				<Separator />
				<CommandMenu />
			</SidebarHeader>
			<SidebarContent
				className={cn(
					"overflow-y-auto",
					isCollapsed
						? "space-y-0 items-center flex flex-col p-0"
						: "gap-0",
				)}
			>
				<NavMain slug={organization?.slug ?? ""} />
				<NavFavorites />
				<NavObjects organizationSlug={organization?.slug ?? ""} />
			</SidebarContent>
			<SidebarFooter>
				{showChangelogCard && latestChangelog && (
					<div className="relative">
						{isCollapsed ? (
							<div
								className="flex justify-center"
								onMouseEnter={() => setIsHovering(true)}
								onMouseLeave={() => setIsHovering(false)}
							>
								<div className="p-2 rounded-lg hover:bg-sidebar-accent cursor-pointer">
									<IconInbox className="h-4 w-4" />
								</div>
								{isHovering && (
									<div className="absolute left-full -top-48 ml-3 z-50 w-80">
										<InfoCard>
											<InfoCardContent>
												<InfoCardTitle>
													{latestChangelog.title}
												</InfoCardTitle>
												<InfoCardDescription>
													{
														latestChangelog.description
													}
												</InfoCardDescription>
												{latestChangelog.previewImages && (
													<InfoCardMedia
														media={latestChangelog.previewImages?.map(
															(src) => ({
																src,
																alt:
																	latestChangelog.title +
																	" Preview",
															}),
														)}
													/>
												)}
												<InfoCardFooter>
													<InfoCardDismiss
														onClick={handleDismiss}
													>
														{t("changelog.dismiss")}
													</InfoCardDismiss>
													<InfoCardAction>
														<Link
															href="/changelog"
															className="flex flex-row items-center gap-1 underline"
															target="_blank"
														>
															{t(
																"changelog.seeWhatsNew",
															)}{" "}
															<IconExternalLink
																size={12}
															/>
														</Link>
													</InfoCardAction>
												</InfoCardFooter>
											</InfoCardContent>
										</InfoCard>
									</div>
								)}
							</div>
						) : (
							<InfoCard>
								<InfoCardContent>
									<InfoCardTitle>
										{latestChangelog.title}
									</InfoCardTitle>
									<InfoCardDescription>
										{latestChangelog.description}
									</InfoCardDescription>
									{latestChangelog.previewImages && (
										<InfoCardMedia
											media={latestChangelog.previewImages?.map(
												(src) => ({
													src,
													alt:
														latestChangelog.title +
														" Preview",
												}),
											)}
										/>
									)}
									<InfoCardFooter>
										<InfoCardDismiss
											onClick={handleDismiss}
										>
											{t("changelog.dismiss")}
										</InfoCardDismiss>
										<InfoCardAction>
											<Link
												href="/changelog"
												className="flex flex-row items-center gap-1 underline"
												target="_blank"
											>
												{t("changelog.seeWhatsNew")}{" "}
												<IconExternalLink size={12} />
											</Link>
										</InfoCardAction>
									</InfoCardFooter>
								</InfoCardContent>
							</InfoCard>
						)}
					</div>
				)}
			</SidebarFooter>
			<SidebarRail />
		</Sidebar>
	);
}
