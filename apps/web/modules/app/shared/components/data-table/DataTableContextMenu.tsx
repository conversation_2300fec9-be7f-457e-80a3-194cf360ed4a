import {
	fetchFavoriteFolders,
	fetchFavorites,
	useUpdateFavorite,
} from "@app/favorites/lib/api";
import AlertDialog from "@app/shared/components/AlertDialog";
import {
	IconClearAll,
	IconCopy,
	IconEdit,
	IconEyeOff,
	IconFolder,
	IconPin,
	IconPinned,
	IconStar,
	IconStarOff,
	IconTrash,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { CustomContextMenu } from "@ui/components/context-menu";
import React, { useState } from "react";
import { toast } from "sonner";

export interface DataTableContextMenuProps {
	children: React.ReactNode;
	record: any;
	cell?: {
		columnId: string;
		value: any;
		rowId: string;
	};
	isFavorite?: boolean;
	isPinned?: boolean;
	organizationId?: string;
	objectType: string;
	onEdit?: (e: React.MouseEvent, recordId: string) => void;
	onEditCell?: (
		e: React.MouseEvent,
		cell: { columnId: string; value: any; rowId: string },
	) => void;
	onFavorite?: (e: React.MouseEvent, recordId: string) => void;
	onPin?: (e: React.MouseEvent, recordId: string) => void;
	onDelete?: (e: React.MouseEvent, recordId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
	onClearValue?: (
		e: React.MouseEvent,
		cell: { columnId: string; rowId: string },
	) => void;
	customMenuItems?: (record: any) => any[];
	asChild?: boolean;
}

export function DataTableContextMenu({
	children,
	record,
	cell,
	isFavorite = false,
	isPinned = false,
	organizationId,
	objectType,
	onEdit,
	onEditCell,
	onFavorite,
	onPin,
	onDelete,
	onCopy,
	onHide,
	onClearValue,
	customMenuItems,
	asChild = false,
}: DataTableContextMenuProps) {
	const [showClearDialog, setShowClearDialog] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [isClearing, setIsClearing] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);

	// Fetch favorite folders and current favorite for this record
	const { data: favoriteFolders = [], isLoading: foldersLoading } = useQuery({
		queryKey: ["favoriteFolders", organizationId],
		queryFn: () => {
			if (!organizationId) {
				console.warn(
					"No organizationId provided for fetching favorite folders",
				);
				return Promise.resolve([]);
			}
			return fetchFavoriteFolders(organizationId);
		},
		enabled: !!organizationId && isFavorite,
	});

	const { data: favorites = [], isLoading: favoritesLoading } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () => {
			if (!organizationId) {
				console.warn(
					"No organizationId provided for fetching favorites",
				);
				return Promise.resolve([]);
			}
			return fetchFavorites(organizationId);
		},
		enabled: !!organizationId && isFavorite,
	});

	const updateFavoriteMutation = useUpdateFavorite(organizationId);

	const currentFavorite = favorites.find(
		(f: any) => f.objectId === record.id && f.objectType === objectType,
	);
	const currentFolderId = currentFavorite?.folderId;

	function handleMoveToFolder(folderId: string | null) {
		if (!currentFavorite) {
			console.error("No current favorite found for record:", record.id);
			toast.error("Favorite not found");
			return;
		}

		const payload = {
			id: currentFavorite.id,
			folderId,
		};

		updateFavoriteMutation.mutate(payload, {
			onSuccess: () => {
				toast.success("Favorite moved to folder");
			},
			onError: (error) => {
				console.error("Failed to move favorite to folder:", error);
				toast.error("Failed to move favorite to folder");
			},
		});
	}

	const handleClearValue = async () => {
		if (!cell || !onClearValue) return;

		setIsClearing(true);
		try {
			await onClearValue({} as React.MouseEvent, {
				columnId: cell.columnId,
				rowId: cell.rowId,
			});
			toast.success("Value cleared");
			setShowClearDialog(false);
		} catch (error) {
			console.error("Failed to clear value:", error);
			toast.error("Failed to clear value");
		} finally {
			setIsClearing(false);
		}
	};

	const handleDelete = async () => {
		if (!onDelete) return;

		setIsDeleting(true);
		try {
			await onDelete({} as React.MouseEvent, record.id);
			toast.success("Record deleted");
			setShowDeleteDialog(false);
		} catch (error) {
			console.error("Failed to delete record:", error);
			toast.error("Failed to delete record");
		} finally {
			setIsDeleting(false);
		}
	};

	const favoriteMenuItems = () => {
		if (!onFavorite) return [];

		if (!isFavorite) {
			return [
				{
					id: "favorite",
					label: "Add to favorites",
					icon: IconStar,
					onClick: (e: any) => onFavorite(e, record.id),
				},
			];
		}

		const items = [];

		const moveToFolderItems: any[] = [
			{
				id: "move-favorite-none",
				label: "No folder",
				icon: undefined,
				onClick: () => handleMoveToFolder(null),
				indicator: {
					show: !currentFolderId,
					color: "bg-blue-500",
				},
			},
		];

		// Add existing folders if any
		if (favoriteFolders.length > 0) {
			moveToFolderItems.push(
				...favoriteFolders.map((folder: any) => ({
					id: `move-favorite-folder-${folder.id}`,
					label: folder.name,
					icon: IconFolder,
					onClick: () => handleMoveToFolder(folder.id),
					indicator: {
						show: currentFolderId === folder.id,
						color: "bg-blue-500",
					},
				})),
			);
		}

		items.push({
			id: "move-favorite",
			label: "Move favorite to",
			icon: IconStar,
			items: moveToFolderItems,
		});

		items.push({
			id: "favorite",
			label: "Remove from favorites",
			icon: IconStarOff,
			onClick: (e: any) => onFavorite(e, record.id),
		});

		return items;
	};

	const pinMenuItems = () => {
		if (!onPin) return [];

		return [
			{
				id: "pin",
				label: isPinned ? "Unpin from header" : "Pin to header",
				icon: isPinned ? IconPinned : IconPin,
				onClick: (e: any) => onPin(e, record.id),
			},
		];
	};

	const defaultMenuItems: (any | "separator")[] = [
		// Cell-level actions (if cell data is provided)
		cell &&
			onEditCell && {
				id: "edit-value",
				label: "Edit value",
				icon: IconEdit,
				onClick: (e: any) => onEditCell(e, cell),
			},

		// Row-level actions
		!cell &&
			onEdit && {
				id: "edit",
				label: "Edit",
				icon: IconEdit,
				onClick: (e: any) => onEdit(e, record.id),
			},

		// Favorites (always row-level)
		...favoriteMenuItems(),

		// Pin (always row-level)
		...pinMenuItems(),

		// Separator after edit/favorite/pin actions
		((cell && onEditCell) || (!cell && onEdit) || onFavorite || onPin) &&
			"separator",

		// Cell or record copy
		onCopy && {
			id: "copy",
			label: cell ? "Copy" : "Copy value",
			icon: IconCopy,
			onClick: (e: any) => {
				const valueToCopy = cell ? cell.value : record;
				// Handle different value types for proper copying
				let finalValue: string;

				if (Array.isArray(valueToCopy)) {
					finalValue = valueToCopy
						.map((item) =>
							typeof item === "object"
								? JSON.stringify(item)
								: String(item),
						)
						.join(", ");
				} else if (
					typeof valueToCopy === "object" &&
					valueToCopy !== null
				) {
					finalValue = JSON.stringify(valueToCopy, null, 2);
				} else {
					finalValue = String(valueToCopy);
				}

				onCopy(e, finalValue);
			},
		},
		// Hide column (cell-level) or record (row-level)
		onHide && {
			id: "hide",
			label: cell ? "Hide column" : "Hide column",
			icon: IconEyeOff,
			onClick: (e: any) => {
				const valueToHide = cell ? cell.columnId : record.id;
				onHide(e, valueToHide);
			},
		},

		// Separator before destructive actions
		(onCopy || onHide) && (onClearValue || onDelete) && "separator",

		// Clear cell value (only for cells)
		cell &&
			onClearValue && {
				id: "clear-value",
				label: "Clear value",
				icon: IconClearAll,
				iconClassName: "text-orange-500",
				onClick: () => setShowClearDialog(true),
			},

		// Separator before delete if clear value exists
		cell && onClearValue && onDelete && "separator",

		// Delete record
		onDelete && {
			id: "delete",
			label: "Delete",
			icon: IconTrash,
			iconClassName: "text-red-400",
			onClick: () => setShowDeleteDialog(true),
		},
	].filter(Boolean);

	// Add fallback item if no menu items exist
	if (
		defaultMenuItems.length === 0 &&
		(!customMenuItems || customMenuItems(record).length === 0)
	) {
		defaultMenuItems.push({
			id: "no-actions",
			label: "No actions available",
			disabled: true,
		});
	}

	// Combine default menu items with custom menu items if provided
	const menuItems = [
		...defaultMenuItems,
		...(customMenuItems ? customMenuItems(record) : []),
	];

	// If still no items after combining, add fallback
	if (menuItems.length === 0) {
		menuItems.push({
			id: "no-actions",
			label: "No actions available",
			disabled: true,
		});
	}

	return (
		<>
			<CustomContextMenu items={menuItems}>{children}</CustomContextMenu>

			{/* Clear Value Alert Dialog */}
			<AlertDialog
				open={showClearDialog}
				onOpenChange={setShowClearDialog}
				title="Clear Value"
				description={`Are you sure you want to clear the value "${cell?.value}" from this cell? This action cannot be undone.`}
				confirmLabel="Clear"
				cancelLabel="Cancel"
				confirmClassName="bg-orange-500 hover:bg-orange-600 text-white"
				onConfirm={handleClearValue}
				loading={isClearing}
			/>

			{/* Delete Record Alert Dialog */}
			<AlertDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				title="Delete Record"
				description="Are you sure you want to delete this record? This action cannot be undone."
				confirmLabel="Delete"
				cancelLabel="Cancel"
				confirmClassName="bg-red-500 hover:bg-red-600 text-white"
				onConfirm={handleDelete}
				loading={isDeleting}
			/>
		</>
	);
}

// Cell-aware context menu component
export interface DataTableCellContextMenuProps {
	children: React.ReactNode;
	record: any;
	cell: {
		columnId: string;
		value: any;
		rowId: string;
	};
	organizationId?: string;
	objectType: string;
	onEditCell?: (
		e: React.MouseEvent,
		cell: { columnId: string; value: any; rowId: string },
	) => void;
	onFavorite?: (e: React.MouseEvent, recordId: string) => void;
	onPin?: (e: React.MouseEvent, recordId: string) => void;
	onDelete?: (e: React.MouseEvent, recordId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
	onClearValue?: (
		e: React.MouseEvent,
		cell: { columnId: string; rowId: string },
	) => void;
	isFavorite?: boolean;
	isPinned?: boolean;
	customMenuItems?: (record: any) => any[];
}

export function DataTableCellContextMenu({
	children,
	record,
	cell,
	organizationId,
	objectType,
	onEditCell,
	onFavorite,
	onPin,
	onDelete,
	onCopy,
	onHide,
	onClearValue,
	isFavorite = false,
	isPinned = false,
	customMenuItems,
}: DataTableCellContextMenuProps) {
	return (
		<DataTableContextMenu
			record={record}
			cell={cell}
			isFavorite={isFavorite}
			isPinned={isPinned}
			organizationId={organizationId}
			objectType={objectType}
			onEditCell={onEditCell}
			onFavorite={onFavorite}
			onPin={onPin}
			onDelete={onDelete}
			onCopy={onCopy}
			onHide={onHide}
			onClearValue={onClearValue}
			customMenuItems={customMenuItems}
		>
			{children}
		</DataTableContextMenu>
	);
}
