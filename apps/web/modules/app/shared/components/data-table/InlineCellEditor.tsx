"use client";

import { CompanySelector } from "@app/shared/components/CompanySelector";
import { IconBuilding, IconTrash } from "@tabler/icons-react";
import { Badge, BadgeEmail } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { GripVertical, Plus, Trash2 } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";
import { CopyableValue } from "../../../../shared/components/CopyableValue";
import { ComposeEmailModal } from "../../../../shared/components/ComposeEmailModal";
import { ArrayFieldType, FieldType, TaggableObjectType } from "@repo/database";
import { useOrganizationTags, useObjectTags, useAddTag, useRemoveTag, useCreateTag } from "@app/shared/hooks/useTags";
import { ColorPicker, getRandomColor } from "@ui/components/color-picker";
import { normalizeTagName, validateTagName } from "@repo/api/src/lib/tag-utils";
import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@ui/components/command";
import { DataTableColumnTags } from "@app/organizations/components/objects/shared/data-table/data-table-column/data-table-column-tags";

interface Company {
	id: string;
	name: string;
	website?: string;
	logo?: string;
	domain?: string;
}

interface Tag {
	id: string;
	name: string;
	color: string | null;
}

interface InlineCellEditorProps {
	value: any;
	onSave: (value: any) => void;
	fieldType?: FieldType;
	arrayFieldType?: ArrayFieldType;
	selectOptions?: Array<{ label: string; value: string }>;
	placeholder?: string;
	isEditing: boolean;
	isSelected: boolean;
	onStartEdit: () => void;
	onStopEdit: () => void;
	onSelect: () => void;
	children: React.ReactNode;
	width?: string;
	organizationId?: string;
	objectType?: TaggableObjectType;
	objectId?: string;
}

function ArrayEditor({
	items,
	onChange,
	onSave,
	onCancel,
	fieldType = "text",
	placeholder = "Add item",
}: {
	items: string[];
	onChange: (items: string[]) => void;
	onSave: (finalItems?: string[]) => void;
	onCancel: () => void;
	fieldType?: ArrayFieldType;
	placeholder?: string;
}) {
	const [newItem, setNewItem] = React.useState("");
	const [hoveredIndex, setHoveredIndex] = React.useState<number | null>(null);
	
	// Email compose modal state
	const [composeModalOpen, setComposeModalOpen] = React.useState(false);
	const [selectedEmail, setSelectedEmail] = React.useState<string>("");

	const handleEmailClick = (email: string) => {
		setSelectedEmail(email);
		setComposeModalOpen(true);
	};

	const handleSave = () => {
		// Auto-add any pending input before saving (with validation)
		if (newItem.trim()) {
			if (validateAndAddItem(newItem.trim())) {
				const updatedItems = [...items, newItem.trim()];
				onChange(updatedItems);
				// Pass the updated items directly to onSave
				onSave(updatedItems);
				return;
			}
		}
		// If no pending input or validation failed, just save current items
		onSave(items);
	};

	const validateAndAddItem = (item: string): boolean => {
		if (fieldType === "email" && !isValidEmail(item)) {
			toast.error("You have entered an invalid email address");
			return false;
		}
		if (fieldType === "phone" && !isValidPhone(item)) {
			toast.error("You have entered an invalid phone number");
			return false;
		}
		return true;
	};

	const handleAddItem = () => {
		if (newItem.trim()) {
			if (validateAndAddItem(newItem.trim())) {
				onChange([...items, newItem.trim()]);
				setNewItem("");
			}
		}
	};

	const handleRemoveItem = (index: number) => {
		const newItems = items.filter((_, i) => i !== index);
		onChange(newItems);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			if (newItem.trim()) {
				// Validate and add the item, then save immediately
				if (validateAndAddItem(newItem.trim())) {
					const updatedItems = [...items, newItem.trim()];
					onChange(updatedItems);
					onSave(updatedItems);
				}
			} else {
				// If input is empty, just save current items
				handleSave();
			}
		} else if (e.key === "Escape") {
			e.preventDefault();
			e.stopPropagation();
			onCancel();
		}
	};

	const getInputType = () => {
		switch (fieldType) {
			case "email":
				return "email";
			case "phone":
				return "tel";
			default:
				return "text";
		}
	};

	return (
		<div className="w-full bg-sidebar border border-blue-500 p-3 z-50 -mt-1 shadow-lg">
			{/* Existing items */}
			<div className="space-y-2 mb-3">
				{items.map((item, index) => (
					<div
						key={index}
						className="flex items-center justify-between gap-1 w-fit"
						onMouseEnter={() => setHoveredIndex(index)}
						onMouseLeave={() => setHoveredIndex(null)}
					>
											{fieldType === "email" || fieldType === "phone" ? (
						<CopyableValue
							value={item}
							type={fieldType}
							showCopyButton={true}
							showDeleteButton={true}
							onDelete={() => handleRemoveItem(index)}
							onEmailClick={fieldType === "email" ? handleEmailClick : undefined}
							className="w-full"
						/>
					) : (
							<div className="flex-1 inline-flex items-center justify-between px-3 py-1.5 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg text-sm font-medium group hover:bg-blue-500/20">
								<span className="truncate">{item}</span>
								<Button
									type="button"
									size="sm"
									variant="ghost"
									onClick={(e) => {
										e.stopPropagation();
										handleRemoveItem(index);
									}}
									className="h-4 w-4 p-0 ml-2 text-muted-foreground hover:text-destructive flex-shrink-0"
								>
									<Trash2 className="h-3 w-3" />
								</Button>
							</div>
						)}
					</div>
				))}
			</div>

			{/* Add new item */}
				<Input
					value={newItem}
					onChange={(e) => setNewItem(e.target.value)}
					onKeyDown={handleKeyDown}
					type={getInputType()}
					placeholder={placeholder}
					className="flex-1 h-8 text-sm border-0 bg-transparent focus:ring-0 focus:border-0 w-full"
					autoFocus
				/>
				
				{/* Email compose modal - only render if fieldType is email */}
				{fieldType === "email" && (
					<ComposeEmailModal
						open={composeModalOpen}
						onOpenChange={setComposeModalOpen}
						toEmail={selectedEmail}
					/>
				)}
		</div>
	);
}

function SelectEditor({
	options,
	value,
	onSave,
	onCancel,
	placeholder = "Select option",
}: {
	options: Array<{ label: string; value: string }>;
	value: string | null;
	onSave: (value: string) => void;
	onCancel: () => void;
	placeholder?: string;
}) {
	const [searchTerm, setSearchTerm] = React.useState("");
	const [hoveredIndex, setHoveredIndex] = React.useState<number>(-1);

	const filteredOptions = React.useMemo(() => {
		return options.filter((option) =>
			option.label.toLowerCase().includes(searchTerm.toLowerCase()),
		);
	}, [options, searchTerm]);

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			if (hoveredIndex >= 0 && filteredOptions[hoveredIndex]) {
				onSave(filteredOptions[hoveredIndex].value);
			} else if (filteredOptions.length === 1) {
				onSave(filteredOptions[0].value);
			}
		} else if (e.key === "Escape") {
			e.preventDefault();
			e.stopPropagation();
			onCancel();
		} else if (e.key === "ArrowDown") {
			e.preventDefault();
			setHoveredIndex((prev) =>
				Math.min(prev + 1, filteredOptions.length - 1),
			);
		} else if (e.key === "ArrowUp") {
			e.preventDefault();
			setHoveredIndex((prev) => Math.max(prev - 1, -1));
		}
	};

	return (
		<div className="w-full bg-sidebar z-50 p-3 rounded-xl border border-border">
			{/* Search input */}
			<div className="mb-3">
				<Input
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={placeholder}
					className="h-8 text-sm border-0 bg-transparent focus:ring-0 focus:border-0"
					autoFocus
				/>
			</div>

			<Separator className="my-2" />

			{/* Options list */}
			<div className="space-y-1 max-h-48 overflow-y-auto">
				{filteredOptions.length > 0 ? (
					filteredOptions.map((option, index) => (
						<div
							key={option.value}
							className={cn(
								"group flex items-center gap-3 p-1 rounded-lg border border-transparent hover:border-border",
								value === option.value &&
									"bg-muted/50 text-blue-400 border border-border",
							)}
							onClick={(e) => {
								e.stopPropagation();
								onSave(option.value);
							}}
							onMouseEnter={() => setHoveredIndex(index)}
						>
							<span>{option.label}</span>
						</div>
					))
				) : (
					<div className="px-3 py-2 text-sm text-muted-foreground">
						No options found
					</div>
				)}
			</div>
		</div>
	);
}

function CompanyEditor({
	value,
	onSave,
	onCancel,
	organizationId,
	placeholder = "Find a company...",
}: {
	value: Company | null;
	onSave: (company: Company | null) => void;
	onCancel: () => void;
	organizationId: string;
	placeholder?: string;
}) {
	const [selectedCompany, setSelectedCompany] =
		React.useState<Company | null>(value);

	const handleSave = () => {
		onSave(selectedCompany);
	};

	const handleCompanyChange = (company: Company | null) => {
		setSelectedCompany(company);
		// Save immediately when company is selected
		onSave(company);
	};

	return (
		<div className="w-full bg-sidebar z-50 rounded-xl">
			<CompanySelector
				value={selectedCompany}
				onValueChange={handleCompanyChange}
				organizationId={organizationId}
				placeholder={placeholder}
				direct={true}
			/>
		</div>
	);
}

function TagsEditor({
	tags = [],
	objectType,
	objectId,
	onSave,
	onCancel,
	placeholder = "Search or create tag...",
}: {
	tags: Tag[];
	objectType: TaggableObjectType;
	objectId: string;
	onSave: (tags: Tag[]) => void;
	onCancel: () => void;
	placeholder?: string;
}) {
	const [value, setValue] = React.useState("");
	const [newTagColor, setNewTagColor] = React.useState(getRandomColor());

	const { data: objectTags, isLoading: isLoadingObjectTags } = useObjectTags(
		objectId,
		objectType
	);
	const { data: availableTags, isLoading: isLoadingTags } =
		useOrganizationTags(objectType);

	const addTag = useAddTag();
	const removeTag = useRemoveTag();
	const createTag = useCreateTag();

	const handleAddTag = async (tagId: string) => {
		try {
			await addTag.mutateAsync({ tagId, objectId, objectType });
			// Get updated tags after adding
			const updatedTags = [...(objectTags?.map(ot => ot.tag) || [])];
			const newTag = availableTags?.find(t => t.id === tagId);
			if (newTag && !updatedTags.find(t => t.id === tagId)) {
				updatedTags.push(newTag);
			}
			onSave(updatedTags as Tag[]);
		} catch (error) {
			console.error("Failed to add tag:", error);
		}
	};

	const handleRemoveTag = async (tagId: string) => {
		try {
			await removeTag.mutateAsync({ tagId, objectId, objectType });
			// Get updated tags after removing
			const updatedTags = (objectTags?.map(ot => ot.tag) || []).filter(t => t.id !== tagId);
			onSave(updatedTags as Tag[]);
		} catch (error) {
			console.error("Failed to remove tag:", error);
		}
	};

	const handleCreateTag = async () => {
		if (!value.trim()) return;

		const validation = validateTagName(value.trim());
		if (!validation.isValid) {
			console.error("Invalid tag name:", validation.error);
			return;
		}

		const normalizedName = normalizeTagName(value.trim());

		try {
			const tag = await createTag.mutateAsync({
				name: normalizedName,
				color: newTagColor,
				objectType,
			});
			await handleAddTag(tag.id);
			setValue("");
			setNewTagColor(getRandomColor());
		} catch (error) {
			console.error("Failed to create tag:", error);
		}
	};

	const handleTagToggle = async (tagId: string) => {
		try {
			const isTagged = objectTags?.some(ot => ot.tag.id === tagId);
			
			if (isTagged) {
				await handleRemoveTag(tagId);
			} else {
				await handleAddTag(tagId);
			}
		} catch (error) {
			console.error("Failed to toggle tag:", error);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && value.trim()) {
			e.preventDefault();
			handleCreateTag();
		} else if (e.key === "Escape") {
			e.preventDefault();
			e.stopPropagation();
			onCancel();
		}
	};

	if (isLoadingObjectTags || isLoadingTags) {
		return <div className="animate-pulse h-32 bg-muted rounded w-full" />;
	}

	const currentTags = objectTags?.map(ot => ot.tag) ?? [];
	const unusedTags = availableTags?.filter(
		(tag) => !objectTags?.some((ot) => ot.tag.id === tag.id)
	) ?? [];

	return (
		<div className="w-full bg-sidebar border border-border rounded-xl p-0">
			<Command>
				<CommandInput
					placeholder={placeholder}
					value={value}
					onValueChange={setValue}
					onKeyDown={handleKeyDown}
				/>
				<CommandList>
					<CommandEmpty className="py-6 text-center text-sm">
						{value.trim() ? (
							<div className="space-y-2 px-2">
								<p>Create new tag:</p>
								<div className="flex items-center gap-2">
									<ColorPicker
										value={newTagColor}
										onValueChange={setNewTagColor}
										className="h-6 w-6"
									/>
									<Badge
										variant="views"
										style={{
											backgroundColor: newTagColor,
											color: "#fff",
										}}
									>
										{normalizeTagName(value.trim())}
									</Badge>
									<Button
										size="sm"
										className="ml-auto h-7"
										onClick={handleCreateTag}
										disabled={!validateTagName(value.trim()).isValid}
									>
										<Plus className="mr-2 h-3 w-3" />
										Create
									</Button>
								</div>
								{!validateTagName(value.trim()).isValid && (
									<p className="text-xs text-destructive">
										{validateTagName(value.trim()).error}
									</p>
								)}
							</div>
						) : (
							"No tags found."
						)}
					</CommandEmpty>
					{(currentTags.length > 0 || unusedTags.length > 0) && (
						<CommandGroup>
							{/* Show current tags first */}
							{currentTags.map((tag) => (
								<CommandItem
									key={tag.id}
									value={tag.name}
									onSelect={() => handleTagToggle(tag.id)}
									className="flex items-center justify-between"
								>
									<div className="flex items-center gap-2">
										<div
											className="h-4 w-4 rounded-full"
											style={{ backgroundColor: tag.color || '#6b7280' }}
										/>
										<span>{tag.name}</span>
									</div>
									<IconSquareRoundedCheckFilled className="h-4 w-4 text-blue-400" />
								</CommandItem>
							))}
							{/* Then show unused tags */}
							{unusedTags.map((tag) => (
								<CommandItem
									key={tag.id}
									value={tag.name}
									onSelect={() => handleTagToggle(tag.id)}
									className="flex items-center justify-between"
								>
									<div className="flex items-center gap-2">
										<div
											className="h-4 w-4 rounded-full"
											style={{ backgroundColor: tag.color || '#6b7280' }}
										/>
										<span>{tag.name}</span>
									</div>
									<IconSquareRoundedCheckFilled className="h-4 w-4 text-blue-400" />
								</CommandItem>
							))}
						</CommandGroup>
					)}
				</CommandList>
			</Command>
		</div>
	);
}

// Validation functions
const isValidEmail = (email: string): boolean => {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
};

const isValidPhone = (phone: string): boolean => {
	// Remove all non-numeric characters for validation
	const cleanPhone = phone.replace(/\D/g, "");
	// Allow 10-15 digits (covers most international formats)
	return cleanPhone.length >= 10 && cleanPhone.length <= 15;
};

// Deep equality check for arrays
const arraysEqual = (a: any[], b: any[]): boolean => {
	if (a.length !== b.length) return false;
	return a.every((val, index) => {
		if (typeof val === "object" && typeof b[index] === "object") {
			return JSON.stringify(val) === JSON.stringify(b[index]);
		}
		return val === b[index];
	});
};

// Check if values have changed
const valuesChanged = (
	original: any,
	current: any,
	fieldType: string,
	arrayFieldType?: string,
): boolean => {
	if (fieldType === "array") {
		if (!Array.isArray(original) || !Array.isArray(current)) {
			return original !== current;
		}

		// Convert objects to string arrays for comparison
		let originalStrings: string[] = [];
		const currentStrings: string[] = current;

		if (arrayFieldType === "email") {
			originalStrings = original.map((item: any) =>
				typeof item === "string" ? item : item.address || "",
			);
		} else if (arrayFieldType === "phone") {
			originalStrings = original.map((item: any) =>
				typeof item === "string" ? item : item.number || "",
			);
		} else {
			originalStrings = original.map((item: any) => String(item));
		}

		return !arraysEqual(originalStrings, currentStrings);
	}

	return original !== current;
};

export function InlineCellEditor({
	value,
	onSave,
	fieldType = "text",
	arrayFieldType = "text",
	selectOptions = [],
	placeholder,
	isEditing,
	isSelected,
	onStartEdit,
	onStopEdit,
	onSelect,
	children,
	width,
	organizationId,
	objectType,
	objectId,
}: InlineCellEditorProps) {
	// Initialize editValue with proper conversion for array fields
	const getInitialEditValue = () => {
		if (fieldType === "array" && Array.isArray(value)) {
			if (arrayFieldType === "email") {
				return value.map((item: any) =>
					typeof item === "string" ? item : item.address || "",
				);
			}
			if (arrayFieldType === "phone") {
				return value.map((item: any) =>
					typeof item === "string" ? item : item.number || "",
				);
			}
			return value.map((item: any) => String(item));
		}
		return value;
	};

	const [editValue, setEditValue] = React.useState(getInitialEditValue);
	const [popoverOpen, setPopoverOpen] = React.useState(false);
	const inputRef = React.useRef<HTMLInputElement>(null);
	const textareaRef = React.useRef<HTMLTextAreaElement>(null);
	const cellRef = React.useRef<HTMLDivElement>(null);
	const [editorPosition, setEditorPosition] = React.useState<{
		top: number;
		left: number;
	}>({ top: 0, left: 0 });

	// Update editValue when value prop changes
	React.useEffect(() => {
		if (fieldType === "array" && Array.isArray(value)) {
			if (arrayFieldType === "email") {
				setEditValue(
					value.map((item: any) =>
						typeof item === "string" ? item : item.address || "",
					),
				);
			} else if (arrayFieldType === "phone") {
				setEditValue(
					value.map((item: any) =>
						typeof item === "string" ? item : item.number || "",
					),
				);
			} else {
				setEditValue(value.map((item: any) => String(item)));
			}
		} else {
			setEditValue(value);
		}
	}, [value, fieldType, arrayFieldType]);

	React.useEffect(() => {
		if (isEditing) {
			if (
				fieldType === "array" ||
				fieldType === "select" ||
				fieldType === "company" ||
				fieldType === "tags"
			) {
				setPopoverOpen(true);
			} else {
				// Focus input for non-array fields
				setTimeout(() => {
					if (inputRef.current) {
						inputRef.current.focus();
						inputRef.current.select();
					} else if (textareaRef.current) {
						textareaRef.current.focus();
						textareaRef.current.select();
					}
				}, 0);
			}
		} else {
			setPopoverOpen(false);
		}
	}, [isEditing, fieldType]);

	// Calculate position for popup editors
	React.useEffect(() => {
		if (
			popoverOpen &&
			(fieldType === "array" ||
				fieldType === "select" ||
				fieldType === "company" ||
				fieldType === "tags") &&
			cellRef.current
		) {
			const rect = cellRef.current.getBoundingClientRect();
			const viewportHeight = window.innerHeight;
			const editorHeight = 300; // Approximate height of the editor

			let top = rect.bottom + 4; // Position below the cell
			let left = rect.left;

			// If the editor would go below the viewport, position it above the cell
			if (top + editorHeight > viewportHeight) {
				top = rect.top - editorHeight - 4;
			}

			// Ensure it doesn't go off the left edge
			if (left < 8) {
				left = 8;
			}

			// Ensure it doesn't go off the right edge
			const editorWidth = 280;
			if (left + editorWidth > window.innerWidth - 8) {
				left = window.innerWidth - editorWidth - 8;
			}

			setEditorPosition({ top, left });
		}
	}, [popoverOpen, fieldType]);

	const saveWithItems = (itemsToSave: any) => {
		// Check if values have actually changed
		if (!valuesChanged(value, itemsToSave, fieldType, arrayFieldType)) {
			// Values haven't changed, just close the editor
			onStopEdit();
			setPopoverOpen(false);
			return;
		}

		// Convert string arrays back to object arrays for saving
		if (fieldType === "array" && Array.isArray(itemsToSave)) {
			if (arrayFieldType === "email") {
				const emailObjects = itemsToSave.map(
					(address: string, index: number) => ({
						address,
						label: "Work",
						isPrimary: index === 0,
						isBad: false,
					}),
				);
				onSave(emailObjects);
			} else if (arrayFieldType === "phone") {
				const phoneObjects = itemsToSave.map(
					(number: string, index: number) => ({
						number,
						label: "Work",
						isPrimary: index === 0,
						isBad: false,
					}),
				);
				onSave(phoneObjects);
			} else {
				onSave(itemsToSave);
			}
		} else {
			onSave(itemsToSave);
		}
		onStopEdit();
		setPopoverOpen(false);
	};

	const handleSave = () => {
		// For simple fields, add validation
		if (fieldType === "email" && editValue && !isValidEmail(editValue)) {
			toast.error("You have entered an invalid email address");
			return;
		}
		if (fieldType === "phone" && editValue && !isValidPhone(editValue)) {
			toast.error("You have entered an invalid phone number");
			return;
		}

		// Check if values have actually changed
		if (!valuesChanged(value, editValue, fieldType, arrayFieldType)) {
			// Values haven't changed, just close the editor
			onStopEdit();
			return;
		}

		saveWithItems(editValue);
	};

	const handleArraySave = (finalItems?: string[]) => {
		// For array fields, use finalItems if provided (from ArrayEditor), otherwise use editValue
		const itemsToSave = finalItems || editValue;
		saveWithItems(itemsToSave);
	};

	const handleCancel = () => {
		// Reset to original converted values
		if (fieldType === "array" && Array.isArray(value)) {
			if (arrayFieldType === "email") {
				setEditValue(
					value.map((item: any) =>
						typeof item === "string" ? item : item.address || "",
					),
				);
			} else if (arrayFieldType === "phone") {
				setEditValue(
					value.map((item: any) =>
						typeof item === "string" ? item : item.number || "",
					),
				);
			} else {
				setEditValue(value.map((item: any) => String(item)));
			}
		} else {
			setEditValue(value);
		}
		onStopEdit();
		setPopoverOpen(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && fieldType !== "textarea") {
			e.preventDefault();
			handleSave();
		} else if (e.key === "Escape") {
			e.preventDefault();
			handleCancel();
		}
	};

	// Render based on field type and editing state
	const renderContent = () => {
		// Handle array field editing
		if (fieldType === "array" && Array.isArray(value)) {
			return (
				<>
					<div
						ref={cellRef}
						onClick={onSelect}
						onDoubleClick={onStartEdit}
						className={cn(
							"cursor-default w-full h-full flex items-center px-2 py-1 transition-colors min-w-0",
							isSelected || isEditing
								? "border-l border-t border-r border-blue-500"
								: "",
							(isSelected || isEditing) && !popoverOpen
								? "border-b border-blue-500"
								: "",
							isEditing && "bg-blue-50/50",
						)}
					>
						<div className="w-full min-w-0 truncate">
							{children}
						</div>
					</div>
					{popoverOpen && (
						<>
							{/* Backdrop */}
							<div
								className="fixed inset-0 z-40"
								onClick={(e) => {
									e.stopPropagation();
									handleArraySave();
								}}
							/>
							{/* Array Editor */}
							<div
								className={cn("fixed z-50 border-t")}
								style={{
									top: editorPosition.top,
									left: editorPosition.left,
									width: width
										? `calc(${width}px - 1px)`
										: `calc(${width}px - 1px)`,
									minWidth: width
										? `calc(${width}px - 1px)`
										: `calc(${width}px - 1px)`,
								}}
								onClick={(e) => e.stopPropagation()}
							>
								<ArrayEditor
									items={editValue || []}
									onChange={setEditValue}
									onSave={handleArraySave}
									onCancel={handleCancel}
									fieldType={arrayFieldType}
									placeholder={`Add ${arrayFieldType === "email" ? "email address" : arrayFieldType === "phone" ? "phone number" : "item"}`}
								/>
							</div>
						</>
					)}
				</>
			);
		}

		// Handle tags field editing
		if (fieldType === "tags" && objectType && objectId) {
			return (
				<>
					<div
						ref={cellRef}
						onClick={onSelect}
						onDoubleClick={onStartEdit}
						className={cn(
							"cursor-default w-full h-full flex items-center px-2 py-1 transition-colors min-w-0",
							isSelected || isEditing
								? "border-l border-t border-r border-blue-500"
								: "",
							(isSelected || isEditing) && !popoverOpen
								? "border-b border-blue-500"
								: "",
							isEditing && "bg-blue-50/50",
						)}
					>
						<div className="w-full min-w-0">
							{/* Display tags using DataTableColumnTags when not editing */}
							<DataTableColumnTags
								objectType={objectType}
								objectId={objectId}
								tags={Array.isArray(value) ? value : []}
								readOnly={true}
							/>
						</div>
					</div>
					{popoverOpen && (
						<>
							{/* Backdrop */}
							<div
								className="fixed inset-0 z-40"
								onClick={(e) => {
									e.stopPropagation();
									handleCancel();
								}}
							/>
							{/* Tags Editor */}
							<div
								className={cn("fixed z-50")}
								style={{
									top: editorPosition.top,
									left: editorPosition.left,
									width: "320px",
									minWidth: "320px",
								}}
								onClick={(e) => e.stopPropagation()}
							>
								<TagsEditor
									tags={Array.isArray(value) ? value : []}
									objectType={objectType}
									objectId={objectId}
									onSave={(newTags) => {
										// For tags, we don't call onSave because tags are managed
										// through their own API endpoints within TagsEditor
										// Just close the editor - the tags are already updated
										onStopEdit();
										setPopoverOpen(false);
									}}
									onCancel={handleCancel}
									placeholder={placeholder || "Search or create tag..."}
								/>
							</div>
						</>
					)}
				</>
			);
		}

		if (fieldType === "select") {
			return (
				<>
					<div
						ref={cellRef}
						onClick={onSelect}
						onDoubleClick={onStartEdit}
						className={cn(
							"cursor-default w-full h-full flex items-center px-2 py-1 transition-colors min-w-0",
							isSelected || isEditing
								? "border border-blue-500"
								: "",
							isEditing && "bg-blue-50/50",
						)}
					>
						<div className="w-full min-w-0 truncate">
							{children}
						</div>
					</div>
					{popoverOpen && (
						<>
							{/* Backdrop */}
							<div
								className="fixed inset-0 z-40"
								onClick={(e) => {
									e.stopPropagation();
									handleCancel();
								}}
							/>
							{/* Select Editor */}
							<div
								className={cn("fixed z-50")}
								style={{
									top: editorPosition.top,
									left: editorPosition.left,
									width: "250px",
									minWidth: "300px",
								}}
								onClick={(e) => e.stopPropagation()}
							>
								<SelectEditor
									options={selectOptions}
									value={value || null}
									onSave={(newValue) => {
										// Check if value has changed before saving
										if (
											!valuesChanged(
												value,
												newValue,
												fieldType,
												arrayFieldType,
											)
										) {
											onStopEdit();
											setPopoverOpen(false);
											return;
										}
										onSave(newValue);
										onStopEdit();
										setPopoverOpen(false);
									}}
									onCancel={handleCancel}
									placeholder={placeholder || "Select option"}
								/>
							</div>
						</>
					)}
				</>
			);
		}

		// Handle company field editing
		if (fieldType === "company") {
			return (
				<>
					<div
						ref={cellRef}
						onClick={onSelect}
						onDoubleClick={onStartEdit}
						className={cn(
							"cursor-default w-full h-full flex items-center px-2 py-1 transition-colors min-w-0",
							isSelected || isEditing
								? "border border-blue-500"
								: "",
							isEditing && "bg-blue-50/50",
						)}
					>
						<div className="w-full min-w-0 truncate">
							{children}
						</div>
					</div>
					{popoverOpen && (
						<>
							{/* Backdrop */}
							<div
								className="fixed inset-0 z-40"
								onClick={(e) => {
									e.stopPropagation();
									handleCancel();
								}}
							/>
							{/* Company Editor */}
							<div
								className={cn("fixed z-50 rounded-xl")}
								style={{
									top: editorPosition.top,
									left: editorPosition.left,
									width: "250px",
									minWidth: "300px",
								}}
								onClick={(e) => e.stopPropagation()}
							>
								<CompanyEditor
									value={value as Company | null}
									onSave={(newCompany) => {
										// Check if value has changed before saving
										const companyChanged =
											value?.id !== newCompany?.id;
										if (
											!companyChanged &&
											value !== null &&
											newCompany !== null
										) {
											onStopEdit();
											setPopoverOpen(false);
											return;
										}
										onSave(newCompany);
										onStopEdit();
										setPopoverOpen(false);
									}}
									onCancel={handleCancel}
									organizationId={organizationId || ""}
									placeholder={
										placeholder || "Find a company..."
									}
								/>
							</div>
						</>
					)}
				</>
			);
		}

		if (isEditing) {
			if (fieldType === "textarea") {
				return (
					<div className="w-full h-full flex border border-blue-500">
						<Textarea
							ref={textareaRef}
							value={editValue || ""}
							onChange={(e) => setEditValue(e.target.value)}
							onBlur={handleSave}
							onKeyDown={(e) => {
								if (e.key === "Escape") {
									e.preventDefault();
									handleCancel();
								}
								// Note: For textarea, Enter creates new line, so we don't handle it for save
							}}
							placeholder={placeholder}
							className="border-0 rounded-none px-2 bg-transparent w-full h-full resize-none focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
							rows={3}
						/>
					</div>
				);
			}
			const inputType =
				fieldType === "email"
					? "email"
					: fieldType === "phone"
						? "tel"
						: fieldType === "number"
							? "number"
							: "text";

			return (
				<div className="w-full h-full flex border border-blue-500">
					<Input
						ref={inputRef}
						type={inputType}
						value={editValue || ""}
						onChange={(e) => setEditValue(e.target.value)}
						onBlur={handleSave}
						onKeyDown={handleKeyDown}
						placeholder={placeholder}
						className="border-0 rounded-none px-2 bg-transparent w-full h-full focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-0"
					/>
				</div>
			);
		}

		return (
			<div
				onClick={onSelect}
				onDoubleClick={onStartEdit}
				className={cn(
					"cursor-default w-full h-full flex items-center px-2 py-1 transition-colors min-w-0 ",
					isSelected
						? "border border-blue-500 focus-visible:border-blue-500 bg-blue-500/5"
						: "",
				)}
			>
				<div className="w-full min-w-0 truncate">{children}</div>
			</div>
		);
	};

	return renderContent();
}
