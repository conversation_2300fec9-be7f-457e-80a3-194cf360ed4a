"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { AppHeader } from "@app/shared/components/AppHeader";
import { AppSidebar } from "@app/shared/components/sidebar/AppSidebar";
import { KanbanProvider } from "@app/tasks/lib/kanban-provider";
import type { Organization, User } from "@repo/database";
import { SidebarInset, SidebarProvider } from "@ui/components/sidebar";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import type { PropsWithChildren } from "react";

export function AppWrapper({ children }: PropsWithChildren) {
	const tS = useTranslations("organizations.settings.sidebar");
	const t = useTranslations();
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization() as {
		activeOrganization: Organization | null;
	};

	const messages = {
		searchSettings: tS("searchSettings"),
		account: {
			heading: tS("account.heading"),
			title: tS("account.title"),
			changeEmail: tS("account.changeEmail"),
			changePassword: tS("account.changePassword"),
			deleteAccount: tS("account.deleteAccount"),
			notifications: tS("account.notifications"),
			emailNotifications: tS("account.emailNotifications"),
			dangerZone: t("settings.menu.account.dangerZone"),
			emailCalendarAccounts: {
				title: tS("account.emailCalendarAccounts.title"),
				connectedAccounts: tS(
					"account.emailCalendarAccounts.connectedAccounts",
				),
				forwardingAddress: tS(
					"account.emailCalendarAccounts.forwardingAddress",
				),
				shareAccess: tS("account.emailCalendarAccounts.shareAccess"),
				watermark: tS("account.emailCalendarAccounts.watermark"),
				blocklist: tS("account.emailCalendarAccounts.blocklist"),
			},
		},
		appearance: {
			heading: "Appearance",
			title: "Appearance Settings",
			theme: "Theme",
			language: "Language",
		},
		ai: {
			heading: "AI",
			title: "AI Settings",
			credits: "Credits",
			usage: "Usage Analytics",
			purchase: "Purchase Credits",
			settings: "Settings",
		},
		data: {
			heading: "Data",
			tags: "Tags",
			managePermissions: "Manage Permissions",
		},
		organization: {
			heading: tS("organization.heading"),
			general: tS("organization.general"),
			members: tS("organization.members"),
			billing: tS("organization.billing"),
			plans: tS("organization.plans"),
			paymentMethods: tS("organization.paymentMethods"),
		},
		security: {
			heading: tS("security.heading"),
			title: tS("security.title"),
		},
	};

	const pathname = usePathname();
	const isSettingsPage = pathname?.includes("/settings");

	const defaultOpen = true;

	return (
		<KanbanProvider>
			<SidebarProvider defaultOpen={defaultOpen}>
				<AppSidebar
					messages={messages}
					organization={activeOrganization}
				/>
				<SidebarInset className="h-screen flex flex-col overflow-hidden">
					{!isSettingsPage && (
						<AppHeader
							user={user as User}
							organizationId={activeOrganization?.id}
							organiationSlug={activeOrganization?.slug || ''}
						/>
					)}
					<div
						className={`flex-1 ${isSettingsPage ? "overflow-y-auto" : "overflow-hidden"}`}
					>
						{children}
					</div>
				</SidebarInset>
			</SidebarProvider>
		</KanbanProvider>
	);
}
