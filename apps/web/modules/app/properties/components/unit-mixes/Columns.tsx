import React from 'react';

import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "./ColumnHeader";
import { DataTableRowActions } from "./RowActions";
import { Checkbox } from "@ui/components/checkbox";
import { formatCompactNumber, formatCurrency } from '@app/organizations/lib/format';
import type { UnitMix } from "@app/unit-mixes/lib/api";

export const columns: ColumnDef<UnitMix>[] = [
	// {
	// 	id: "select",
	// 	header: ({ table }) => (
	// 		<Checkbox
	// 			checked={
	// 				table.getIsAllPageRowsSelected() ||
	// 				(table.getIsSomePageRowsSelected() && "indeterminate")
	// 			}
	// 			onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
	// 			aria-label="Select row"
	// 			className="translate-y-[2px] !h-4 !w-4"
	// 		/>
	// 	),
	// 	cell: ({ row }) => (
	// 		<Checkbox
	// 			checked={row.getIsSelected()}
	// 			onClick={(e) => {
	// 				e.stopPropagation()
	// 			}}
	// 			onCheckedChange={(value) => row.toggleSelected(!!value)}
	// 			aria-label="Select row"
	// 			className="translate-y-[2px] !h-4 !w-4"
	// 		/>
	// 	),
	// 	enableSorting: false,
	// 	enableHiding: false,
	// },
	{
		accessorKey: "name",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Name" />
		),
		cell: ({ row }) => {
			return (
				<div className="flex flex-row items-center space-x-2 px-2">
					<span className="max-w-[500px] truncate font-medium cursor-pointer">
						{row.original.name}
					</span>
				</div>
			)
		},
	},
	{
		accessorKey: "units",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Units" />
		),
		cell: ({ row }) => {
			const units = row?.original?.units;
			return (
				<div className="flex items-center">
					<span>{units != null ? formatCompactNumber(units) : '-'}</span>
				</div>
			)
		},
	},
	{
		accessorKey: "minSquareFootage",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Min Sqft" />
		),
		cell: ({ row }) => {
			const minSqft = row?.original?.minSquareFootage;
			return (
				<div className="flex items-center">
					<span>{minSqft != null ? formatCompactNumber(minSqft) : '-'}</span>
				</div>
			)
		},
	},
	{
		accessorKey: "maxSquareFootage",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Max Sqft" />
		),
		cell: ({ row }) => {
			const maxSqft = row?.original?.maxSquareFootage;
			return (
				<span>{maxSqft != null ? formatCompactNumber(maxSqft) : '-'}</span>
			)
		},
	},
	{
		accessorKey: "minPrice",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Min Price" />
		),
		cell: ({ row }) => {
			const minPrice = row?.original?.minPrice;
			return (
				<span>{minPrice != null ? formatCurrency(minPrice) : '-'}</span>
			)
		},
	},
	{
		accessorKey: "maxPrice",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Max Price" />
		),
		cell: ({ row }) => {
			const maxPrice = row?.original?.maxPrice;
			return (
				<span>{maxPrice != null ? formatCurrency(maxPrice) : '-'}</span>
			)
		},
	},
	{
		accessorKey: "minRent",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Min Rent" />
		),
		cell: ({ row }) => {
			const minRent = row?.original?.minRent;
			return (
				<span>{minRent != null ? formatCurrency(minRent) : '-'}</span>
			)
		},
	},
	{
		accessorKey: "maxRent",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Max Rent" />
		),
		cell: ({ row }) => {
			const maxRent = row?.original?.maxRent;
			return (
				<span>{maxRent != null ? formatCurrency(maxRent) : '-'}</span>
			)
		},
	},
	{
		id: "actions",
		cell: ({ row }) => <DataTableRowActions row={row} />,
	},
]