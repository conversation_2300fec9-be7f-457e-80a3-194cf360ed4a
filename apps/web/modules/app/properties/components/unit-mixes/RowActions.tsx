"use client"

import { Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from "@ui/components/dropdown-menu"
import { IconDots } from "@tabler/icons-react";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
   row,
}: DataTableRowActionsProps<TData>) {
    const unitMix: any = row.original;
    const { activeOrganization } = useActiveOrganization();
  
    const handleDelete = async () => {
        console.log("Delete unit mix")
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    className="flex !h-6 !w-6 p-0 data-[state=open]:bg-muted"
                >
                    <IconDots className="h-3 w-3" />
                    <span className="sr-only">Open menu</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
                <DropdownMenuItem
                    onClick={(e) => {
                        e.stopPropagation()
                        navigator.clipboard.writeText(unitMix.name)
                        toast.success("Copied to clipboard")
                    }}
                >
                    Copy Name
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    className='bg-red-700 hover:bg-red-900 text-foreground rounded-lg'
                    onClick={(e) => {
                        e.stopPropagation()
                        e.preventDefault()

                        handleDelete()
                    }}
                >
                    Delete
                    <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
