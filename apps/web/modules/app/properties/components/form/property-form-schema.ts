import { z } from "zod";

// --- Schema ---
const preprocessNumber = (val: unknown) => (val === "" || val === null || Number.isNaN(val)) ? undefined : val;

export const propertySchema = z.object({
	// Basic Info
	name: z.string().min(1, "Property name is required"),
	image: z.string().optional(),
	
	// Property Classification
	propertyType: z.string().optional(),
	propertySubType: z.string().optional(),
	market: z.string().optional(),
	subMarket: z.string().optional(),
	listingId: z.string().optional(),
	status: z.string().optional(),
	
	// REA (Real Estate API) Integration
	reaId: z.string().optional(),
	
	// Simple relationships
	tags: z.array(z.string()).default([]),
	lists: z.array(z.string()).default([]),
	linkedContacts: z.array(z.string()).default([]),
	linkedCompanies: z.array(z.string()).default([]),
	tasks: z.array(z.string()).default([]),
	
	// Address - compatible with AddressField component
	address: z
		.object({
			street: z.string().optional(),
			street2: z.string().optional(), // Keep for backward compatibility
			city: z.string().optional(),
			state: z.string().optional(), // Keep for additional state info
			postalCode: z.string().optional(), // Maps to zip
			zip: z.string().optional(), // Keep for backward compatibility
			county: z.string().optional(), // Keep for additional county info
			country: z.string().optional(),
			fullAddress: z.string().optional(), // Required by REA AddressField
			reaId: z.string().optional(), // REA property ID from autocomplete
		})
		.refine(
			(addr) =>
				!!(
					addr.street ||
					addr.city ||
					addr.postalCode ||
					addr.zip ||
					addr.fullAddress
				),
			{
				message: "At least one address field is required",
				path: ["fullAddress"],
			},
		),
	
	// Location & Geographic
	website: z.string().optional(),
	subdivision: z.string().optional(),
	lotNumber: z.string().optional(),
	parcelNumber: z.string().optional(),
	zoning: z.string().optional(),
	
	// Physical Details
	yearBuilt: z.preprocess(preprocessNumber, z.number().optional()),
	squareFootage: z.preprocess(preprocessNumber, z.number().optional()),
	units: z.preprocess(preprocessNumber, z.number().optional()),
	floors: z.preprocess(preprocessNumber, z.number().optional()),
	structures: z.preprocess(preprocessNumber, z.number().optional()),
	bedrooms: z.preprocess(preprocessNumber, z.number().optional()),
	bathrooms: z.preprocess(preprocessNumber, z.number().optional()),
	roomsCount: z.preprocess(preprocessNumber, z.number().optional()),
	buildingSquareFeet: z.preprocess(preprocessNumber, z.number().optional()),
	garageSquareFeet: z.preprocess(preprocessNumber, z.number().optional()),
	livingSquareFeet: z.preprocess(preprocessNumber, z.number().optional()),
	lotSquareFeet: z.preprocess(preprocessNumber, z.number().optional()),
	lotSize: z.preprocess(preprocessNumber, z.number().optional()),
	lotType: z.string().optional(),
	lotAcres: z.preprocess(preprocessNumber, z.number().optional()),
	construction: z.string().optional(),
	primaryUse: z.string().optional(),
	propertyUse: z.string().optional(),
	class: z.string().optional(),
	parking: z.string().optional(),
	parkingSpaces: z.preprocess(preprocessNumber, z.number().optional()),
	garageType: z.string().optional(),
	heatingType: z.string().optional(),
	meterType: z.string().optional(),
	legalDescription: z.string().optional(),
	
	// Financial Information
	price: z.preprocess(preprocessNumber, z.number().optional()),
	estimatedValue: z.preprocess(preprocessNumber, z.number().optional()),
	pricePerSquareFoot: z.preprocess(preprocessNumber, z.number().optional()),
	equity: z.preprocess(preprocessNumber, z.number().optional()),
	equityPercent: z.preprocess(preprocessNumber, z.number().optional()),
	estimatedEquity: z.preprocess(preprocessNumber, z.number().optional()),
	saleDate: z.coerce.date().optional(),
	salePrice: z.preprocess(preprocessNumber, z.number().optional()),
	lastSalePrice: z.preprocess(preprocessNumber, z.number().optional()),
	lastSaleDate: z.coerce.date().optional(),
	landValue: z.preprocess(preprocessNumber, z.number().optional()),
	buildingValue: z.preprocess(preprocessNumber, z.number().optional()),
	cap: z.preprocess(preprocessNumber, z.number().optional()),
	exchange: z.boolean().optional(),
	exchangeId: z.string().optional(),
	
	// Boolean Flags
	absenteeOwner: z.boolean().default(false),
	inStateAbsenteeOwner: z.boolean().default(false),
	outOfStateAbsenteeOwner: z.boolean().default(false),
	ownerOccupied: z.boolean().default(false),
	corporateOwned: z.boolean().default(false),
	vacant: z.boolean().default(false),
	mobileHome: z.boolean().default(false),
	carport: z.boolean().default(false),
	auction: z.boolean().default(false),
	cashBuyer: z.boolean().default(false),
	investorBuyer: z.boolean().default(false),
	freeClear: z.boolean().default(false),
	highEquity: z.boolean().default(false),
	privateLender: z.boolean().default(false),
	deedInLieu: z.boolean().default(false),
	quitClaim: z.boolean().default(false),
	sheriffsDeed: z.boolean().default(false),
	warrantyDeed: z.boolean().default(false),
	inherited: z.boolean().default(false),
	spousalDeath: z.boolean().default(false),
	lien: z.boolean().default(false),
	taxLien: z.boolean().default(false),
	preForeclosure: z.boolean().default(false),
	trusteeSale: z.boolean().default(false),
	floodZone: z.boolean().default(false),
	
	// MLS Data
	mlsActive: z.boolean().default(false),
	mlsCancelled: z.boolean().default(false),
	mlsFailed: z.boolean().default(false),
	mlsHasPhotos: z.boolean().default(false),
	mlsPending: z.boolean().default(false),
	mlsSold: z.boolean().default(false),
	mlsDaysOnMarket: z.preprocess(preprocessNumber, z.number().optional()),
	mlsListingPrice: z.preprocess(preprocessNumber, z.number().optional()),
	mlsListingPricePerSquareFoot: z.preprocess(preprocessNumber, z.number().optional()),
	mlsSoldPrice: z.preprocess(preprocessNumber, z.number().optional()),
	mlsStatus: z.string().optional(),
	mlsType: z.string().optional(),
	mlsListingDate: z.string().optional(),
	
	// Legal & Environmental
	floodZoneDescription: z.string().optional(),
	floodZoneType: z.string().optional(),
	noticeType: z.string().optional(),
	lastUpdateDate: z.string().optional(),
	
	// Demographics
	fmrEfficiency: z.preprocess(preprocessNumber, z.number().optional()),
	fmrFourBedroom: z.preprocess(preprocessNumber, z.number().optional()),
	fmrOneBedroom: z.preprocess(preprocessNumber, z.number().optional()),
	fmrThreeBedroom: z.preprocess(preprocessNumber, z.number().optional()),
	fmrTwoBedroom: z.preprocess(preprocessNumber, z.number().optional()),
	fmrYear: z.preprocess(preprocessNumber, z.number().optional()),
	hudAreaCode: z.string().optional(),
	hudAreaName: z.string().optional(),
	medianIncome: z.preprocess(preprocessNumber, z.number().optional()),
	suggestedRent: z.preprocess(preprocessNumber, z.number().optional()),
	
	// Complex nested data from REA API
	reaApiData: z.any().optional(),
	
	// Legacy/Optional fields
	description: z.string().optional(),
	customFields: z.record(z.string(), z.any()).optional(),
});

export type PropertyFormData = z.infer<typeof propertySchema>;

export interface CreatePropertyModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	initialValues?: Record<string, any>;
} 