"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { IconMapPin, IconX } from "@tabler/icons-react";
import { Input } from "@ui/components/input";
import { <PERSON><PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@ui/components/command";
import { GeocodeStatus } from "./GeocodeStatus";
import type { PropertyFormData } from "../property-form-schema";
import type { GeocodeResult } from "../../../lib/geocoding";
import type { AutocompleteResult } from "@app/organizations/components/objects/views/map/types";
import { fetchAutocomplete } from "@app/organizations/components/objects/views/map/api";

interface AddressFormProps {
	form: UseFormReturn<PropertyFormData>;
	coordinates: [number, number] | null;
	isGeocoding: boolean;
	geocodeResult: GeocodeResult | null;
}

export function AddressForm({
	form,
	coordinates,
	isGeocoding,
	geocodeResult,
}: AddressFormProps) {
	// Address autocomplete state
	const [addressAutocomplete, setAddressAutocomplete] = useState("");
	const [addressSuggestions, setAddressSuggestions] = useState<AutocompleteResult[]>([]);
	const [showAddressSuggestions, setShowAddressSuggestions] = useState(false);
	const [addressLoading, setAddressLoading] = useState(false);
	const [showManualEntry, setShowManualEntry] = useState(false);
	const [addressSelected, setAddressSelected] = useState(false);

	const addressAutocompleteRef = useRef<HTMLInputElement>(null);
	const isTypingRef = useRef(false);

	// Fetch address suggestions with debouncing
	useEffect(() => {
		let ignore = false;
		let timeoutId: NodeJS.Timeout;

		if (addressAutocomplete.length < 3) {
			setAddressSuggestions([]);
			setShowAddressSuggestions(false);
			return;
		}

		// Only fetch if user is actively typing
		if (!isTypingRef.current) return;

		setAddressLoading(true);
		
		timeoutId = setTimeout(() => {
			fetchAutocomplete({ query: addressAutocomplete })
				.then((results) => {
					if (!ignore) {
						setAddressSuggestions(results);
						setShowAddressSuggestions(true);
					}
				})
				.catch((error) => {
					console.error("Address autocomplete error:", error);
					if (!ignore) {
						setAddressSuggestions([]);
						setShowAddressSuggestions(false);
					}
				})
				.finally(() => {
					if (!ignore) {
						setAddressLoading(false);
					}
				});
		}, 300); // Debounce the API call

		return () => {
			ignore = true;
			clearTimeout(timeoutId);
		};
	}, [addressAutocomplete]);

	// Handle address autocomplete input changes
	const handleAddressAutocompleteChange = useCallback((value: string) => {
		isTypingRef.current = true;
		setAddressAutocomplete(value);
		
		// Clear the typing flag after a delay
		setTimeout(() => {
			isTypingRef.current = false;
		}, 500);
	}, []);

	// Autofill handler
	const handleAddressSuggestionSelect = useCallback((suggestion: AutocompleteResult) => {
		form.setValue("address.street", suggestion.address || "");
		form.setValue("address.city", suggestion.city || "");
		form.setValue("address.state", suggestion.state || "");
		form.setValue("address.zip", suggestion.zip || "");
		setAddressAutocomplete(suggestion.address || ""); // Show selected address in input
		setShowAddressSuggestions(false);
		setAddressSelected(true);
		isTypingRef.current = false;
	}, [form]);

	const clearAutocomplete = useCallback(() => {
		setAddressAutocomplete("");
		setAddressSuggestions([]);
		setShowAddressSuggestions(false);
		setAddressSelected(false);
		setShowManualEntry(false);
		form.setValue("address.street", "");
		form.setValue("address.street2", "");
		form.setValue("address.city", "");
		form.setValue("address.state", "");
		form.setValue("address.zip", "");
		form.setValue("address.county", "");
		form.setValue("address.country", "");
		isTypingRef.current = false;
	}, [form]);

	const handleManualEntry = useCallback(() => {
		setShowManualEntry(true);
		setAddressSelected(true);
		setShowAddressSuggestions(false);
		isTypingRef.current = false;
	}, []);

	return (
		<div className="space-y-4">
			{/* Address Section with Autocomplete */}
			<div className="space-y-1">
				<h3 className="text-xs text-muted-foreground">
					Address
					<span className="text-red-500 ml-1">*</span>
				</h3>
				<Popover open={showAddressSuggestions} onOpenChange={setShowAddressSuggestions}>
					<PopoverTrigger asChild>
						<div className="relative">
							<Input
								ref={addressAutocompleteRef}
								value={addressAutocomplete}
								onChange={(e) => handleAddressAutocompleteChange(e.target.value)}
								placeholder="Start typing an address..."
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50 pr-8"
								autoComplete="off"
								onFocus={() => {
									if (addressSuggestions.length > 0) setShowAddressSuggestions(true);
								}}
							/>
							{addressAutocomplete && (
								<button
									type="button"
									className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
									onClick={clearAutocomplete}
									tabIndex={-1}
								>
									<IconX className="h-4 w-4" />
								</button>
							)}
						</div>
					</PopoverTrigger>
					<PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
						<Command className="h-auto">
							<CommandList className="max-h-[200px] p-1">
								{addressLoading && <div className="p-3 text-sm text-muted-foreground">Loading...</div>}
								{!addressLoading && addressSuggestions.length === 0 && (
									<div className="p-3 text-sm text-muted-foreground">No suggestions found</div>
								)}
								{addressSuggestions.map((suggestion, idx) => (
									<CommandItem
										key={suggestion.address + idx}
										value={suggestion.address}
										onSelect={() => handleAddressSuggestionSelect(suggestion)}
										className="cursor-pointer !p-2 rounded-md hover:bg-secondary/10"
									>
										<div className="flex flex-col">
											<div className="font-medium text-sm">{suggestion.address}</div>
										</div>
									</CommandItem>
								))}
							</CommandList>
						</Command>
					</PopoverContent>
				</Popover>

				<div className="flex justify-end">
					<button
						type="button"
						onClick={handleManualEntry}
						className="text-xs text-blue-600 hover:text-blue-800 underline"
					>
						Enter manually
					</button>
				</div>

				{/* Geocoding Status */}
				<GeocodeStatus isGeocoding={isGeocoding} geocodeResult={geocodeResult} />
			</div>

			{/* Manual Address Fields */}
			{(addressSelected || showManualEntry) && (
				<div className="space-y-3">
					<div className="flex items-center justify-between">
						<h3 className="text-sm font-medium">
							Address Details <span className="text-red-500">*</span>
						</h3>
						<button
							type="button"
							onClick={() => {
								setAddressSelected(false);
								setShowManualEntry(false);
								clearAutocomplete();
							}}
							className="text-xs text-blue-600 hover:text-blue-800 underline"
						>
							Use autocomplete
						</button>
					</div>

					{/* Show selected address if from autocomplete */}
					{addressSelected && !showManualEntry && addressAutocomplete && (
						<div className="p-2 bg-green-50 border border-green-200 rounded-md">
							<div className="text-sm font-medium text-green-800">{addressAutocomplete}</div>
							<div className="text-xs text-green-600">Address selected from suggestions</div>
						</div>
					)}

					<div className="space-y-2">
						<Input
							{...form.register("address.street", { required: "Street address is required" })}
							placeholder="Street address"
							className={cn(
								"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
								form.formState.errors.address?.street && "border-red-500"
							)}
						/>
						{form.formState.errors.address?.street && (
							<div className="text-xs text-red-500">{form.formState.errors.address.street.message}</div>
						)}

						<Input 
							{...form.register("address.street2")} 
							placeholder="Unit, suite, etc. (optional)" 
							className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
						/>

						<div className="grid grid-cols-2 gap-2">
							<div>
								<Input
									{...form.register("address.city", { required: "City is required" })}
									placeholder="City"
									className={cn(
										"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
										form.formState.errors.address?.city && "border-red-500"
									)}
								/>
								{form.formState.errors.address?.city && (
									<div className="text-xs text-red-500 mt-1">{form.formState.errors.address.city.message}</div>
								)}
							</div>
							<div>
								<Input
									{...form.register("address.state", { required: "State is required" })}
									placeholder="State"
									className={cn(
										"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
										form.formState.errors.address?.state && "border-red-500"
									)}
								/>
								{form.formState.errors.address?.state && (
									<div className="text-xs text-red-500 mt-1">{form.formState.errors.address.state.message}</div>
								)}
							</div>
						</div>

						<div className="grid grid-cols-2 gap-2">
							<div>
								<Input
									{...form.register("address.zip", { required: "ZIP code is required" })}
									placeholder="ZIP code"
									className={cn(
										"cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50",
										form.formState.errors.address?.zip && "border-red-500"
									)}
								/>
								{form.formState.errors.address?.zip && (
									<div className="text-xs text-red-500 mt-1">{form.formState.errors.address.zip.message}</div>
								)}
							</div>
							<Input 
								{...form.register("address.country")} 
								placeholder="Country" 
								defaultValue="United States" 
								className="cursor-pointer hover:!bg-secondary/10 focus:cursor-text focus:!bg-muted/50"
							/>
						</div>
					</div>

					{/* Coordinates display */}
					{coordinates && (
						<div className="text-xs text-muted-foreground bg-muted p-2 rounded-md">
							<div className="flex items-center gap-1">
								<IconMapPin className="h-3 w-3" />
								<span>
									Coordinates: {coordinates[1].toFixed(6)}, {coordinates[0].toFixed(6)}
								</span>
							</div>
						</div>
					)}
				</div>
			)}
		</div>
	);
} 