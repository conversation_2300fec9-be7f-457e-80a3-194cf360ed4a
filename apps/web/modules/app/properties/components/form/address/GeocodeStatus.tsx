import { Icon<PERSON>heck, IconLoader2, IconX } from "@tabler/icons-react";
import type { GeocodeResult } from "../../../lib/geocoding";

interface GeocodeStatusProps {
	isGeocoding: boolean;
	geocodeResult: GeocodeResult | null;
}

export function GeocodeStatus({
	isGeocoding,
	geocodeResult,
}: GeocodeStatusProps) {
	if (isGeocoding) {
		return (
			<div className="flex items-center gap-2 text-sm text-muted-foreground">
				<IconLoader2 className="h-4 w-4 animate-spin" />
				<span>Finding location...</span>
			</div>
		);
	}

	if (geocodeResult?.success) {
		return (
			<div className="flex items-center gap-2 text-sm text-green-600">
				<IconCheck className="h-4 w-4" />
				<span>Location found</span>
				{geocodeResult.confidence && geocodeResult.confidence < 0.8 && (
					<span className="text-xs text-yellow-600">
						(Low confidence)
					</span>
				)}
			</div>
		);
	}

	if (geocodeResult?.error) {
		return (
			<div className="flex items-center gap-2 text-sm text-red-600">
				<IconX className="h-4 w-4" />
				<span>Could not find location</span>
			</div>
		);
	}

	return null;
} 