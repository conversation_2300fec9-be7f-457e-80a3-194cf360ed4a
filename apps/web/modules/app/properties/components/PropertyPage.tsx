"use client";

import React, { useState } from "react";
import { useProperty, useUpdateProperty } from "@app/properties/lib/api";
import PageHeader from "@shared/components/PageHeader";
import ReorderList from "@ui/components/reorder-list";
import GridItemContainer from "@ui/components/grid-item-container";
import type { ActiveOrganization } from "@repo/auth";
import { TagSelector } from "@app/shared/components/TagSelector";
import { MoreInfo } from "@app/shared/components/MoreInfo";
import { Activities } from "@app/activities";
import { useSession } from "@app/auth/hooks/use-session";
import { useContactProperties } from "@shared/hooks/useContactProperties";
import { useLinkedContacts } from "@shared/hooks/useLinkedContacts";
import { IconPlus } from "@tabler/icons-react";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import { createNote } from "@app/notes/lib/api";
import { useQueryClient } from "@tanstack/react-query";

interface PropertyPageProps {
	id: string;
	activeOrganization: ActiveOrganization | null;
	isFavorite: boolean;
	onNavigate?: (direction: "prev" | "next") => void;
	hasPrevious?: boolean;
	hasNext?: boolean;
}

const PropertyPage = ({
	id,
	activeOrganization,
	isFavorite,
	onNavigate,
	hasPrevious,
	hasNext,
}: PropertyPageProps) => {
	const { data: property } = useProperty(id, activeOrganization?.id);
	const { mutateAsync: updateProperty } = useUpdateProperty(activeOrganization?.id);
	const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
	const { user } = useSession();
	const { openCreateTask } = useTasks();
	const queryClient = useQueryClient();

	if (!activeOrganization) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-muted-foreground">Loading...</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-[calc(100vh-50px)]">
			{/* Header */}
			<PageHeader
				activeOrganization={activeOrganization}
				data={property}
				isFavorite={isFavorite}
				objectType="property"
				onTitleUpdate={() => {}}
				onNavigate={onNavigate}
				hasPrevious={hasPrevious}
				hasNext={hasNext}
			/>

		{/* Main Content */}
		<div className="flex flex-1 overflow-hidden">
			<div className="flex-1 flex flex-col overflow-hidden">
				<Activities data={property} organization={activeOrganization} user={user} recordType="property" />
			</div>

			{/* Right Column - 1/3 width */}
			<div className="w-1/3 flex flex-col overflow-hidden border-l">
				{/* Right column content will go here */}
			</div>
		</div>
		</div>
	);
};

export default PropertyPage;