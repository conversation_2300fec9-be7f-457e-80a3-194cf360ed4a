import { Label } from '@ui/components/label'
import { DetailInput } from "@ui/components/input";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@ui/components/tooltip"
import { IconCopy } from "@tabler/icons-react";
import { Button } from '@ui/components/button';
import { toast } from "sonner";
import { cn } from "@ui/lib";
import React, { useState } from "react";
import { ADDRESS_FIELDS, ADDRESS_LABELS } from "@app/organizations/components/objects/constants";

interface GeoLocation {
  type: string;
  coordinates: number[];
}

type PropertyDetailsProps = {
  selectedProperty: any
};

type AddressFieldKey = keyof typeof ADDRESS_FIELDS;
type AddressField = typeof ADDRESS_FIELDS[AddressFieldKey];

const PropertyDetails = ({ selectedProperty }: PropertyDetailsProps) => {
  const [editingField, setEditingField] = useState<string | null>(null);
  
  const getAddressLabel = (addressKey: string): string => {
    const entry = Object.entries(ADDRESS_FIELDS).find(([_, val]) => val === addressKey);
    if (entry) {
      const [key] = entry;
      return ADDRESS_LABELS[ADDRESS_FIELDS[key as AddressFieldKey]];
    }
    return addressKey;
  };

  const renderSection = (title: string, data: any) => {
    if (!data || Object.keys(data).length === 0) return null;

    const filteredData = Object.entries(data).reduce((acc, [key, value]) => {
      // Skip specific fields we don't want to show
      if (
        key === '_id' || 
        key === 'propertyId' || 
        key === 'createdAt' || 
        key === 'updatedAt' ||
        key === 'isDeleted' ||
        key === 'createdBy' ||
        value === null || 
        value === undefined || 
        value === ''
      ) {
        return acc;
      }

      // Handle nested objects (like address)
      if (typeof value === 'object' && !Array.isArray(value)) {
        if (key === 'location' && (value as GeoLocation).type === 'Point') {
          const geoLocation = value as GeoLocation;
          acc['latitude'] = geoLocation.coordinates[1];
          acc['longitude'] = geoLocation.coordinates[0];
        } else if (key === 'address') {
          // Use address field constants
          Object.entries(value).forEach(([addressKey, addressValue]) => {
            if (addressValue && addressValue !== '') {
              acc[getAddressLabel(addressKey)] = addressValue;
            }
          });
        } else {
          Object.entries(value).forEach(([subKey, subValue]) => {
            if (subValue && subValue !== '') {
              acc[`${key}_${subKey}`] = subValue;
            }
          });
        }
      } else {
        acc[key] = value;
      }

      return acc;
    }, {} as Record<string, any>);

    if (Object.keys(filteredData).length === 0) return null;

    return (
      <div className="border-b border-border last:border-0">
        <div className="flex items-center justify-between border-b border-r border-muted bg-background z-50 sticky top-0">
          <div className="flex items-center hover:text-accent-foreground p-2">
            <Label className="uppercase text-[10px]">{title}</Label>
          </div>
          <Tooltip>
            <TooltipTrigger>
              <Button
                onClick={() => copyToClipboard(filteredData, title)}
                variant="ghost"
                size="icon"
                className="mr-2"
              >
                <IconCopy size={14} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side={'right'}>
              Copy {title}
            </TooltipContent>
          </Tooltip>
        </div>

        {Object.entries(filteredData).map(([key, value]) => (
          <div key={key} className="px-2">
            <div className={'flex flex-row items-center'}>
              <Tooltip>
                <TooltipTrigger className='w-[30%] !text-left'>
                  <div className="text-[10px] uppercase font-normal text-gray-500 truncate">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                </TooltipTrigger>
                <TooltipContent side={'right'}>
                  {key.replace(/([A-Z])/g, ' $1').trim().replace(/\b\w/g, c => c.toUpperCase())}
                </TooltipContent>
              </Tooltip>
              <div className="w-[70%]">
                <DetailInput
                  type={typeof value === 'number' ? 'number' : 'text'}
                  value={String(value)}
                  disabled={true}
                  className="!cursor-default !border-none"
                  divCn="hover:!bg-transparent !border-none"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const copyToClipboard = (data: any, label: string) => {
    const dataString = Object.entries(data)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
    
    navigator.clipboard.writeText(dataString).then(() => {
      toast.success(`${label} copied to clipboard`);
    }, (err) => {
      console.error('Could not copy text: ', err);
      toast.error(`Failed to copy ${label.toLowerCase()}`);
    });
  };

  return (
    <div className="h-full overflow-y-auto no-scrollbar">
      <div className="space-y-4">
        {/* Basic Property Info */}
        {renderSection("Property Information", {
          name: selectedProperty?.name,
          propertyType: selectedProperty?.propertyType,
          status: selectedProperty?.status,
        })}

        {/* Location Information */}
        {renderSection("Location", selectedProperty?.location)}

        {/* Legal Information */}
        {renderSection("Legal Information", selectedProperty?.legal)}

        {/* Physical Details */}
        {renderSection("Physical Details", selectedProperty?.physicalDetails)}

        {/* Lists and Links */}
        {selectedProperty?.lists?.length > 0 && renderSection("Lists", { lists: selectedProperty.lists })}
        {selectedProperty?.linkedContacts?.length > 0 && renderSection("Linked Contacts", { contacts: selectedProperty.linkedContacts })}
        {selectedProperty?.linkedCompanies?.length > 0 && renderSection("Linked Companies", { companies: selectedProperty.linkedCompanies })}
      </div>
    </div>
  );
}

export default PropertyDetails; 