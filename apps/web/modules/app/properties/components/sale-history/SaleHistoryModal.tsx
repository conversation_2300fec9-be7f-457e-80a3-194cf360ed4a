"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { formatNumberWithCommas, removeCommasFromNumber } from "@shared/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { IconCalendar, IconPercentage, IconRuler, IconTrash } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { StandardizedModal } from "@ui/components/standardized-modal";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useCreateSaleHistory, useUpdateSaleHistory, useDeleteSaleHistory, type SaleHistory } from "@app/sale-history/lib/api";
import { useParams } from "next/navigation";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from "@ui/components/form";
import {
	Command,
	CommandList,
} from "@ui/components/command";
import { RelatedObjectSelector } from "@app/shared/components/RelatedObjectSelector";
import { DatePicker, DatePickerAlternative } from "@ui/components/date-picker";
import { Popover, PopoverContent } from "@ui/components/popover";
import { PopoverTrigger } from "@radix-ui/react-popover";
import { cn } from "@ui/lib";
import { Calendar } from "@ui/components/calendar";
import { format } from "date-fns";

// Types for related objects
interface RelatedObject {
	id: string;
	name: string;
	recordType: "contact" | "company" | "property";
	subtitle?: string;
}

interface SaleHistoryModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	editData?: SaleHistory | null;
	propertyId?: string;
}

const formSchema = z.object({
	seller: z.string().optional(),
	buyer: z.string().optional(),
	saleDate: z.coerce.date().optional(),
	salePrice: z.coerce.number().optional(),
	askingPrice: z.coerce.number().optional(),
	transactionType: z.string().optional(),
	pricePerSquareFoot: z.coerce.number().optional(),
	pricePerUnit: z.coerce.number().optional(),
	capRate: z.coerce.number().optional(),
	grmRate: z.coerce.number().optional(),
	transferredOwnershipPercentage: z.coerce.number().optional(),
});

type SaleHistoryFormData = z.infer<typeof formSchema>;

export function SaleHistoryModal({
	open,
	onOpenChange,
	editData,
	propertyId: propPropertyId,
}: SaleHistoryModalProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const params = useParams();
	
	// Get propertyId from props or params
	const propertyId = propPropertyId || (params?.id as string);
	
	const createSaleHistory = useCreateSaleHistory();
	const updateSaleHistory = useUpdateSaleHistory();
	const deleteSaleHistory = useDeleteSaleHistory();

	const [isSubmitting, setIsSubmitting] = useState(false);
	const [sellerObject, setSellerObject] = useState<RelatedObject | null>(null);
	const [buyerObject, setBuyerObject] = useState<RelatedObject | null>(null);
	
	const isEdit = !!editData;

	const form = useForm<SaleHistoryFormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			seller: "",
			buyer: "",
			saleDate: undefined,	
			salePrice: undefined,
			askingPrice: undefined,
			transactionType: "",
			pricePerSquareFoot: undefined,
			pricePerUnit: undefined,
			transferredOwnershipPercentage: undefined,
			capRate: undefined,
			grmRate: undefined,
		},
	});

	// Apply edit data when modal opens or editData changes
	React.useEffect(() => {
		if (open && editData) {
			form.reset({
				seller: editData.seller || "",
				buyer: editData.buyer || "",
				saleDate: editData.saleDate || undefined,
				salePrice: editData.salePrice || undefined,
				askingPrice: editData.askingPrice || undefined,
				transactionType: editData.transactionType || "",
				pricePerSquareFoot: editData.pricePerSquareFoot || undefined,
				pricePerUnit: editData.pricePerUnit || undefined,
				transferredOwnershipPercentage: editData.transferredOwnershipPercentage || undefined,
				capRate: editData.capRate || undefined,
				grmRate: editData.grmRate || undefined,
			});
		}
	}, [open, editData, form]);

	// Reset form when modal opens/closes
	React.useEffect(() => {
		if (!open) {
			form.reset();
			setSellerObject(null);
			setBuyerObject(null);
		}
	}, [open, form]);

	const onSubmit = async (formData: SaleHistoryFormData) => {
		if (!activeOrganization?.id || !user?.id || !propertyId) {
			toast.error("Missing organization, user, or property data");
			return;
		}

		setIsSubmitting(true);

		try {
			// Process the form data - use linked object names or fallback to text input
			const processedData = {
				...formData,
				seller: sellerObject?.name || formData.seller || "",
				buyer: buyerObject?.name || formData.buyer || "",
			};

			// Process numeric fields
			const finalData = Object.fromEntries(
				Object.entries(processedData).map(([key, value]) => {
					const numericFields = ['salePrice', 'askingPrice', 'pricePerSquareFoot', 'pricePerUnit', 'transferredOwnershipPercentage', 'capRate', 'grmRate'];
					
					if (numericFields.includes(key)) {
						const stringValue = value === null || value === undefined ? '' : String(value);
						const numValue = stringValue === '' ? undefined : removeCommasFromNumber(stringValue);
						return [key, numValue === null || isNaN(numValue as number) ? undefined : numValue];
					}
					return [key, value];
				})
			);

			// Clean undefined values
			const cleanedData = Object.fromEntries(
				Object.entries(finalData).filter(([_, value]) => value !== undefined && value !== '')
			);

			if (isEdit && editData?.id) {
				// Update existing sale history
				await updateSaleHistory.mutateAsync({
					id: editData.id,
					data: cleanedData,
				});
				toast.success("Sale history updated successfully");
			} else {
				// Create new sale history
				await createSaleHistory.mutateAsync({
					organizationId: activeOrganization.id,
					propertyId: propertyId,
					...cleanedData,
				});
				toast.success("Sale history created successfully");
			}

			setIsSubmitting(false);
			onOpenChange(false);
		} catch (error: any) {
			console.error("Sale history submission error:", error);
			toast.error(error?.message || "Failed to save sale history");
			setIsSubmitting(false);
		}
	};

	const onCancel = () => {
		onOpenChange(false);
		form.reset();
		setSellerObject(null);
		setBuyerObject(null);
	};

	const handleDelete = async () => {
		if (!editData?.id || !activeOrganization?.id) {
			toast.error("Missing data for deletion");
			return;
		}

		try {
			await deleteSaleHistory.mutateAsync({
				id: editData.id,
				organizationId: activeOrganization.id,
			});
			toast.success("Sale history deleted successfully");
			onOpenChange(false);
		} catch (error: any) {
			console.error("Sale history deletion error:", error);
			toast.error(error?.message || "Failed to delete sale history");
		}
	};

	const isLoading = createSaleHistory.isPending || updateSaleHistory.isPending || deleteSaleHistory.isPending || isSubmitting;

	const submitButton = (
		<div className="flex flex-row items-center gap-2">
			{isEdit && (
				<Button
					variant="ghost"
					size="sm"
					className="border-red-700 bg-red-900 hover:bg-red-800 text-white h-6 px-2"
					onClick={handleDelete}
					disabled={isLoading}
				>
					<IconTrash className="h-3 w-3" />
				</Button>
			)}
			<Button 
				className="text-xs px-2 rounded-md h-6" 
				size="sm" 
				variant="ghost" 
				onClick={onCancel}
				disabled={isLoading}
			>
				Cancel
			</Button>
			<Button
				variant="action"
				size="sm"
				type="submit"
				onClick={form.handleSubmit(onSubmit)}
				disabled={isLoading}
			>
				{isLoading ? (isEdit ? "Saving..." : "Creating...") : (isEdit ? "Save" : "Add Sale History")}
			</Button>
		</div>
	);

	return (
		<StandardizedModal
			open={open}
			onOpenChange={onOpenChange}
			title={isEdit ? "Edit Sale History" : "Add Sale History"}
			description=""
			maxWidth="lg"
			preventOutsideClick={true}
			footer={submitButton}
		>
			<Command>
				<CommandList className="p-2 !pt-0 !max-h-[600px] !h-fit !min-h-fit">
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col space-y-3">
							{/* Seller */}
							<div className="w-full">
								<h3 className="text-xs text-muted-foreground">
                    Seller
                  </h3>
								<div className="flex items-center gap-2">
									<FormField
										control={form.control}
										name="seller"
										render={({ field }) => (
											<FormItem className="flex-1">
												<FormControl>
													<Input
														{...field}
														value={sellerObject?.name || field.value || ''}
														onChange={(e) => {
															if (!sellerObject) {
																field.onChange(e.target.value);
															}
														}}
														placeholder="Enter seller name"
														disabled={!!sellerObject}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<RelatedObjectSelector
										value={sellerObject}
										onValueChange={setSellerObject}
										displayMode="icon"
										size="xl"
										placeholder="Link seller"
									/>
								</div>
							</div>

							{/* Buyer */}
							<div className="w-full">
								<h3 className="text-xs text-muted-foreground">
                    Buyer
                  </h3>
								<div className="flex items-center gap-2">
									<FormField
										control={form.control}
										name="buyer"
										render={({ field }) => (
											<FormItem className="flex-1">
												<FormControl>
													<Input
														{...field}
														value={buyerObject?.name || field.value || ''}
														onChange={(e) => {
															if (!buyerObject) {
																field.onChange(e.target.value);
															}
														}}
														placeholder="Enter buyer name"
														disabled={!!buyerObject}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<RelatedObjectSelector
										value={buyerObject}
										onValueChange={setBuyerObject}
										displayMode="icon"
										size="xl"
										placeholder="Link buyer"
									/>
								</div>
							</div>

							{/* Sale Date */}
							<div className="w-full">
								<h3 className="text-xs text-muted-foreground">
                    Sale Date
                  </h3>
								<FormField
									control={form.control}
									name="saleDate"
									render={({ field }) => (
										<FormItem>
											<FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal bg-transparent",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <IconCalendar className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value ? new Date(field.value) : undefined}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() || date < new Date("1900-01-01")
                              }
                              captionLayout="dropdown"
                            />
                          </PopoverContent>
                        </Popover>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Asking Price and Sale Price */}
							<div className="w-full space-x-2 flex flex-row">
								<div className="w-1/2">
									<h3 className="text-xs text-muted-foreground">
                    Asking Price
                  </h3>
									<FormField
										control={form.control}
										name="askingPrice"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														placeholder="Asking Price"
														{...field}
														value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
														onChange={(e) => field.onChange(removeCommasFromNumber(e.target.value))}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="w-1/2">
									<h3 className="text-xs text-muted-foreground">
                    Sale Price
                  </h3>
									<FormField
										control={form.control}
										name="salePrice"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														placeholder="Sale Price"
														{...field}
														value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
														onChange={(e) => field.onChange(removeCommasFromNumber(e.target.value))}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Transaction Type */}
							<div className="w-full">
								<h3 className="text-xs text-muted-foreground">
                    Transaction Type
                  </h3>
								<FormField
									control={form.control}
									name="transactionType"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input 
													{...field}
													placeholder="Transaction Type" 
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Price Per Square Foot and Price Per Unit */}
							<div className="w-full space-x-2 flex flex-row">
								<div className="w-1/2">
									<h3 className="text-xs text-muted-foreground">
                    Price Per Square Foot
                  </h3>
									<FormField
										control={form.control}
										name="pricePerSquareFoot"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														placeholder="Price Per Sq Ft"
														{...field}
														value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
														onChange={(e) => field.onChange(removeCommasFromNumber(e.target.value))}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="w-1/2">
									<h3 className="text-xs text-muted-foreground">
                    Price Per Unit
                  </h3>
									<FormField
										control={form.control}
										name="pricePerUnit"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														placeholder="Price Per Unit"
														{...field}
														value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
														onChange={(e) => field.onChange(removeCommasFromNumber(e.target.value))}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* CAP Rate and GRM Rate */}
							<div className="w-full space-x-2 flex flex-row">
								<div className="w-1/2">
									<h3 className="text-xs text-muted-foreground">
                    CAP Rate
                  </h3>
									<FormField
										control={form.control}
										name="capRate"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
                            leftIcon={<IconPercentage />}
														placeholder="0"
														{...field}
														value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
														onChange={(e) => field.onChange(removeCommasFromNumber(e.target.value))}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="w-1/2">
									<h3 className="text-xs text-muted-foreground">
                    GRM Rate
                  </h3>
									<FormField
										control={form.control}
										name="grmRate"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
                            leftIcon={<IconPercentage />}
														placeholder="0"
														{...field}
														value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
														onChange={(e) => field.onChange(removeCommasFromNumber(e.target.value))}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Transferred Ownership Percentage */}
							<div className="w-full">
								<h3 className="text-xs text-muted-foreground">
                  Transferred Ownership %
                </h3>
								<FormField
									control={form.control}
									name="transferredOwnershipPercentage"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input
													{...field}
													leftIcon={<IconPercentage />}
													placeholder="0"
													value={field.value !== undefined ? formatNumberWithCommas(field.value) : ''}
													onChange={(e) => {
														const value = removeCommasFromNumber(e.target.value);
														field.onChange(value === null ? undefined : value);
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</form>
					</Form>
				</CommandList>
			</Command>
		</StandardizedModal>
	);
} 