import React from 'react';

import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "./ColumnHeader";
import { DataTableRowActions } from "./RowActions";
import { Checkbox } from "@ui/components/checkbox";
import { formatCurrency } from '@app/organizations/lib/format';
import type { SaleHistory } from "@app/sale-history/lib/api";
import { format } from 'date-fns';
import { Badge } from '@ui/components/badge';

export const columns: ColumnDef<SaleHistory>[] = [
	{
		accessorKey: "saleDate",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Sale Date" />
		),
		cell: ({ row }) => {
			const saleDate = row.original.saleDate;

			return (
				<div className="flex flex-row items-center space-x-2 px-2">
					<span className="max-w-[150px] truncate font-medium">
						{saleDate ? format(saleDate, "MM/dd/yyyy") : "N/A"}
					</span>
				</div>
			)
		},
	},
	{
		accessorKey: "seller",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Seller" />
		),
		cell: ({ row }) => {
			const seller = row.original.seller;
			if (!seller) return <span className="text-muted-foreground">N/A</span>;
			
			return (
				<div className="flex flex-row items-center space-x-2 px-2">
					<span className="max-w-[150px] truncate">
						{seller}
					</span>
				</div>
			)
		},
	},
	{
		accessorKey: "buyer",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Buyer" />
		),
		cell: ({ row }) => {
			const buyer = row.original.buyer;
			if (!buyer) return <span className="text-muted-foreground">N/A</span>;
			
			return (
				<div className="flex flex-row items-center space-x-2 px-2">
					<span className="max-w-[150px] truncate">
						{buyer}
					</span>
				</div>
			)
		},
	},
	{
		accessorKey: "salePrice",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Sale Price" />
		),
		cell: ({ row }) => {
			const salePrice = row.original.salePrice;
			return (
				<span className="font-medium">
					{salePrice ? formatCurrency(salePrice) : "N/A"}
				</span>
			)
		},
	},
	{
		accessorKey: "transactionType",
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Type" />
		),
		cell: ({ row }) => {
			const transactionType = row.original.transactionType;
			if (!transactionType) return <span className="text-muted-foreground">N/A</span>;
			
			return (
				<Badge className="w-fit">
					{transactionType}
				</Badge>
			)
		},
	},
	{
		id: "actions",
		cell: ({ row }) => <DataTableRowActions row={row} />,
	},
]