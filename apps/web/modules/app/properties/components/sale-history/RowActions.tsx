"use client"

import { Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from "@ui/components/dropdown-menu"
import { IconDots } from "@tabler/icons-react";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import type { SaleHistory } from "@app/sale-history/lib/api";

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
   row,
}: DataTableRowActionsProps<TData>) {
    const saleHistory = row.original as SaleHistory;
    const { activeOrganization } = useActiveOrganization();
  
    const handleDelete = async () => {
        console.log("Delete sale history", saleHistory.id);
    };

    const handleEdit = () => {
        console.log("Edit sale history", saleHistory.id);
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    className="flex !h-6 !w-6 p-0 data-[state=open]:bg-muted"
                >
                    <IconDots className="h-3 w-3" />
                    <span className="sr-only">Open menu</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
                <DropdownMenuItem
                    onClick={(e) => {
                        e.stopPropagation();
                        handleEdit();
                    }}
                >
                    Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                    onClick={(e) => {
                        e.stopPropagation();
                        const copyText = `Sale: ${saleHistory.seller || 'Unknown'} to ${saleHistory.buyer || 'Unknown'} - ${saleHistory.salePrice ? `$${saleHistory.salePrice}` : 'Price unknown'}`;
                        navigator.clipboard.writeText(copyText);
                        toast.success("Sale details copied to clipboard");
                    }}
                >
                    Copy Details
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    className='bg-red-700 hover:bg-red-900 text-foreground rounded-lg'
                    onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleDelete();
                    }}
                >
                    Delete
                    <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
