"use client"

import React, { useState } from 'react';
import { DataTableToolbar } from "./TableToolbar";
import {
	ColumnFiltersState,
	getCoreRowModel, getFacetedRowModel, getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel, SortingState,
	useReactTable, VisibilityState
} from "@tanstack/react-table";
import { columns } from "./Columns";
import { Button } from "@ui/components/button";
import SaleHistoryTable from './DataTable';
import { useParams } from 'next/navigation';
import { useActiveOrganization } from '@app/organizations/hooks/use-active-organization';
import { useSaleHistory, type SaleHistory } from '@app/sale-history/lib/api';
import { IconPlus } from '@tabler/icons-react';
import { SaleHistoryModal } from './SaleHistoryModal';

const SaleHistoryContent = () => {
	const { activeOrganization } = useActiveOrganization();
	const params = useParams();
	const orgId = activeOrganization?.id || "";
	const propertyId = params?.id as string;

	// Modal state
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [editData, setEditData] = useState<SaleHistory | null>(null);

	const [sorting, setSorting] = React.useState<SortingState>([]);
	const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
	const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
	const [rowSelection, setRowSelection] = useState({});

	let { data: saleHistory } = useSaleHistory(orgId);

	let data = saleHistory || [];

	const table = useReactTable({
		data,
		columns,
		state: {
			sorting,
			columnVisibility,
			rowSelection,
			columnFilters,
		},
		enableRowSelection: true,
		onRowSelectionChange: setRowSelection,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
	});

	const handleOpenModal = (saleHistory?: SaleHistory) => {
		setEditData(saleHistory || null);
		setIsModalOpen(true);
	};

	const handleCloseModal = () => {
		setIsModalOpen(false);
		setEditData(null);
	};

	return (
		<div>
			<div className={'border-b border-muted p-2 w-full flex justify-between items-center'}>
				<div>
					<DataTableToolbar table={table} />
				</div>
				<div className={'flex justify-end space-x-2 flex-row items-center'}>
					<Button
						onClick={() => handleOpenModal()}
						variant="action"
						className="gap-1"
					>
						<IconPlus className="h-4 w-4" />
						Add Sale History
					</Button>
				</div>
			</div>

			<div className="w-full">
        		<div className="h-full flex-1 flex-col space-y-8 md:flex">
					<SaleHistoryTable 
						columns={columns}
						table={table} 
						isLoading={false}
						onEditSaleHistory={handleOpenModal}
					/>
				</div>
			</div>

			<SaleHistoryModal
				open={isModalOpen}
				onOpenChange={handleCloseModal}
				editData={editData}
				propertyId={propertyId}
			/>
		</div>
	)
}

export default SaleHistoryContent;
