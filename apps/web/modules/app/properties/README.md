# Properties Module

This module provides API functions and React Query hooks for managing properties in the application.

## Usage

### Basic Property Operations

```typescript
import { 
  useProperties, 
  useProperty, 
  useCreateProperty, 
  useUpdateProperty, 
  useDeleteProperty 
} from "@/properties";

// Fetch all properties
const { data: properties, isLoading } = useProperties(organizationId);

// Fetch single property
const { data: property } = useProperty(propertyId, organizationId);

// Create property
const createProperty = useCreateProperty();
const handleCreate = async (propertyData: PropertyCreateInput) => {
  await createProperty.mutateAsync(propertyData);
};

// Update property
const updateProperty = useUpdateProperty(organizationId);
const handleUpdate = async (updates: UpdatePropertyPayload) => {
  await updateProperty.mutateAsync(updates);
};

// Delete property
const deleteProperty = useDeleteProperty();
const handleDelete = async (propertyId: string) => {
  await deleteProperty.mutateAsync(propertyId);
};
```

### Field-Level Updates

```typescript
import { useUpdatePropertyField, useClearPropertyField } from "@/properties";

// Update specific field
const updateField = useUpdatePropertyField();
const handleFieldUpdate = async (propertyId: string, field: string, value: any) => {
  await updateField.mutateAsync({
    id: propertyId,
    field,
    value,
    organizationId
  });
};

// Clear specific field
const clearField = useClearPropertyField();
const handleFieldClear = async (propertyId: string, field: string) => {
  await clearField.mutateAsync({
    id: propertyId,
    field,
    organizationId
  });
};
```

### Search Properties

```typescript
import { useSearchProperties } from "@/properties";

const { data: searchResults } = useSearchProperties(query, organizationId);
```

### Real Estate API Integration

```typescript
import { 
  usePropertyInfo, 
  usePropertyBoundary, 
  usePropertyAutocomplete 
} from "@/properties";

// Get property info from REA API
const propertyInfo = usePropertyInfo();
const handleGetInfo = async (address: string) => {
  const info = await propertyInfo.mutateAsync(address);
};

// Get property boundary
const propertyBoundary = usePropertyBoundary();
const handleGetBoundary = async (coords: { lat: number; lng: number; id?: number }) => {
  const boundary = await propertyBoundary.mutateAsync(coords);
};

// Property autocomplete
const propertyAutocomplete = usePropertyAutocomplete();
const handleAutocomplete = async (query: string) => {
  const results = await propertyAutocomplete.mutateAsync(query);
};
```

### Related Entities

```typescript
import { 
  usePropertyUnitMixes, 
  usePropertySaleHistory, 
  usePropertyMortgages 
} from "@/properties";

// Get unit mixes
const { data: unitMixes } = usePropertyUnitMixes(propertyId, organizationId);

// Get sale history
const { data: saleHistory } = usePropertySaleHistory(propertyId, organizationId);

// Get mortgages
const { data: mortgages } = usePropertyMortgages(propertyId, organizationId);
```

### Batch Operations

```typescript
import { useBatchDeleteProperties } from "@/properties";

const batchDelete = useBatchDeleteProperties();
const handleBatchDelete = async (propertyIds: string[]) => {
  await batchDelete.mutateAsync({
    ids: propertyIds,
    organizationId
  });
};
```

## Key Features

- **Complete CRUD operations** for properties
- **Field-level updates** with optimistic updates
- **Search functionality** across property attributes
- **Real Estate API integration** for property enrichment
- **Related entities support** (unit mixes, sale history, mortgages)
- **Batch operations** for bulk actions
- **Optimistic updates** for better UX
- **Error handling** with meaningful error messages
- **React Query integration** for caching and background updates
- **Organization-scoped** data access

## Types

The module exports comprehensive TypeScript types for:
- `UpdatePropertyPayload` - Property update interface
- `PropertyUnitMixPayload` - Unit mix data structure
- `PropertySaleHistoryPayload` - Sale history data structure
- `PropertyMortgagePayload` - Mortgage data structure

## API Endpoints

The module interfaces with the following API endpoints:
- `POST /api/objects/properties` - Create property
- `GET /api/objects/properties` - List properties
- `GET /api/objects/properties/:id` - Get property
- `PATCH /api/objects/properties/:id` - Update property
- `DELETE /api/objects/properties/:id` - Delete property
- `PATCH /api/objects/properties/:id/field` - Update field
- `DELETE /api/objects/properties/:id/field/:field` - Clear field
- `POST /api/properties/rea/*` - Real Estate API endpoints 