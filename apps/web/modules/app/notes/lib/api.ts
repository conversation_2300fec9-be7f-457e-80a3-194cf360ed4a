import { auth, Session } from "@repo/auth";
import type { Note } from "@repo/database/src/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// PAYLOAD INTERFACES
export interface CreateNotePayload {
	title: string;
	parentDocument?: string | null;
	objectId?: string | null;
	objectType?: string | null;
	organizationId?: string | null;
}

export interface UpdateNotePayload {
	id: string;
	title?: string;
	content?: string | null;
	coverImage?: string | null;
	icon?: string | null;
	isPublished?: boolean;
	objectId?: string | null;
	objectType?: string | null;
	organizationId?: string | null;
}

// CREATE NOTE
export async function createNote(payload: CreateNotePayload): Promise<Note> {
	const res = await fetch("/api/notes", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(payload),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to create note");
	}
	return res.json();
}

// FETCH ALL NOTES
export async function fetchNotes(organizationId?: string): Promise<Note[]> {
	const url = organizationId 
		? `/api/notes?organizationId=${organizationId}`
		: "/api/notes";
	const res = await fetch(url);
	if (!res.ok) {
		throw new Error("Failed to fetch notes");
	}
	return res.json();
}

// FETCH SINGLE NOTE
export async function fetchNote(id: string): Promise<Note> {
	const res = await fetch(`/api/notes/${id}`);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch note");
	}
	return res.json();
}

// UPDATE NOTE
export async function updateNote(payload: UpdateNotePayload): Promise<Note> {
	const { id, ...data } = payload;
	const res = await fetch(`/api/notes/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(data),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to update note");
	}
	return res.json();
}

// DELETE NOTE
export async function deleteNote(id: string): Promise<void> {
	const res = await fetch(`/api/notes/${id}`, {
		method: "DELETE",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to delete note");
	}
}

// ARCHIVE NOTE (and children)
export async function archiveNote(id: string): Promise<void> {
	const res = await fetch(`/api/notes/${id}/archive`, {
		method: "POST",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to archive note");
	}
}

// RESTORE NOTE (and children)
export async function restoreNote(id: string): Promise<void> {
	const res = await fetch(`/api/notes/${id}/restore`, {
		method: "POST",
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to restore note");
	}
}

// FETCH SIDEBAR NOTES (by parentDocument)
export async function fetchSidebarNotes(
	parentDocument?: string | null,
): Promise<Note[]> {
	const url = parentDocument
		? `/api/notes/sidebar?parentDocument=${parentDocument}`
		: "/api/notes/sidebar";
	const res = await fetch(url);
	if (!res.ok) {
		throw new Error("Failed to fetch sidebar notes");
	}
	return res.json();
}

// FETCH TRASHED NOTES
export async function fetchTrashedNotes(): Promise<Note[]> {
	const res = await fetch("/api/notes/trash");
	if (!res.ok) {
		throw new Error("Failed to fetch trashed notes");
	}
	return res.json();
}

// FETCH NOTE COUNT
export async function fetchNoteCount(): Promise<number> {
	const res = await fetch("/api/notes/count");
	if (!res.ok) {
		throw new Error("Failed to fetch note count");
	}
	const data = await res.json();
	return data.count;
}

// FETCH PUBLIC NOTE
export async function fetchPublicNote(id: string): Promise<Note> {
	const res = await fetch(`/api/note/${id}`);
	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to fetch public note");
	}
	return res.json();
}

// TOGGLE NOTE PUBLISHED STATUS
export async function toggleNotePublished(
	id: string,
	isPublished: boolean,
): Promise<Note> {
	const res = await fetch(`/api/notes/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify({ isPublished }),
	});
	if (!res.ok) {
		const error = await res.json();
		throw new Error(
			error.error || "Failed to toggle note published status",
		);
	}
	return res.json();
}

export async function fetchNotesForObjectRecord(
	objectId: string,
	objectType: string,
	organizationId?: string
): Promise<Note[]> {
	const params = new URLSearchParams({
		objectId,
		objectType,
	});
	
	if (organizationId) {
		params.set('organizationId', organizationId);
	}
	
	const url = `/api/notes?${params.toString()}`;
	const res = await fetch(url);
	if (!res.ok) {
		throw new Error("Failed to fetch notes for object");
	}
	return res.json();
}

// REACT QUERY HOOKS
export function useCreateNote(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createNote,
		onMutate: async (newNote) => {
			// Cancel outgoing refetches to avoid race conditions
			await queryClient.cancelQueries({
				queryKey: ["notes", organizationId],
			});

			// Snapshot the previous value
			const previousNotes = queryClient.getQueryData(["notes", organizationId]);

			// Optimistically update the cache
			queryClient.setQueryData(
				["notes", organizationId],
				(old: Note[] = []) => {
					const optimisticNote = {
						id: `temp-${Date.now()}`, // Temporary ID
						orgId: organizationId || "",
						userId: "", // Will be filled by server
						title: newNote.title,
						isArchived: false,
						parentDocument: newNote.parentDocument || null,
						content: null,
						coverImage: null,
						icon: null,
						objectId: newNote.objectId || null,
						objectType: newNote.objectType || null,
						isPublished: false,
						createdAt: new Date(),
						updatedAt: new Date(),
						isDeleted: false,
						deletedAt: null,
						deletedBy: null,
					} as Note;
					return [optimisticNote, ...old];
				},
			);

			return { previousNotes };
		},
		onError: (err, newNote, context) => {
			// Revert to previous state on error
			if (context?.previousNotes) {
				queryClient.setQueryData(["notes", organizationId], context.previousNotes);
			}
		},
		onSettled: () => {
			// Always refetch after error or success to sync with server
			queryClient.invalidateQueries({
				queryKey: ["notes", organizationId],
			});
		},
	});
}

export function useUpdateNote(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateNote,
		onMutate: async (updatedNote) => {
			await queryClient.cancelQueries({
				queryKey: ["notes", organizationId],
			});
			await queryClient.cancelQueries({
				queryKey: ["note", updatedNote.id],
			});

			const previousNotes = queryClient.getQueryData(["notes", organizationId]);
			const previousNote = queryClient.getQueryData(["note", updatedNote.id]);

			// Optimistically update both list and individual note
			queryClient.setQueryData(
				["notes", organizationId],
				(old: Note[] = []) => {
					return old.map((note) =>
						note.id === updatedNote.id
							? { ...note, ...updatedNote, updatedAt: new Date() }
							: note,
					);
				},
			);

			if (previousNote) {
				queryClient.setQueryData(
					["note", updatedNote.id],
					(old: Note) => ({ ...old, ...updatedNote, updatedAt: new Date() }),
				);
			}

			return { previousNotes, previousNote };
		},
		onError: (err, updatedNote, context) => {
			// Revert both queries on error
			if (context?.previousNotes) {
				queryClient.setQueryData(["notes", organizationId], context.previousNotes);
			}
			if (context?.previousNote) {
				queryClient.setQueryData(["note", updatedNote.id], context.previousNote);
			}
		},
		onSettled: (data, error, variables) => {
			// Invalidate both queries but only the specific ones needed
			queryClient.invalidateQueries({
				queryKey: ["notes", organizationId],
			});
			queryClient.invalidateQueries({
				queryKey: ["note", variables.id],
			});
		},
	});
}

export function useDeleteNote(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteNote,
		onMutate: async (noteId) => {
			await queryClient.cancelQueries({
				queryKey: ["notes", organizationId],
			});

			const previousNotes = queryClient.getQueryData(["notes", organizationId]);

			// Optimistically remove from list
			queryClient.setQueryData(
				["notes", organizationId],
				(old: Note[] = []) => old.filter((note) => note.id !== noteId),
			);

			// Remove individual note from cache
			queryClient.removeQueries({
				queryKey: ["note", noteId],
			});

			return { previousNotes };
		},
		onError: (err, noteId, context) => {
			// Revert to previous state on error
			if (context?.previousNotes) {
				queryClient.setQueryData(["notes", organizationId], context.previousNotes);
			}
		},
		onSettled: () => {
			// Refetch to ensure consistency
			queryClient.invalidateQueries({
				queryKey: ["notes", organizationId],
			});
		},
	});
}

export function useNotes(organizationId?: string) {
	return useQuery({
		queryKey: ["notes", organizationId],
		queryFn: () => fetchNotes(organizationId),
		staleTime: 2 * 60 * 1000, // 2 minutes - data is considered fresh
		gcTime: 10 * 60 * 1000, // 10 minutes - cache garbage collection time
		refetchOnWindowFocus: false, // Don't refetch on window focus to reduce API calls
		refetchOnMount: false, // Don't refetch on component mount if data is fresh
		enabled: !!organizationId,
	});
}

export function useNote(id: string | undefined) {
	return useQuery({
		queryKey: ["note", id],
		queryFn: () => (id ? fetchNote(id) : Promise.resolve(null)),
		enabled: !!id,
		staleTime: 5 * 60 * 1000, // 5 minutes - individual notes stay fresh longer
		gcTime: 15 * 60 * 1000, // 15 minutes - keep individual notes in cache longer
		refetchOnWindowFocus: false,
	});
}

export function usePublicNote(id: string | undefined) {
	return useQuery({
		queryKey: ["publicNote", id],
		queryFn: () => fetchPublicNote(id!),
		enabled: !!id,
		staleTime: 10 * 60 * 1000, // 10 minutes - public notes change less frequently
		gcTime: 30 * 60 * 1000, // 30 minutes - keep public notes cached longer
		refetchOnWindowFocus: false,
	});
}

export function useToggleNotePublished(organizationId?: string) {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			id,
			isPublished,
		}: {
			id: string;
			isPublished: boolean;
		}) => toggleNotePublished(id, isPublished),
		onSuccess: (updatedNote: Note) => {
			queryClient.setQueryData(["note", updatedNote.id], updatedNote);
			// Invalidate organization-specific notes instead of all notes
			queryClient.invalidateQueries({
				queryKey: ["notes", organizationId],
			});
		},
	});
}

export function useNotesForObjectRecord(
	objectId: string | undefined,
	objectType: string | undefined,
	organizationId?: string
) {
	return useQuery({
		queryKey: ["notes", "object", objectId, objectType, organizationId],
		queryFn: () => 
			objectId && objectType 
				? fetchNotesForObjectRecord(objectId, objectType, organizationId)
				: Promise.resolve([]),
		enabled: !!objectId && !!objectType,
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		refetchOnWindowFocus: false,
		refetchOnMount: false,
	});
}
