"use client";

import { DataTableGroup } from "@app/notes/components/DataTableGroup";
import { DataTableSort } from "@app/notes/components/DataTableSort";
import { updateNotePreferences } from "@app/notes/lib/preferences";
import { STORAGE_KEYS } from "@app/shared/lib/constants";
import { setLocalStorage } from "@app/shared/lib/local-storage";
import {
	IconAntennaBars5,
	IconAt,
	IconCalendarTime,
	IconClock,
	IconGlobe,
	IconId,
	IconLayoutGrid,
	IconLayoutList,
	IconLetterT,
	IconLetterTSmall,
	IconProgress,
	IconQuestionMark,
	IconTableOptions,
	IconUserCircle,
} from "@tabler/icons-react";
import type { Table } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Switch } from "@ui/components/switch";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import React, { useEffect, useState } from "react";

interface TableSettingsProps {
	showFavorites: boolean;
	onShowFavoritesChange: (show: boolean) => void;
	table: Table<any>;
	view: string;
	onViewChange: (view: string) => void;
	groupBy: string;
	onGroupByChange: (groupBy: string) => void;
	user: any;
	users: Record<string, any>;
}

const iconMapping = {
	Title: IconLetterTSmall,
	"Created at": IconCalendarTime,
	"Created by": IconUserCircle,
	Record: IconId,
	Status: IconGlobe,
};

export function TableSettings({
	showFavorites,
	onShowFavoritesChange,
	table,
	view,
	onViewChange,
	groupBy,
	onGroupByChange,
	user,
	users,
}: TableSettingsProps) {
	const [isOpen, setIsOpen] = useState(false);

	useEffect(() => {
		updateNotePreferences({ view });
	}, [view]);

	useEffect(() => {
		setLocalStorage(STORAGE_KEYS.NOTES_VIEW, view);
	}, [view]);

	return (
		<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
			<DropdownMenuTrigger asChild>
				<Button variant="relio" size="sm" className="w-full">
					<IconTableOptions className="size-3 transition-all text-muted-foreground group-hover:text-primary" />
					<span className="text-sm truncate">View Settings</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-[280px] p-4">
				<div className="space-y-4">
					{/* View Section */}
					<div>
						<div className="flex items-center justify-between mb-1">
							<span className="font-medium text-xs text-muted-foreground">
								View
							</span>
							<Tooltip>
								<TooltipTrigger asChild>
									<IconQuestionMark className="h-4 w-4 text-muted-foreground border rounded-full p-0.5" />
								</TooltipTrigger>
								<TooltipContent>
									<span>
										Viewing options are applied just for you
										and are synced between organizations.
									</span>
								</TooltipContent>
							</Tooltip>
						</div>
						<div className="grid grid-cols-2 gap-1 border border-input rounded-lg p-1">
							<Button
								variant={
									view === "list" ? "secondary" : "ghost"
								}
								className="w-full h-full py-2 px-4 flex flex-col items-center gap-1 relative"
								onClick={() => onViewChange("list")}
							>
								<IconLayoutList className="size-4" />
								<span className="text-xs">List</span>
							</Button>
							<Button
								variant={
									view === "grid" ? "secondary" : "ghost"
								}
								className="w-full h-full py-2 px-4 flex flex-col items-center gap-1"
								onClick={() => onViewChange("grid")}
							>
								<IconLayoutGrid className="size-4" />
								<span className="text-xs">Grid</span>
							</Button>
						</div>
					</div>

					{/* Completed Tasks Section */}
					<div className="flex items-center justify-between">
						<span className="text-xs">Show favorites</span>
						<Switch
							checked={showFavorites}
							onCheckedChange={onShowFavoritesChange}
						/>
					</div>

					{/* Group Section */}
					<div>
						<div className="flex items-center justify-between mb-1">
							<span className="font-medium text-xs text-muted-foreground">
								Grouped by
							</span>
						</div>
						<div className="space-y-2">
							<div className="flex flex-col gap-1">
								<DataTableGroup
									groupBy={groupBy}
									onGroupByChange={onGroupByChange}
									user={user}
								/>
							</div>
						</div>
					</div>

					{/* Sort Section */}
					<div>
						<div className="flex items-center justify-between mb-1">
							<span className="font-medium text-xs text-muted-foreground">
								Sorted by
							</span>
						</div>
						<div className="space-y-2">
							<div className="flex flex-col gap-1">
								<DataTableSort table={table} />
							</div>
						</div>
					</div>

					{/* Visibility Section */}
					<div>
						<div>
							<span className="font-medium text-xs text-muted-foreground">
								Visibility
							</span>
						</div>

						{/* Columns Section */}
						<div className="flex flex-col gap-1">
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="outline"
										className="h-8 gap-2 rounded-lg flex items-center justify-start w-full text-xs font-mono"
									>
										Table Columns{" "}
										<span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
											{
												table
													.getAllColumns()
													.filter((column) =>
														column.getIsVisible(),
													).length
											}
										</span>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-70 space-y-0.5">
									{table
										.getAllColumns()
										.filter(
											(column) =>
												typeof column.accessorFn !==
													"undefined" &&
												column.getCanHide(),
										)
										.map((column) => {
											let label: string;
											const accessorKey = (
												column.columnDef as any
											).accessorKey;

											if (
												typeof column.columnDef
													.header === "function" &&
												accessorKey
											) {
												label = accessorKey as string;
											} else if (
												typeof column.columnDef
													.header === "string"
											) {
												label = column.columnDef.header;
											} else {
												label = column.id;
											}

											const labelForIcon =
												label === "createdAt"
													? "Created At"
													: label
															.charAt(0)
															.toUpperCase() +
														label.slice(1);

											const Icon =
												iconMapping[
													labelForIcon as keyof typeof iconMapping
												];

											return (
												<DropdownMenuCheckboxItem
													key={column.id}
													className="capitalize h-8 flex items-center gap-2"
													checked={column.getIsVisible()}
													onCheckedChange={(value) =>
														column.toggleVisibility(
															!!value,
														)
													}
												>
													{Icon && (
														<Icon className="h-4 w-4 text-muted-foreground" />
													)}
													<span>{labelForIcon}</span>
												</DropdownMenuCheckboxItem>
											);
										})}
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					</div>

					{/* Filter Section */}
					{/* <div>
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-xs text-muted-foreground">Filter by</span>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-8 gap-2 rounded-lg flex items-center justify-start w-full">
                  Assignee <span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
                    {Object.keys(users).length}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-70">
                {Object.values(users).map((user) => {
                  const typedUser = user as any;
                  return (
                    <DropdownMenuCheckboxItem
                      key={typedUser.id}
                      className="capitalize"
                      checked={(table.getColumn('assignee')?.getFilterValue() as string[] | undefined)?.includes(typedUser.id)}
                      onCheckedChange={(checked) => {
                        const filterValues = table.getColumn('assignee')?.getFilterValue() as string[] || [];
                        if (checked) {
                          table.getColumn('assignee')?.setFilterValue([...filterValues, typedUser.id]);
                        } else {
                          table.getColumn('assignee')?.setFilterValue(
                            filterValues.filter((id) => id !== typedUser.id)
                          );
                        }
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <UserAvatar name={typedUser.name} avatarUrl={typedUser.avatarUrl} className="size-4" />
                        {typedUser.name}
                      </div>
                    </DropdownMenuCheckboxItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div> */}
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
