"use client";

import { useCoverImage } from "@app/notes/hooks/useCoverImage";
import { useUpdateNote } from "@app/notes/lib/api";
import { useEdgeStore } from "@repo/storage";
import { Spinner } from "@shared/components/Spinner";
import { But<PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { ImageIcon, Upload, X } from "lucide-react";
import React, { useState } from "react";
import { useDropzone } from "react-dropzone";

interface CoverImageModalProps {
	noteId: string;
	organizationId: string;
	existingCoverUrl?: string;
}

export function CoverImageModal({
	noteId,
	organizationId,
	existingCoverUrl,
}: CoverImageModalProps) {
	const coverImage = useCoverImage();
	const updateNoteMutation = useUpdateNote();
	const { edgestore } = useEdgeStore();

	const [file, setFile] = useState<File | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [progress, setProgress] = useState(0);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		accept: {
			"image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"],
		},
		maxFiles: 1,
		onDrop: (acceptedFiles) => {
			if (acceptedFiles[0]) {
				setFile(acceptedFiles[0]);
			}
		},
	});

	const onChange = async (file?: File) => {
		if (file) {
			setIsSubmitting(true);
			setFile(file);

			let res;

			if (edgestore.publicFiles) {
				try {
					res = await edgestore.publicFiles.upload({
						file,
						onProgressChange: (progress) => {
							setProgress(progress);
						},
						options: {
							replaceTargetUrl:
								existingCoverUrl || coverImage.url,
						},
					});

					await updateNoteMutation.mutateAsync({
						id: noteId,
						coverImage: res?.url,
						organizationId,
					});

					coverImage.onClose();
				} catch (error) {
					console.error("Upload failed", error);
				} finally {
					setIsSubmitting(false);
					setProgress(0);
					setFile(null);
				}
			} else {
				console.error("edgestore.publicFiles is undefined");
				setIsSubmitting(false);
				setFile(null);
			}
		}
	};

	const onClose = () => {
		setFile(null);
		setIsSubmitting(false);
		setProgress(0);
		coverImage.onClose();
	};

	const onUpload = () => {
		if (file) {
			onChange(file);
		}
	};

	return (
		<Dialog open={coverImage.isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-md !rounded-2xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 m-0 bg-clip-padding overflow-y-auto">
				<DialogHeader className="-mt-2">
					<span className="text-sm font-medium">Cover Image</span>
				</DialogHeader>

				<div className="overflow-y-auto no-scrollbar px-1 pb-16">
					<div
						{...getRootProps()}
						className={cn(
							"border-2 bg-secondary/20 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
							isDragActive
								? "border-primary bg-primary/5"
								: "border-muted-foreground/25",
							file && "border-primary bg-primary/5",
						)}
					>
						<input {...getInputProps()} />

						{file ? (
							<div className="space-y-2">
								<div className="w-16 h-16 mx-auto bg-primary/10 rounded-lg flex items-center justify-center">
									<ImageIcon className="h-8 w-8 text-primary" />
								</div>
								<p className="text-sm font-medium">
									{file.name}
								</p>
								<p className="text-xs text-muted-foreground">
									{(file.size / 1024 / 1024).toFixed(2)} MB
								</p>
							</div>
						) : (
							<div className="space-y-2">
								<div className="w-16 h-16 mx-auto bg-muted rounded-lg flex items-center justify-center">
									<Upload className="h-8 w-8 text-muted-foreground" />
								</div>
								<div>
									<p className="text-sm font-medium">
										{isDragActive
											? "Drop your image here"
											: "Choose an image or drag & drop"}
									</p>
									<p className="text-xs text-muted-foreground">
										PNG, JPG, GIF, WebP up to 10MB
									</p>
								</div>
							</div>
						)}
					</div>

					{isSubmitting && (
						<div className="space-y-2">
							<div className="flex items-center justify-between text-sm">
								<span>Uploading...</span>
								<span>{progress}%</span>
							</div>
							<div className="w-full bg-secondary rounded-full h-2">
								<div
									className="bg-primary h-2 rounded-full transition-all duration-300"
									style={{ width: `${progress}%` }}
								/>
							</div>
						</div>
					)}
				</div>

				<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
					<Button
						variant="outline"
						onClick={onClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={onUpload}
						disabled={!file || isSubmitting}
						className="gap-2"
					>
						{isSubmitting ? (
							<>
								<Spinner className="h-4 w-4" />
								Uploading...
							</>
						) : (
							<>
								<Upload className="h-4 w-4" />
								Upload Cover
							</>
						)}
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
}
