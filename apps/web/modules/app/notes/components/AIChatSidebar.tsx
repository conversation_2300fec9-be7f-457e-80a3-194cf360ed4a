"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@ui/components/scroll-area";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { 
  AIInput, 
  AIInputTextarea, 
  AIInputToolbar, 
  AIInputSubmit 
} from "@ui/components/ai-input";
import { 
  AIReasoning, 
  AIReasoningTrigger, 
  AIReasoningContent 
} from "@ui/components/ai-reasoning";
import { BrainIcon, UserIcon } from "lucide-react";
import { useState } from "react";

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  reasoning?: string;
}

interface AIChatSidebarProps {
  noteId: string;
  noteContent: string;
  noteTitle: string;
}

export function AIChatSidebar({ noteId, noteContent, noteTitle }: AIChatSidebarProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: `I've created a note for ${noteTitle}. Let me know if you'd like any adjustments! 🚀`,
      role: 'assistant',
      timestamp: new Date(),
      reasoning: 'I analyzed the note content and title to provide context about what the user is working on.'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `I understand you're asking about "${inputValue.trim()}". Based on your note "${noteTitle}", I can help you with that. This is a simulated response for now.`,
        role: 'assistant',
        timestamp: new Date(),
        reasoning: `I analyzed the user's question "${inputValue.trim()}" in the context of their note about "${noteTitle}" to provide a relevant response.`
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 2000);
  };

  return (
    <div className="flex flex-col h-full bg-background border-r border-border">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center size-8 rounded-lg bg-primary/10">
            <BrainIcon className="size-4 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-sm">Ask SummArize</h3>
            <p className="text-xs text-muted-foreground">AI Assistant</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="space-y-2">
              <div className={`flex gap-3 ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                <Avatar className="size-6">
                  <AvatarFallback className="text-xs">
                    {message.role === 'user' ? (
                      <UserIcon className="size-3" />
                    ) : (
                      <BrainIcon className="size-3" />
                    )}
                  </AvatarFallback>
                </Avatar>
                <div className={`flex-1 space-y-1 ${message.role === 'user' ? 'text-right' : ''}`}>
                  <div
                    className={`inline-block p-3 rounded-lg text-sm max-w-[80%] ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground ml-auto'
                        : 'bg-muted text-foreground'
                    }`}
                  >
                    {message.content}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {message.timestamp.toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
              </div>
              
              {/* AI Reasoning */}
              {message.role === 'assistant' && message.reasoning && (
                <div className="ml-9">
                  <AIReasoning>
                    <AIReasoningTrigger />
                    <AIReasoningContent>
                      {message.reasoning}
                    </AIReasoningContent>
                  </AIReasoning>
                </div>
              )}
            </div>
          ))}
          
          {isLoading && (
            <div className="flex gap-3">
              <Avatar className="size-6">
                <AvatarFallback className="text-xs">
                  <BrainIcon className="size-3" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="inline-block p-3 rounded-lg text-sm bg-muted text-foreground">
                  <div className="flex items-center gap-2">
                    <div className="animate-pulse">Thinking...</div>
                    <div className="flex gap-1">
                      <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                      <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                      <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <AIInput onSubmit={handleSubmit}>
          <AIInputTextarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask about this note..."
            disabled={isLoading}
          />
          <AIInputToolbar>
            <div className="flex-1" />
            <AIInputSubmit 
              status={isLoading ? 'submitted' : 'ready'} 
              disabled={!inputValue.trim() || isLoading}
            />
          </AIInputToolbar>
        </AIInput>
      </div>
    </div>
  );
} 