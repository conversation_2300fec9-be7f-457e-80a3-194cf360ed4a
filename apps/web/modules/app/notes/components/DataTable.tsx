"use client";

import { NoteModal } from "../../../../app/(authenticated)/app/(organizations)/[organizationSlug]/notes/NoteModal";
import EmptyContainer from "@app/shared/components/EmptyContainer";
import {
	type ColumnDef,
	flexRender,
	type HeaderGroup,
	type Row,
	type Table,
} from "@tanstack/react-table";
import { Skeleton } from "@ui/components/skeleton";
import {
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	Table as UITable,
} from "@ui/components/table";
import React from "react";

interface NoteTableProps {
	columns: ColumnDef<any, unknown>[];
	table: Table<any>;
	isLoading: boolean;
	groupBy: string;
}

// Helper to group rows by a field
const groupRows = (rows: Row<any>[], groupBy: string) => {
	if (!groupBy) return { "All Notes": rows };
	const groups: Record<string, Row<any>[]> = {};
	rows.forEach((row) => {
		let groupValue = row.original[groupBy];
		if (groupBy === "createdAt" && groupValue) {
			// Group by date string (YYYY-MM-DD)
			groupValue = new Date(groupValue).toLocaleDateString();
		} else if (groupBy === "createdBy") {
			// Extract the user's name from the createdBy object
			groupValue = groupValue?.name || "Unknown";
		}
		if (!groupValue) groupValue = "Ungrouped";
		if (!groups[groupValue]) groups[groupValue] = [];
		groups[groupValue].push(row);
	});
	return groups;
};

const NoteTable = ({ columns, table, isLoading, groupBy }: NoteTableProps) => {
	const [selectedNote, setSelectedNote] = React.useState<any | null>(null);
	const [isNoteModalOpen, setIsNoteModalOpen] = React.useState(false);

	const handleRowClick = (row: Row<any>) => {
		setSelectedNote(row.original);
		setIsNoteModalOpen(true);
	};

	const handleModalOpenChange = (open: boolean) => {
		setIsNoteModalOpen(open);
		if (!open) setSelectedNote(null);
	};

	const TableSkeleton = ({
		columns,
	}: {
		columns: ColumnDef<any, unknown>[];
	}) => (
		<>
			{[...Array(5)].map((_, index) => (
				<TableRow key={index}>
					{columns.map((column, cellIndex) => (
						<TableCell key={cellIndex}>
							<Skeleton className="h-6 w-full" />
						</TableCell>
					))}
				</TableRow>
			))}
		</>
	);

	// Group rows
	const grouped = groupRows(table.getRowModel().rows, groupBy);

	return (
		<div className="space-y-4">
			<UITable>
				<TableHeader>
					{table
						.getHeaderGroups()
						.map((headerGroup: HeaderGroup<any>) => (
							<TableRow
								key={headerGroup.id}
								className="border-b hover:bg-transparent"
							>
								{headerGroup.headers.map((header) => (
									<TableHead
										key={header.id}
										colSpan={header.colSpan}
										className="px-4 text-xs font-medium text-zinc-500 !h-8"
									>
										<div className="flex items-center h-full w-full !-mt-4">
											{header.isPlaceholder
												? null
												: flexRender(
														header.column.columnDef
															.header,
														header.getContext(),
													)}
										</div>
									</TableHead>
								))}
							</TableRow>
						))}
				</TableHeader>
				<TableBody>
					{isLoading ? (
						<TableSkeleton columns={columns} />
					) : Object.entries(grouped).length > 0 ? (
						Object.entries(grouped).map(([group, rows]) => (
							<React.Fragment key={group}>
								<TableRow>
									<TableCell
										colSpan={columns.length}
										className="bg-zinc-100 dark:bg-sidebar px-4 py-2"
									>
										<div className="flex items-center gap-x-2">
											<h2 className="text-xs">{group}</h2>
											<span className="rounded-sm bg-zinc-100 dark:bg-sidebar px-1 py-0 text-xs font-medium border border-input font-mono">
												{rows.length}
											</span>
										</div>
									</TableCell>
								</TableRow>
								{rows.map((row: Row<any>) => (
									<TableRow
										key={row.id}
										className="cursor-pointer"
										onClick={() => handleRowClick(row)}
									>
										{row.getVisibleCells().map((cell) => (
											<TableCell key={cell.id}>
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext(),
												)}
											</TableCell>
										))}
									</TableRow>
								))}
							</React.Fragment>
						))
					) : (
						<TableRow>
							<TableCell
								colSpan={columns.length}
								className="h-24 text-center rounded-b-lg"
							>
								<EmptyContainer
									title="No notes found"
									subtitle="You have no notes for this organization."
								/>
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</UITable>
			<NoteModal
				open={isNoteModalOpen}
				onOpenChange={handleModalOpenChange}
				note={selectedNote}
				noteToEdit={selectedNote}
				isFavorite={selectedNote?.isFavorite}
			/>
		</div>
	);
};

export default NoteTable;
