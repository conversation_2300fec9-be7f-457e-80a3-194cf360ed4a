import { IconSortAscending, IconSortDescending } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuRadioGroup,
	DropdownMenuRadioItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ArrowDown, ArrowUp, Calendar, SortAsc } from "lucide-react";
import React from "react";

interface SortField {
	label: string;
	value: string;
	icon?: React.ReactNode;
}

interface SortDropdownProps {
	sortField: string;
	sortDirection: "asc" | "desc";
	onSortFieldChange: (field: string) => void;
	onSortDirectionChange: (dir: "asc" | "desc") => void;
	fields: SortField[];
}

export function SortDropdown({
	sortField,
	sortDirection,
	onSortFieldChange,
	onSortDirectionChange,
	fields,
}: SortDropdownProps) {
	const currentField = fields.find((f) => f.value === sortField);

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="relio" size="sm" className="group">
					{sortDirection === "asc" ? (
						<IconSortAscending className="size-4 text-muted-foreground group-hover:text-primary" />
					) : (
						<IconSortDescending className="size-4 text-muted-foreground group-hover:text-primary" />
					)}
					<span className="opacity-60 truncate text-xs">
						Sorted by
					</span>
					<span className="font-mono text-[10px] font-medium opacity-90 ml-1 truncate bg-sidebar px-1 py-0.5 rounded-md">
						{currentField?.label}
					</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="start" className="w-56">
				<DropdownMenuRadioGroup
					value={sortField}
					onValueChange={onSortFieldChange}
				>
					{fields.map((field) => (
						<DropdownMenuRadioItem
							key={field.value}
							value={field.value}
							className="flex items-center gap-2"
						>
							{field.icon}
							{field.label}
						</DropdownMenuRadioItem>
					))}
				</DropdownMenuRadioGroup>
				<DropdownMenuSeparator />
				<DropdownMenuRadioGroup
					value={sortDirection}
					onValueChange={(value) =>
						onSortDirectionChange(value as "asc" | "desc")
					}
				>
					<DropdownMenuRadioItem
						value="asc"
						className="flex items-center gap-2"
					>
						<ArrowUp className="w-4 h-4" />
						Ascending
					</DropdownMenuRadioItem>
					<DropdownMenuRadioItem
						value="desc"
						className="flex items-center gap-2"
					>
						<ArrowDown className="w-4 h-4" />
						Descending
					</DropdownMenuRadioItem>
				</DropdownMenuRadioGroup>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
