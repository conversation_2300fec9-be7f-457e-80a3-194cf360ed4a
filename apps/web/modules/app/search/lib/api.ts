import { ObjectType } from "@repo/database";
import { useQuery } from "@tanstack/react-query";
import * as React from "react";

// PAYLOAD INTERFACES
export interface SearchParams {
	query: string;
	organizationId: string;
	type?: ObjectType | "user" | "all";
	limit?: number;
	offset?: number;
}

export interface SearchResult {
	id: string;
	title: string;
	type: ObjectType | "user";
	searchResultType: ObjectType | "user";
	url: string;
	createdAt: Date;
	updatedAt: Date;
	// Task-specific fields
	description?: string;
	status?: string;
	priority?: string;
	assignee?: {
		id: string;
		name: string;
		image?: string;
	};
	createdBy?: {
		id: string;
		name: string;
		image?: string;
	};
	// Note-specific fields
	content?: string;
	isPublished?: boolean;
	icon?: string;
	coverImage?: string;
	objectId?: string;
	objectType?: ObjectType;
	// Contact-specific fields
	firstName?: string;
	lastName?: string;
	name?: string;
	email?: string;
	phone?: string;
	jobTitle?: string;
	company?: {
		id: string;
		name: string;
	};
	avatarUrl?: string;
	// User-specific fields
	role?: string;
	username?: string;
	memberSince?: Date;
	lastLogin?: Date;
	// Property-specific fields
	propertyType?: string;
	market?: string;
	location?: {
		address?: {
			street?: string;
			street2?: string;
			city?: string;
			state?: string;
			zip?: string;
			country?: string;
		};
		location?: any;
	};
	creator?: {
		id: string;
		name: string;
		image?: string;
	};
}

export interface SearchResponse {
	results: SearchResult[];
	pagination: {
		total: number;
		limit: number;
		offset: number;
		hasMore: boolean;
	};
	counts: {
		tasks: number;
		notes: number;
		contacts: number;
		properties: number;
		users: number;
	};
}

// SEARCH FUNCTION
export async function searchContent(
	params: SearchParams,
): Promise<SearchResponse> {
	const {
		query,
		organizationId,
		type = "all",
		limit = 20,
		offset = 0,
	} = params;

	const searchParams = new URLSearchParams({
		query,
		organizationId,
		type,
		limit: limit.toString(),
		offset: offset.toString(),
	});

	const res = await fetch(`/api/search?${searchParams.toString()}`);

	if (!res.ok) {
		const error = await res.json();
		throw new Error(error.error || "Failed to search content");
	}

	return res.json();
}

// REACT QUERY HOOKS
export function useSearch(params: SearchParams, enabled = true) {
	return useQuery({
		queryKey: [
			"search",
			params.query,
			params.organizationId,
			params.type,
			params.limit,
			params.offset,
		],
		queryFn: () => searchContent(params),
		enabled: enabled && !!params.query && !!params.organizationId,
		staleTime: 1000 * 30, // Cache for 30 seconds
		gcTime: 1000 * 60 * 5, // Keep in cache for 5 minutes
	});
}

export function useSearchDebounced(
	query: string,
	organizationId: string,
	type: ObjectType | "user" | "all" = "all",
	limit = 20,
	offset = 0,
	debounceMs = 300,
) {
	const [debouncedQuery, setDebouncedQuery] = React.useState(query);

	React.useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedQuery(query);
		}, debounceMs);

		return () => clearTimeout(timer);
	}, [query, debounceMs]);

	return useSearch(
		{
			query: debouncedQuery,
			organizationId,
			type,
			limit,
			offset,
		},
		debouncedQuery.length > 0,
	);
}
