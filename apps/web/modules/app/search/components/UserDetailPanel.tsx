import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconCalendar,
	IconMail,
	IconShield,
	IconUser,
	IconAt,
} from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import type { UserSearchResultDetails } from "../types";

interface UserDetailPanelProps {
	user: UserSearchResultDetails;
	onNavigate?: () => void;
}

const HeadingText = ({ text }: { text: string }) => {
	return <h4 className="text-xs text-muted-foreground mb-1">{text}</h4>;
};

const formatDate = (dateString?: string): string => {
	if (!dateString) return "";
	
	try {
		const date = new Date(dateString);
		return date.toLocaleDateString(undefined, {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	} catch {
		return dateString;
	}
};

export function UserDetailPanel({
	user,
	onNavigate,
}: UserDetailPanelProps) {
	const router = useRouter();

	const handleOpenUser = () => {
		onNavigate?.();
		router.push(user.url);
	};

	return (
		<div
			className="w-80 border-l border-border bg-secondary/20"
			style={{
				height: "450px",
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* Fixed Header */}
			<div className="flex-shrink-0 p-4 border-b border-border">
				<div className="flex items-center gap-2">
					<div>
						<UserAvatar
							name={user.name}
							avatarUrl={user.avatarUrl}
							className="h-8 w-8"
						/>
					</div>
					<div className="flex flex-col">
						<span className="font-medium text-sm">
							{user.name}
						</span>
						{user.role && (
							<span className="text-xs text-muted-foreground capitalize">
								{user.role}
							</span>
						)}
					</div>
				</div>
			</div>

			{/* Scrollable Content */}
			<div
				className="p-4"
				style={{
					flex: 1,
					overflowY: "auto",
					minHeight: 0,
					maxHeight: "calc(450px - 80px - 30px)",
				}}
			>
				<div className="space-y-3">
					{user.email && (
						<div>
							<HeadingText text="Email" />
							<div className="flex items-center gap-2">
								<IconMail className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">{user.email}</span>
							</div>
						</div>
					)}

					{user.username && (
						<div>
							<HeadingText text="Username" />
							<div className="flex items-center gap-2">
								<IconAt className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">@{user.username}</span>
							</div>
						</div>
					)}

					{user.role && (
						<div>
							<HeadingText text="Role" />
							<div className="flex items-center gap-2">
								<IconShield className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm capitalize">
									{user.role}
								</span>
							</div>
						</div>
					)}

					{user.memberSince && (
						<div>
							<HeadingText text="Member Since" />
							<div className="flex items-center gap-2">
								<IconCalendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{formatDate(user.memberSince)}
								</span>
							</div>
						</div>
					)}

					{user.lastLogin && (
						<div>
							<HeadingText text="Last Login" />
							<div className="flex items-center gap-2">
								<IconUser className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm">
									{formatDate(user.lastLogin)}
								</span>
							</div>
						</div>
					)}

					{user.description && (
						<div>
							<HeadingText text="Description" />
							<p className="text-sm">{user.description}</p>
						</div>
					)}

					{user.createdByName && (
						<div>
							<HeadingText text="Added by" />
							<div className="flex items-center gap-2">
								<UserAvatar
									className="h-6 w-6"
									name={user.createdByName}
									avatarUrl={user.createdByAvatarUrl}
								/>
								<span className="text-sm">
									{user.createdByName}
								</span>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
} 