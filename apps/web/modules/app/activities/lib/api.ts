import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { createMentionNotification } from "@app/notifications/lib/api";

// Activity types
export interface Activity {
	id: string;
	organizationId: string;
	userId: string;
	recordId?: string;
	recordType?: "contact" | "company" | "property";
	type: "note" | "call" | "email" | "meeting" | "task" | "system";
	message: string;
	resolved: boolean;
	resolvedBy?: string;
	phone?: string;
	result?: string;
	system: boolean;
	edited: boolean;
	editedAt?: Date;
	mentionedUsers: string[];
	user: {
		id: string;
		name: string;
		email: string;
		image?: string;
	};
	resolver?: {
		id: string;
		name: string;
		email: string;
		image?: string;
	};
	replies: ActivityReply[];
	createdAt: Date;
	updatedAt: Date;
}

export interface ActivityReply {
	id: string;
	activityId: string;
	userId: string;
	message: string;
	edited: boolean;
	editedAt?: Date;
	user: {
		id: string;
		name: string;
		email: string;
		image?: string;
	};
	createdAt: Date;
	updatedAt: Date;
}

export interface CreateActivityPayload {
	recordId?: string;
	recordType?: "contact" | "company" | "property";
	type?: "note" | "call" | "email" | "meeting" | "task" | "system";
	message: string;
	phone?: string;
	result?: string;
	system?: boolean;
	mentionedUsers?: string[];
	organizationId: string;
}

export interface UpdateActivityPayload {
	message?: string;
	phone?: string;
	result?: string;
	resolved?: boolean;
	mentionedUsers?: string[];
	organizationId: string;
}

export interface CreateActivityReplyPayload {
	message: string;
	organizationId: string;
	mentionedUsers: string[];
}

// Query keys
export const activityKeys = {
	all: ["activities"] as const,
	byOrganization: (organizationId: string) => [...activityKeys.all, organizationId] as const,
	byRecord: (recordId: string, organizationId: string) => 
		[...activityKeys.byOrganization(organizationId), "record", recordId] as const,
	byId: (id: string, organizationId: string) => 
		[...activityKeys.byOrganization(organizationId), "activity", id] as const,
};

// Get activities by record ID
export function useActivities(recordId?: string, organizationId?: string) {
	return useQuery({
		queryKey: recordId && organizationId 
			? activityKeys.byRecord(recordId, organizationId)
			: activityKeys.all,
		queryFn: async () => {
			if (!organizationId) return [];
			
			const endpoint = recordId 
				? `/api/activities/record/${recordId}?organizationId=${organizationId}`
				: `/api/activities?organizationId=${organizationId}`;
				
			const response = await fetch(endpoint);
			
			if (!response.ok) {
				throw new Error("Failed to fetch activities");
			}
			
			const result = await response.json();
			return result.activities as Activity[];
		},
		enabled: !!organizationId,
	});
}

// Get paginated activities for organization
export function useActivitiesPaginated(organizationId?: string, limit = 50, offset = 0) {
	return useQuery({
		queryKey: [...activityKeys.byOrganization(organizationId || ""), "paginated", limit, offset],
		queryFn: async () => {
			if (!organizationId) return { activities: [], pagination: { total: 0, limit, offset, hasMore: false } };
			
			const response = await fetch(`/api/activities?organizationId=${organizationId}&limit=${limit}&offset=${offset}`);
			
			if (!response.ok) {
				throw new Error("Failed to fetch activities");
			}
			
			return response.json();
		},
		enabled: !!organizationId,
	});
}

// Create activity
export function useCreateActivity(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (payload: CreateActivityPayload) => {
			const response = await fetch("/api/activities", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(payload),
			});
			
			if (!response.ok) {
				const error = await response.json().catch(() => ({}));
				throw new Error(error.error || "Failed to create activity");
			}
			
			const result = await response.json();
			return result.activity as Activity;
		},
		onSuccess: async (data, variables) => {
			// Invalidate and refetch activities
			if (organizationId) {
				queryClient.invalidateQueries({ queryKey: activityKeys.byOrganization(organizationId) });
			}
			
			// If activity is for a specific record, invalidate that query too
			if (data.recordId && organizationId) {
				queryClient.invalidateQueries({ 
					queryKey: activityKeys.byRecord(data.recordId, organizationId) 
				});
			}

			// Create mention notifications for mentioned users
			if (variables.mentionedUsers && variables.mentionedUsers.length > 0) {
				try {
					// Create notifications for each mentioned user
					for (const mentionedUserId of variables.mentionedUsers) {
						// Don't create notification if user mentions themselves
						if (mentionedUserId !== data.userId) {
							await createMentionNotification({
								userId: mentionedUserId,
								mentionedById: data.userId,
								mentionedByName: data.user.name,
								mentionedByImage: data.user.image,
								activityId: data.id,
								organizationId: variables.organizationId,
								message: variables.message, // Include the activity message
								recordType: variables.recordType,
								recordId: variables.recordId,
							});
						}
					}
				} catch (error) {
					console.error('Failed to create mention notifications:', error);
					// Don't throw error to prevent activity creation from failing
				}
			}
			
			toast.success("Activity created successfully");
		},
		onError: (error: any) => {
			console.error("Error creating activity:", error);
			toast.error(error?.message || "Failed to create activity");
		},
	});
}

// Update activity
export function useUpdateActivity(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...payload }: UpdateActivityPayload & { id: string }) => {
			const response = await fetch(`/api/activities/${id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(payload),
			});
			
			if (!response.ok) {
				const error = await response.json().catch(() => ({}));
				throw new Error(error.error || "Failed to update activity");
			}
			
			const result = await response.json();
			return result.activity as Activity;
		},
		onSuccess: async (data, variables) => {
			// Invalidate and refetch activities
			if (organizationId) {
				queryClient.invalidateQueries({ queryKey: activityKeys.byOrganization(organizationId) });
			}
			
			// If activity is for a specific record, invalidate that query too
			if (data.recordId && organizationId) {
				queryClient.invalidateQueries({ 
					queryKey: activityKeys.byRecord(data.recordId, organizationId) 
				});
			}

			// Create mention notifications for mentioned users (only if there are new mentions)
			if (variables.mentionedUsers && variables.mentionedUsers.length > 0) {
				try {
					// Create notifications for each mentioned user
					for (const mentionedUserId of variables.mentionedUsers) {
						// Don't create notification if user mentions themselves
						if (mentionedUserId !== data.userId) {
							await createMentionNotification({
								userId: mentionedUserId,
								mentionedById: data.userId,
								mentionedByName: data.user.name,
								mentionedByImage: data.user.image,
								activityId: data.id,
								organizationId: variables.organizationId,
								message: variables.message, // Include the activity message
								recordType: data.recordType,
								recordId: data.recordId,
							});
						}
					}
				} catch (error) {
					console.error('Failed to create mention notifications:', error);
					// Don't throw error to prevent activity update from failing
				}
			}
			
			toast.success("Activity updated successfully");
		},
		onError: (error: any) => {
			console.error("Error updating activity:", error);
			toast.error(error?.message || "Failed to update activity");
		},
	});
}

// Delete activity
export function useDeleteActivity(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, organizationId: orgId }: { id: string; organizationId: string }) => {
			const response = await fetch(`/api/activities/${id}?organizationId=${orgId}`, {
				method: "DELETE",
			});
			
			if (!response.ok) {
				const error = await response.json().catch(() => ({}));
				throw new Error(error.error || "Failed to delete activity");
			}
			
			return { id };
		},
		onSuccess: (data, variables) => {
			// Invalidate and refetch activities
			if (organizationId) {
				queryClient.invalidateQueries({ queryKey: activityKeys.byOrganization(organizationId) });
			}
			
			queryClient.removeQueries({ 
				queryKey: activityKeys.byId(data.id, variables.organizationId) 
			});
			
			toast.success("Activity deleted successfully");
		},
		onError: (error: any) => {
			console.error("Error deleting activity:", error);
			toast.error(error?.message || "Failed to delete activity");
		},
	});
}

// Create activity reply
export function useCreateActivityReply(activityId: string, organizationId?: string, recordType?: string, recordId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (payload: CreateActivityReplyPayload) => {
			const response = await fetch(`/api/activities/${activityId}/replies`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(payload),
			});
			
			if (!response.ok) {
				const error = await response.json().catch(() => ({}));
				throw new Error(error.error || "Failed to create reply");
			}
			
			const result = await response.json();
			return result.reply as ActivityReply;
		},
		onSuccess: async (data, variables) => {
			// Invalidate and refetch activities to get updated replies
			if (organizationId) {
				queryClient.invalidateQueries({ queryKey: activityKeys.byOrganization(organizationId) });
			}

			// Create mention notifications for mentioned users in replies
			if (variables.mentionedUsers && variables.mentionedUsers.length > 0) {
				try {
					// Create notifications for each mentioned user
					for (const mentionedUserId of variables.mentionedUsers) {
						// Don't create notification if user mentions themselves
						if (mentionedUserId !== data.userId) {
							await createMentionNotification({
								userId: mentionedUserId,
								mentionedById: data.userId,
								mentionedByName: data.user.name,
								mentionedByImage: data.user.image,
								activityId: activityId,
								organizationId: variables.organizationId,
								message: variables.message, // Include the reply message
								recordType: recordType,
								recordId: recordId,
							});
						}
					}
				} catch (error) {
					console.error('Failed to create mention notifications for reply:', error);
					// Don't throw error to prevent reply creation from failing
				}
			}
			
			toast.success("Reply added successfully");
		},
		onError: (error: any) => {
			console.error("Error creating activity reply:", error);
			toast.error(error?.message || "Failed to add reply");
		},
	});
}

// Delete activity reply
export function useDeleteActivityReply(organizationId?: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ replyId, organizationId: orgId }: { replyId: string; organizationId: string }) => {
			const response = await fetch(`/api/activities/replies/${replyId}?organizationId=${orgId}`, {
				method: "DELETE",
			});
			
			if (!response.ok) {
				const error = await response.json().catch(() => ({}));
				throw new Error(error.error || "Failed to delete reply");
			}
			
			return { replyId };
		},
		onSuccess: () => {
			// Invalidate and refetch activities to get updated replies
			if (organizationId) {
				queryClient.invalidateQueries({ queryKey: activityKeys.byOrganization(organizationId) });
			}
			
			toast.success("Reply deleted successfully");
		},
		onError: (error: any) => {
			console.error("Error deleting activity reply:", error);
			toast.error(error?.message || "Failed to delete reply");
		},
	});
} 