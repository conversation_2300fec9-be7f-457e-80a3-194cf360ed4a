"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { useEditor, EditorContent, ReactRenderer, mergeAttributes } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Mention from '@tiptap/extension-mention';
import tippy from 'tippy.js';
import { Button } from '@ui/components/button';
import { cn } from '@ui/lib';
import { UserAvatar } from '@shared/components/UserAvatar';
import { useCreateActivity, useUpdateActivity } from '../lib/api';
import { MentionList } from './MentionList';
import type { ActiveOrganization } from "@repo/auth";
import { useFullOrganizationQuery } from '@app/organizations/lib/api';
import { formatPhoneNumber } from '@shared/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@ui/components/popover';
import {
	EmojiPicker,
	EmojiPickerContent,
	EmojiPickerFooter,
	EmojiPickerSearch,
} from "@shared/components/EmojiPicker";
import { IconAt, IconMoodPlus, IconMoodSmile, IconTrash } from '@tabler/icons-react';

const CALL_RESULTS = [
	"Connected",
	"No Answer",
	"Voicemail",
	"Busy",
	"Wrong Number",
	"Callback Requested",
	"Not Interested",
	"Interested",
	"Meeting Scheduled"
];

interface Member {
	user: {
		id: string;
		name: string;
		image?: string;
	};
}

interface Reaction {
	emoji: string;
	count: number;
	users: string[]; // Array of user IDs who reacted
}

interface ActivityEditorProps {
	isEdit?: any;
	handleStateChange: any;
	user: any;
	organization: ActiveOrganization;
	data: any;
	recordType: "contact" | "company" | "property";
	currentUser?: any; // Added to match UserActivity props
}

interface MentionNodeAttrs {
	id: string;
	label: string;
}

const ActivityEditor = ({ isEdit, handleStateChange, user, organization, data, recordType, currentUser = user }: ActivityEditorProps) => {
	const { mutateAsync: createActivity, isPending: isCreating } = useCreateActivity(organization?.id);
	const { mutateAsync: updateActivity, isPending: isUpdating } = useUpdateActivity(organization?.id);
	const { data: organizationData, isLoading: isLoadingOrganization } = useFullOrganizationQuery(organization?.id || '');
	
	const [value, setValue] = useState<string>('');
	const [callLog, setCallLog] = useState<boolean>(false);
	const [selectedPhone, setSelectedPhone] = useState<string>('');
	const [result, setResult] = useState<string>('');
	const [openEmojiPicker, setOpenEmojiPicker] = useState<boolean>(false);
	const [reactions, setReactions] = useState<Reaction[]>([]);

	const organizationMembers = useMemo(() => {
		if (!organizationData?.members || !Array.isArray(organizationData.members)) return [];
		return organizationData.members
			.filter((member: any) => member.user && member.user.id && member.user.name)
			.map((member: any) => ({
				id: member.user.id,
				label: member.user.name,
				image: member.user.image,
			}));
	}, [organizationData?.members]);

	const handleEmojiSelect = ({ emoji }: { emoji: string }) => {
		setOpenEmojiPicker(false);
		
		// Find if this emoji already exists in reactions
		const existingReactionIndex = reactions.findIndex(r => r.emoji === emoji);
		
		if (existingReactionIndex > -1) {
			// If user already reacted, remove their reaction
			const existingReaction = reactions[existingReactionIndex];
			if (existingReaction.users.includes(user.id)) {
				if (existingReaction.count === 1) {
					// Remove the reaction entirely if it was the last one
					setReactions(reactions.filter((_, i) => i !== existingReactionIndex));
				} else {
					// Decrease count and remove user from the list
					setReactions(reactions.map((reaction, i) => 
						i === existingReactionIndex 
							? {
								...reaction,
								count: reaction.count - 1,
								users: reaction.users.filter(id => id !== user.id)
							}
							: reaction
					));
				}
			} else {
				// Add user's reaction to existing emoji
				setReactions(reactions.map((reaction, i) => 
					i === existingReactionIndex 
						? {
							...reaction,
							count: reaction.count + 1,
							users: [...reaction.users, user.id]
						}
						: reaction
				));
			}
		} else {
			// Add new reaction
			setReactions([...reactions, {
				emoji,
				count: 1,
				users: [user.id]
			}]);
		}
	};

	// Initialize the TipTap editor with extensions and configuration
const editor = useEditor({
		extensions: [
			StarterKit,
			Placeholder.configure({
				placeholder: callLog ? "Add call notes..." : "Add note or select '/' to type a command",
			}),
			Mention.configure({
				HTMLAttributes: {
					class: 'mention bg-blue-100 text-blue-800 px-1 rounded',
				},
				renderLabel({ options, node }) {
					return `@${node.attrs.label ?? node.attrs.id}`;
				},
				suggestion: {
					items: ({ query }) => {
						return organizationMembers
							.filter((member) =>
								member.label.toLowerCase().includes(query.toLowerCase())
							)
							.slice(0, 5);
					},
					char: '@',
					command: ({ editor, range, props }) => {
						editor
							.chain()
							.focus()
							.insertContentAt(range, [
								{
									type: 'mention',
									attrs: props,
								},
								{
									type: 'text',
									text: ' ',
								},
							])
							.run();
					},
					allow: ({ editor, range }) => {
						return editor.can().insertContentAt(range, { type: 'mention' });
					},
					render: () => {
						let component: ReactRenderer | null = null;
						let popup: any[] | null = null;

						return {
							onStart: (props: any) => {
								component = new ReactRenderer(MentionList, {
									props,
									editor: props.editor,
								});

								if (!props.clientRect) {
									return;
								}

								popup = tippy('body', {
									getReferenceClientRect: props.clientRect,
									appendTo: () => document.body,
									content: component.element,
									showOnCreate: true,
									interactive: true,
									trigger: 'manual',
									placement: 'bottom-start',
								});
							},

							onUpdate(props: any) {
								component?.updateProps(props);

								if (!props.clientRect) {
									return;
								}

								popup?.[0].setProps({
									getReferenceClientRect: props.clientRect,
								});
							},

							onKeyDown(props: any) {
								if (props.event.key === 'Escape') {
									popup?.[0].hide();
									return true;
								}
								if (component?.ref && typeof component.ref === 'object' && 'onKeyDown' in component.ref) {
									return (component.ref.onKeyDown as (props: any) => boolean)(props);
								}
								return false;
							},

							onExit() {
								popup?.[0].destroy();
								component?.destroy();
							},
						};
					},
				},
			})
		],
		content: value,
		onUpdate: ({ editor }) => {
			setValue(editor.getHTML());
		},
		immediatelyRender: false,
		editorProps: {
			attributes: {
				class: 'prose prose-sm max-w-none focus:outline-none text-sm',
			},
		},
	}, [organizationMembers]);

	// Handle edit mode initialization
	useEffect(() => {
		if (isEdit && editor) {
			editor.commands.setContent(isEdit.message || '');
			setValue(isEdit.message || '');
			setCallLog(isEdit.type === "call");
			setSelectedPhone(isEdit.phone || '');
			setResult(isEdit.result || '');
		}
	}, [isEdit, editor]);

	/**
	 * Handle saving the activity (create or update)
	 */
const handleSave = async () => {
		if (!value.trim() || value === "<p></p>" || value === "<p><br></p>") return;

		try {
			// Extract mentioned user IDs
			const mentionedUserIds = editor?.getJSON().content
				?.flatMap(node => node.content ?? [])
				?.filter(node => node.type === 'mention')
				?.map(node => node?.attrs?.id)
				?? [];

			if (!isEdit) {
				await createActivity({
					recordId: data?.id,
					recordType,
					type: callLog ? "call" : "note",
					message: value,
					phone: selectedPhone || undefined,
					result: result || undefined,
					mentionedUsers: mentionedUserIds,
					organizationId: organization?.id || '',
				});
			} else {
				await updateActivity({
					id: isEdit.id,
					message: value,
					phone: selectedPhone || undefined,
					result: result || undefined,
					mentionedUsers: mentionedUserIds,
					organizationId: organization?.id || '',
				});
			}

			// Reset form
			if (editor) {
				editor.commands.clearContent();
			}

			setValue('');
			setCallLog(false);
			setSelectedPhone('');
			setResult('');
			handleStateChange('isEdit', null);
		} catch (error) {
			console.error("Error saving activity:", error);
		}
	};

	/**
	 * Cancel editing and reset form state
	 */
const handleCancelEdit = () => {
		handleStateChange('isEdit', null);
		if (editor) {
			editor.commands.clearContent();
		}
		setValue('');
		setCallLog(false);
		setSelectedPhone('');
		setResult('');
		setReactions([]);
	};

	const getPhoneNumbers = () => {
		if (!data?.phone) return [];
		if (Array.isArray(data.phone)) {
			return data.phone.map((p: any) => ({
				number: p.number || p.value || p,
				label: p.label || 'Phone'
			}));
		}
		return [];
	};

	const phoneNumbers = getPhoneNumbers();

	return (
		<div className="relative bg-sidebar border border-border rounded-2xl transition-all duration-300 hover:border-blue-500 group mt-2">
			<div className="max-h-[25vh] min-h-0 flex flex-col gap-2">
				<div className="overflow-hidden w-full h-full relative flex flex-col">
					<div className="flex flex-row items-center gap-3 p-4 w-full relative">
						{/* Activity Options - Visible on hover like in UserActivity */}
						{/* <div className="absolute right-1 top-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
							<div className="flex items-center gap-1 border border-border rounded-lg p-0.5 bg-sidebar shadow-sm">
								<Button
									variant="ghost"
									size="sm"
									onClick={handleCancelEdit}
									className="h-6 w-6 p-1.5 hover:bg-muted"
									title="Cancel"
								>
									<IconTrash className="h-3 w-3" />
								</Button>
							</div>
						</div> */}
						<div className="flex items-center justify-center flex-shrink-0">
							<UserAvatar 
								name={user?.name}
								avatarUrl={user?.image}
								className="h-6 w-6 rounded-full"
							/>
						</div>
						<div className="flex-1 min-w-0">
							<EditorContent
								className="tiptap h-full w-full prose prose-sm max-w-none [&_.ProseMirror]:outline-none"
								editor={editor}
							/>
						</div>
					</div>

					{/* Reactions Display */}
					{reactions.length > 0 && (
						<div className="flex flex-wrap gap-[5px] self-start px-4 pb-2">
							{reactions.map((reaction, index) => (
								<button
									key={index}
									type="button"
									aria-pressed={reaction.users.includes(user.id)}
									data-state={reaction.users.includes(user.id) ? "on" : "off"}
									onClick={() => handleEmojiSelect({ emoji: reaction.emoji })}
									className={cn(
										"rounded-lg border-none transition-all duration-200",
										"bg-[#171919] hover:bg-[#293C60]/80",
										reaction.users.includes(user.id) && "shadow-[inset_0_0_0_1px_#266DF0] bg-[#293C60]"
									)}
								>
									<div className="flex flex-row items-center justify-start gap-1 px-2 py-1">
										<div className="font-inter text-[14px] leading-5 font-normal text-[#9FA1A7]">
											{reaction.emoji}
										</div>
										<div 
											className={cn(
												"font-inter text-[12px] leading-4 font-medium tracking-[-0.02em]",
												reaction.users.includes(user.id) ? "text-[#266DF0]" : "text-[#9FA1A7]"
											)}
										>
											{reaction.count}
										</div>
									</div>
								</button>
							))}
							<button
								type="button"
								aria-label="Pick emoji"
								onClick={() => setOpenEmojiPicker(true)}
								className="rounded-lg border-none transition-all duration-200 bg-[#171919] hover:bg-[#293C60]/80 p-2"
							>
								<IconMoodPlus className="h-3 w-3 text-[#9FA1A7]" />
							</button>
						</div>
					)}

					{/* Divider */}
					<div className="h-[1px] bg-[#F4F5F6]/[0.04] transition-opacity duration-140" />

					{/* Bottom Actions */}
					<div className="flex items-center justify-between p-2 w-full">
						<div className="flex items-center gap-2">
							{/* Call Log Switch */}
							{recordType === 'contact' && (
								<div className='flex items-center gap-2'>
									<Switch
										checked={callLog}
										onCheckedChange={setCallLog}
										id="call-log"
										className="h-4 w-7"
									/>
									<Label htmlFor="call-log" className='text-xs text-muted-foreground truncate'>
										{callLog ? "Call Log" : "Note"}
									</Label>
								</div>
							)}

							{/* Phone Numbers */}
							{callLog && phoneNumbers.length > 0 && (
								<Select
									value={selectedPhone}
									onValueChange={setSelectedPhone}
								>
									<SelectTrigger iconSize="size-3 ml-2" className="h-7 text-xs border-transparent bg-transparent hover:bg-muted/50 rounded-lg truncate">
										<SelectValue placeholder="Phone" />
									</SelectTrigger>
									<SelectContent className='rounded-xl'>
										{phoneNumbers.map((phone: any, index: number) => (
											<SelectItem value={phone.number} key={index} className='rounded-lg'>
												{formatPhoneNumber(phone.number)}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							)}

							{/* Call Result */}
							{callLog && (
								<Select
									value={result}
									onValueChange={setResult}
								>
									<SelectTrigger iconSize="size-3 ml-2" className="h-7 text-xs border-transparent bg-transparent hover:bg-muted/50 rounded-lg truncate">
										<SelectValue placeholder="Result" />
									</SelectTrigger>
									<SelectContent className='rounded-xl'>
										{CALL_RESULTS.map((result, index) => (
											<SelectItem value={result} key={index} className='rounded-lg'>
												{result}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							)}
						</div>

						<div className="flex items-center gap-2">
							{/* Quick Actions */}
							<div className="flex items-center gap-2">
								{/* {!reactions.length && (
									<Popover onOpenChange={setOpenEmojiPicker} open={openEmojiPicker}>
										<PopoverTrigger asChild>
											<Button
												variant="ghost"
												size="sm"
												className="p-1 py-0.5 h-6 hover:bg-muted/50 rounded-lg"
												title="Add reaction"
											>
												<IconMoodPlus className="size-4 text-muted-foreground" />
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-fit p-0" align="start" side="top">
											<EmojiPicker
												className="h-[342px]"
												onEmojiSelect={handleEmojiSelect}
											>
												<EmojiPickerSearch />
												<EmojiPickerContent />
												<EmojiPickerFooter />
											</EmojiPicker>
										</PopoverContent>
									</Popover>
								)} */}
								<Button
									variant="ghost"
									size="sm"
									className="p-1 py-0.5 h-6 hover:bg-muted/50 rounded-lg"
									title="Mention someone"
									disabled={isLoadingOrganization || !organizationMembers?.length}
									onClick={() => {
										if (editor) {
											editor.commands.insertContent('@');
											editor.commands.focus('end');
										}
									}}
								>
									<IconAt className="size-4 text-muted-foreground" />
								</Button>
							</div>

							{/* Submit Button */}
							<Button
								disabled={!value.trim() || value === "<p></p>" || value === "<p><br></p>" || isCreating || isUpdating}
								size="sm"
								className={cn(
									"h-7 px-3 rounded-lg bg-[#266DF0] hover:bg-[#4285FF] transition-colors",
									"shadow-[inset_0px_0px_0px_1px_rgba(244,245,246,0.1),0px_2px_4px_-2px_rgba(38,109,240,0.12),0px_3px_6px_-2px_rgba(38,109,240,0.08)]",
									"disabled:opacity-40 disabled:cursor-default"
								)}
								onClick={handleSave}
							>
								{isCreating || isUpdating ? "Saving..." : (isEdit ? "Update" : "Comment")}
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ActivityEditor; 