"use client";

import React, { useState, useMemo } from 'react';
import { formatDistanceToNow, format } from 'date-fns';
import { cn } from '@ui/lib';
import { UserAvatar } from '@shared/components/UserAvatar';
import { Button } from '@ui/components/button';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { MoreHorizontal, Trash2, Send } from 'lucide-react';
import { useCreateActivityReply, useDeleteActivityReply } from '../lib/api';
import type { ActiveOrganization } from "@repo/auth";
import type { Activity, ActivityReply } from '../lib/api';
import { useEditor, EditorContent, ReactRenderer } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Mention from '@tiptap/extension-mention';
import tippy from 'tippy.js';
import { useFullOrganizationQuery } from '@app/organizations/lib/api';
import { MentionList } from './MentionList';
import { IconAt } from '@tabler/icons-react';

type ActivityRepliesProps = {
	activity: Activity;
	organization: ActiveOrganization;
	currentUser: any;
};

const ActivityReplies = ({ activity, organization, currentUser }: ActivityRepliesProps) => {
	const [showReplyInput, setShowReplyInput] = useState(false);
	const [deleteReplyId, setDeleteReplyId] = useState<string | null>(null);
	const { data: organizationData } = useFullOrganizationQuery(organization?.id || '');

	const { mutateAsync: createReply, isPending: isCreatingReply } = useCreateActivityReply(
		activity.id, 
		organization?.id,
		activity.recordType,
		activity.recordId
	);
	const { mutateAsync: deleteReply } = useDeleteActivityReply(organization?.id);

	// Transform organization members into the format needed for mentions
	const organizationMembers = useMemo(() => {
		if (!organizationData?.members) return [];
		return organizationData.members.map((member: any) => ({
			id: member.user.id,
			label: member.user.name,
			image: member.user.image,
		}));
	}, [organizationData?.members]);

	const memoizedSuggestion = useMemo(() => {
		return {
			items: ({ query }: { query: string }) => {
				if (!organizationMembers?.length) return [];
				return organizationMembers
					.filter((user: any) => 
						user.label.toLowerCase().includes((query || '').toLowerCase())
					)
					.slice(0, 5);
			},
			render: () => {
				let component: ReactRenderer | null = null;
				let popup: any[] | null = null;

				return {
					onStart: (props: any) => {
						component = new ReactRenderer(MentionList, {
							props,
							editor: props.editor,
						});

						if (!props.clientRect) {
							return;
						}

						popup = tippy('body', {
							getReferenceClientRect: props.clientRect,
							appendTo: () => document.body,
							content: component.element,
							showOnCreate: true,
							interactive: true,
							trigger: 'manual',
							placement: 'bottom-start',
							arrow: false,
							offset: [0, 10],
							hideOnClick: false,
							theme: 'custom',
						});
					},

					onUpdate(props: any) {
						component?.updateProps(props);

						if (!props.clientRect) {
							return;
						}

						popup?.[0].setProps({
							getReferenceClientRect: props.clientRect,
						});
					},

					onKeyDown(props: any) {
						if (props.event.key === 'Escape') {
							popup?.[0].hide();
							return true;
						}

						if (component?.ref && typeof component.ref === 'object' && 'onKeyDown' in component.ref) {
							return (component.ref as { onKeyDown: (props: any) => boolean }).onKeyDown(props);
						}
						return false;
					},

					onExit() {
						popup?.[0].destroy();
						component?.destroy();
					},
				};
			},
		};
	}, [organizationMembers]);

	const editor = useEditor({
		extensions: [
			StarterKit,
			Placeholder.configure({
				placeholder: "Write a reply...",
			}),
			Mention.configure({
				HTMLAttributes: {
					class: 'inline-flex items-center rounded bg-blue-100 px-1.5 py-0.5 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200',
				},
				renderLabel({ options, node }) {
					return `@${node.attrs.label ?? node.attrs.id}`;
				},
				suggestion: {
					char: '@',
					items: ({ query }) => memoizedSuggestion.items({ query }),
					render: memoizedSuggestion.render,
					command: ({ editor, range, props }) => {
						editor
							.chain()
							.focus()
							.deleteRange(range)
							.insertContent([
								{
									type: 'mention',
									attrs: props,
								},
								{
									type: 'text',
									text: ' ',
								},
							])
							.run();
					},
				},
			})
		],
		content: '',
		editorProps: {
			attributes: {
				class: 'prose prose-sm max-w-none focus:outline-none text-sm min-h-[60px] px-3 py-2',
			},
		},
	});

	const handleSubmitReply = async () => {
		if (!editor || !editor.getText().trim()) return;

		try {
			// Extract mentioned user IDs
			const mentionedUserIds = editor.getJSON().content
				?.flatMap(node => node.content ?? [])
				?.filter(node => node.type === 'mention')
				?.map(node => node?.attrs?.id)
				?? [];

			await createReply({
				message: editor.getHTML(),
				organizationId: organization?.id || '',
				mentionedUsers: mentionedUserIds,
			});

			editor.commands.clearContent();
			setShowReplyInput(false);
		} catch (error) {
			console.error('Error creating reply:', error);
		}
	};

	const handleDeleteReply = async (replyId: string) => {
		try {
			await deleteReply({
				replyId,
				organizationId: organization?.id || '',
			});
			setDeleteReplyId(null);
		} catch (error) {
			console.error('Error deleting reply:', error);
		}
	};

	const replies = activity.replies || [];

	return (
		<div className="space-y-4">
			{/* Existing Replies */}
			{replies.map((reply: ActivityReply) => (
				<div key={reply.id} className="flex items-start gap-3">
					<UserAvatar
						name={reply.user.name}
						avatarUrl={reply.user.image}
						className="h-6 w-6"
					/>
					<div className="flex-1 min-w-0">
						<div className="flex items-center gap-2 mb-1">
							<span className="font-medium text-sm">{reply.user.name}</span>
							{reply.edited && (
								<span className="text-xs text-muted-foreground">(edited)</span>
							)}
						</div>
						<div className="text-sm whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: reply.message }} />
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="text-xs text-muted-foreground mt-1">
									{formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
								</div>
							</TooltipTrigger>
							<TooltipContent>
								{format(new Date(reply.createdAt), 'PPpp')}
							</TooltipContent>
						</Tooltip>
					</div>

					{/* Reply Actions */}
					{reply.userId === currentUser?.id && (
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="h-6 w-6 p-0"
								>
									<MoreHorizontal className="h-3 w-3" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem 
									onClick={() => setDeleteReplyId(reply.id)}
									className="text-destructive"
								>
									<Trash2 className="h-3 w-3 mr-2" />
									Delete
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					)}
				</div>
			))}

			{/* Reply Input */}
			{showReplyInput ? (
				<div className="flex flex-col gap-2">
					<div className="flex items-start gap-3">
						<UserAvatar
							name={currentUser?.name}
							avatarUrl={currentUser?.image}
							className="h-6 w-6"
						/>
						<div className="flex-1">
							<div className="relative bg-background border border-input rounded-md">
								<EditorContent editor={editor} />
								<Button
									variant="ghost"
									size="sm"
									className="absolute right-2 top-2 p-1 h-6 hover:bg-muted/50 rounded-lg"
									title="Mention someone"
									onClick={() => {
										if (editor) {
											editor.commands.insertContent('@');
											editor.commands.focus('end');
										}
									}}
								>
									<IconAt className="size-4 text-muted-foreground" />
								</Button>
							</div>
						</div>
					</div>
					<div className="flex justify-end gap-2">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => {
								setShowReplyInput(false);
								editor?.commands.clearContent();
							}}
						>
							Cancel
						</Button>
						<Button
							size="sm"
							onClick={handleSubmitReply}
							disabled={!editor?.getText().trim() || isCreatingReply}
						>
							<Send className="h-3 w-3 mr-2" />
							Reply
						</Button>
					</div>
				</div>
			) : (
				<Button
					variant="ghost"
					size="sm"
					className="text-muted-foreground"
					onClick={() => setShowReplyInput(true)}
				>
					Write a reply...
				</Button>
			)}

			{/* Delete Reply Dialog */}
			<AlertDialog open={!!deleteReplyId} onOpenChange={(open) => !open && setDeleteReplyId(null)}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Reply</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete this reply? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction 
							onClick={() => deleteReplyId && handleDeleteReply(deleteReplyId)}
							className="bg-destructive text-destructive-foreground"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default ActivityReplies; 