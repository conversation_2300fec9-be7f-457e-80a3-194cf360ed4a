"use client";

import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { UserAvatar } from '@shared/components/UserAvatar';

interface MentionListProps {
  items: Array<{ 
    id: string; 
    label: string; 
    image?: string;
    email?: string;
  }>;
  command: (item: { id: string; label: string }) => void;
}

export const MentionList = forwardRef<any, MentionListProps>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = props.items[index];
    if (item) {
      props.command(item);
    }
  };

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length);
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  return (
    <div className="bg-sidebar border border-border rounded-xl shadow-lg overflow-hidden p-1 relative max-h-[400px] min-w-[300px] overflow-y-auto space-y-1">
      <div className="text-muted-foreground text-xs font-medium p-1">Users</div>
      {props.items && props.items.length > 0 ? (
        props.items.map((item, index) => (
          <div className="flex-1 gap-1" key={index}>
          <button
            className={`flex items-center gap-1 border border-transparent rounded-lg m-0 px-3 py-2 text-left w-full cursor-pointer h-8 hover:bg-muted/50 hover:!border hover:!border-border`}
            onClick={() => selectItem(index)}
            type="button"
          >
            <UserAvatar
              name={item.label}
              avatarUrl={item.image}
              className="h-5 w-5"
            />
            <div className="flex flex-col">
              <span className="text-sm font-medium">{item.label}</span>
              <span className="text-xs">{item.email || ''}</span>
            </div>
          </button>
          </div>
        ))
      ) : (
        <div className="flex items-center justify-center gap-2 bg-transparent border border-dashed border-border rounded-lg m-0 px-3 py-4 text-left w-full">
          <span className="text-sm text-muted-foreground">No users found</span>
        </div>
      )}
    </div>
  );
});

MentionList.displayName = 'MentionList'; 