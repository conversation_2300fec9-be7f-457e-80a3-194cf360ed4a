"use client";

import React, { useState } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	Ta<PERSON>Trigger,
} from "@ui/components/tabs";
import { Label } from "@ui/components/label";
import ActivityEditor from "./ActivityEditor";
import ActivityList from "./ActivityList";
import type { ActiveOrganization } from "@repo/auth";
import { IconActivity, IconBuilding, IconCalendarDollar, IconChartBar, IconFile, IconListNumbers, IconMail, IconNote, IconNotes } from '@tabler/icons-react';
import UnitMixContent from '@app/properties/components/unit-mixes';
import SaleHistoryContent from '@app/properties/components/sale-history';
import { NotesContent } from '@shared/components/NotesContent';
import FilesContent from '@app/files/components/FilesContent';

interface ActivitiesProps {
	data: any;
	organization: ActiveOrganization;
	user: any;
	recordType: "contact" | "company" | "property";
}

interface State {
	isEdit?: any;
}

const Activities = ({ data, organization, user, recordType }: ActivitiesProps) => {
	const [state, setState] = React.useState<State>({
		isEdit: null
	});

	const handleStateChange = (key: string, value: any) => {
		setState((prevState) => ({
			...prevState,
			[key]: value,
		}));
	};

	if (!data) {
		return <div className="flex items-center justify-center h-full">
			<div className="text-center text-muted-foreground">
				<div className="text-lg font-medium mb-2">Loading...</div>
			</div>
		</div>;
	}

	return (
		<div className="flex flex-col h-full">
			<Tabs defaultValue="activity" className="w-full flex flex-col h-full">
				{/* Sticky Tabs Header */}
				<div className="sticky top-0 z-10 border-b border-muted px-2">
					<TabsList className="w-full justify-start gap-2 h-[48px] rounded-none !bg-transparent !p-0 !border-none">
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="activity">
							<IconActivity className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Activity</Label>
						</TabsTrigger>
						{recordType === "contact" && (
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="emails">
							<IconMail className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Emails</Label>
						</TabsTrigger>
						)}
						{recordType === "contact" && (
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="companies">
							<IconBuilding className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Companies</Label>
						</TabsTrigger>
						)}
						{recordType === "property" && (
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="unit-mixes">
							<IconListNumbers className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Unit Mixes</Label>
						</TabsTrigger>
						)}
						{recordType === "property" && (
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="sale-history">
							<IconCalendarDollar className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Sale History</Label>
						</TabsTrigger>
						)}
						{(recordType === "property" || recordType === "contact") && (
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="notes">
							<IconNote className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Notes</Label>
						</TabsTrigger>
						)}
						{(recordType === "property" || recordType === "contact") && (
						<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="files">
							<IconFile className="w-4 h-4 group-hover:text-foreground" />
							<Label className="group-hover:text-foreground">Files</Label>
						</TabsTrigger>
						)}
					</TabsList>
				</div>

				{/* Tab Content with proper scrolling */}
				<div className="flex-1 min-h-0">
					<TabsContent value="activity" className="px-2 h-full mt-0 focus-visible:outline-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0">
						<div className="flex flex-col h-full">
							{/* Activity Editor - Fixed at top */}
							<div className="flex-shrink-0">
								<ActivityEditor
									isEdit={state.isEdit}
									handleStateChange={handleStateChange}
									user={user}
									organization={organization}
									data={data}
									recordType={recordType}
								/>
							</div>
							
							{/* Activity List - Scrollable */}
							<div className="flex-1 overflow-y-auto min-h-0 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
								<div>
									<ActivityList
										data={data}
										user={user}
										organization={organization}
										isEdit={state.isEdit}
										handleStateChange={handleStateChange}
									/>
								</div>
							</div>
						</div>
					</TabsContent>

					<TabsContent value="unit-mixes">
						<UnitMixContent />
					</TabsContent>

					<TabsContent value="sale-history">
						<SaleHistoryContent />
					</TabsContent>

					<TabsContent value="notes">
						<NotesContent 
							objectId={data.id}
							objectType={recordType} 
							objectName={data.name} 
						/>
					</TabsContent>
					
					<TabsContent value="emails" className="px-2 h-full mt-0">
						<div className="flex items-center justify-center h-full">
							<div className="text-center text-muted-foreground">
								<div className="text-lg font-medium mb-2">Email Activities</div>
								<div className="text-sm">Email activities will be displayed here when implemented.</div>
							</div>
						</div>
					</TabsContent>
					
					<TabsContent value="companies" className="px-2 h-full mt-0">
						<div className="flex items-center justify-center h-full">
							<div className="text-center text-muted-foreground">
								<div className="text-lg font-medium mb-2">Related Companies</div>
								<div className="text-sm">Company relationships will be displayed here when implemented.</div>
							</div>
						</div>
					</TabsContent>

					<TabsContent value="files" className="h-full mt-0 focus-visible:outline-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0">
						<FilesContent
							objectId={data.id}
							objectType={recordType}
							objectName={data.name}
						/>
					</TabsContent>
				</div>
			</Tabs>
		</div>
	);
};

export default Activities; 