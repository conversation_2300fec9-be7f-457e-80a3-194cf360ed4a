import { CALL_RESULTS } from "@app/shared/lib/constants";
import {
	IconMinus,
	IconTrendingDown,
	IconTrendingUp,
} from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import {
	Card,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import React, { useState } from "react";

interface CallResultsWidgetProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
}

export function CallResultsWidget({
	selectedFilter,
	data,
	isLoading,
}: CallResultsWidgetProps) {
	const [selectedCallResult, setSelectedCallResult] = useState(CALL_RESULTS[0].value);
	const callData = data?.callResultsCount ?? 0;
	const callPercentage = data?.callPercentage ?? 0;

	const getTrendIcon = () => {
		if (callPercentage > 0) return <IconTrendingUp className="size-4" />;
		if (callPercentage < 0) return <IconTrendingDown className="size-4" />;
		return <IconMinus className="size-4" />;
	};

	const getTrendText = () => {
		if (callPercentage > 0) return "Call volume increasing";
		if (callPercentage < 0) return "Call volume decreasing";
		return "Stable call volume";
	};

	const getBadgeStatus = () => {
		if (callPercentage > 70) return "success";
		if (callPercentage > 50) return "info";
		if (callPercentage > 25) return "warning";
		return "error";
	};

	return (
		<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
			<CardHeader className="pb-2">
				<div className="flex items-center justify-between">
					<CardDescription>Call Results</CardDescription>
					<Badge status={getBadgeStatus()}>
						{getTrendIcon()}
						{isLoading ? "..." : `${callPercentage}%`}
					</Badge>
				</div>
				<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
					{isLoading
						? "..."
						: Array.isArray(callData)
							? callData.length
							: callData}
				</CardTitle>
				<div className="pt-2">
					<Select
						value={selectedCallResult}
						onValueChange={(value) => setSelectedCallResult(value)}
					>
						<SelectTrigger className="w-full h-8 rounded-lg">
							<SelectValue placeholder={selectedCallResult} />
						</SelectTrigger>
						<SelectContent>
							{CALL_RESULTS.map((item, index) => (
								<SelectItem
									key={index}
									value={item.value}
									onClick={() => setSelectedCallResult(item.value)}
								>
									{item.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			</CardHeader>
			<CardFooter className="flex-col items-start gap-1.5 text-sm pt-0">
				<div className="line-clamp-1 flex gap-2 font-medium">
					{getTrendText()} {getTrendIcon()}
				</div>
				<div className="text-muted-foreground">
					Total calls for {selectedFilter.toLowerCase()}
				</div>
			</CardFooter>
		</Card>
	);
}
