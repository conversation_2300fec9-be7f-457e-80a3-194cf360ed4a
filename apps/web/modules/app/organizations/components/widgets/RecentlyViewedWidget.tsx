import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import React, { useState } from "react";
import RecentlyViewed from "../RecentlyViewed";

interface RecentlyViewedWidgetProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
	user: any;
	activeOrganization: any;
}

export function RecentlyViewedWidget({
	selectedFilter,
	data,
	isLoading,
	user,
	activeOrganization,
}: RecentlyViewedWidgetProps) {
	const [recordType, setRecordType] = useState("contact");

	return (
		<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
			<CardHeader className="flex flex-col items-stretch space-y-2 border-b px-6 py-5 sm:py-6 sm:flex-row">
				<div className="flex flex-1 flex-col justify-center gap-1">
					<CardTitle>Recently Viewed</CardTitle>
					<CardDescription>
						Records viewed {selectedFilter.toLowerCase()}.
					</CardDescription>
				</div>
				<Select
					value={recordType}
					onValueChange={(value) => setRecordType(value)}
				>
					<SelectTrigger className="w-[180px] h-8 rounded-lg">
						<SelectValue
							defaultValue={"contact"}
							placeholder="Contacts"
						/>
					</SelectTrigger>
					<SelectContent>
						{[
							{ value: "contact", label: "Contacts" },
							{ value: "property", label: "Properties" },
							{ value: "company", label: "Companies" },
						].map((item, index) => (
							<SelectItem key={index} value={item.value}>
								{item.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</CardHeader>
			<CardContent className="p-2">
				<RecentlyViewed
					user={user}
					organization={activeOrganization}
					recordType={recordType}
					currentWorkspace={activeOrganization}
					currentUser={user}
				/>
			</CardContent>
		</Card>
	);
}
