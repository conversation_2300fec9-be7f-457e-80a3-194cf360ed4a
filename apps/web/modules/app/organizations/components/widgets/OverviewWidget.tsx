import { Card } from "@ui/components/card";
import React from "react";
import Overview from "../Overview";

interface OverviewWidgetProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
}

export function OverviewWidget({
	selectedFilter,
	data,
	isLoading,
}: OverviewWidgetProps) {
	const callData = data?.callResultsCount ?? 0;
	const noteData = data?.notesCount ?? 0;
	const taskData = data?.tasksNotDoneCount ?? 0;
	const timeSeriesData = data?.timeSeriesData ?? [];

	return (
		<Card>
			<Overview
				callData={callData}
				noteData={noteData}
				taskData={taskData}
				selectedFilter={selectedFilter}
				timeSeriesData={timeSeriesData}
			/>
		</Card>
	);
}
