import {
	IconMinus,
	IconTrendingDown,
	IconTrendingUp,
} from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import {
	Card,
	CardAction,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import React from "react";

interface TasksSummaryWidgetProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
}

export function TasksSummaryWidget({
	selectedFilter,
	data,
	isLoading,
}: TasksSummaryWidgetProps) {
	const taskData = data?.tasksNotDoneCount ?? 0;
	const taskPercentage = data?.tasksPercentage ?? 0;

	const getTrendIcon = () => {
		if (taskPercentage > 0) return <IconTrendingUp className="size-4" />;
		if (taskPercentage < 0) return <IconTrendingDown className="size-4" />;
		return <IconMinus className="size-4" />;
	};

	const getBadgeStatus = () => {
		if (taskPercentage > 70) return "success";
		if (taskPercentage > 50) return "info";
		if (taskPercentage > 25) return "warning";
		return "error";
	};

	const getTrendText = () => {
		if (taskPercentage > 0) return "Tasks not completed increasing";
		if (taskPercentage < 0) return "Tasks not completed decreasing";
		return "Stable tasks not completed";
	};

	return (
		<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardDescription>Tasks Not Completed</CardDescription>
					<Badge status={getBadgeStatus()}>
						{getTrendIcon()}
						{isLoading ? "..." : `${taskPercentage}%`}
					</Badge>
				</div>
				<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
					{isLoading ? "..." : taskData}
				</CardTitle>
			</CardHeader>
			<CardFooter className="flex-col items-start gap-1.5 text-sm">
				<div className="line-clamp-1 flex gap-2 font-medium">
					{getTrendText()} {getTrendIcon()}
				</div>
				<div className="text-muted-foreground">
					Tasks pending for {selectedFilter.toLowerCase()}
				</div>
			</CardFooter>
		</Card>
	);
}
