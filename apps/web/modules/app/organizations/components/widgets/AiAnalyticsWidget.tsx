import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconSparkles } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import { Bot, TrendingUp, Users, Zap } from "lucide-react";
import React from "react";

interface AiAnalyticsWidgetProps {
	selectedFilter: string;
	data: any;
	isLoading: boolean;
}

interface AiUsageData {
	currentPeriod: {
		total: number;
		used: number;
		remaining: number;
	};
	breakdown: {
		dataChat: { credits: number; percentage: number; sessions: number };
		taskCreation: { credits: number; percentage: number; sessions: number };
		analytics: { credits: number; percentage: number; sessions: number };
	};
	organizationStats?: {
		totalUsers: number;
		activeUsers: number;
		totalCreditsUsed: number;
	};
	permissions?: {
		isAdmin: boolean;
	};
}

export function AiAnalyticsWidget({
	selectedFilter,
	data,
	isLoading: parentLoading,
}: AiAnalyticsWidgetProps) {
	const { activeOrganization } = useActiveOrganization();

	const { data: aiData, isLoading } = useQuery({
		queryKey: ["ai-usage-widget", activeOrganization?.id],
		queryFn: async (): Promise<AiUsageData> => {
			if (!activeOrganization?.id) throw new Error("No organization");

			const response = await fetch(
				`/api/ai/usage/${activeOrganization.id}`,
				{
					method: "GET",
					headers: {
						"Content-Type": "application/json",
					},
				},
			);
			if (!response.ok) {
				throw new Error("Failed to fetch AI usage data");
			}
			return (await response.json()) as AiUsageData;
		},
		enabled: !!activeOrganization?.id,
		refetchInterval: 30000, // Refresh every 30 seconds
	});

	if (isLoading || parentLoading) {
		return (
			<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
				<CardHeader>
					<CardDescription>AI Usage Analytics</CardDescription>
					<CardTitle className="text-2xl font-semibold">
						<div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						<div className="h-2 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
						<div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
					</div>
				</CardContent>
			</Card>
		);
	}

	if (!aiData) {
		return (
			<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
				<CardHeader>
					<CardDescription>AI Usage Analytics</CardDescription>
					<CardTitle className="text-2xl font-semibold flex items-center gap-2">
						<Bot className="h-6 w-6" />
						No Data
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground">
						Start using AI features to see analytics here
					</p>
				</CardContent>
			</Card>
		);
	}

	const usagePercentage =
		(aiData.currentPeriod.used / aiData.currentPeriod.total) * 100;
	const isAdmin = aiData.permissions?.isAdmin;

	return (
		<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm">
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardDescription className="flex items-center gap-2">
						<IconSparkles className="h-4 w-4" />
						AI Usage Analytics
					</CardDescription>
					{isAdmin && (
						<Badge
							variant="views"
							className="text-xs flex items-center gap-1"
						>
							<Users className="h-3 w-3" />
							Admin View
						</Badge>
					)}
				</div>
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<CardTitle className="text-xl font-semibold flex items-center gap-2">
							<Zap className="h-5 w-5 text-blue-500" />
							{aiData.currentPeriod.used}
						</CardTitle>
						<span className="text-sm text-muted-foreground">
							/ {aiData.currentPeriod.total} credits
						</span>
					</div>
					<Progress value={usagePercentage} className="h-2" />
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Organization Stats for Admins */}
				{isAdmin && aiData.organizationStats && (
					<div className="grid grid-cols-2 gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
						<div className="text-center">
							<div className="text-lg font-bold text-blue-600">
								{aiData.organizationStats.activeUsers}
							</div>
							<div className="text-xs text-muted-foreground">
								Active Users
							</div>
						</div>
						<div className="text-center">
							<div className="text-lg font-bold text-blue-600">
								{aiData.organizationStats.totalCreditsUsed}
							</div>
							<div className="text-xs text-muted-foreground">
								Total Used
							</div>
						</div>
					</div>
				)}

				{/* Feature Breakdown */}
				<div className="space-y-2">
					<h4 className="text-sm font-medium">Top Features</h4>
					<div className="space-y-1">
						{[
							{
								name: "Data Chat",
								data: aiData.breakdown.dataChat,
							},
							{
								name: "Task Creation",
								data: aiData.breakdown.taskCreation,
							},
							{
								name: "Analytics",
								data: aiData.breakdown.analytics,
							},
						]
							.filter((feature) => feature.data.credits > 0)
							.sort((a, b) => b.data.credits - a.data.credits)
							.slice(0, 2)
							.map((feature) => (
								<div
									key={feature.name}
									className="flex items-center justify-between text-sm"
								>
									<span className="text-muted-foreground">
										{feature.name}
									</span>
									<div className="flex items-center gap-1">
										<span className="font-medium">
											{feature.data.credits}
										</span>
										<span className="text-xs text-muted-foreground">
											({feature.data.percentage}%)
										</span>
									</div>
								</div>
							))}
					</div>
				</div>

				{/* Status Indicator */}
				<div className="flex items-center gap-2 text-sm">
					<TrendingUp className="h-4 w-4 text-green-500" />
					<span className="text-muted-foreground">
						{aiData.currentPeriod.remaining} credits remaining today
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
