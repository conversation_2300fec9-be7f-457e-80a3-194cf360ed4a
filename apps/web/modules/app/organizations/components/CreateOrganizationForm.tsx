"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	organizationList<PERSON>uery<PERSON>ey,
	useCreateOrganizationMutation,
} from "@app/organizations/lib/api";
import { createInitialOrganizationViews } from "@app/organizations/lib/initial-views";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Loader } from "@ui/components/loader";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
	name: z.string().min(3).max(32),
});

type FormValues = z.infer<typeof formSchema>;

export function CreateOrganizationForm({
	defaultName,
}: {
	defaultName?: string;
}) {
	const t = useTranslations();
	const router = useRouter();
	const queryClient = useQueryClient();
	const { setActiveOrganization } = useActiveOrganization();
	const createOrganizationMutation = useCreateOrganizationMutation();
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: defaultName ?? "",
		},
	});

	const onSubmit = form.handleSubmit(async ({ name }) => {
		try {
			// Add minimum loading time to ensure loader is visible
			const [newOrganization] = await Promise.all([
				createOrganizationMutation.mutateAsync({
					name,
				}),
				new Promise(resolve => setTimeout(resolve, 1500))
			]);

			if (!newOrganization) {
				throw new Error("Failed to create organization");
			}

			// Create initial views for the new organization
			await createInitialOrganizationViews(newOrganization.id);

			await setActiveOrganization(newOrganization.id);

			await queryClient.invalidateQueries({
				queryKey: organizationListQueryKey,
			});

			router.replace(`/app/${newOrganization.slug}`);
		} catch (e) {
			toast.error(t("organizations.createForm.notifications.error"));
		}
	});

	if (form.formState.isSubmitting) {
		return (
			<Card className="mx-auto w-full max-w-md p-6">
				<div className="flex flex-col items-center justify-center py-16">
					<Loader 
						variant="loading-dots" 
						text="Setting up your organization" 
						className="text-muted-foreground"
					/>
				</div>
			</Card>
		);
	}

	return (
		<Card className="mx-auto w-full max-w-md p-6">
			<h1 className="font-extrabold text-2xl md:text-3xl">
				{t("organizations.createForm.title")}
			</h1>
			<p className="mt-2 mb-6 text-foreground/60">
				{t("organizations.createForm.subtitle")}
			</p>

			<Form {...form}>
				<form onSubmit={onSubmit}>
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>
									{t("organizations.createForm.name")}
								</FormLabel>
								<FormControl>
									<Input {...field} autoComplete="email" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<Button
						className="mt-6 w-full"
						variant="primary"
						type="submit"
						loading={form.formState.isSubmitting}
					>
						{t("organizations.createForm.submit")}
					</Button>
				</form>
			</Form>
		</Card>
	);
}
