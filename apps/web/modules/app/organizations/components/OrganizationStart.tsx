"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useGetFilteredData } from "@app/organizations/hooks/use-analytics";
import { FILTER_TYPES } from "@app/shared/lib/constants";
import { IconCheck, IconX } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { RotateCcw, Settings } from "lucide-react";
import React, { useRef, useState } from "react";
import { DashboardGrid } from "./DashboardGrid";

const OrganizationStart = () => {
	const { user } = useSession();
	const [selectedFilter, setSelectedFilter] = useState("This Week");
	const [editMode, setEditMode] = useState(false);
	const resetLayoutRef = useRef<(() => void) | null>(null);

	const { data, isLoading } = useGetFilteredData({
		filter: selectedFilter,
	});

	return (
		<div>
			<div className="flex-col md:flex sm:flex">
				<div className="flex-1 space-y-4 p-4">
					{/* Header with tabs on left, dropdown and edit button on right */}
					<div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
						<Tabs defaultValue="overview" className="text-xs">
							<TabsList>
								<TabsTrigger value="overview">
									Overview
								</TabsTrigger>
								<TabsTrigger disabled value="reports">
									Reports
								</TabsTrigger>
							</TabsList>
						</Tabs>

						<div className="flex items-center gap-2">
							<Select
								value={selectedFilter}
								onValueChange={setSelectedFilter}
							>
								<SelectTrigger className="w-[180px] h-8 rounded-lg">
									<SelectValue placeholder={selectedFilter} />
								</SelectTrigger>
								<SelectContent>
									{FILTER_TYPES.map((item, index) => (
										<SelectItem key={index} value={item}>
											{item}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							<Button
								tooltip={editMode ? "Save Layout" : "Edit Layout"}
								variant="relio"
								size="sm"
								onClick={() => setEditMode(!editMode)}
								className="flex items-center gap-2"
							>
								{editMode ? (
									<IconCheck className="h-4 w-4" />
								) : (
									<Settings className="h-4 w-4" />
								)}
							</Button>
							{editMode && (
								<Button
									tooltip="Reset Layout"
									variant="relio"
									size="sm"
									onClick={() => resetLayoutRef.current?.()}
									className="flex items-center gap-2"
								>
									<RotateCcw className="h-4 w-4" />
								</Button>
							)}
						</div>
					</div>

					{/* Tabs content */}
					<Tabs
						defaultValue="overview"
						className="space-y-4 text-xs"
						asChild
					>
						<div>
							<TabsContent value="overview" className="space-y-4">
								<DashboardGrid
									selectedFilter={selectedFilter}
									data={data}
									isLoading={isLoading}
									editable={editMode}
									resetLayoutRef={resetLayoutRef}
								/>
							</TabsContent>
							<TabsContent value="reports" className="space-y-4">
								Coming Soon!
							</TabsContent>
						</div>
					</Tabs>
				</div>
			</div>
		</div>
	);
};

export default OrganizationStart;
