import type { ObjectConfig } from "../../../../object-views/lib/types";
import { companyFilterFields, companySheetFields } from "../constants";
import { createInfiniteQueryOptions } from "../shared/query-options-factory";
import { columns } from "./columns";
import type { Company } from "./schema";
import { companySchema } from "./schema";
import { searchParamsParser, searchParamsSerializer } from "./search-params";

export interface CompanyMeta {
	objectType: "company";
	totalCount: number;
	hasNextPage: boolean;
}

export const companyConfig: ObjectConfig<Company, CompanyMeta> = {
	type: "company",
	schema: companySchema,
	columns,
	filterFields: companyFilterFields,
	sheetFields: companySheetFields,
	searchParamsParser,
	apiEndpoint: "/api/objects/companies",
	primaryColumn: "name",
};

export const companyQueryOptions = createInfiniteQueryOptions<
	Company,
	CompanyMeta
>({
	objectType: "company",
	searchParamsSerializer,
	apiEndpoint: "/api/objects/companies/infinite",
});
