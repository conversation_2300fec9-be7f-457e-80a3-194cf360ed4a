import { z } from "zod";

// Company schema for frontend
export const companySchema = z.object({
	id: z.string(),
	name: z.string(),
	website: z.string().nullable(),
	industry: z.string().nullable(),
	size: z.string().nullable(),
	description: z.string().nullable(),
	logo: z.string().nullable(),
	address: z.any().nullable(),
	phone: z.string().nullable(),
	email: z.string().nullable(),
	organizationId: z.string(),
	createdBy: z.string(),
	updatedBy: z.string().nullable(),
	isDeleted: z.boolean(),
	deletedAt: z.date().nullable(),
	deletedBy: z.string().nullable(),
	createdAt: z.date(),
	updatedAt: z.date(),
	contactCount: z.number().optional(),
	tags: z.array(z.string()).default([]),
	contacts: z
		.array(
			z.object({
				id: z.string(),
				firstName: z.string().nullable(),
				lastName: z.string().nullable(),
				email: z.any(),
			}),
		)
		.optional(),
	creator: z
		.object({
			id: z.string(),
			name: z.string(),
			email: z.string(),
			image: z.string().nullable(),
		})
		.optional(),
});

export type Company = z.infer<typeof companySchema>;
