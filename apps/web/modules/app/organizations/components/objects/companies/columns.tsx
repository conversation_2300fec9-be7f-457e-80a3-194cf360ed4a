import {
	IconAt,
	IconBuilding,
	IconCalendarClock,
	IconPhone,
	IconTag,
	IconUsers,
	IconWorldWww,
} from "@tabler/icons-react";
import type { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@ui/components/badge";
import { Checkbox } from "@ui/components/checkbox";
import { EmailCell } from "@shared/components/EmailCell";
import { DataTableColumnTags } from "../shared/data-table/data-table-column/data-table-column-tags";
import { format } from "date-fns";
import type { Company } from "./schema";



export const columns: ColumnDef<Company>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={table.getIsAllPageRowsSelected()}
				onCheckedChange={(value) =>
					table.toggleAllPageRowsSelected(!!value)
				}
				aria-label="Select all"
				className="translate-y-[2px]"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
				className="translate-y-[2px]"
			/>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "id",
		header: "ID",
		cell: ({ row }) => <div className="w-[80px]">{row.getValue("id")}</div>,
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "name",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconBuilding className="h-4 w-4" />
				Company Name
			</div>
		),
		cell: ({ row }) => {
			const name = row.getValue("name") as string;
			const website = row.original.website;

			return (
				<div className="flex items-center space-x-3">
					{row.original.logo && (
						<img
							src={row.original.logo}
							alt={`${name} logo`}
							className="w-8 h-8 rounded-lg object-cover"
						/>
					)}
					<div className="flex flex-col">
						<span className="font-medium">{name}</span>
						{website && (
							<span className="text-sm text-muted-foreground truncate max-w-[200px]">
								{website}
							</span>
						)}
					</div>
				</div>
			);
		},
		size: 250,
	},
	{
		accessorKey: "industry",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconBuilding className="h-4 w-4" />
				Industry
			</div>
		),
		cell: ({ row }) => {
			const industry = row.getValue("industry") as string;
			return (
				<div className="w-[120px]">
					{industry || (
						<span className="text-muted-foreground">—</span>
					)}
				</div>
			);
		},
		filterFn: (row, id, value) => {
			return value.includes(row.getValue(id));
		},
		size: 120,
	},
	{
		accessorKey: "size",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconUsers className="h-4 w-4" />
				Company Size
			</div>
		),
		cell: ({ row }) => {
			const size = row.getValue("size") as string;
			return (
				<div className="w-[120px]">
					{size || <span className="text-muted-foreground">—</span>}
				</div>
			);
		},
		filterFn: (row, id, value) => {
			return value.includes(row.getValue(id));
		},
		size: 120,
	},
	{
		accessorKey: "contactCount",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconUsers className="h-4 w-4" />
				Contacts
			</div>
		),
		cell: ({ row }) => {
			const contactCount = row.getValue("contactCount") as number;
			return (
				<div className="w-[80px] text-center">
					<Badge>{contactCount || 0}</Badge>
				</div>
			);
		},
		size: 80,
	},
	{
		accessorKey: "phone",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconPhone className="h-4 w-4" />
				Phone
			</div>
		),
		cell: ({ row }) => {
			const phone = row.getValue("phone") as string;
			return (
				<div className="w-[140px]">
					{phone ? (
						<a
							href={`tel:${phone}`}
							className="text-blue-600 hover:text-blue-800 hover:underline"
						>
							{phone}
						</a>
					) : (
						<span className="text-muted-foreground">—</span>
					)}
				</div>
			);
		},
		size: 140,
	},
	{
		accessorKey: "email",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconAt className="h-4 w-4" />
				Email
			</div>
		),
		cell: ({ row }) => {
			const email = row.getValue("email") as string;
			return (
				<div className="w-[200px]">
					{email ? (
						<EmailCell 
							email={email} 
							recipientName={row.original.name}
							subject={`Regarding ${row.original.name}`}
							type="company"
						/>
					) : (
						<span className="text-muted-foreground">—</span>
					)}
				</div>
			);
		},
		size: 200,
	},
	{
		accessorKey: "website",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconWorldWww className="h-4 w-4" />
				Website
			</div>
		),
		cell: ({ row }) => {
			const website = row.getValue("website") as string;
			return (
				<div className="w-[160px]">
					{website ? (
						<a
							href={website}
							target="_blank"
							rel="noopener noreferrer"
							className="text-blue-600 hover:text-blue-800 hover:underline truncate block"
						>
							{website.replace(/^https?:\/\//, "")}
						</a>
					) : (
						<span className="text-muted-foreground">—</span>
					)}
				</div>
			);
		},
		size: 160,
	},
	{
		accessorKey: "createdAt",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconCalendarClock className="h-4 w-4" />
				Created
			</div>
		),
		cell: ({ row }) => {
			const date = row.getValue("createdAt") as Date;
			return (
				<div className="w-[120px] text-sm text-muted-foreground">
					{format(new Date(date), "MMM d, yyyy")}
				</div>
			);
		},
		size: 120,
	},
	{
		id: "tags",
		accessorFn: (row) => row.tags || [],
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconTag className="h-4 w-4" />
				Tags
			</div>
		),
		// No custom cell renderer - will use InlineCellEditor with fieldType="tags"
		size: 200,
		minSize: 150,
		maxSize: 400,
	},
];
