import {
	createDynamicStringArrayParam,
	createSearchParamsParser,
	createStringParam,
} from "../shared/search-params-factory";

// Company-specific search parameters
const companySpecificParams = {
	// Company filters
	name: createStringParam(),
	industry: createStringParam(),
	size: createStringParam(),
	website: createStringParam(),
	email: createStringParam(),
	phone: createStringParam(),
	description: createStringParam(),
	tags: createDynamicStringArrayParam(),
};

// Create the complete search params parser for companies
export const companySearchParams = createSearchParamsParser(
	companySpecificParams,
);

// Export the types and utilities
export const searchParamsParser = companySearchParams.parser;
export const searchParamsCache = companySearchParams.cache;
export const searchParamsSerializer = companySearchParams.serializer;
export type CompanySearchParamsType = typeof companySearchParams.type;
