"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	type DragStartEvent,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { EllipsisDropdown } from "@shared/components/EllipsisDropdown";
import {
	IconChevronLeft,
	IconChevronRight,
	IconColumns,
	IconDownload,
	IconEyeOff,
	IconGrid3x3,
	IconGripVertical,
	IconLayoutGrid,
	IconPlus,
	IconSquareRoundedCheckFilled,
	IconTable,
	IconTag,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { useState } from "react";
import { toast } from "sonner";
import {
	getFieldIcon,
} from "./constants/available-attributes";
import { ObjectType } from "@repo/database";

interface MapViewSettingsProps {
	view: {
		id: string;
		name: string;
		viewType?: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		mapConfig?: {
			displayType?: "table" | "grid";
			rowDensity?: "compact" | "normal" | "comfortable";
			showExportOptions?: boolean;
			allowColumnReorder?: boolean;
			showSearchBar?: boolean;
		};
	};
	objectType: ObjectType;
	primaryColumn?: string;
	onUpdateView?: (updatedView: any) => void;
	onExportData?: (format: "csv" | "json") => void;
	setAddColumnOpen: (open: boolean) => void;
}

// Sortable column item component
function SortableColumnItem({
	column,
	index,
	onRemoveColumn,
	objectType,
	hasIssues = false,
}: {
	column: any;
	index: number;
	onRemoveColumn: (field: string) => void;
	objectType: ObjectType;
	hasIssues?: boolean;
}) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: column.field,
		data: {
			type: "column",
			column,
			index,
		},
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={cn(
				"group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border",
				isDragging && "opacity-50",
			)}
		>
			<div
				{...attributes}
				{...listeners}
				className="cursor-grab active:cursor-grabbing"
			>
				<IconGripVertical className="h-3 w-3 text-muted-foreground" />
			</div>
			<div className="flex items-center gap-2 flex-1 min-w-0">
				{/* Column icon based on field type */}
				{(() => {
					const FieldIcon = getFieldIcon(column.field, objectType);
					return (
						<FieldIcon className="h-4 w-4 text-muted-foreground" />
					);
				})()}

				<span
					className={cn(
						"text-sm truncate cursor-default",
						// hasIssues && "text-muted-foreground"
					)}
				>
					{column.headerName}
				</span>
			</div>
			<EllipsisDropdown className="p-1">
				<EllipsisDropdown.Item className="gap-1">
					<IconTag className="h-3 w-3" />
					Change attribute title
				</EllipsisDropdown.Item>
				<EllipsisDropdown.Item
					className="gap-1"
					onClick={() => onRemoveColumn(column.field)}
				>
					<IconEyeOff className="h-3 w-3" />
					Hide column
				</EllipsisDropdown.Item>
			</EllipsisDropdown>
		</div>
	);
}

export const MapViewSettings = ({
	view,
	objectType,
	primaryColumn = "name",
	onUpdateView,
	onExportData,
	setAddColumnOpen,
}: MapViewSettingsProps) => {
	const [activeId, setActiveId] = useState<string | null>(null);

	// Table-specific state
	const [tableScreen, setTableScreen] = useState<
		"main" | "viewType" | "density" | "export" | "columns"
	>("main");

	const queryClient = useQueryClient();
	const { activeOrganization } = useActiveOrganization();

	// Check if a column might have rendering issues
	const hasRenderingIssues = (field: string): boolean => {
		// These are known problematic fields that might not render properly
		const problematicFields = [
			"name", // Computed field that might not be in the data
			"summary", // Might not exist in Contact schema
			"notes", // Not in Contact schema
			"tags", // Not a direct field in Contact schema
		];

		return objectType === "contact" && problematicFields.includes(field);
	};

	// DnD sensors
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
	);

	// Mutation to update view columns
	const updateViewMutation = useMutation({
		mutationFn: async (updates: {
			columnDefs?: any[];
			mapConfig?: any;
		}) => {
			if (!view?.id) return;

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify(updates),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update view");
			}

			return response.json();
		},
		onMutate: async (updates) => {
			if (!view?.id || !activeOrganization?.id) return;

			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["objectView", view.id],
			});
			await queryClient.cancelQueries({
				queryKey: ["objectViews", activeOrganization.id, objectType],
			});

			// Snapshot the previous values
			const previousView = queryClient.getQueryData([
				"objectView",
				view.id,
			]);
			const previousViews = queryClient.getQueryData([
				"objectViews",
				activeOrganization.id,
				objectType,
			]);

			// Optimistically update the single view
			queryClient.setQueryData(["objectView", view.id], (old: any) => {
				if (!old) return old;
				return {
					...old,
					...updates,
					updatedAt: new Date().toISOString(),
				};
			});

			// Optimistically update the views list
			queryClient.setQueryData(
				["objectViews", activeOrganization.id, objectType],
				(old: any[]) => {
					if (!old || !Array.isArray(old)) return old;
					return old.map((v: any) =>
						v.id === view.id
							? {
									...v,
									...(updates.columnDefs && {
										columnDefs: [...updates.columnDefs],
									}), // Ensure new array reference
									...(updates.mapConfig && {
										mapConfig: {
											...updates.mapConfig,
										},
									}),
									updatedAt: new Date().toISOString(),
									// Force a new reference by adding a cache invalidation key
									_cacheKey: Date.now(),
								}
							: v,
					);
				},
			);

			return { previousView, previousViews };
		},
		onSuccess: (data, variables, context) => {
			if (onUpdateView && data) {
				onUpdateView(data);
			}

			// Invalidate queries to ensure fresh data
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: [
						"objectViews",
						activeOrganization.id,
						objectType,
					],
				});
				// Also invalidate all views for this organization to be safe
				queryClient.invalidateQueries({
					queryKey: ["objectViews", activeOrganization.id],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}
		},
		onError: (error, variables, context) => {
			toast.error(`Failed to update view: ${error.message}`);

			// Rollback optimistic updates
			if (context?.previousView && view?.id) {
				queryClient.setQueryData(
					["objectView", view.id],
					context.previousView,
				);
			}
			if (context?.previousViews && activeOrganization?.id) {
				queryClient.setQueryData(
					["objectViews", activeOrganization.id, objectType],
					context.previousViews,
				);
			}
		},
	});

	// Handle table display type change
	const handleDisplayTypeChange = (displayType: "table" | "grid") => {
		if (!view?.id) return;

		const updatedMapConfig = {
			displayType,
			rowDensity: view?.mapConfig?.rowDensity || "normal",
			showExportOptions: view?.mapConfig?.showExportOptions ?? true,
			allowColumnReorder: view?.mapConfig?.allowColumnReorder ?? true,
			showSearchBar: view?.mapConfig?.showSearchBar ?? true,
		};

		updateViewMutation.mutate({ mapConfig: updatedMapConfig });
		setTableScreen("main");
		toast.success(`View changed to ${displayType}`);
	};

	// Handle row density change
	const handleRowDensityChange = (density: "compact" | "normal" | "comfortable") => {
		if (!view?.id) return;

		const updatedMapConfig = {
			displayType: view?.mapConfig?.displayType || "table",
			rowDensity: density,
			showExportOptions: view?.mapConfig?.showExportOptions ?? true,
			allowColumnReorder: view?.mapConfig?.allowColumnReorder ?? true,
			showSearchBar: view?.mapConfig?.showSearchBar ?? true,
		};

		updateViewMutation.mutate({ mapConfig: updatedMapConfig });
		setTableScreen("main");
		toast.success(`Row density set to ${density}`);
	};

	// Handle export
	const handleExport = (format: "csv" | "json") => {
		onExportData?.(format);
		setTableScreen("main");
		toast.success(`Exporting as ${format.toUpperCase()}`);
	};

	const handleRemoveColumn = (field: string) => {
		if (!view?.columnDefs) return;

		if (field === primaryColumn) {
			console.warn("Cannot remove primary column:", primaryColumn);
			return;
		}

		const updatedColumnDefs = view.columnDefs.filter(
			(col) => col.field !== field,
		);
		updateViewMutation.mutate({ columnDefs: updatedColumnDefs });
	};

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		setActiveId(null);

		if (!over) return;

		// Handle column reordering for table view
		if (!view?.columnDefs) return;

		// Work with all columns except primary column
		const reorderableColumns = view.columnDefs.filter(
			(col) => col.field !== primaryColumn,
		);
		const primaryColumns = view.columnDefs.filter(
			(col) => col.field === primaryColumn,
		);

		const oldIndex = reorderableColumns.findIndex(
			(col) => col.field === active.id,
		);
		const newIndex = reorderableColumns.findIndex(
			(col) => col.field === over.id,
		);

		if (oldIndex !== newIndex) {
			// Reorder the columns
			const reorderedColumns = arrayMove(
				reorderableColumns,
				oldIndex,
				newIndex,
			);
			// Keep primary column at the beginning
			const finalColumns = [...primaryColumns, ...reorderedColumns];
			updateViewMutation.mutate({ columnDefs: finalColumns });
		}
	};

	return (
		<div className="p-1">
			{tableScreen === "main" && (
				<>
					<div className="text-xs text-muted-foreground my-2 mx-2">
						View settings
					</div>

					{/* Display type option */}
					<div
						className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
						onClick={() => setTableScreen("viewType")}
					>
						{view?.mapConfig?.displayType === "grid" ? (
							<IconLayoutGrid className="h-4 w-4 text-muted-foreground" />
						) : (
							<IconTable className="h-4 w-4 text-muted-foreground" />
						)}
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Display as{" "}
								{view?.mapConfig?.displayType === "grid"
									? "Grid"
									: "Table"}
							</span>
						</div>
						<IconChevronRight className="h-4 w-4 text-muted-foreground" />
					</div>

					{/* Row density option */}
					<div
						className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
						onClick={() => setTableScreen("density")}
					>
						<IconGrid3x3 className="h-4 w-4 text-muted-foreground" />
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Row density{" "}
								{view?.mapConfig?.rowDensity
									? view.mapConfig.rowDensity.charAt(0).toUpperCase() +
										view.mapConfig.rowDensity.slice(1)
									: "Normal"}
							</span>
						</div>
						<IconChevronRight className="h-4 w-4 text-muted-foreground" />
					</div>

					{/* Export option */}
					{onExportData && (
						<div
							className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
							onClick={() => setTableScreen("export")}
						>
							<IconDownload className="h-4 w-4 text-muted-foreground" />
							<div className="flex items-center gap-2 flex-1 min-w-0">
								<span className="text-sm truncate">
									Export data
								</span>
							</div>
							<IconChevronRight className="h-4 w-4 text-muted-foreground" />
						</div>
					)}

					<Separator className="my-1" />

					{/* Columns section */}
					<div
						className="group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
						onClick={() => setTableScreen("columns")}
					>
						<IconColumns className="h-4 w-4 text-muted-foreground" />
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Manage columns
							</span>
						</div>
						<div className="flex items-center gap-2 mr-1">
							<span className="text-xs text-muted-foreground">
								{view.columnDefs?.filter(col => col.field !== primaryColumn).length || 0}
							</span>
							<IconChevronRight className="h-4 w-4 text-muted-foreground" />
						</div>
					</div>
				</>
			)}

			{tableScreen === "viewType" && (
				<>
					<div className="flex items-center gap-2 mb-2 p-1">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setTableScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Display type
						</div>
					</div>

					<div className="p-1 space-y-1">
						<div
							className="group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
							onClick={() => handleDisplayTypeChange("table")}
						>
							<IconTable className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm flex-1">Table</span>
							{view?.mapConfig?.displayType !== "grid" && (
								<IconSquareRoundedCheckFilled className="h-4 w-4 text-blue-400" />
							)}
						</div>
						<div
							className="group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
							onClick={() => handleDisplayTypeChange("grid")}
						>
							<IconLayoutGrid className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm flex-1">Grid</span>
							{view?.mapConfig?.displayType === "grid" && (
								<IconSquareRoundedCheckFilled className="h-4 w-4 text-blue-400" />
							)}
						</div>
					</div>
				</>
			)}

			{tableScreen === "density" && (
				<>
					<div className="flex items-center gap-2 mb-2 p-1">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setTableScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Row density
						</div>
					</div>

					<div className="p-1 space-y-1">
						{(["compact", "normal", "comfortable"] as const).map((density) => (
							<div
								key={density}
								className="group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
								onClick={() => handleRowDensityChange(density)}
							>
								<span className="text-sm flex-1 capitalize">{density}</span>
								{(view?.mapConfig?.rowDensity || "normal") === density && (
									<IconSquareRoundedCheckFilled className="h-4 w-4 text-blue-500" />
								)}
							</div>
						))}
					</div>
				</>
			)}

			{tableScreen === "export" && (
				<>
					<div className="flex items-center gap-2 mb-2 p-1">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setTableScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Export data
						</div>
					</div>

					<div className="p-1 space-y-1">
						<div
							className="group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
							onClick={() => handleExport("csv")}
						>
							<IconDownload className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm flex-1">Export as CSV</span>
						</div>
						<div
							className="group flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer"
							onClick={() => handleExport("json")}
						>
							<IconDownload className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm flex-1">Export as JSON</span>
						</div>
					</div>
				</>
			)}

			{tableScreen === "columns" && (
				<>
					<div className="flex items-center gap-2 mb-2 p-1">
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={() => setTableScreen("main")}
						>
							<IconChevronLeft className="h-4 w-4" />
						</Button>
						<div className="text-xs text-muted-foreground">
							Manage columns
						</div>
					</div>

					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragStart={handleDragStart}
						onDragEnd={handleDragEnd}
					>
						<SortableContext
							items={
								view.columnDefs
									?.filter(
										(col) =>
											col.field !== primaryColumn,
									)
									.map((col) => col.field) || []
							}
							strategy={verticalListSortingStrategy}
						>
							<div className="space-y-1">
								{view.columnDefs
									?.filter(
										(col) =>
											col.field !== primaryColumn,
									)
									.map((column, index) => (
										<SortableColumnItem
											key={column.field}
											column={column}
											index={index}
											onRemoveColumn={
												handleRemoveColumn
											}
											objectType={objectType as ObjectType}
											hasIssues={hasRenderingIssues(
												column.field,
											)}
										/>
									))}
							</div>
						</SortableContext>
					</DndContext>

					<Separator className="my-1" />

					<div
						className={cn(
							"group flex items-center gap-3 p-1 h-8 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border cursor-pointer",
						)}
						onClick={() => setAddColumnOpen(true)}
						data-testid="add-column-button"
					>
						<div>
							<IconPlus className="h-4 w-4 text-muted-foreground" />
						</div>
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">
								Add column
							</span>
						</div>

						<div className="flex items-center gap-2 mr-1">
							<IconChevronRight className="h-4 w-4 text-muted-foreground" />
						</div>
					</div>
				</>
			)}
		</div>
	);
}; 