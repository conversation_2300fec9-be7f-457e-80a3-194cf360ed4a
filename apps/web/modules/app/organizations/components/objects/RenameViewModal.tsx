"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { IconEdit } from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Kbd } from "@ui/components/kbd";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

interface RenameViewModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	view?: {
		id: string;
		name: string;
	};
	organizationId?: string;
	objectType?: string;
	onViewRenamed?: (updatedView: any) => void;
}

const renameSchema = z.object({
	name: z.string().min(1, "View name is required"),
});

type RenameFormData = z.infer<typeof renameSchema>;

export function RenameViewModal({
	open,
	onOpenChange,
	view,
	organizationId,
	objectType,
	onViewRenamed,
}: RenameViewModalProps) {
	const queryClient = useQueryClient();
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm<RenameFormData>({
		resolver: zodResolver(renameSchema),
		defaultValues: {
			name: view?.name || "",
		},
	});

	// Reset form when modal opens with new view
	React.useEffect(() => {
		if (open && view) {
			form.reset({
				name: view.name,
			});
		}
	}, [open, view, form]);

	const updateViewMutation = useMutation({
		mutationFn: async (data: { name: string }) => {
			if (!view?.id) throw new Error("No view ID provided");

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					name: data.name,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to rename view");
			}

			return response.json();
		},
		onSuccess: (updatedView) => {
			// Invalidate specific object type views
			if (organizationId && objectType) {
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, objectType],
				});

				queryClient.invalidateQueries({
					queryKey: ["userDefaultView", objectType, organizationId],
				});
			}

			// Invalidate all object type queries to ensure NavFavorites updates
			// Since views can be favorited from any object type
			if (organizationId) {
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, "contact"],
				});
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, "company"],
				});
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, "property"],
				});
			}

			// Invalidate single view query
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}

			// Invalidate favorites to update NavFavorites sidebar
			if (organizationId) {
				queryClient.invalidateQueries({
					queryKey: ["favorites", organizationId],
				});
			}

			if (onViewRenamed && updatedView) {
				onViewRenamed(updatedView);
			}

			toast.success("View renamed successfully");
			onOpenChange(false);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to rename view");
		},
	});

	async function onSubmit(data: RenameFormData) {
		if (!view) return;

		setIsSubmitting(true);
		try {
			await updateViewMutation.mutateAsync(data);
		} finally {
			setIsSubmitting(false);
		}
	}

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Escape") {
			onOpenChange(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-lg m-0"
				onPointerDownOutside={(e) => e.preventDefault()}
				onKeyDown={handleKeyDown}
			>
				<DialogTitle className="sr-only">Rename View</DialogTitle>
				<DialogDescription className="sr-only">
					Rename the selected view
				</DialogDescription>

				<div className="flex items-center justify-between px-4 pt-2 pb-1">
					<div className="flex flex-row items-center gap-x-2">
						<IconEdit className="h-5 w-5" />
						<span className="text-md font-light">
							Edit {view?.name || "View"}
						</span>
					</div>
				</div>

				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="flex flex-col w-full"
				>
					<div className="flex flex-col px-2 pt-2 pb-4">
						<div className="mb-2">
							<label className="text-sm text-muted-foreground px-3 mb-2 block">
								Title (required)
							</label>
							<Input
								{...form.register("name")}
								autoFocus
								className="shadow-none selection:bg-transparent dark:bg-transparent bg-transparent file:bg-transparent ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full font-semibold border-none rounded-2xl text-xl md:text-xl"
								placeholder="View name"
							/>
							{form.formState.errors.name && (
								<span className="text-red-500 text-sm mt-1 px-3">
									{form.formState.errors.name.message}
								</span>
							)}
						</div>
					</div>

					{/* Footer */}
					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting}
							className="text-muted-foreground"
						>
							Cancel
							<Kbd className="ml-2 text-xs bg-muted px-1.5 py-0.5 rounded-md">
								ESC
							</Kbd>
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={isSubmitting || !form.formState.isValid}
						>
							{isSubmitting ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>Saving...</span>
								</div>
							) : (
								<div className="flex items-center gap-2">
									<span>Save</span>
									<Kbd className="rounded-md">↵</Kbd>
								</div>
							)}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
