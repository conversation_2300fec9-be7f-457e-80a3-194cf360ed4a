"use client";

import {
	IconChevronDown,
	IconChevronDown as IconChevronLeft,
	IconPlus,
	IconSettings,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { useMemo, useState } from "react";
import {
	AVAILABLE_ATTRIBUTES,
} from "./constants/available-attributes";
import { ObjectType } from "@repo/database";
import { KanbanViewSettings } from "./KanbanViewSettings";
import { TableViewSettings } from "./TableViewSettings";
import { MapViewSettings } from "./MapViewSettings";

interface ViewSettingsDropdownProps {
	view?: {
		id: string;
		name: string;
		viewType?: string;
		statusAttribute?: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		cardRowFields?: Array<{
			field: string;
			headerName: string;
			type?: string;
		}>;
		showAttributeLabels?: boolean;
		kanbanConfig?: {
			customStatuses?: Array<{
				label: string;
				value: string;
				color: string;
				trackTime?: boolean;
				showConfetti?: boolean;
				targetTime?: number;
				targetTimeUnit?: string;
			}>;
			hiddenColumns?: string[];
		};
		mapConfig?: {
			displayType?: "table" | "grid";
			rowDensity?: "compact" | "normal" | "comfortable";
			showExportOptions?: boolean;
			allowColumnReorder?: boolean;
			showSearchBar?: boolean;
		};
	};
	objectType: ObjectType;
	primaryColumn?: string;
	onUpdateView?: (updatedView: any) => void;
	showAttributeLabels?: boolean;
	onShowAttributeLabelsChange?: (show: boolean) => void;
	onExportData?: (format: "csv" | "json") => void;
}

export const ViewSettingsDropdown = ({
	view,
	objectType,
	primaryColumn = "name",
	onUpdateView,
	showAttributeLabels = true,
	onShowAttributeLabelsChange,
	onExportData,
}: ViewSettingsDropdownProps) => {
	const [open, setOpen] = useState(false);
	const [addColumnOpen, setAddColumnOpen] = useState(false);
	const [searchQuery, setSearchQuery] = useState("");

	// Check view types
	const isKanbanView = view?.viewType === "kanban";
	const isPropertyMapView = objectType === "property" && view?.viewType === "map";

	const availableAttributes = AVAILABLE_ATTRIBUTES[objectType] || [];
	const currentColumnFields = new Set(
		view?.columnDefs?.map((col) => col.field) || [],
	);

	// Filter available attributes to exclude already added columns
	const filteredAttributes = useMemo(() => {
		return availableAttributes.filter((attr) => {
			const matchesSearch =
				attr.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
				attr.field.toLowerCase().includes(searchQuery.toLowerCase());
			const notAlreadyAdded = !currentColumnFields.has(attr.field);
			return matchesSearch && notAlreadyAdded;
		});
	}, [availableAttributes, currentColumnFields, searchQuery]);

	// Group filtered attributes by category
	const groupedAttributes = useMemo(() => {
		const groups: Record<string, typeof filteredAttributes> = {};
		filteredAttributes.forEach((attr) => {
			if (!groups[attr.category]) {
				groups[attr.category] = [];
			}
			groups[attr.category].push(attr);
		});
		return groups;
	}, [filteredAttributes]);

	const handleAddColumn = (field: string, label: string) => {
		if (!view?.columnDefs) return;

		const newColumn = {
			field,
			headerName: label,
			width: 150,
		};

		const updatedView = {
			...view,
			columnDefs: [...view.columnDefs, newColumn],
		};

		onUpdateView?.(updatedView);
		setAddColumnOpen(false);
		setSearchQuery("");
	};

	// Reset screens when popover opens
	const handleOpenChange = (newOpen: boolean) => {
		setOpen(newOpen);
		if (newOpen) {
			setAddColumnOpen(false);
			setSearchQuery("");
		}
	};

	if (!view) return null;

	return (
		<Popover open={open} onOpenChange={handleOpenChange}>
			<PopoverTrigger asChild>
				<Button
					variant="relio"
					size="sm"
					className="justify-between"
					data-testid="view-settings-button"
				>
					<div className="flex items-center gap-2 text-sm">
						<IconSettings className="h-4 w-4" />
						View settings
					</div>
					<IconChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-64 p-0" align="start">
				{isKanbanView ? (
					<KanbanViewSettings
						view={view}
						objectType={objectType}
						onUpdateView={onUpdateView}
						showAttributeLabels={showAttributeLabels}
						onShowAttributeLabelsChange={onShowAttributeLabelsChange}
						setAddColumnOpen={setAddColumnOpen}
					/>
				) : isPropertyMapView ? (
					<MapViewSettings
						view={view}
						objectType={objectType}
						primaryColumn={primaryColumn}
						onUpdateView={onUpdateView}
						onExportData={onExportData}
						setAddColumnOpen={setAddColumnOpen}
					/>
				) : !addColumnOpen ? (
					<TableViewSettings
						view={view}
						objectType={objectType}
						primaryColumn={primaryColumn}
						onUpdateView={onUpdateView}
						setAddColumnOpen={setAddColumnOpen}
					/>
				) : (
					<div className="p-2">
						<div className="flex items-center gap-2 mb-2">
							<Button
								variant="ghost"
								size="icon"
								className="h-6 w-6"
								onClick={() => {
									setAddColumnOpen(false);
									setSearchQuery("");
								}}
							>
								<IconChevronLeft className="h-4 w-4" />
							</Button>
							<div className="text-xs text-muted-foreground">
								Add column
							</div>
						</div>

						<Input
							placeholder="Search attributes..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							className="mb-4"
							autoFocus
						/>

						<div className="max-h-80 overflow-y-auto">
							{Object.entries(groupedAttributes).map(
								([category, attributes]) => (
									<div key={category} className="mb-4">
										{attributes.length > 0 && (
											<>
												<div className="text-[10px] font-mono text-muted-foreground mb-2 px-2">
													{category}
												</div>
												<div className="space-y-1 w-full">
													{attributes.map((attr) => {
														const Icon = attr.icon;
														return (
															<button
																key={attr.field}
																className="group w-full cursor-pointer flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border"
																onClick={() =>
																	handleAddColumn(
																		attr.field,
																		attr.label,
																	)
																}
															>
																<Icon className="h-4 w-4 text-muted-foreground" />
																<span className="text-sm">
																	{attr.label}
																</span>
															</button>
														);
													})}
												</div>
											</>
										)}
									</div>
								),
							)}

							{Object.keys(groupedAttributes).length === 0 && (
								<div className="text-center py-8 text-muted-foreground">
									<p className="text-sm">
										No attributes found
									</p>
									<p className="text-xs">
										Try a different search term
									</p>
								</div>
							)}
						</div>
					</div>
				)}
			</PopoverContent>
		</Popover>
	);
};
