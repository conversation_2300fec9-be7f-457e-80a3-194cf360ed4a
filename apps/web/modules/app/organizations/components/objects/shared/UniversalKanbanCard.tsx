"use client";

import { DataTableContextMenu } from "@app/shared/components/data-table/DataTableContextMenu";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import {
	IconAt,
	IconBrowserMaximize,
	IconBuilding,
	IconClock,
	IconCurrencyDollar,
	IconDots,
	IconEdit,
	IconHome,
	IconMapPin,
	IconMessage,
	IconMessagePlus,
	IconPhone,
	IconStar,
	IconStarFilled,
	IconUsers,
} from "@tabler/icons-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { formatDistanceToNow } from "date-fns";
import { useCallback, useMemo, useState } from "react";
import { ObjectType } from "@repo/database";
import { BaseObjectSchema } from "@app/object-views/lib/types";

interface UniversalKanbanCardProps<TData extends BaseObjectSchema> {
	item: TData;
	objectType: ObjectType;
	statusAttribute: string;
	isSelected?: boolean;
	selectedItems?: Set<string>;
	onSelect?: (itemId: string) => void;
	onEdit?: (itemId: string) => void;
	onDelete?: (itemId: string) => void;
	// Time tracking props
	trackTime?: boolean;
	targetTime?: number;
	targetTimeUnit?: string;
	// Card row fields
	cardRowFields?: Array<{
		field: string;
		headerName: string;
		type?: string;
	}>;
	// Show/hide attribute labels
	showAttributeLabels?: boolean;
	// Context menu props
	organizationId?: string;
	isFavorite?: boolean;
	isPinned?: boolean;
	onFavorite?: (e: React.MouseEvent, itemId: string) => void;
	onPin?: (e: React.MouseEvent, itemId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
}

export function UniversalKanbanCard<TData extends BaseObjectSchema>({
	item,
	objectType,
	statusAttribute,
	isSelected = false,
	selectedItems = new Set(),
	onSelect,
	onEdit,
	onDelete,
	trackTime = false,
	targetTime = 1,
	targetTimeUnit = "days",
	cardRowFields = [],
	showAttributeLabels = true,
	organizationId,
	isFavorite = false,
	isPinned = false,
	onFavorite,
	onPin,
	onCopy,
	onHide,
}: UniversalKanbanCardProps<TData>) {
	const [isHovered, setIsHovered] = useState(false);

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
		isOver,
	} = useSortable({
		id: item.id,
		data: {
			type: "item",
			item: item,
			objectType,
			status: (item as any)[statusAttribute],
			selectedItemsCount: isSelected ? selectedItems.size : 1,
		},
		transition: {
			duration: 150,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const style = useMemo(() => {
		const transformString = transform
			? CSS.Transform.toString(transform)
			: "";
		const styleObj: React.CSSProperties = {
			transform: isDragging
				? `rotate(2deg) ${transformString}`
				: transformString,
			transition,
			opacity: isDragging ? 0.5 : 1,
			cursor: isDragging ? "grabbing" : "grab",
			touchAction: "none",
			position: "relative",
			// zIndex: isDragging ? 1000 : 1,
			display: "block",
		};

		return styleObj;
	}, [transform, transition, isDragging]);

	const handleCheckboxChange = useCallback(
		(checked: boolean | string) => {
			onSelect?.(item.id);
		},
		[onSelect, item.id],
	);

	const handleCheckboxClick = useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();
			e.preventDefault();
			onSelect?.(item.id);
		},
		[onSelect, item.id],
	);

	// Context menu handlers
	const handleContextEdit = useCallback(
		(e: React.MouseEvent, recordId: string) => {
			onEdit?.(recordId);
		},
		[onEdit],
	);

	const handleContextDelete = useCallback(
		(e: React.MouseEvent, recordId: string) => {
			onDelete?.(recordId);
		},
		[onDelete],
	);

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((part) => part.charAt(0))
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	const getRelativeTime = (date: string | Date) => {
		return formatDistanceToNow(new Date(date), { addSuffix: false });
	};

	// Calculate time spent in current status and color coding
	const getTimeTracking = useCallback(
		(hoursSpent: number) => {
			if (!trackTime || !targetTime) {
				return {
					colorClass: "text-zinc-500",
					bgClass: "bg-zinc-100 dark:bg-zinc-800",
					ratio: 0,
				};
			}

			// Convert target time to hours
			let targetHours = targetTime;
			switch (targetTimeUnit) {
				case "hours":
					targetHours = targetTime;
					break;
				case "days":
					targetHours = targetTime * 24;
					break;
				case "weeks":
					targetHours = targetTime * 24 * 7;
					break;
			}

			const ratio = hoursSpent / targetHours;

			// Color coding based on ratio
			if (ratio >= 1) {
				// Over target - red
				return {
					colorClass: "text-red-600 dark:text-red-400",
					bgClass: "bg-red-50 dark:bg-red-950/50",
					ratio,
				};
			}
			if (ratio >= 0.7) {
				// Getting close - yellow/orange
				return {
					colorClass: "text-orange-600 dark:text-orange-400",
					bgClass: "bg-orange-50 dark:bg-orange-950/50",
					ratio,
				};
			}
			// Within target - green/default
			return {
				colorClass: "text-green-600 dark:text-green-400",
				bgClass: "bg-green-50 dark:bg-green-950/50",
				ratio,
			};
		},
		[trackTime, targetTime, targetTimeUnit],
	);

	// Get status change information with time tracking
	const getStatusChangeInfo = useMemo(() => {
		// TODO: Fetch real status history from API once schema is regenerated
		// For now, mock the status change info based on created/updated dates
		const currentStatus = (item as any)[statusAttribute];
		if (!currentStatus) return null;

		// Simple heuristic: if updated recently, show "moved", otherwise show "created"
		const updatedAt = new Date(item.updatedAt);
		const createdAt = new Date(item.createdAt);
		const timeDiff = updatedAt.getTime() - createdAt.getTime();
		const isRecentlyMoved = timeDiff > 60000; // More than 1 minute difference

		const statusChangeTime = isRecentlyMoved
			? updatedAt.getTime()
			: createdAt.getTime();
		const hoursSpent = (Date.now() - statusChangeTime) / (1000 * 60 * 60);
		const days = Math.floor(hoursSpent / 24);

		// Format time display
		let timeText = "";
		if (hoursSpent < 1) {
			timeText = "<1h";
		} else if (hoursSpent < 24) {
			timeText = `${Math.floor(hoursSpent)}h`;
		} else {
			timeText = `${days}d`;
		}

		// Get time tracking colors
		const timeTracking = getTimeTracking(hoursSpent);

		// Capitalize status for display
		const statusLabel = currentStatus
			.split("_")
			.map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(" ");

		const action = isRecentlyMoved ? "Moved to" : "Created";
		const actionDate = isRecentlyMoved ? updatedAt : createdAt;

		return {
			days,
			hoursSpent,
			timeText,
			action,
			statusLabel,
			actionDate,
			timeTracking,
			tooltipText: (
				<div
					className={`flex ${action === "Created" && !trackTime ? "items-center gap-1" : "flex-col"} w-full`}
				>
					<span>
						{action === "Created"
							? action
							: `${action} ${statusLabel}`}
					</span>
					<span className="text-xs opacity-80">
						{actionDate.toLocaleString()}
					</span>
					{trackTime && targetTime && (
						<div className="text-xs opacity-80 mt-1 items-center">
							<span>
								Target: {targetTime} {targetTimeUnit}
							</span>
							<span className="text-xs opacity-80 ml-1">
								{timeTracking.ratio > 0 &&
									`(${Math.round(timeTracking.ratio * 100)}%)`}
							</span>
						</div>
					)}
				</div>
			),
		};
	}, [
		item,
		statusAttribute,
		getTimeTracking,
		trackTime,
		targetTime,
		targetTimeUnit,
	]);

	const handleEditClick = useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();
			e.preventDefault();
			onEdit?.(item.id);
		},
		[onEdit, item.id],
	);

	const getObjectData = () => {
		switch (objectType) {
			case "contact": {
				const contact = item as any;
				const name =
					contact.name ||
					`${contact.firstName || ""} ${contact.lastName || ""}`.trim() ||
					"Unnamed Contact";
				const primaryEmail =
					contact.email?.[0]?.address || contact.email?.[0] || "";
				return {
					title: name,
					subtitle: contact.title || primaryEmail,
					avatar: contact.image,
					initials: getInitials(name),
					avatarColor:
						"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300",
					primaryIcon: IconAt,
					secondaryIcon: IconMessage,
				};
			}
			case "company": {
				const company = item as any;
				const companyName = company.name || "Unnamed Company";
				return {
					title: companyName,
					subtitle: company.industry,
					avatar: company.logo,
					initials: getInitials(companyName),
					avatarColor:
						"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300",
					primaryIcon: IconBuilding,
					secondaryIcon: IconAt,
				};
			}
			case "property": {
				const property = item as any;
				const propertyName =
					property.name || property.address || "Unnamed Property";
				return {
					title: propertyName,
					subtitle: property.propertyType?.replace("_", " "),
					avatar: property.image,
					initials: getInitials(propertyName),
					avatarColor:
						"bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300",
					primaryIcon: IconHome,
					secondaryIcon: IconMapPin,
				};
			}
			default:
				return {
					title:
						(item as any).name ||
						(item as any).title ||
						`Item ${item.id}`,
					subtitle: null,
					avatar: null,
					initials: "??",
					avatarColor:
						"bg-gray-100 text-gray-600 dark:bg-gray-900 dark:text-gray-300",
					primaryIcon: IconDots,
					secondaryIcon: IconDots,
				};
		}
	};

	const objectData = getObjectData();

	// Render dynamic card row fields
	const renderCardRowFields = () => {
		if (!cardRowFields || cardRowFields.length === 0) return null;

		return cardRowFields.map((field) => {
			const value = (item as any)[field.field];
			if (!value) return null;

			// Handle different field types
			const renderFieldValue = () => {
				switch (field.field) {
					case "email":
						// Handle email arrays
						if (Array.isArray(value)) {
							const primaryEmail = value[0]?.address || value[0];
							return primaryEmail ? (
								<a
									href={`mailto:${primaryEmail}`}
									className="text-blue-600 hover:underline"
									onClick={(e) => e.stopPropagation()}
								>
									{primaryEmail}
								</a>
							) : null;
						}
						return (
							<a
								href={`mailto:${value}`}
								className="text-blue-600 hover:underline"
								onClick={(e) => e.stopPropagation()}
							>
								{value}
							</a>
						);
					case "phone":
						// Handle phone arrays
						if (Array.isArray(value)) {
							const primaryPhone = value[0]?.number || value[0];
							return primaryPhone ? (
								<a
									href={`tel:${primaryPhone}`}
									className="text-blue-600 hover:underline"
									onClick={(e) => e.stopPropagation()}
								>
									{primaryPhone}
								</a>
							) : null;
						}
						return (
							<a
								href={`tel:${value}`}
								className="text-blue-600 hover:underline"
								onClick={(e) => e.stopPropagation()}
							>
								{value}
							</a>
						);
					case "website":
						return (
							<a
								href={
									value.startsWith("http")
										? value
										: `https://${value}`
								}
								target="_blank"
								rel="noopener noreferrer"
								className="text-blue-600 hover:underline"
								onClick={(e) => e.stopPropagation()}
							>
								{value}
							</a>
						);
					default:
						// Handle simple text values
						return <span>{value.toString()}</span>;
				}
			};

			return (
				<div
					key={field.field}
					className={`text-xs ${showAttributeLabels ? "text-muted-foreground space-y-1" : "text-foreground"}`}
				>
					{showAttributeLabels && (
						<div className="font-medium">{field.headerName}</div>
					)}
					<div
						className={showAttributeLabels ? "text-foreground" : ""}
					>
						{renderFieldValue()}
					</div>
				</div>
			);
		});
	};

	return (
		<DataTableContextMenu
			record={item}
			objectType={objectType}
			organizationId={organizationId}
			isFavorite={isFavorite}
			isPinned={isPinned}
			onEdit={handleContextEdit}
			onFavorite={onFavorite}
			onPin={onPin}
			onDelete={handleContextDelete}
			onCopy={onCopy}
			onHide={onHide}
		>
			<div
				ref={setNodeRef}
				style={style}
				{...attributes}
				{...listeners}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				className={`relative rounded-lg border bg-zinc-100 dark:bg-accent/50 dark:hover:!bg-accent/50 hover:bg-zinc-100 p-2 cursor-grab active:cursor-grabbing ${
					isDragging
						? "border-blue-500"
						: isSelected
							? "border-blue-500"
							: "border-accent"
				}`}
			>
				<div className="relative">
					<div className={"flex flex-col"}>
						<div
							className={
								"flex flex-row items-start justify-between"
							}
						>
							<div className={"flex flex-row items-center gap-2"}>
								{/* Avatar/Checkbox toggle based on hover */}
								<div className="relative w-6 h-6 flex-shrink-0">
									{isHovered ? (
										<div
											className="checkbox-container"
											onMouseDown={(e) =>
												e.stopPropagation()
											}
											onMouseUp={(e) =>
												e.stopPropagation()
											}
										>
											<Checkbox
												checked={isSelected}
												onCheckedChange={
													handleCheckboxChange
												}
												className="translate-y-[2px] cursor-pointer w-5 h-5"
												onClick={handleCheckboxClick}
											/>
										</div>
									) : objectType === "contact" ? (
										<ContactAvatar
											name={objectData.title}
											avatarUrl={objectData.avatar}
											className="w-5 h-5 flex-shrink-0"
										/>
									) : (
										<Avatar className="w-6 h-6 flex-shrink-0">
											<AvatarImage
												src={objectData.avatar}
												alt={objectData.title}
											/>
											<AvatarFallback
												className={`text-xs ${objectData.avatarColor}`}
											>
												{objectData.initials}
											</AvatarFallback>
										</Avatar>
									)}
								</div>
								<div className={"flex flex-col"}>
									<p className="text-sm text-zinc-800 dark:text-zinc-100 gap-2 flex flex-row items-center">
										{objectData.title}
									</p>
									{objectData.subtitle && (
										<div
											className={
												"text-xs text-zinc-500 capitalize"
											}
										>
											{objectData.subtitle}
										</div>
									)}
								</div>
							</div>
							<div className="flex items-center justify-center">
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											className="h-6 w-6 text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 transition-colors"
											variant="ghost"
											size="icon"
											onClick={(e) => {
												e.stopPropagation();
												e.preventDefault();
											}}
										>
											<IconBrowserMaximize className="w-4 h-4" />
										</Button>
									</TooltipTrigger>
									<TooltipContent side="bottom">
										Expand
									</TooltipContent>
								</Tooltip>
							</div>
						</div>

						{/* Dynamic card row fields */}
						{cardRowFields && cardRowFields.length > 0 && (
							<div className="mt-3 space-y-2">
								{renderCardRowFields()}
							</div>
						)}

						<div
							className={"flex flex-row items-center gap-1 mt-1"}
						>
							<div className="ml-auto flex items-center gap-1 edit-button-container justify-between w-full">
								<div className="flex items-center gap-1 w-full justify-between">
									<div className="flex items-center gap-1">
										{isHovered && (
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														onClick={
															handleEditClick
														}
														className="h-6 w-6 text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 transition-colors"
														variant="ghost"
														size="icon"
													>
														<IconEdit className="w-4 h-4" />
													</Button>
												</TooltipTrigger>
												<TooltipContent side="bottom">
													Edit{" "}
													{objectType.slice(0, -1)}
												</TooltipContent>
											</Tooltip>
										)}

										{/* <Tooltip>
									<TooltipTrigger asChild>
										<Button
											className="h-6 w-6 text-zinc-500 hover:text-yellow-400 transition-colors"
											variant="ghost"
											size="icon"
										>
											{isHovered && (
												<IconStar className="w-4 h-4" />
											)}
										</Button>
									</TooltipTrigger>
									<TooltipContent side="bottom">
										Add to favorites
									</TooltipContent>
								</Tooltip> */}
									</div>
									<div className="flex items-center gap-1">
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													className="h-6 w-6 text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 transition-colors"
													variant="ghost"
													size="icon"
													onClick={(e) => {
														e.stopPropagation();
														e.preventDefault();
													}}
												>
													<IconMessagePlus className="w-4 h-4" />
												</Button>
											</TooltipTrigger>
											<TooltipContent side="bottom">
												Add a comment
											</TooltipContent>
										</Tooltip>
									</div>
								</div>

								{getStatusChangeInfo && (
									<Tooltip>
										<TooltipTrigger asChild>
											<div
												className={`flex items-center gap-1 text-xs cursor-default px-1.5 py-0.5 rounded-md transition-colors ${
													trackTime
														? getStatusChangeInfo
																.timeTracking
																.colorClass
														: "text-zinc-500"
												} ${
													trackTime
														? getStatusChangeInfo
																.timeTracking
																.bgClass
														: ""
												}`}
												onClick={(e) => {
													e.stopPropagation();
													e.preventDefault();
												}}
											>
												<IconClock className="w-3 h-3" />
												<span className="font-medium">
													{
														getStatusChangeInfo.timeText
													}
												</span>
											</div>
										</TooltipTrigger>
										<TooltipContent side="bottom">
											{getStatusChangeInfo.tooltipText}
										</TooltipContent>
									</Tooltip>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</DataTableContextMenu>
	);
}
