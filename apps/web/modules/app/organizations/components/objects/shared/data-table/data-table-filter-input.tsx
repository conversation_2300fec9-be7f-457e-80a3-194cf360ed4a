"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { useDebounce } from "@app/shared/hooks/useDebounce";
import { InputWithAddons } from "@ui/components/input-with-addons";
import { Label } from "@ui/components/label";
import { Search } from "lucide-react";
import { useEffect, useState } from "react";
import type { DataTableInputFilterField } from "./types";

function getFilter(filterValue: unknown) {
	return typeof filterValue === "string" ? filterValue : null;
}

export function DataTableFilterInput<TData>({
	value: _value,
}: DataTableInputFilterField<TData>) {
	const value = _value as string;
	const { table, columnFilters } = useDataTable();
	const column = table.getColumn(value);
	const filterValue = columnFilters.find((i) => i.id === value)?.value;
	const filters = getFilter(filterValue);
	const [input, setInput] = useState<string | null>(filters);

	const debouncedInput = useDebounce(input, 500);

	useEffect(() => {
		const newValue = debouncedInput?.trim() === "" ? null : debouncedInput;
		if (debouncedInput === null) return;
		column?.setFilterValue(newValue);
	}, [debouncedInput]);

	useEffect(() => {
		if (debouncedInput?.trim() !== filters) {
			setInput(filters);
		}
	}, [filters]);

	return (
		<div className="grid w-full gap-1.5">
			<Label
				htmlFor={value}
				className="sr-only px-2 text-muted-foreground"
			>
				{value}
			</Label>
			<InputWithAddons
				placeholder="Search"
				leading={<Search className="mt-0.5 h-4 w-4" />}
				className="bg-muted/50 rounded-none border-none"
				containerClassName="h-9 bg-muted/50"
				name={value}
				id={value}
				value={input || ""}
				onChange={(e) => setInput(e.target.value)}
			/>
		</div>
	);
}
