"use client";

import { fetchFavorites, useToggleFavorite } from "@app/favorites/lib/api";
import {
	useInfiniteQuery as useInfiniteTanstackQuery,
	useQuery,
} from "@tanstack/react-query";
import { useQueryStates } from "nuqs";
import { useMemo } from "react";
import { toast } from "sonner";
import type {
	BaseObjectSchema,
	FacetMetadataSchema,
	ObjectConfig,
} from "../../../../object-views/lib/types";
import { DataTableInfinite } from "../DataTableInfinite";
import { createGenericApiHooks } from "./api-factory";
import { createFacetedFilterHelpers } from "./query-options-factory";
import { FieldType } from "@repo/database";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

interface UniversalDataTableProps<TData extends BaseObjectSchema, TMeta = any> {
	organizationId: string;
	config: ObjectConfig<TData, TMeta>;
	queryOptions: (search: any, orgId: string) => any;
	renderHeader?: () => React.ReactNode;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
			visible?: boolean;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
	};
	onUpdateView?: (updatedView: any) => void;
}

export function UniversalDataTable<
	TData extends BaseObjectSchema,
	TMeta = any,
>({
	organizationId,
	config,
	queryOptions,
	renderHeader,
	view,
	onUpdateView,
}: UniversalDataTableProps<TData, TMeta>) {
	const [search, setSearch] = useQueryStates(config.searchParamsParser);

	// Create generic API hooks for this object type
	const apiHooks = useMemo(
		() =>
			createGenericApiHooks<TData>({
				objectType: config.type,
				apiEndpoint: config.apiEndpoint,
			}),
		[config.type, config.apiEndpoint],
	);

	// Get mutations for cell-level operations
	const updateFieldMutation = apiHooks.useUpdateField();
	const clearFieldMutation = apiHooks.useClearField();
	const deleteMutation = apiHooks.useDeleteRecord();
	const toggleFavoriteMutation = useToggleFavorite(organizationId);

	// Fetch favorites to check status
	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	const {
		data,
		isFetching,
		isLoading,
		fetchNextPage,
		hasNextPage,
		fetchPreviousPage,
		refetch,
	} = useInfiniteTanstackQuery(queryOptions(search, organizationId));

	const flatData = useMemo(() => {
		const rawData =
			data?.pages?.flatMap((page: any) => page.data ?? []) ?? [];

		// Deduplicate by ID to prevent optimistic update duplicates
		const seen = new Set();
		const deduplicatedData = rawData.filter((item: any) => {
			if (seen.has(item.id)) {
				return false;
			}
			seen.add(item.id);
			return true;
		});

		return deduplicatedData;
	}, [data?.pages]);

	const lastPage = data?.pages?.[data?.pages.length - 1] as any;
	const totalDBRowCount = lastPage?.meta?.totalRowCount || 0;
	const filterDBRowCount = lastPage?.meta?.filterRowCount || 0;
	const metadata = lastPage?.meta?.metadata;
	const chartData = lastPage?.meta?.chartData || [];
	const facets = lastPage?.meta?.facets || {};
	const totalFetched = flatData.length || 0;

	const { sort, start, size, id, cursor, direction, live, ...filter } =
		search;

	// Convert view filters to table column filter format
	const convertedViewFilters = useMemo(() => {
		if (!view?.filters || !Array.isArray(view.filters)) return [];

		return view.filters
			.map((viewFilter) => {
				const { field, logic, text, number } = viewFilter;

				// Convert based on logic type
				switch (logic) {
					case "contains":
					case "equals":
						return { id: field, value: text || number };

					case "in":
						// Convert comma-separated string back to array
						return {
							id: field,
							value: text ? text.split(",") : [],
						};

					case "between":
						// Handle range/slider filters
						return { id: field, value: number || [] };

					default:
						return { id: field, value: text || number };
				}
			})
			.filter(
				(filterItem) =>
					filterItem.value !== undefined && filterItem.value !== null,
			);
	}, [view?.filters, view?.id]);

	// Merge URL filters with view filters (URL filters take precedence)
	const mergedFilters = useMemo(() => {
		const urlFilters = Object.entries(filter)
			.map(([key, value]) => ({
				id: key,
				value,
			}))
			.filter(({ value }) => value !== undefined && value !== null);

		// Create a map of URL filters for quick lookup
		const urlFilterMap = new Map(urlFilters.map((f) => [f.id, f]));

		// Start with converted view filters, then override with URL filters
		const combined = [...convertedViewFilters];

		// Add or override with URL filters
		urlFilters.forEach((urlFilter) => {
			const existingIndex = combined.findIndex(
				(f) => f.id === urlFilter.id,
			);
			if (existingIndex >= 0) {
				combined[existingIndex] = urlFilter;
			} else {
				combined.push(urlFilter);
			}
		});

		return combined;
	}, [filter, convertedViewFilters]);

	const filterFields = useMemo(() => {
		return config.filterFields.map((field) => {
			const facetsField = facets?.[field.value as string];
			if (!facetsField) return field;
			if (field.options && field.options.length > 0) return field;

			const options = facetsField.rows.map((row: any) => ({
				label: `${row.value}`,
				value: row.value,
			}));

			return { ...field, options };
		});
	}, [facets, config.filterFields]);

	const { getFacetedUniqueValues, getFacetedMinMaxValues } =
		createFacetedFilterHelpers(facets);

	// Define field types for different object types
	const getFieldTypes = (): Record<
		string, FieldType
	> => {
		switch (config.type) {
			case "contact":
				return {
					firstName: "text",
					lastName: "text",
					name: "text",
					title: "text",
					summary: "textarea",
					status: "select",
					persona: "select",
					stage: "select",
					email: "array",
					phone: "array",
					company: "company",
					website: "text",
					linkedin: "text",
					facebook: "text",
					twitter: "text",
					instagram: "text",
					tags: "tags",
				};
			case "company":
				return {
					name: "text",
					industry: "text",
					size: "text",
					website: "text",
					email: "array",
					phone: "array",
					description: "textarea",
					tags: "tags",
				};
			case "property":
				return {
					name: "text",
					propertyType: "text",
					description: "textarea",
					price: "number",
					units: "number",
					tags: "tags",
				};
			default:
				return {};
		}
	};

	const getArrayFieldTypes = (): Record<
		string,
		"email" | "phone" | "text"
	> => {
		switch (config.type) {
			case "contact":
			case "company":
				return {
					email: "email",
					phone: "phone",
				};
			default:
				return {};
		}
	};

	const getSelectOptions = (): Record<
		string,
		Array<{ label: string; value: string }>
	> => {
		switch (config.type) {
			case "contact":
				return {
					status: CONTACT_STATUS.map((status) => ({
						label: status.label,
						value: status.value,
					})),
					persona: CONTACT_PERSONA.map((persona) => ({
						label: persona.label,
						value: persona.value,
					})),
					stage: CONTACT_STAGE.map((stage) => ({
						label: stage.label,
						value: stage.value,
					})),
				};
			default:
				return {};
		}
	};

	if (!organizationId) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Organization not found...
					</p>
				</div>
			</div>
		);
	}

	return (
		<DataTableInfinite
			organizationId={organizationId}
			objectType={config.type}
			columns={config.columns}
			data={flatData}
			totalRows={totalDBRowCount}
			filterRows={filterDBRowCount}
			totalRowsFetched={totalFetched}
			defaultColumnFilters={mergedFilters}
			defaultColumnSorting={sort ? [sort] : undefined}
			defaultRowSelection={search.id ? { [search.id]: true } : undefined}
			defaultColumnVisibility={(() => {
				// If we have a view with column definitions, set visibility based on view settings
				if (view?.columnDefs) {
					const visibility: Record<string, boolean> = {};

					// Map view column fields to actual column IDs and respect their visibility
					view.columnDefs.forEach((colDef) => {
						if (colDef.field) {
							// Handle field name mapping (e.g. "company.name" -> "company")
							const columnId =
								colDef.field === "company.name"
									? "company"
									: colDef.field;
							// Use the visible property from columnDef, defaulting to true if not specified
							visibility[columnId] = colDef.visible !== false;
						}
					});

					visibility.select = true;
					return visibility;
				}
				return {};
			})()}
			meta={metadata}
			filterFields={filterFields as any}
			sheetFields={config.sheetFields as any}
			isFetching={isFetching}
			isLoading={isLoading}
			fetchNextPage={() => fetchNextPage({}) as any}
			fetchPreviousPage={() => fetchPreviousPage({}) as any}
			refetch={() => refetch({}) as any}
			chartData={chartData}
			chartDataColumnId="createdAt"
			getRowClassName={(row: any) => {
				// Default row styling - can be overridden by object-specific config
				return "";
			}}
			getRowId={(row: TData) => row.id}
			getFacetedUniqueValues={getFacetedUniqueValues}
			getFacetedMinMaxValues={getFacetedMinMaxValues}
			renderSheetTitle={(props: any) => {
				// Default title rendering - should be overridden by object-specific config
				return `${config.type} Details`;
			}}
			renderHeader={renderHeader}
			searchParamsParser={config.searchParamsParser}
			primaryColumn={config.primaryColumn}
			view={view}
			onUpdateView={onUpdateView}
			// Context menu handlers
			onEdit={(e, recordId) => {
				console.warn("Edit record:", recordId);
				// TODO: Implement edit functionality
			}}
			onFavorite={(e, recordId) => {
				if (!organizationId) return;
				toggleFavoriteMutation.mutate(
					{
						objectId: recordId,
						objectType: config.type,
						organizationId,
					},
					{
						onSuccess: (result) => {
							toast.success(
								result.added
									? "Added to favorites"
									: "Removed from favorites",
							);
						},
						onError: (error: any) => {
							toast.error(
								"Failed to update favorite: " + error.message,
							);
						},
					},
				);
			}}
			onDelete={(e, recordId) => {
				deleteMutation.mutate(
					{
						id: recordId,
						organizationId,
					},
					{
						onSuccess: () => {
							toast.success("Record deleted successfully");
							refetch();
						},
						onError: (error: any) => {
							toast.error(
								"Failed to delete record: " + error.message,
							);
						},
					},
				);
			}}
			onCopy={(e, value) => {
				navigator.clipboard
					.writeText(value)
					.then(() => {
						toast.success("Copied to clipboard");
					})
					.catch((err) => {
						console.error("Failed to copy:", err);
						toast.error("Failed to copy to clipboard");
					});
			}}
			onHide={(e, columnId) => {
				if (!view?.columnDefs || !view.id) {
					toast.error("Cannot hide column: View not found");
					return;
				}

				// Don't allow hiding the primary column
				if (columnId === config.primaryColumn) {
					toast.error("Cannot hide the primary column");
					return;
				}

				// Update the view's columnDefs to hide the column
				const updatedColumnDefs = view.columnDefs.map((col) =>
					col.field === columnId ? { ...col, visible: false } : col,
				);

				// Immediately update the local view to trigger re-sync
				const optimisticUpdatedView = {
					...view,
					columnDefs: updatedColumnDefs,
					updatedAt: new Date().toISOString(), // Force change detection
				};

				if (onUpdateView) {
					onUpdateView(optimisticUpdatedView);
				}

				// Clear any column filters for the hidden column to prevent TanStack Table errors
				const currentFilters = search.filter || {};
				const hasColumnFilter = Object.keys(currentFilters).some(
					(key) => key === columnId,
				);

				if (hasColumnFilter) {
					const updatedFilters = { ...currentFilters };
					delete updatedFilters[columnId];
					setSearch({ filter: updatedFilters });
				}

				// API call to update the view
				fetch(`/api/object-views/views/${view.id}`, {
					method: "PATCH",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify({
						columnDefs: updatedColumnDefs,
					}),
				})
					.then(async (response) => {
						if (!response.ok) {
							const error = await response.json();
							throw new Error(
								error.error || "Failed to update view",
							);
						}
						return response.json();
					})
					.then((updatedView) => {
						if (onUpdateView && updatedView) {
							onUpdateView(updatedView);
						}
					})
					.catch((error) => {
						console.error("🔍 API error:", error);
						toast.error(`Failed to hide column: ${error.message}`);
						// Revert the optimistic update on error
						if (onUpdateView) {
							onUpdateView(view);
						}
					});
			}}
			onEditCell={async (e, cell) => {
				if (!organizationId) return;

				updateFieldMutation.mutate(
					{
						id: cell.rowId,
						field: cell.columnId,
						value: cell.value,
						organizationId,
					},
					{
						onSuccess: () => {
							toast.success("Field updated successfully");
							refetch();
						},
						onError: (error: any) => {
							toast.error(
								"Failed to update field: " + error.message,
							);
						},
					},
				);
			}}
			onClearValue={async (e, cell) => {
				if (!organizationId) return;

				clearFieldMutation.mutate(
					{
						id: cell.rowId,
						field: cell.columnId,
						organizationId,
					},
					{
						onSuccess: () => {
							toast.success("Field cleared successfully");
							refetch();
						},
						onError: (error: any) => {
							toast.error(
								"Failed to clear field: " + error.message,
							);
						},
					},
				);
			}}
			isFavorite={(record) => {
				return favorites.some(
					(fav) =>
						fav.objectId === record.id &&
						fav.objectType === config.type,
				);
			}}
			fieldTypes={getFieldTypes()}
			arrayFieldTypes={getArrayFieldTypes()}
			selectOptions={getSelectOptions()}
			enableInlineEditing={true}
			readonlyColumns={config.readonlyColumns}
		/>
	);
}
