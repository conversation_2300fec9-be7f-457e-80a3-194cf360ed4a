"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { useControls } from "@app/organizations/lib/controls-provider";
import { formatCompactNumber } from "@app/organizations/lib/format";
import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@ui/components/accordion";
import { Button } from "@ui/components/button";
import { Kbd } from "@ui/components/kbd";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { PanelLeftClose, PanelLeftOpen } from "lucide-react";
import type React from "react";
import { useMemo } from "react";
import { DataTableFilterCheckbox } from "./data-table-filter-checkbox";
import { DataTableFilterControlsDrawer } from "./data-table-filter-controls-drawer";
import { DataTableFilterInput } from "./data-table-filter-input";
import { DataTableFilterResetButton } from "./data-table-filter-reset-button";
import { DataTableFilterSlider } from "./data-table-filter-slider";
import { DataTableFilterTimerange } from "./data-table-filter-timerange";
import { DataTableResetButton } from "./data-table-reset-button";
import { IconLayoutSidebarLeftCollapseFilled, IconLayoutSidebarLeftExpand } from "@tabler/icons-react";

// FIXME: use @container (especially for the slider element) to restructure elements

// TODO: only pass the columns to generate the filters!
// https://tanstack.com/table/v8/docs/framework/react/examples/filters

export function DataTableFilterControls() {
	const { filterFields, table, isLoading, columnFilters } = useDataTable();
	const { open, setOpen } = useControls();
	const filters = table.getState().columnFilters;

	useHotkeys([
		{
			key: "b",
			callback: () => setOpen((prev) => !prev),
		},
	]);

	const rows = useMemo(
		() => ({
			total: table.getCoreRowModel().rows.length,
			filtered: table.getFilteredRowModel().rows.length,
		}),
		[isLoading, columnFilters],
	);

	return (
		<div className="flex flex-col h-full relative">
			{/* Mobile filter controls drawer */}
			<div className="block sm:hidden p-2 border-b flex-shrink-0">
				<DataTableFilterControlsDrawer />
			</div>

			{/* Filter count and reset button */}
			<div className="flex items-center justify-between gap-2 p-2 border-b flex-shrink-0">
				<div>
					<p className="text-sm text-muted-foreground">
						<span className="font-mono font-bold">
							{formatCompactNumber(rows.filtered)}
						</span>{" "}
						<span className="text-muted-foreground">of </span>
						<span className="font-mono font-bold">
							{formatCompactNumber(rows.total)}
						</span>{" "}
						<span className="text-muted-foreground">
							records
						</span>
					</p>
				</div>
				{filters.length ? <DataTableResetButton /> : null}
			</div>

			{/* Filter fields accordion - scrollable area */}
			<div className="flex-1 overflow-y-auto min-h-0 pb-16">
				<Accordion
					type="multiple"
					defaultValue={filterFields
						?.filter(({ defaultOpen }) => defaultOpen)
						?.map(({ value }) => value as string)}
				>
					{filterFields?.map((field) => {
						const value = field.value as string;
						return (
							<AccordionItem
								key={value}
								value={value}
								className="border-b"
							>
								<AccordionTrigger className="cursor-pointer w-full px-2 py-0 hover:no-underline data-[state=closed]:text-muted-foreground data-[state=open]:text-foreground focus-within:data-[state=closed]:text-foreground hover:data-[state=closed]:text-foreground">
									<div className="flex w-full items-center justify-between gap-2 truncate py-2 pr-2">
										<div className="flex items-center gap-2 truncate">
											<p className="text-sm font-medium">
												{field.label}
											</p>
											{value !==
												field.label.toLowerCase() &&
											!field.commandDisabled ? (
												<p className="mt-px truncate font-mono text-[10px] text-muted-foreground">
													{value}
												</p>
											) : null}
										</div>
										<DataTableFilterResetButton
											{...field}
										/>
									</div>
								</AccordionTrigger>
								<AccordionContent>
									{/* REMINDER: avoid the focus state to be cut due to overflow-hidden */}
									{/* REMINDER: need to move within here because of accordion height animation */}
									<div className="p-1">
										{(() => {
											switch (field.type) {
												case "checkbox": {
													return (
														<DataTableFilterCheckbox
															{...field}
														/>
													);
												}
												case "slider": {
													return (
														<DataTableFilterSlider
															{...field}
														/>
													);
												}
												case "input": {
													return (
														<DataTableFilterInput
															{...field}
														/>
													);
												}
												case "timerange": {
													return (
														<DataTableFilterTimerange
															{...field}
														/>
													);
												}
											}
										})()}
									</div>
								</AccordionContent>
							</AccordionItem>
						);
					})}
				</Accordion>
			</div>

			{/* Floating toggle button - bottom left */}
			<div className="absolute bottom-31 left-2 z-10">
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setOpen((prev) => !prev)}
							>
								{open ? (
									<IconLayoutSidebarLeftCollapseFilled className="h-4 w-4" />
								) : (
									<IconLayoutSidebarLeftExpand className="h-4 w-4" />
								)}
							</Button>
						</TooltipTrigger>
						<TooltipContent side="right">
							<p>
								Toggle controls with{" "}
								<Kbd className="ml-1 text-muted-foreground group-hover:text-accent-foreground">
									<span>B</span>
								</Kbd>
							</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>
		</div>
	);
}
