import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import { useMediaQuery } from "@app/shared/hooks/useMediaQuery";
import { Button } from "@ui/components/button";
import {
	Drawer,
	DrawerClose,
	DrawerContent,
	DrawerDes<PERSON>,
	DrawerFooter,
	Drawer<PERSON>eader,
	<PERSON>er<PERSON><PERSON><PERSON>,
	DrawerTrigger,
} from "@ui/components/drawer";
import { Kbd } from "@ui/components/kbd";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { FilterIcon } from "lucide-react";
import React from "react";
import { DataTableFilterControls } from "./data-table-filter-controls";

export function DataTableFilterControlsDrawer() {
	const triggerButtonRef = React.useRef<HTMLButtonElement>(null);
	const isMobile = useMediaQuery("(max-width: 640px)");

	useHotkeys([
		{
			key: "b",
			callback: () => {
				// Only trigger click if we're on mobile
				if (isMobile) {
					triggerButtonRef.current?.click();
				}
			},
		},
	]);

	return (
		<Drawer>
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<DrawerTrigger asChild>
							<Button
								ref={triggerButtonRef}
								variant="ghost"
								size="icon"
								className="h-9 w-9"
							>
								<FilterIcon className="w-4 h-4" />
							</Button>
						</DrawerTrigger>
					</TooltipTrigger>
					<TooltipContent side="right">
						<p>
							Toggle controls with{" "}
							<Kbd className="ml-1 text-muted-foreground group-hover:text-accent-foreground">
								<span className="mr-1">⌘</span>
								<span>B</span>
							</Kbd>
						</p>
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
			<DrawerContent className="max-h-[calc(100dvh-4rem)]">
				<DrawerHeader className="sr-only">
					<DrawerTitle>Filters</DrawerTitle>
					<DrawerDescription>
						Adjust your table filters
					</DrawerDescription>
				</DrawerHeader>
				<div className="px-4 flex-1 overflow-y-auto">
					<DataTableFilterControls />
				</div>
				<DrawerFooter>
					<DrawerClose asChild>
						<Button variant="outline" className="w-full">
							Close
						</Button>
					</DrawerClose>
				</DrawerFooter>
			</DrawerContent>
		</Drawer>
	);
}
