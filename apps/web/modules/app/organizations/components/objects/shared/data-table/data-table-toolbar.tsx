"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";

interface DataTableToolbarProps {
	renderActions?: () => React.ReactNode;
}

export function DataTableToolbar({ renderActions }: DataTableToolbarProps) {
	const { table } = useDataTable();

	return (
		<div className="flex flex-wrap items-center justify-between gap-4">
			<div className="flex flex-wrap items-center gap-2">
				{/* Toggle button moved to data-table-filter-controls */}
			</div>
			<div className="ml-auto flex items-center gap-2">
				{renderActions?.()}
			</div>
		</div>
	);
}
