"use client";

import { useCopyToClipboard } from "@app/shared/hooks/useCopyToClipboard";
import {
	HoverCard,
	HoverCardContent,
	HoverCardTrigger,
} from "@ui/components/hover-card";
import { cn } from "@ui/lib";
import { format, formatDistanceToNowStrict } from "date-fns";
import { Check, Copy } from "lucide-react";
import type { ComponentPropsWithoutRef } from "react";

type HoverCardContentProps = ComponentPropsWithoutRef<typeof HoverCardContent>;

interface DataTableColumnTimestampProps {
	date: Date;
	side?: HoverCardContentProps["side"];
	sideOffset?: HoverCardContentProps["sideOffset"];
	align?: HoverCardContentProps["align"];
	alignOffset?: HoverCardContentProps["alignOffset"];
	className?: string;
}

export function DataTableColumnTimestamp({
	date,
	side = "right",
	align = "start",
	alignOffset = -4,
	sideOffset,
	className,
}: DataTableColumnTimestampProps) {
	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

	return (
		<HoverCard openDelay={0} closeDelay={0}>
			<HoverCardTrigger asChild>
				<div className={cn("whitespace-nowrap font-mono", className)}>
					{format(date, "LLL dd, y HH:mm:ss")}
				</div>
			</HoverCardTrigger>
			<HoverCardContent
				className="z-10 w-auto p-2"
				{...{ side, align, alignOffset, sideOffset }}
			>
				<dl className="flex flex-col gap-1">
					<Row value={String(date.getTime())} label="Timestamp" />
					<Row
						value={format(date, "LLL dd, y HH:mm:ss")}
						label="UTC"
					/>
					<Row
						value={format(date, "LLL dd, y HH:mm:ss")}
						label={timezone}
					/>
					<Row
						value={formatDistanceToNowStrict(date, {
							addSuffix: true,
						})}
						label="Relative"
					/>
				</dl>
			</HoverCardContent>
		</HoverCard>
	);
}

function Row({ value, label }: { value: string; label: string }) {
	const { copy, isCopied } = useCopyToClipboard();

	return (
		<div
			className="group flex items-center justify-between gap-4 text-sm"
			onClick={(e) => {
				e.stopPropagation();
				copy(value);
			}}
		>
			<dt className="text-muted-foreground">{label}</dt>
			<dd className="flex items-center gap-1 truncate font-mono">
				<span className="invisible group-hover:visible">
					{!isCopied ? (
						<Copy className="h-3 w-3" />
					) : (
						<Check className="h-3 w-3" />
					)}
				</span>
				{value}
			</dd>
		</div>
	);
}
