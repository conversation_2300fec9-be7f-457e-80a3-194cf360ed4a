import { CreateContactButton } from "@app/contacts/components/CreateContactButton";
import { ObjectType } from "@repo/database";
import { objectItems } from "@app/organizations/components/objects/constants";
import { CreatePropertyButton } from "@app/properties/components/CreatePropertyButton";
import { EllipsisDropdown } from "@shared/components/EllipsisDropdown";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconBuilding,
	IconEye,
	IconFilter,
	IconLayoutGrid,
	IconLink,
	IconPlus,
	IconSettings,
} from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { DropdownMenuSeparator } from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import { useState } from "react";
import { CreateViewModal } from "./CreateViewModal";
import { ObjectViewsDropdown } from "./ObjectViewsDropdown";
import { ViewSettingsDropdown } from "./ViewSettingsDropdown";
import { singularToPluralMap } from "@repo/database";

export const ObjectsHeader = ({
	objectType,
	organizationId,
	view,
	views = [],
	onViewChange,
	onViewRenamed,
	user,
	handleCreateNew,
	primaryColumn,
	onUpdateView,
	showAttributeLabels,
	onShowAttributeLabelsChange,
}: {
	objectType: ObjectType;
	organizationId: string;
	view?: any;
	views?: any[];
	onViewChange?: (viewId: string) => void;
	onViewRenamed?: (updatedView: any) => void;
	user?: {
		name: string;
		avatarUrl?: string | null;
	};
	handleCreateNew?: () => void;
	primaryColumn?: string;
	onUpdateView?: (updatedView: any) => void;
	showAttributeLabels?: boolean;
	onShowAttributeLabelsChange?: (show: boolean) => void;
}) => {
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

	const handleCreateNewView = () => {
		setIsCreateModalOpen(true);
	};
	

	const objectItem = objectItems.find((item) => item.objectType === objectType);

	return (
		<>
			<div className="flex items-center p-2 px-4 justify-between border-b">
				<div className="flex items-center gap-2">
					<div
						className={cn(
							"flex items-center justify-center rounded-lg p-1",
							objectItem?.className || "bg-muted p-1 rounded-md",
						)}
					>
						{objectItem?.icon && (
							<objectItem.icon
								className={cn("h-3 w-3 text-white")}
							/>
						)}
					</div>
					<h1 className="text-md capitalize">{singularToPluralMap[objectType]}</h1>
				</div>

				<div className="flex items-center gap-2">
					<ObjectViewsDropdown
						view={view}
						views={views}
						onViewChange={onViewChange || (() => {})}
						onCreateNew={handleCreateNewView}
						onViewRenamed={onViewRenamed}
						objectType={objectType as ObjectType}
					/>

					<ViewSettingsDropdown
						view={view}
						objectType={objectType as ObjectType}
						primaryColumn={primaryColumn}
						onUpdateView={onUpdateView}
						showAttributeLabels={showAttributeLabels}
						onShowAttributeLabelsChange={
							onShowAttributeLabelsChange
						}
					/>
					{objectType === "property" && (
						<CreatePropertyButton icon={false} />
					)}
					{objectType === "contact" && (
						<CreateContactButton icon={false} />
					)}
					<EllipsisDropdown>
						<EllipsisDropdown.Item className="flex items-center gap-x-2">
							<IconSettings className="h-4 w-4" />
							Object settings
						</EllipsisDropdown.Item>
						<EllipsisDropdown.Item className="flex items-center gap-x-2">
							<IconLayoutGrid className="h-4 w-4" />
							Manage attributes
						</EllipsisDropdown.Item>
						<DropdownMenuSeparator />
						<EllipsisDropdown.Item className="flex items-center gap-x-2">
							<IconLink className="h-4 w-4" />
							Add integration
						</EllipsisDropdown.Item>
					</EllipsisDropdown>
				</div>
			</div>

			<CreateViewModal
				open={isCreateModalOpen}
				onOpenChange={setIsCreateModalOpen}
				objectType={objectType as ObjectType}
				organizationId={organizationId}
				onViewCreated={(viewId) => {
					onViewChange?.(viewId);
					setIsCreateModalOpen(false);
				}}
			/>
		</>
	);
};
