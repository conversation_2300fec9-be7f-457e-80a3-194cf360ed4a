"use client";

import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { IconChevronDown, IconChevronRight } from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import { AVAILABLE_ATTRIBUTES } from "./constants/available-attributes";
import { ObjectType } from "@repo/database";

interface AddColumnPopoverProps {
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
	};
	objectType: ObjectType;
	primaryColumn?: string;
	children: React.ReactNode;
	onColumnAdded?: () => void;
}

export const AddColumnPopover = ({
	view,
	objectType,
	primaryColumn = "name",
	children,
	onColumnAdded,
}: AddColumnPopoverProps) => {
	const [open, setOpen] = useState(false);
	const [searchQuery, setSearchQuery] = useState("");
	const queryClient = useQueryClient();
	const { activeOrganization } = useActiveOrganization();

	// Mutation to update view columns
	const updateViewMutation = useMutation({
		mutationFn: async (updatedColumnDefs: any[]) => {
			if (!view?.id) return;

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					columnDefs: updatedColumnDefs,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update view");
			}

			return response.json();
		},
		onMutate: async (updatedColumnDefs) => {
			if (!view?.id || !activeOrganization?.id) return;

			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: ["objectView", view.id],
			});
			await queryClient.cancelQueries({
				queryKey: ["objectViews", activeOrganization.id, objectType],
			});

			// Snapshot the previous values
			const previousView = queryClient.getQueryData([
				"objectView",
				view.id,
			]);
			const previousViews = queryClient.getQueryData([
				"objectViews",
				activeOrganization.id,
				objectType,
			]);

			// Optimistically update the single view
			queryClient.setQueryData(["objectView", view.id], (old: any) => {
				if (!old) return old;
				return {
					...old,
					columnDefs: updatedColumnDefs,
					updatedAt: new Date().toISOString(),
				};
			});

			// Optimistically update the views list
			queryClient.setQueryData(
				["objectViews", activeOrganization.id, objectType],
				(old: any[]) => {
					if (!old || !Array.isArray(old)) return old;
					return old.map((v: any) =>
						v.id === view.id
							? {
									...v,
									columnDefs: [...updatedColumnDefs], // Ensure new array reference
									updatedAt: new Date().toISOString(),
									// Force a new reference by adding a cache invalidation key
									_cacheKey: Date.now(),
								}
							: v,
					);
				},
			);

			return { previousView, previousViews };
		},
		onSuccess: (data, variables, context) => {
			// Invalidate queries to ensure fresh data
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: [
						"objectViews",
						activeOrganization.id,
						objectType,
					],
				});
				// Also invalidate all views for this organization to be safe
				queryClient.invalidateQueries({
					queryKey: ["objectViews", activeOrganization.id],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}

			// Call the onColumnAdded callback
			onColumnAdded?.();
		},
		onError: (error, variables, context) => {
			toast.error(`Failed to update view: ${error.message}`);

			// Rollback optimistic updates
			if (context?.previousView && view?.id) {
				queryClient.setQueryData(
					["objectView", view.id],
					context.previousView,
				);
			}
			if (context?.previousViews && activeOrganization?.id) {
				queryClient.setQueryData(
					["objectViews", activeOrganization.id, objectType],
					context.previousViews,
				);
			}
		},
	});

	const availableAttributes = AVAILABLE_ATTRIBUTES[objectType] || [];
	const currentColumnFields = new Set(
		view?.columnDefs?.map((col) => col.field) || [],
	);

	// Filter available attributes to exclude already added columns and primary column
	const filteredAttributes = useMemo(() => {
		return availableAttributes.filter((attr) => {
			const matchesSearch =
				attr.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
				attr.field.toLowerCase().includes(searchQuery.toLowerCase());
			const notAlreadyAdded = !currentColumnFields.has(attr.field);
			const notPrimaryColumn = attr.field !== primaryColumn;
			return matchesSearch && notAlreadyAdded && notPrimaryColumn;
		});
	}, [availableAttributes, currentColumnFields, searchQuery, primaryColumn]);

	// Group filtered attributes by category
	const groupedAttributes = useMemo(() => {
		const groups: Record<string, typeof filteredAttributes> = {};
		filteredAttributes.forEach((attr) => {
			if (!groups[attr.category]) {
				groups[attr.category] = [];
			}
			groups[attr.category].push(attr);
		});
		return groups;
	}, [filteredAttributes]);

	const handleAddColumn = (field: string, label: string) => {
		if (!view?.columnDefs) return;

		const newColumn = {
			field,
			headerName: label,
			width: 150,
		};

		const updatedColumnDefs = [...view.columnDefs, newColumn];
		updateViewMutation.mutate(updatedColumnDefs);
		setOpen(false);
		setSearchQuery("");
	};

	if (!view) return null;

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>{children}</PopoverTrigger>
			<PopoverContent className="w-64 p-0" align="start">
				<div className="p-2">
					<div className="flex items-center gap-2 mb-2">
						<div className="text-xs text-muted-foreground">
							Add column
						</div>
					</div>

					<Input
						placeholder="Search attributes..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="mx-2 mb-4"
						autoFocus
					/>

					<div className="max-h-80 overflow-y-auto">
						{Object.entries(groupedAttributes).map(
							([category, attributes]) => (
								<div key={category} className="mb-4">
									{attributes.length > 0 && (
										<>
											<div className="text-[10px] font-mono text-muted-foreground mb-2 px-2">
												{category}
											</div>
											<div className="space-y-1 w-full">
												{attributes.map((attr) => {
													const Icon = attr.icon;
													return (
														<button
															key={attr.field}
															className="group w-full cursor-pointer flex items-center gap-3 p-1 rounded-lg hover:bg-muted/50 border border-transparent hover:border-border"
															onClick={() =>
																handleAddColumn(
																	attr.field,
																	attr.label,
																)
															}
														>
															<Icon className="h-4 w-4 text-muted-foreground" />
															<span className="text-sm">
																{attr.label}
															</span>
														</button>
													);
												})}
											</div>
										</>
									)}
								</div>
							),
						)}

						{Object.keys(groupedAttributes).length === 0 && (
							<div className="text-center py-8 text-muted-foreground">
								<p className="text-sm">No attributes found</p>
								<p className="text-xs">
									Try a different search term
								</p>
							</div>
						)}
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
};
