import { infiniteQueryOptions, keepPreviousData } from "@tanstack/react-query";
import type {
	BaseChartSchema,
	ContactSchema,
	FacetMetadataSchema,
} from "./schema";
import { type SearchParamsType, searchParamsSerializer } from "./search-params";
import { ObjectType } from "@repo/database";

export type ObjectsMeta = {
	objectType: ObjectType;
	totalCount: number;
	hasNextPage: boolean;
};

export type InfiniteQueryMeta<TMeta = Record<string, unknown>> = {
	totalRowCount: number;
	filterRowCount: number;
	chartData: BaseChartSchema[];
	facets: Record<string, FacetMetadataSchema>;
	metadata?: TMeta;
};

export type InfiniteQueryResponse<TData, TMeta = unknown> = {
	data: TData;
	meta: InfiniteQueryMeta<TMeta>;
	prevCursor: string | null;
	nextCursor: string | null;
};

export const contactsDataOptions = (
	search: SearchParamsType,
	orgId: string,
) => {
	return infiniteQueryOptions({
		queryKey: [
			"contacts-infinite",
			orgId,
			searchParamsSerializer({ ...search, id: null, live: null }),
		],
		queryFn: async ({ pageParam }) => {
			const cursor = pageParam.cursor as string;
			const direction = pageParam.direction as
				| "next"
				| "prev"
				| undefined;

			// Build query parameters properly
			const params = new URLSearchParams();
			params.append("organizationId", orgId);

			if (cursor) {
				params.append("cursor", new Date(cursor).toISOString());
			}

			params.append("direction", direction || "next");
			params.append("size", "50");

			// Add search filters with proper formatting
			Object.entries(search).forEach(([key, value]) => {
				if (value !== null && value !== undefined && value !== "") {
					// Special handling for createdAt to preserve timestamp format
					if (key === "createdAt" && Array.isArray(value)) {
						// Convert Date objects back to timestamps and join with colon
						const timestamps = value.map((date) =>
							date instanceof Date ? date.getTime() : date,
						);
						params.append(key, timestamps.join(":"));
					} else {
						params.append(key, String(value));
					}
				}
			});

			const finalUrl = `/api/objects/contacts/infinite?${params.toString()}`;

			// Call the infinite contacts endpoint
			const response = await fetch(finalUrl, {
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch contacts");
			}

			const json = await response.json();

			// The infinite API returns the expected format directly
			const transformedResponse: InfiniteQueryResponse<
				ContactSchema[],
				ObjectsMeta
			> = {
				data: json.data || [],
				meta: json.meta || {
					totalRowCount: json.meta?.totalRowCount || 0,
					filterRowCount: json.meta?.filterRowCount || 0,
					chartData: json.meta?.chartData || [],
					facets: json.meta?.facets || {},
					metadata: json.meta?.metadata || {
						objectType: "contact" as const,
						totalCount: 0,
						hasNextPage: false,
					},
				},
				prevCursor: json.prevCursor || null,
				nextCursor: json.nextCursor || null,
			};

			return transformedResponse;
		},
		initialPageParam: {
			cursor: new Date().toISOString(),
			direction: "next",
		},
		getPreviousPageParam: (firstPage, _pages) => {
			if (!firstPage.prevCursor) return null;
			return { cursor: firstPage.prevCursor, direction: "prev" };
		},
		getNextPageParam: (lastPage, _pages) => {
			if (!lastPage.nextCursor) return null;
			return { cursor: lastPage.nextCursor, direction: "next" };
		},
		refetchOnWindowFocus: false,
		placeholderData: keepPreviousData,
	});
};

export const companiesDataOptions = (
	search: SearchParamsType,
	orgId: string,
) => {
	return infiniteQueryOptions({
		queryKey: [
			"companies-infinite",
			orgId,
			searchParamsSerializer({ ...search, id: null, live: null }),
		],
		queryFn: async ({ pageParam }) => {
			const cursor = pageParam.cursor as string;
			const direction = pageParam.direction as
				| "next"
				| "prev"
				| undefined;

			// Build query parameters properly
			const params = new URLSearchParams();
			params.append("organizationId", orgId);

			if (cursor) {
				params.append("cursor", new Date(cursor).toISOString());
			}

			params.append("direction", direction || "next");
			params.append("size", "50");

			// Add search filters with proper formatting
			Object.entries(search).forEach(([key, value]) => {
				if (value !== null && value !== undefined && value !== "") {
					// Special handling for createdAt to preserve timestamp format
					if (key === "createdAt" && Array.isArray(value)) {
						// Convert Date objects back to timestamps and join with colon
						const timestamps = value.map((date) =>
							date instanceof Date ? date.getTime() : date,
						);
						params.append(key, timestamps.join(":"));
					} else {
						params.append(key, String(value));
					}
				}
			});

			const response = await fetch(
				`/api/objects/companies/infinite?${params.toString()}`,
			);

			if (!response.ok) {
				throw new Error("Failed to fetch companies");
			}

			const json = await response.json();
			return json;
		},
		initialPageParam: {
			cursor: new Date().toISOString(),
			direction: "next",
		},
		getPreviousPageParam: (firstPage, _pages) => {
			if (!firstPage.prevCursor) return null;
			return { cursor: firstPage.prevCursor, direction: "prev" };
		},
		getNextPageParam: (lastPage, _pages) => {
			if (!lastPage.nextCursor) return null;
			return { cursor: lastPage.nextCursor, direction: "next" };
		},
		refetchOnWindowFocus: false,
		placeholderData: keepPreviousData,
	});
};

export const propertiesDataOptions = (
	search: SearchParamsType,
	orgId: string,
) => {
	return infiniteQueryOptions({
		queryKey: [
			"properties-infinite",
			orgId,
			searchParamsSerializer({ ...search, id: null, live: null }),
		],
		queryFn: async ({ pageParam }) => {
			const cursor = pageParam.cursor as string;
			const direction = pageParam.direction as
				| "next"
				| "prev"
				| undefined;

			// Build query parameters properly
			const params = new URLSearchParams();
			params.append("organizationId", orgId);

			if (cursor) {
				params.append("cursor", new Date(cursor).toISOString());
			}

			params.append("direction", direction || "next");
			params.append("size", "50");

			// Add search filters with proper formatting
			Object.entries(search).forEach(([key, value]) => {
				if (value !== null && value !== undefined && value !== "") {
					// Special handling for createdAt to preserve timestamp format
					if (key === "createdAt" && Array.isArray(value)) {
						// Convert Date objects back to timestamps and join with colon
						const timestamps = value.map((date) =>
							date instanceof Date ? date.getTime() : date,
						);
						params.append(key, timestamps.join(":"));
					} else {
						params.append(key, String(value));
					}
				}
			});

			const response = await fetch(
				`/api/objects/properties/infinite?${params.toString()}`,
			);

			if (!response.ok) {
				throw new Error("Failed to fetch properties");
			}

			const json = await response.json();
			return json;
		},
		initialPageParam: {
			cursor: new Date().toISOString(),
			direction: "next",
		},
		getPreviousPageParam: (firstPage, _pages) => {
			if (!firstPage.prevCursor) return null;
			return { cursor: firstPage.prevCursor, direction: "prev" };
		},
		getNextPageParam: (lastPage, _pages) => {
			if (!lastPage.nextCursor) return null;
			return { cursor: lastPage.nextCursor, direction: "next" };
		},
		refetchOnWindowFocus: false,
		placeholderData: keepPreviousData,
	});
};
