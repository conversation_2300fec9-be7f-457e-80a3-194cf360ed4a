"use client";

import { propertiesConfig, propertyQueryOptions } from "@app/organizations/components/objects/properties/config";
import { createDynamicColumns } from "@app/organizations/components/objects/shared/DynamicColumnFactory";
import { useMemo, useEffect, useState } from "react";
import { cn } from "@ui/lib";
import { DataTableInfinite } from "@app/organizations/components/objects/DataTableInfinite";
import { propertySearchParams } from "@app/organizations/components/objects/properties/search-params";
import { propertyFilterFields } from "@app/organizations/components/objects/constants";

function PropertyTable({
	properties,
	isOpen,
	onClose,
	onPropertySelect,
	view,
	organizationId,
	selectedProperty,
	isLoading,
	isFetching,
}: {
	properties: any[];
	isOpen: boolean;
	onClose: () => void;
	onPropertySelect: (property: any) => void;
	view: any;
	organizationId: string;
	selectedProperty: any;
	isLoading?: boolean;
	isFetching?: boolean;
}) {
	const [rowSelection, setRowSelection] = useState({});

	// Update row selection when selectedProperty changes
	useEffect(() => {
		if (selectedProperty) {
			// Find the property in the data array using either id or _id
			const selectedIndex = properties.findIndex(p => 
				p.id === selectedProperty.id || p._id === selectedProperty._id
			);
			if (selectedIndex !== -1) {
				setRowSelection({ [selectedIndex]: true });
			}
		} else {
			setRowSelection({});
		}
	}, [selectedProperty, properties]);

	// Handle row selection changes from the table
	const handleRowSelectionChange = (newSelection: Record<string, boolean>) => {
		setRowSelection(newSelection);
		
		// Get the selected property and notify parent
		const selectedIndices = Object.keys(newSelection);
		if (selectedIndices.length === 1) {
			const selectedIndex = parseInt(selectedIndices[0]);
			const selectedProp = properties[selectedIndex];
			if (selectedProp) {
				onPropertySelect({ 
					...selectedProp,
					// Ensure both id and _id are present
					id: selectedProp.id || selectedProp._id,
					_id: selectedProp._id || selectedProp.id,
					source: "database",
					isSelected: true 
				});
			}
		} else {
			// If no selection or multiple selections, clear the selected property
			onPropertySelect(null);
		}
	};

	// Create dynamic columns based on view configuration
	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				"property",
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			const filteredFilterFields = propertyFilterFields.filter(
				(field) => availableColumnIds.has(field.value as string),
			);

			return {
				...propertiesConfig,
				columns: dynamicColumns,
				filterFields: filteredFilterFields,
			};
		}
		return propertiesConfig;
	}, [view?.columnDefs, view?.id, view?.updatedAt]);

	if (!isOpen) return null;

	return (
		<div className={cn(
			"bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/75",
			"shadow-lg",
			"h-[25vh]",
			"border-t border-border",
			"flex flex-col"
		)}>
			<div className="flex-1 flex flex-col overflow-hidden">
				<DataTableInfinite
					columns={dynamicConfig.columns}
					data={properties}
					isLoading={isLoading}
					isFetching={isFetching}
					organizationId={organizationId}
					view={view}
					onUpdateView={() => {}}
					meta={{ 
						total: properties.length, 
						pageSize: 50, // Standard page size
						currentPage: 1,
						lastPage: Math.ceil(properties.length / 50)
					}}
					chartDataColumnId=""
					fetchNextPage={async () => {
						// Implement if needed
						await Promise.resolve();
					}}
					refetch={async () => {
						// Implement if needed
						await Promise.resolve();
					}}
					hasNextPage={false}
					searchParamsParser={propertySearchParams.parser}
					objectType="property"
					primaryColumn="name"
					renderSheetTitle={() => "Property Details"}
					getRowId={(row: any) => row._id}
					filterFields={dynamicConfig.filterFields as any[]}
					renderFilterBar={true}
					renderFilterControls={true}
					selectedProperty={selectedProperty}
					onPropertySelect={onPropertySelect}
					defaultRowSelection={rowSelection}
					onRowSelectionChange={handleRowSelectionChange}
				/>
			</div>
		</div>
	);
}

export { PropertyTable };