"use client";

import type MapboxDraw from "@mapbox/mapbox-gl-draw";
import {
	IconMaximize,
	IconMinus,
	IconNavigation,
	IconPencil,
	IconSearch,
	IconSettings,
	IconStack2,
	IconX,
} from "@tabler/icons-react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Separator } from "@ui/components/separator";
import { Switch } from "@ui/components/switch";
import { cn } from "@ui/lib";
import mapboxgl from "mapbox-gl";
import React from "react";

import type { AutocompleteResult, MapControlsState } from "../types";

interface MapControlsProps {
	controlsState: MapControlsState;
	updateControls: (updates: Partial<MapControlsState>) => void;
	map: mapboxgl.Map | null;
	draw: MapboxDraw | null;
	mappableProperties: any[];
	autocompleteResults: AutocompleteResult[];
	isAutocompleteVisible: boolean;
	setAutocompleteVisible: (visible: boolean) => void;
	onSearch: (e: React.FormEvent) => void;
	onAutocompleteSelect: (result: AutocompleteResult) => void;
	onSearchValueChange: (value: string) => void;
	className?: string;
}

export function MapControls({
	controlsState,
	updateControls,
	map,
	draw,
	mappableProperties,
	autocompleteResults,
	isAutocompleteVisible,
	setAutocompleteVisible,
	onSearch,
	onAutocompleteSelect,
	onSearchValueChange,
	className
}: MapControlsProps) {
	const toggleSatelliteView = (enabled: boolean) => {
		updateControls({ showSatellite: enabled });
		if (map) {
			const style = enabled
				? "mapbox://styles/mapbox/satellite-streets-v12"
				: "mapbox://styles/mapbox/streets-v12";
			map.setStyle(style);
		}
	};

	const toggleTraffic = (enabled: boolean) => {
		updateControls({ showTraffic: enabled });
		if (map) {
			if (enabled) {
				map.addSource("traffic", {
					type: "vector",
					url: "mapbox://mapbox.mapbox-traffic-v1",
				});
				map.addLayer({
					id: "traffic",
					type: "line",
					source: "traffic",
					"source-layer": "traffic",
					paint: {
						"line-color": [
							"case",
							["==", ["get", "congestion"], "low"],
							"#00ff00",
							["==", ["get", "congestion"], "moderate"],
							"#ffff00",
							["==", ["get", "congestion"], "heavy"],
							"#ff8000",
							["==", ["get", "congestion"], "severe"],
							"#ff0000",
							"#000000",
						],
						"line-width": 2,
					},
				});
			} else {
				if (map.getLayer("traffic")) {
					map.removeLayer("traffic");
				}
				if (map.getSource("traffic")) {
					map.removeSource("traffic");
				}
			}
		}
	};

	const toggle3D = (enabled: boolean) => {
		updateControls({ show3D: enabled });
		if (map) {
			if (enabled) {
				map.setPitch(45);
				map.setBearing(-17.6);
			} else {
				map.setPitch(0);
				map.setBearing(0);
			}
		}
	};

	const setDrawMode = (mode: string) => {
		if (draw) {
			draw.changeMode(mode);
			updateControls({ isDrawMode: mode.includes("draw_") });
		}
	};

	const toggleDrawControls = () => {
		updateControls({
			isDrawControlsOpen: !controlsState.isDrawControlsOpen,
			isControlsOpen: false,
		});
	};

	const toggleMainControls = () => {
		updateControls({
			isControlsOpen: !controlsState.isControlsOpen,
			isDrawControlsOpen: false,
		});
	};

	return (
		<div className={cn("absolute top-2 right-2.5 z-10 flex items-start gap-2", className)}>
			{/* Search Bar */}
			<div className="relative">
				<form onSubmit={onSearch} className="flex items-center gap-2">
					<Input
						type="text"
						placeholder={
							controlsState.isSearching
								? "Searching..."
								: "Search properties..."
						}
						value={controlsState.searchValue}
						onChange={(e) => onSearchValueChange(e.target.value)}
						onFocus={() => {
							if (autocompleteResults.length > 0) {
								setAutocompleteVisible(true);
							}
						}}
						onBlur={() => {
							setTimeout(
								() => setAutocompleteVisible(false),
								200,
							);
						}}
						disabled={controlsState.isSearching}
						className="w-64 !bg-sidebar border border-border"
						leftIcon={
							controlsState.isSearching ? (
								<div className="animate-spin h-4 w-4 border-2 border-muted-foreground border-t-transparent rounded-full" />
							) : (
								<IconSearch className="h-4 w-4 text-muted-foreground" />
							)
						}
					/>
				</form>

				{/* Autocomplete Dropdown */}
				{isAutocompleteVisible && autocompleteResults.length > 0 && (
					<div className="absolute top-full left-0 right-0 z-50 mt-1 bg-sidebar border border-border rounded-lg shadow-lg max-h-[400px] w-[350px] overflow-y-auto">
						{autocompleteResults.map((result, index) => (
							<div
								key={index}
								className="px-3 py-2 hover:bg-muted cursor-pointer border-b border-border last:border-b-0"
								onClick={() => onAutocompleteSelect(result)}
							>
								<div className="text-sm font-medium">
									{result.address}
								</div>
								{result.city && result.state && (
									<div className="text-xs text-muted-foreground">
										{result.city}, {result.state}{" "}
										{result.zip}
									</div>
								)}
							</div>
						))}
					</div>
				)}
			</div>

			{/* Draw Control Button */}
			<Button
				variant="ghost"
				size="icon"
				onClick={toggleDrawControls}
				className={`!bg-sidebar !border !border-border ${controlsState.isDrawControlsOpen ? "!bg-blue-50 !text-blue-600" : ""}`}
			>
				<IconPencil className="h-4 w-4" />
			</Button>

			{/* Draw Controls Dropdown */}
			{controlsState.isDrawControlsOpen && (
				<Card className="absolute top-12 right-16 w-64 shadow-xl border-0">
					<CardContent className="p-4 space-y-4">
						<div className="flex items-center justify-between">
							<h3 className="font-semibold text-sm">
								Drawing Tools
							</h3>
							<Button
								variant="ghost"
								size="icon"
								onClick={() =>
									updateControls({
										isDrawControlsOpen: false,
									})
								}
								className="h-6 w-6"
							>
								<IconMinus className="h-3 w-3" />
							</Button>
						</div>

						<Separator />

						{/* Drawing Mode Selection */}
						<div className="space-y-2">
							<Label className="text-sm font-medium">
								Draw Mode
							</Label>
							<div className="grid grid-cols-1 gap-2">
								<Button
									variant={
										controlsState.isDrawMode &&
										draw?.getMode() === "draw_polygon"
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() => setDrawMode("draw_polygon")}
									className="text-xs justify-start"
								>
									<IconStack2 className="h-3 w-3 mr-2" />
									Polygon
								</Button>
								<Button
									variant={
										controlsState.isDrawMode &&
										draw?.getMode() === "draw_line_string"
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() =>
										setDrawMode("draw_line_string")
									}
									className="text-xs justify-start"
								>
									<IconMinus className="h-3 w-3 mr-2" />
									Line
								</Button>
								<Button
									variant={
										controlsState.isDrawMode &&
										draw?.getMode() === "draw_point"
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() => setDrawMode("draw_point")}
									className="text-xs justify-start"
								>
									●<span className="ml-2">Point</span>
								</Button>
							</div>
						</div>

						<Separator />

						{/* Selection and Edit */}
						<div className="space-y-2">
							<Label className="text-sm font-medium">
								Edit Tools
							</Label>
							<div className="grid grid-cols-1 gap-2">
								<Button
									variant={
										!controlsState.isDrawMode
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() => setDrawMode("simple_select")}
									className="text-xs justify-start"
								>
									<IconNavigation className="h-3 w-3 mr-2" />
									Select & Edit
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => {
										if (draw) {
											const selectedFeatures =
												draw.getSelected();
											if (
												selectedFeatures.features
													.length > 0
											) {
												draw.delete(
													selectedFeatures.features.map(
														(f) => f.id as string,
													),
												);
											}
										}
									}}
									className="text-xs justify-start"
								>
									<IconX className="h-3 w-3 mr-2" />
									Delete Selected
								</Button>
							</div>
						</div>

						<Separator />

						{/* Actions */}
						<div className="space-y-2">
							<Label className="text-sm font-medium">
								Actions
							</Label>
							<div className="grid grid-cols-1 gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => {
									}}
									className="text-xs justify-start"
								>
									💾 Export Features
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => {
										if (draw) {
											draw.deleteAll();
										}
									}}
									className="text-xs justify-start"
								>
									🗑️ Clear All
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Controls Toggle Button */}
			<Button
				variant="ghost"
				size="icon"
				onClick={toggleMainControls}
				className="!bg-sidebar !border !border-border"
			>
				<IconSettings className="h-4 w-4" />
			</Button>

			{/* Expandable Controls Panel */}
			{controlsState.isControlsOpen && (
				<Card className="absolute top-12 right-0 w-72 shadow-xl border-0">
					<CardContent className="p-4 space-y-4">
						<div className="flex items-center justify-between">
							<h3 className="font-semibold text-sm">
								Map Controls
							</h3>
							<Button
								variant="ghost"
								size="icon"
								onClick={() =>
									updateControls({ isControlsOpen: false })
								}
								className="h-6 w-6"
							>
								<IconMinus className="h-3 w-3" />
							</Button>
						</div>

						<Separator />

						{/* Layer Controls */}
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<IconStack2 className="h-4 w-4" />
									<Label
										htmlFor="satellite"
										className="text-sm"
									>
										Satellite View
									</Label>
								</div>
								<Switch
									id="satellite"
									checked={controlsState.showSatellite}
									onCheckedChange={toggleSatelliteView}
								/>
							</div>

							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<IconNavigation className="h-4 w-4" />
									<Label
										htmlFor="traffic"
										className="text-sm"
									>
										Traffic Layer
									</Label>
								</div>
								<Switch
									id="traffic"
									checked={controlsState.showTraffic}
									onCheckedChange={toggleTraffic}
								/>
							</div>

							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<IconMaximize className="h-4 w-4" />
									<Label htmlFor="3d" className="text-sm">
										3D View
									</Label>
								</div>
								<Switch
									id="3d"
									checked={controlsState.show3D}
									onCheckedChange={toggle3D}
								/>
							</div>

							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<IconStack2 className="h-4 w-4" />
									<Label
										htmlFor="boundaries"
										className="text-sm"
									>
										Property Boundaries
									</Label>
								</div>
								<Switch
									id="boundaries"
									checked={controlsState.showBoundaries}
									onCheckedChange={(checked) =>
										updateControls({
											showBoundaries: checked,
										})
									}
								/>
							</div>
						</div>

						<Separator />

						{/* Quick Actions */}
						<div className="space-y-2">
							<Label className="text-sm font-medium">
								Quick Actions
							</Label>
							<div className="grid grid-cols-2 gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => {
										if (
											map &&
											mappableProperties.length > 0
										) {
											const bounds =
												new mapboxgl.LngLatBounds();
																					mappableProperties.forEach(
											(property) => {
												const location =
													property.location as any;
												const coordinates = location?.coordinates || location?.location?.coordinates;
												if (
													Array.isArray(coordinates) &&
													coordinates.length === 2 &&
													!isNaN(coordinates[0]) &&
													!isNaN(coordinates[1])
												) {
													const [lng, lat] = coordinates;
													bounds.extend([lng, lat]);
												}
											},
										);
											map.fitBounds(bounds, {
												padding: 50,
												maxZoom: 12,
											});
										}
									}}
									className="text-xs"
								>
									Show All
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => {
										if (map) {
											map.flyTo({
												center: [-98.5795, 39.8283],
												zoom: 4,
											});
										}
									}}
									className="text-xs"
								>
									Reset View
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => {
										updateControls({ searchValue: "" });
									}}
									className="text-xs"
								>
									Clear Search
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
