"use client";

import { useUpdateContactField } from "@app/contacts/lib/api";
import { useContacts } from "@app/contacts/lib/contacts-provider";
import {
	useInfiniteQuery as useInfiniteTanstackQuery,
	useMutation,
	useQueryClient,
} from "@tanstack/react-query";
import { useQueryStates } from "nuqs";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { ObjectType } from "@repo/database";
import { companyConfig, companyQueryOptions } from "../companies/config";
import { contactConfig, contactQueryOptions } from "../contacts/config";
import { ObjectsHeader } from "../ObjectsHeader";
import { propertiesConfig, propertyQueryOptions } from "../properties/config";
import { createGenericApiHooks } from "../shared/api-factory";
import { UniversalKanbanBoard } from "../shared/UniversalKanbanBoard";

interface KanbanViewProps {
	objectType: ObjectType;
	organizationId: string;
	view?: {
		id: string;
		name: string;
		statusAttribute?: string;
		showAttributeLabels?: boolean;
		kanbanConfig?: {
			customStatuses?: any[];
		};
		columnDefs?: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
	};
	user?: {
		name: string;
		avatarUrl?: string | null;
	};
	views?: any[];
	onViewChange?: (viewId: string) => void;
	onRenameView?: (viewId: string) => void;
	onDuplicateView?: (viewId: string) => void;
	onDeleteView?: (viewId: string) => void;
	onToggleViewFavorite?: (viewId: string) => void;
	onToggleViewPublic?: (viewId: string) => void;
	onSetViewDefault?: (viewId: string) => void;
	onViewRenamed?: (updatedView: any) => void;
	showAttributeLabels?: boolean;
}

function useObjectData(
	objectType: ObjectType,
	organizationId: string,
	view?: any,
) {
	let queryOptions, config;

	switch (objectType) {
		case "contact":
			queryOptions = contactQueryOptions;
			config = contactConfig;
			break;
		case "company":
			queryOptions = companyQueryOptions;
			config = companyConfig;
			break;
		case "property":
			queryOptions = propertyQueryOptions;
			config = propertiesConfig;
			break;
		default:
			throw new Error(`Unknown object type: ${objectType}`);
	}

	// Parse search params similar to UniversalDataTable
	const [search] = useQueryStates(config.searchParamsParser);

	// Use infinite query like UniversalDataTable
	const result = useInfiniteTanstackQuery(
		queryOptions(search, organizationId) as any,
	);

	// Flatten the data like UniversalDataTable does
	const flatData = useMemo(
		() => result.data?.pages?.flatMap((page: any) => page.data ?? []) ?? [],
		[result.data?.pages],
	);

	// Create generic API hooks for this object type
	const apiHooks = useMemo(
		() =>
			createGenericApiHooks<any>({
				objectType: config.type,
				apiEndpoint: config.apiEndpoint,
			}),
		[config.type, config.apiEndpoint],
	);

	return {
		...result,
		data: flatData,
		config,
		apiHooks,
	};
}

export function KanbanView({
	objectType,
	organizationId,
	view,
	user,
	views = [],
	onViewChange,
	onRenameView,
	onDuplicateView,
	onDeleteView,
	onToggleViewFavorite,
	onToggleViewPublic,
	onSetViewDefault,
	onViewRenamed,
	showAttributeLabels: propShowAttributeLabels,
}: KanbanViewProps) {
	const queryClient = useQueryClient();

	// Local state for show attribute labels - initialize from view object
	const [showAttributeLabels, setShowAttributeLabels] = useState(
		view?.showAttributeLabels ?? true,
	);

	// Sync local state with view object when it changes
	useEffect(() => {
		setShowAttributeLabels(view?.showAttributeLabels ?? true);
	}, [view?.showAttributeLabels]);

	// Only use contacts provider if objectType is contacts
	let contactsProvider: {
		openCreateContact: (initialValues?: Record<string, any>) => void;
	} | null = null;
	try {
		if (objectType === "contact") {
			contactsProvider = useContacts();
		}
	} catch (error) {
		// useContacts is not available - ContactsProvider not in scope
		console.warn("ContactsProvider not available for contact creation");
	}

	// Fetch data for the object type
	const {
		data: objectData,
		isLoading,
		config,
		apiHooks,
	} = useObjectData(objectType, organizationId, view);

	// Get mutations for field updates - use specific contacts API if available for better optimistic updates
	const contactsUpdateField = useUpdateContactField();
	const genericUpdateField = apiHooks.useUpdateField();

	const handleCreateNew = () => {
		// TODO: Implement object creation for other types
		console.warn(`Create new ${objectType.slice(0, -1)}`);
	};

	const handleCreateNewWithStatus = (fieldValue: string) => {
		if (objectType === "contact" && contactsProvider) {
			// Determine which field the kanban view is using
			const statusAttribute = view?.statusAttribute || "status";

			// Create initial values object with the appropriate field
			const initialValues: Record<string, any> = {
				[statusAttribute]: fieldValue,
			};

			contactsProvider.openCreateContact(initialValues);
		} else {
			// TODO: Implement creation for other object types
			console.warn(
				`Create new ${objectType.slice(0, -1)} with status: ${fieldValue}`,
			);
		}
	};

	const handleStatusUpdate = async (objectId: string, newStatus: string) => {
		const statusAttribute = view?.statusAttribute || "status";

		// Use specific contacts API for better optimistic updates
		if (objectType === "contact") {
			return contactsUpdateField.mutateAsync({
				id: objectId,
				field: statusAttribute,
				value: newStatus,
				organizationId,
			});
		} else {
			return genericUpdateField.mutateAsync({
				id: objectId,
				field: statusAttribute,
				value: newStatus,
				organizationId,
			});
		}
	};

	if (!organizationId) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Organization not found...
					</p>
				</div>
			</div>
		);
	}

	const renderHeader = () => (
		<ObjectsHeader
			objectType={objectType}
			organizationId={organizationId}
			view={view}
			views={views}
			onViewChange={onViewChange}
			onViewRenamed={onViewRenamed}
			onUpdateView={onViewRenamed}
			user={user}
			handleCreateNew={handleCreateNew}
			primaryColumn={config.primaryColumn}
			showAttributeLabels={showAttributeLabels}
			onShowAttributeLabelsChange={setShowAttributeLabels}
		/>
	);

	return (
		<div className="h-full flex flex-col">
			{renderHeader()}
			<div className="flex-1">
				<UniversalKanbanBoard
					objectType={objectType}
					organizationId={organizationId}
					data={objectData || []}
					isLoading={isLoading}
					view={view}
					onStatusUpdate={handleStatusUpdate}
					onUpdateView={onViewRenamed}
					onCreateNew={handleCreateNewWithStatus}
					showAttributeLabels={showAttributeLabels}
				/>
			</div>
		</div>
	);
}
