import MapboxDraw from "@mapbox/mapbox-gl-draw";
import { useInfiniteQuery as useInfiniteTanstackQuery } from "@tanstack/react-query";
import mapboxgl from "mapbox-gl";
import { useQueryStates } from "nuqs";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTheme } from "next-themes";
import {
	propertiesConfig,
	propertyQueryOptions,
} from "../../properties/config";
import type { Property } from "../../properties/schema";
import { createDynamicColumns } from "../../shared/DynamicColumnFactory";
import {
	createDebouncedAutocomplete,
	fetchAutocomplete,
	fetchPropertyBoundary,
	fetchPropertyInfo,
	geocodeAddress,
} from "./api";
import type {
	AutocompleteResult,
	MapControlsState,
	PropertyBoundary,
	PropertyBoundaryPayload,
	SelectedProperty,
} from "./types";

// Helper function to convert WKT POLYGON to GeoJSON
function convertWKTToGeoJSON(wkt: string): GeoJSON.Polygon | null {
	try {
		// Extract coordinates from WKT POLYGON string
		// Format: "POLYGON((-117.859608 33.655203, -117.859896 33.655606, ...))"
		const match = wkt.match(/POLYGON\(\(([^)]+)\)\)/);
		if (!match) return null;

		const coordsString = match[1];
		const coordinates = coordsString.split(", ").map((coordPair) => {
			const [lng, lat] = coordPair.trim().split(" ").map(Number);
			return [lng, lat];
		});

		// GeoJSON Polygon requires coordinates to be an array of linear rings
		// The first ring is the exterior boundary
		return {
			type: "Polygon",
			coordinates: [coordinates],
		};
	} catch (error) {
		console.error("Error converting WKT to GeoJSON:", error);
		return null;
	}
}

// MAP INITIALIZATION HOOK
export function useMapbox(
	containerRef: React.RefObject<HTMLDivElement | null>,
) {
	const { theme } = useTheme();
	const map = useRef<mapboxgl.Map | null>(null);
	const drawRef = useRef<MapboxDraw | null>(null);
	const [mapLoaded, setMapLoaded] = useState(false);

	useEffect(() => {
		if (
			containerRef.current &&
			!map.current &&
			typeof window !== "undefined"
		) {
			const initializeMap = () => {
				try {
					// Ensure container is clean
					if (containerRef.current) {
						containerRef.current.innerHTML = "";
					}

					// Set the access token
					mapboxgl.accessToken =
						process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "";

					if (!mapboxgl.accessToken) {
						console.warn(
							"Mapbox access token not found. Please set NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN environment variable.",
						);
						return;
					}

					map.current = new mapboxgl.Map({
						container: containerRef.current!,
						style: theme === "dark" ? "mapbox://styles/bobbyalv/cm0lsz7fr027p01qre07udu5u" : "mapbox://styles/mapbox/streets-v12",
						center: [-98.5795, 39.8283],
						zoom: 4,
						preserveDrawingBuffer: true,
						// Enable drag rotation and pitch control for 3D navigation
						dragRotate: true,
						dragPan: true,
						scrollZoom: true,
						doubleClickZoom: true,
						keyboard: true,
						pitchWithRotate: true,
						touchZoomRotate: true
					});

					map.current.on("load", () => {
						setMapLoaded(true);
					});

					// Add draw control
					drawRef.current = new MapboxDraw({
						displayControlsDefault: false,
						controls: {},
						defaultMode: "simple_select",
						styles: [
							// Drawing styles configuration...
							{
								id: "gl-draw-polygon-fill-inactive",
								type: "fill",
								filter: [
									"all",
									["==", "active", "false"],
									["==", "$type", "Polygon"],
									["!=", "mode", "static"],
								],
								paint: {
									"fill-color": "#3fb1ce",
									"fill-outline-color": "#3fb1ce",
									"fill-opacity": 0.1,
								},
							},
							// ... other styles
						],
					});

					map.current.addControl(drawRef.current);

					map.current.on("error", (e: any) => {
						console.error("🗺️ Map error:", e);
					});
				} catch (error) {
					console.error("Failed to initialize Mapbox:", error);
				}
			};

			initializeMap();
		}

		return () => {
			if (map.current) {
				map.current.remove();
				map.current = null;
			}
			if (drawRef.current) {
				drawRef.current = null;
			}
		};
	}, [containerRef]);

	return { map: map.current, draw: drawRef.current, mapLoaded };
}

// MAP CONTROLS HOOK
export function useMapControls() {
	const [controlsState, setControlsState] = useState<MapControlsState>({
		isControlsOpen: false,
		isDrawControlsOpen: false,
		searchValue: "",
		isSearching: false,
		autocompleteResults: [],
		showAutocomplete: false,
		showSatellite: false,
		showTraffic: false,
		show3D: false,
		isDrawMode: false,
		showBoundaries: true, // Default to true for automatic boundary display
	});

	const updateControls = useCallback((updates: Partial<MapControlsState>) => {
		setControlsState((prev) => ({ ...prev, ...updates }));
	}, []);

	return { controlsState, updateControls };
}

// PROPERTY DATA HOOK
export function usePropertyData(organizationId: string, view?: any) {
	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				"property",
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			const filteredFilterFields = propertiesConfig.filterFields.filter(
				(field) => availableColumnIds.has(field.value as string),
			);

			return {
				...propertiesConfig,
				columns: dynamicColumns,
				filterFields: filteredFilterFields,
			};
		}
		return propertiesConfig;
	}, [view?.columnDefs, view?.id, view?.updatedAt]);

	const [search] = useQueryStates(dynamicConfig.searchParamsParser);

	const filteredSearch = useMemo(() => {
		const { size, start, direction, cursor, live, id, ...cleanSearch } =
			search;
		return cleanSearch;
	}, [search]);

	const {
		data: queryData,
		isLoading,
		isFetching,
	} = useInfiniteTanstackQuery(
		propertyQueryOptions(filteredSearch as any, organizationId),
	);

	const properties = useMemo(() => {
		if (!queryData?.pages) return [];
		return queryData.pages.flatMap((page) => page.data || []) as Property[];
	}, [queryData]);

	const mappableProperties = useMemo(() => {
		return properties.filter((property) => {
			const location = property.location as any;
			const hasLocation = location && typeof location === "object";
			
			// Handle both direct coordinates and nested location structure
			const coordinates = location.coordinates || location.location?.coordinates;
			const hasCoordinates = Array.isArray(coordinates);
			const hasTwoCoords = hasCoordinates && coordinates.length === 2;
			const hasValidNumbers =
				hasTwoCoords &&
				typeof coordinates[0] === "number" &&
				typeof coordinates[1] === "number" &&
				!isNaN(coordinates[0]) &&
				!isNaN(coordinates[1]) &&
				coordinates[0] !== null &&
				coordinates[1] !== null;

			return (
				hasLocation && hasCoordinates && hasTwoCoords && hasValidNumbers
			);
		});
	}, [properties]);

	return { properties, mappableProperties, isLoading, isFetching };
}

// AUTOCOMPLETE HOOK
export function useAutocomplete() {
	const [results, setResults] = useState<AutocompleteResult[]>([]);
	const [isVisible, setIsVisible] = useState(false);

	const handleAutocomplete = useCallback(async (query: string) => {
		try {
			const data = await fetchAutocomplete({
				query,
			});
			setResults(data);
			setIsVisible(data.length > 0);
		} catch (error) {
			console.error("Autocomplete error:", error);
			setResults([]);
			setIsVisible(false);
		}
	}, []);

	const debouncedAutocomplete = useMemo(
		() => createDebouncedAutocomplete(handleAutocomplete),
		[handleAutocomplete],
	);

	const clearResults = useCallback(() => {
		setResults([]);
		setIsVisible(false);
	}, []);

	return {
		results,
		isVisible,
		setVisible: setIsVisible,
		handleAutocomplete: debouncedAutocomplete,
		clearResults,
	};
}

// PROPERTY SEARCH HOOK
export function usePropertySearch(mappableProperties: Property[]) {
	const [selectedProperty, setSelectedProperty] =
		useState<SelectedProperty | null>(null);

	const searchProperty = useCallback(
		async (searchValue: string) => {
			if (!searchValue.trim()) return;

			try {
				// Step 1: Geocode the address
				if (!mapboxgl.accessToken) {
					throw new Error("Mapbox access token not found");
				}
				const feature = await geocodeAddress(
					searchValue,
					mapboxgl.accessToken,
				);
				const coordinates = feature.center; // [lng, lat]
				const formattedAddress = feature.place_name.replace(
					/, United States/g,
					"",
				);

				// Step 2: Check if property exists in database
				const foundProperty = mappableProperties.find((property) => {
					const location = property.location as any;
					const propCoordinates = location?.coordinates || location?.location?.coordinates;
					if (
						propCoordinates &&
						Array.isArray(propCoordinates) &&
						propCoordinates.length === 2
					) {
						const [propLng, propLat] = propCoordinates;
						const [searchLng, searchLat] = coordinates;

						const lngDiff = Math.abs(propLng - searchLng);
						const latDiff = Math.abs(propLat - searchLat);
						const tolerance = 0.0005; // roughly 50 meters

						if (lngDiff < tolerance && latDiff < tolerance) {
							return true;
						}
					}

					// Text matching
					if (
						property.name
							?.toLowerCase()
							.includes(searchValue.toLowerCase())
					) {
						return true;
					}

					if (
						property.address &&
						typeof property.address === "object"
					) {
						const address = property.address as any;
						const addressText = Object.values(address)
							.join(" ")
							.toLowerCase();
						if (addressText.includes(searchValue.toLowerCase())) {
							return true;
						}
					}

					return false;
				});

				if (foundProperty) {
					setSelectedProperty({
						...foundProperty,
						source: "database",
					} as SelectedProperty);
					const location = (foundProperty.location as any);
					const foundCoordinates = location?.coordinates || location?.location?.coordinates;
					return {
						property: foundProperty,
						coordinates: foundCoordinates,
					};
				}
				// Fetch from REA API
				const result = await fetchPropertyInfo({
					address: formattedAddress,
				});

				const propertyData: SelectedProperty = {
					...({} as Property), // Base property structure
					source: "rea",
					coordinates: coordinates,
					searchAddress: formattedAddress,
					reaResponse: result,
					...(result.data && Object.keys(result.data).length > 0
						? result.data
						: {}),
					propertyInfo: {
						address: {
							address: formattedAddress,
							label: feature.place_name,
							coordinates: coordinates,
						},
						...(result.data && Object.keys(result.data).length > 0
							? result.data.propertyInfo || {}
							: {}),
					},
					statusCode: result.statusCode,
					statusMessage: result.statusMessage,
					live: result.live,
				};

				setSelectedProperty(propertyData);
				return { property: propertyData, coordinates };
			} catch (error) {
				console.error("Error searching for property:", error);
				throw error;
			}
		},
		[mappableProperties],
	);

	const clearSelection = useCallback(() => {
		setSelectedProperty(null);
	}, []);

	return {
		selectedProperty,
		searchProperty,
		clearSelection,
		setSelectedProperty,
	};
}

// MAP MARKERS HOOK
export function useMapMarkers(
	map: mapboxgl.Map | null,
	mapLoaded: boolean,
	mappableProperties: Property[],
	selectedProperty: SelectedProperty | null,
	onPropertyClick: (property: Property) => void,
) {
	useEffect(() => {
		if (!map || !mapLoaded || !mapboxgl) {
			return;
		}

		// Clear existing markers
		document
			.querySelectorAll(".mapboxgl-marker")
			.forEach((marker) => marker.remove());

		// Add markers for each property from database
		mappableProperties.forEach((property) => {
			const location = property.location as any;
			const coordinates = location?.coordinates || location?.location?.coordinates;

			if (!coordinates || coordinates.length !== 2) {
				return;
			}

			const [lng, lat] = coordinates;

			try {
				const propertyAny = property as any;
				const selectedAny = selectedProperty as any;
				const propertyId = propertyAny._id || property.id;
				const selectedId = selectedAny?._id || selectedProperty?.id;
				const markerColor =
					propertyId === selectedId ? "#FF0000" : "#3FB1CE";

				const marker = new mapboxgl.Marker({
					color: markerColor,
				})
					.setLngLat([lng, lat])
					.addTo(map);

				marker.getElement().addEventListener("click", () => {
					onPropertyClick(property);
				});
			} catch (error) {
				console.error("🗺️ Error adding marker:", error);
			}
		});

		// Add marker for selected property if it's not in mappableProperties (e.g., from search/REA)
		if (selectedProperty && selectedProperty.source !== "database") {
			const coordinates =
				(selectedProperty as any).coordinates ||
				(selectedProperty.location as any)?.coordinates;

			if (
				coordinates &&
				Array.isArray(coordinates) &&
				coordinates.length === 2
			) {
				const [lng, lat] = coordinates;

				try {
					const marker = new mapboxgl.Marker({
						color: "#FF0000", // Red for selected searched property
					})
						.setLngLat([lng, lat])
						.addTo(map);

					// Make it clickable but don't call onPropertyClick since it's already selected
					marker.getElement().style.cursor = "pointer";
				} catch (error) {
					console.error(
						"🗺️ Error adding searched property marker:",
						error,
					);
				}
			}
		}
	}, [map, mapLoaded, mappableProperties, selectedProperty, onPropertyClick]);
}

// PROPERTY BOUNDARIES HOOK
export function usePropertyBoundaries(map: mapboxgl.Map | null) {
	const [boundaries, setBoundaries] = useState<Map<string, PropertyBoundary>>(
		new Map(),
	);
	const [isLoading, setIsLoading] = useState(false);

	// Add boundary to map
	const addBoundaryToMap = useCallback(
		(boundary: PropertyBoundary) => {
			if (!map) return;

			const sourceId = `boundary-${boundary.id}`;
			const fillLayerId = `boundary-fill-${boundary.id}`;
			const lineLayerId = `boundary-line-${boundary.id}`;

			if (map.getSource(sourceId)) {
				if (map.getLayer(fillLayerId)) map.removeLayer(fillLayerId);
				if (map.getLayer(lineLayerId)) map.removeLayer(lineLayerId);
				map.removeSource(sourceId);
			}

			// Add source
			map.addSource(sourceId, {
				type: "geojson",
				data: {
					type: "Feature",
					geometry: boundary.geometry,
					properties: boundary.properties,
				},
			});

			// Add fill layer (background with opacity)
			map.addLayer({
				id: fillLayerId,
				type: "fill",
				source: sourceId,
				paint: {
					"fill-color": boundary.properties.isPinned
						? "#ffeb3b"
						: boundary.properties.isSelected
							? "#ff5722"
							: "#2196f3",
					"fill-opacity": 0.4,
				},
			});

			// Add line layer (boundary outline)
			map.addLayer({
				id: lineLayerId,
				type: "line",
				source: sourceId,
				paint: {
					"line-color": boundary.properties.isPinned
						? "#ffc107"
						: boundary.properties.isSelected
							? "#d32f2f"
							: "#1976d2",
					"line-width": 3,
					"line-opacity": 0.9,
				},
			});
		},
		[map],
	);

	// Remove boundary from map
	const removeBoundaryFromMap = useCallback(
		(boundaryId: string) => {
			if (!map) return;

			const sourceId = `boundary-${boundaryId}`;
			const fillLayerId = `boundary-fill-${boundaryId}`;
			const lineLayerId = `boundary-line-${boundaryId}`;

			if (map.getLayer(fillLayerId)) map.removeLayer(fillLayerId);
			if (map.getLayer(lineLayerId)) map.removeLayer(lineLayerId);
			if (map.getSource(sourceId)) map.removeSource(sourceId);
		},
		[map],
	);

	// Fetch and add boundary
	const fetchAndAddBoundary = useCallback(
		async (
			payload: PropertyBoundaryPayload,
			propertyId: string,
			isPinned = false,
			isSelected = false,
		) => {
			if (!map) return;

			setIsLoading(true);
			try {
				const boundaryData = await fetchPropertyBoundary(payload);
				
				if (boundaryData?.data?.geometry) {
					// Convert WKT polygon to GeoJSON
					const wktGeometry = boundaryData.data.geometry;

					const geoJsonGeometry = convertWKTToGeoJSON(wktGeometry);

					if (geoJsonGeometry) {
						const boundary: PropertyBoundary = {
							id: propertyId,
							geometry: geoJsonGeometry,
							properties: {
								propertyId,
								address: payload.address,
								isPinned,
								isSelected,
							},
						};

						setBoundaries(
							(prev) => new Map(prev.set(propertyId, boundary)),
						);
						addBoundaryToMap(boundary);
						
					} else {
					}
				} else {
				}
			} catch (error) {
				// Check if it's an authentication error
				const errorMessage = error instanceof Error ? error.message : String(error);
				if (errorMessage.includes("Authentication required") || errorMessage.includes("Unauthorized")) {
					console.warn("🔐 Boundary fetch failed due to authentication. This is expected if user session expired.");
					// Don't show error to user for auth issues, just fail silently
					// The user can still use the map without boundaries
				} else {
					// For other errors, could show a toast notification here if needed
					console.warn("⚠️ Boundary fetch failed:", errorMessage);
				}
			} finally {
				setIsLoading(false);
			}
		},
		[map, addBoundaryToMap],
	);

	// Remove boundary
	const removeBoundary = useCallback(
		(propertyId: string) => {
			setBoundaries((prev) => {
				const newMap = new Map(prev);
				newMap.delete(propertyId);
				return newMap;
			});
			removeBoundaryFromMap(propertyId);
		},
		[removeBoundaryFromMap],
	);

	// Update boundary status (pinned/selected)
	const updateBoundaryStatus = useCallback(
		(propertyId: string, isPinned?: boolean, isSelected?: boolean) => {
			setBoundaries((prev) => {
				const boundary = prev.get(propertyId);
				if (!boundary) return prev;

				const updatedBoundary = {
					...boundary,
					properties: {
						...boundary.properties,
						isPinned: isPinned ?? boundary.properties.isPinned,
						isSelected:
							isSelected ?? boundary.properties.isSelected,
					},
				};

				// Remove and re-add to update colors
				removeBoundaryFromMap(propertyId);
				addBoundaryToMap(updatedBoundary);

				const newMap = new Map(prev);
				newMap.set(propertyId, updatedBoundary);
				return newMap;
			});
		},
		[addBoundaryToMap, removeBoundaryFromMap],
	);

	// Clear all boundaries
	const clearBoundaries = useCallback(() => {
		setBoundaries((prev) => {
			prev.forEach((_, propertyId) => {
				removeBoundaryFromMap(propertyId);
			});
			return new Map();
		});
	}, [removeBoundaryFromMap]);

	return {
		boundaries,
		isLoading,
		fetchAndAddBoundary,
		removeBoundary,
		updateBoundaryStatus,
		clearBoundaries,
	};
}
