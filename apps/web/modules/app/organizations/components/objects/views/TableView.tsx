"use client";

import { useMemo } from "react";
import { ObjectType } from "@repo/database";
import { companyConfig, companyQueryOptions } from "../companies/config";
import { contactConfig, contactQueryOptions } from "../contacts/config";
import { ObjectsHeader } from "../ObjectsHeader";
import { propertiesConfig, propertyQueryOptions } from "../properties/config";
import { createDynamicColumns } from "../shared/DynamicColumnFactory";
import { UniversalDataTable } from "../shared/UniversalDataTable";

interface TableViewProps {
	objectType: ObjectType;
	organizationId: string;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
	};
	user?: {
		name: string;
		avatarUrl?: string | null;
	};
	views?: any[];
	onViewChange?: (viewId: string) => void;
	onRenameView?: (viewId: string) => void;
	onDuplicateView?: (viewId: string) => void;
	onDeleteView?: (viewId: string) => void;
	onToggleViewFavorite?: (viewId: string) => void;
	onToggleViewPublic?: (viewId: string) => void;
	onSetViewDefault?: (viewId: string) => void;
	onViewRenamed?: (updatedView: any) => void;
}

function ContactsTable({
	organizationId,
	renderHeader,
	view,
	onViewRenamed,
}: {
	organizationId: string;
	renderHeader: () => React.ReactNode;
	view?: any;
	onViewRenamed?: (updatedView: any) => void;
}) {
	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				"contact",
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			// Filter existing filterFields to only include those with corresponding columns
			const existingFilterFields = contactConfig.filterFields.filter(
				(field) => availableColumnIds.has(field.value as string),
			);

			// Create filter fields for new columns that don't have explicit filter field definitions
			const existingFilterFieldIds = new Set(
				existingFilterFields.map((field) => field.value),
			);
			const newFilterFields = (Array.from(availableColumnIds) as string[])
				.filter(
					(columnId) =>
						!existingFilterFieldIds.has(columnId) &&
						columnId !== "select" && // Exclude system columns
						columnId !== "actions",
				)
				.map((columnId) => {
					// Find the column definition to get the header name
					const columnDef = view.columnDefs.find(
						(def: any) => def.field === columnId,
					);
					const label = columnDef?.headerName || columnId;

					// Create a basic input filter field for new columns
					return {
						label,
						value: columnId,
						type: "input" as const,
					};
				});

			// Combine existing and new filter fields
			const allFilterFields = [
				...existingFilterFields,
				...newFilterFields,
			];

			return {
				...contactConfig,
				columns: dynamicColumns,
				filterFields: allFilterFields,
			};
		}
		return contactConfig;
	}, [view?.columnDefs, view?.id, view?.updatedAt]);

	return (
		<UniversalDataTable
			organizationId={organizationId}
			config={dynamicConfig}
			queryOptions={contactQueryOptions}
			renderHeader={renderHeader}
			view={view}
			onUpdateView={onViewRenamed}
		/>
	);
}

function CompaniesTable({
	organizationId,
	renderHeader,
	view,
	onViewRenamed,
}: {
	organizationId: string;
	renderHeader: () => React.ReactNode;
	view?: any;
	onViewRenamed?: (updatedView: any) => void;
}) {
	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				"company",
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			// Filter existing filterFields to only include those with corresponding columns
			const existingFilterFields = companyConfig.filterFields.filter(
				(field) => availableColumnIds.has(field.value as string),
			);

			// Create filter fields for new columns that don't have explicit filter field definitions
			const existingFilterFieldIds = new Set(
				existingFilterFields.map((field) => field.value),
			);
			const newFilterFields = (Array.from(availableColumnIds) as string[])
				.filter(
					(columnId) =>
						!existingFilterFieldIds.has(columnId) &&
						columnId !== "select" && // Exclude system columns
						columnId !== "actions",
				)
				.map((columnId) => {
					// Find the column definition to get the header name
					const columnDef = view.columnDefs.find(
						(def: any) => def.field === columnId,
					);
					const label = columnDef?.headerName || columnId;

					// Create a basic input filter field for new columns
					return {
						label,
						value: columnId,
						type: "input" as const,
					};
				});

			// Combine existing and new filter fields
			const allFilterFields = [
				...existingFilterFields,
				...newFilterFields,
			];

			return {
				...companyConfig,
				columns: dynamicColumns,
				filterFields: allFilterFields,
			};
		}
		return companyConfig;
	}, [view?.columnDefs, view?.id, view?.updatedAt]);

	return (
		<UniversalDataTable
			organizationId={organizationId}
			config={dynamicConfig}
			queryOptions={companyQueryOptions}
			renderHeader={renderHeader}
			view={view}
			onUpdateView={onViewRenamed}
		/>
	);
}

function PropertiesTable({
	organizationId,
	renderHeader,
	view,
	onViewRenamed,
}: {
	organizationId: string;
	renderHeader: () => React.ReactNode;
	view?: any;
	onViewRenamed?: (updatedView: any) => void;
}) {
	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				"property",
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			// Filter filterFields to only include those with corresponding columns
			const filteredFilterFields = propertiesConfig.filterFields.filter(
				(field) => availableColumnIds.has(field.value as string),
			);

			return {
				...propertiesConfig,
				columns: dynamicColumns,
				filterFields: filteredFilterFields,
			};
		}
		return propertiesConfig;
	}, [view?.columnDefs, view?.id, view?.updatedAt]);

	return (
		<UniversalDataTable
			organizationId={organizationId}
			config={dynamicConfig}
			queryOptions={propertyQueryOptions}
			renderHeader={renderHeader}
			view={view}
			onUpdateView={onViewRenamed}
		/>
	);
}

export function TableView({
	objectType,
	organizationId,
	view,
	user,
	views = [],
	onViewChange,
	onRenameView,
	onDuplicateView,
	onDeleteView,
	onToggleViewFavorite,
	onToggleViewPublic,
	onSetViewDefault,
	onViewRenamed,
}: TableViewProps) {
	const handleCreateNew = () => {
		// TODO: Implement object creation
		console.warn(`Create new ${objectType.slice(0, -1)}`);
	};

	if (!organizationId) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Organization not found...
					</p>
				</div>
			</div>
		);
	}

	const renderHeader = () => (
		<ObjectsHeader
			objectType={objectType}
			organizationId={organizationId}
			view={view}
			views={views}
			onViewChange={onViewChange}
			onViewRenamed={onViewRenamed}
			onUpdateView={onViewRenamed}
			user={user}
			handleCreateNew={handleCreateNew}
			primaryColumn={
				objectType === "contact"
					? contactConfig.primaryColumn
					: objectType === "company"
						? companyConfig.primaryColumn
						: propertiesConfig.primaryColumn
			}
		/>
	);

	return (
		<div className="h-full">
			{objectType === "contact" && (
				<ContactsTable
					organizationId={organizationId}
					renderHeader={renderHeader}
					view={view}
					onViewRenamed={onViewRenamed}
				/>
			)}
			{objectType === "company" && (
				<CompaniesTable
					organizationId={organizationId}
					renderHeader={renderHeader}
					view={view}
					onViewRenamed={onViewRenamed}
				/>
			)}
			{objectType === "property" && (
				<PropertiesTable
					organizationId={organizationId}
					renderHeader={renderHeader}
					view={view}
					onViewRenamed={onViewRenamed}
				/>
			)}
		</div>
	);
}
