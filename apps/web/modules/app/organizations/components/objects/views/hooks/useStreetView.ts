import { useEffect, useRef, useState } from "react";

interface UseStreetViewProps {
	selectedProperty: any | null;
}

interface StreetViewState {
	isLoading: boolean;
	hasError: boolean;
	isAvailable: boolean;
	errorMessage?: string;
}

// Add proper types for Google Maps
type GoogleMapsType = {
	maps: {
		LatLng: new (lat: number, lng: number) => any;
		StreetViewService: new () => any;
		StreetViewPanorama: new (element: HTMLElement, options: any) => any;
		StreetViewStatus: {
			OK: string;
		};
		StreetViewSource: {
			OUTDOOR: string;
		};
		geometry: {
			spherical: {
				computeHeading: (from: any, to: any) => number;
			};
		};
	};
};

export const useStreetView = ({ selectedProperty }: UseStreetViewProps) => {
	const streetViewRef = useRef<HTMLDivElement | null>(null);
	const panoramaRef = useRef<any>(null);
	const [state, setState] = useState<StreetViewState>({
		isLoading: false,
		hasError: false,
		isAvailable: false,
	});

	const getPropertyCoordinates = (property: any) => {
		if (property?.source === "database") {
			// Use flattened coordinates if available, otherwise nested location structure
			const coords = property?.coordinates || 
						  property.location?.coordinates || 
						  property.location?.location?.coordinates;
			if (Array.isArray(coords) && coords.length === 2) {
				return {
					lat: coords[1] as number,
					lng: coords[0] as number,
				};
			}
			return { lat: undefined, lng: undefined };
		}
		// For REA properties, get coordinates from the data
		const coordinates =
			property?.coordinates || property?.propertyInfo?.coordinates;
		if (coordinates && Array.isArray(coordinates) && coordinates.length === 2) {
			return {
				lat: coordinates[1] as number,
				lng: coordinates[0] as number,
			};
		}
		// Fallback to latitude/longitude fields
		return {
			lat: property?.data?.latitude || property?.propertyInfo?.latitude,
			lng: property?.data?.longitude || property?.propertyInfo?.longitude,
		};
	};

	const waitForGoogleMaps = async (): Promise<GoogleMapsType> => {
		if ((window as any).google?.maps) return (window as any).google;

		return new Promise<GoogleMapsType>((resolve) => {
			const checkGoogle = () => {
				if ((window as any).google?.maps) {
					resolve((window as any).google);
				} else {
					setTimeout(checkGoogle, 100);
				}
			};
			checkGoogle();
		});
	};

	const createPanorama = (google: GoogleMapsType) => {
		if (!streetViewRef.current) return null;

		// Clean up existing panorama
		if (panoramaRef.current) {
			panoramaRef.current.setVisible(false);
			panoramaRef.current = null;
		}

		return new google.maps.StreetViewPanorama(streetViewRef.current, {
			disableDefaultUI: true,
			addressControl: false,
			clickToGo: false,
			imageDateControl: false,
			panControl: false,
			zoomControl: false,
			enableCloseButton: false,
			motionTracking: false,
			motionTrackingControl: false,
			showRoadLabels: false,
			visible: false,
			pov: { heading: 0, pitch: 0 },
			zoom: 1,
		});
	};

	const handleStreetViewResponse = (
		google: GoogleMapsType,
		panorama: any,
		data: any,
		status: string,
		lat: number,
		lng: number,
	) => {
		if (status === google.maps.StreetViewStatus.OK && data) {
			panorama.setPano(data?.location?.pano || "");

			// Calculate the heading to face the property
			const propertyLocation = new google.maps.LatLng(lat, lng);
			const panoLocation = data.location.latLng;
			const heading = google.maps.geometry.spherical.computeHeading(
				panoLocation,
				propertyLocation,
			);

			panorama.setPov({
				heading: heading || 0,
				pitch: 0,
			});
			panorama.setVisible(true);

			setState({
				isLoading: false,
				hasError: false,
				isAvailable: true,
			});
		} else {
			setState({
				isLoading: false,
				hasError: true,
				isAvailable: false,
				errorMessage: "Street view not available for this location",
			});
		}
	};

	const initializeStreetView = async () => {
		if (!selectedProperty || !streetViewRef.current) return;

		setState((prev) => ({ ...prev, isLoading: true, hasError: false }));

		try {
			const google = await waitForGoogleMaps();

			const panorama = createPanorama(google);
			if (!panorama) return;

			panoramaRef.current = panorama;

			const { lat, lng } = getPropertyCoordinates(selectedProperty);

			if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
				setState({
					isLoading: false,
					hasError: true,
					isAvailable: false,
					errorMessage: "Invalid property coordinates",
				});
				return;
			}

			const streetViewService = new google.maps.StreetViewService();
			streetViewService.getPanorama(
				{
					location: new google.maps.LatLng(lat, lng),
					radius: 50, // Reduced radius for more accurate results
					source: google.maps.StreetViewSource.OUTDOOR,
				},
				(data: any, status: any) =>
					handleStreetViewResponse(google, panorama, data, status, lat, lng),
			);
		} catch (error) {
			console.error("Error setting up Street View:", error);
			setState({
				isLoading: false,
				hasError: true,
				isAvailable: false,
				errorMessage: "Failed to load street view",
			});
		}
	};

	useEffect(() => {
		if (selectedProperty) {
			initializeStreetView();
		} else {
			setState({
				isLoading: false,
				hasError: false,
				isAvailable: false,
			});
		}
	}, [selectedProperty]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (panoramaRef.current) {
				panoramaRef.current.setVisible(false);
				panoramaRef.current = null;
			}
		};
	}, []);

	return {
		streetViewRef,
		...state,
	};
};
