import {
	createDateRangeParam,
	createDynamicStringArrayParam,
	createNumberRangeParam,
	createSearchParamsParser,
	createStringArrayParam,
	createStringParam,
} from "../shared/search-params-factory";
import { PROPERTY_STATUSES, PROPERTY_TYPES } from "./schema";

// Property-specific search parameters
const propertySpecificParams = {
	// Property filters
	name: createStringParam(),
	address: createStringParam(),
	city: createStringParam(),
	state: createStringParam(),
	propertyType: createStringArrayParam(PROPERTY_TYPES.map(type => type.value)),
	status: createStringArrayParam(PROPERTY_STATUSES.map(status => status.value)),
	price: createNumberRangeParam(),
	bedrooms: createNumberRangeParam(),
	bathrooms: createNumberRangeParam(),
	squareFeet: createNumberRangeParam(),
	tags: createDynamicStringArrayParam(),
};

// Create the complete search params parser for properties
export const propertySearchParams = createSearchParamsParser(
	propertySpecificParams,
);

// Export the types and utilities
export const searchParamsParser = propertySearchParams.parser;
export const searchParamsCache = propertySearchParams.cache;
export const searchParamsSerializer = propertySearchParams.serializer;
export type PropertySearchParamsType = typeof propertySearchParams.type;
