import type { ObjectConfig } from "../../../../object-views/lib/types";
import { propertyFilterFields, propertySheetFields } from "../constants";
import { createGenericApiHandlers } from "../shared/api-factory";
import { createInfiniteQueryOptions } from "../shared/query-options-factory";
import { columns } from "./columns";
import type { Property } from "./schema";
import { PropertySchema } from "./schema";
import { searchParamsParser, searchParamsSerializer } from "./search-params";

export interface PropertyMeta {
	objectType: "property";
	totalCount: number;
	hasNextPage: boolean;
}

export const propertiesConfig: ObjectConfig<Property, PropertyMeta> = {
	type: "property",
	schema: PropertySchema,
	columns,
	filterFields: propertyFilterFields,
	sheetFields: propertySheetFields,
	searchParamsParser: searchParamsParser,
	apiEndpoint: "/api/objects/properties",
	primaryColumn: "name",
	apiHandlers: createGenericApiHandlers<Property>({
		objectType: "property",
		apiEndpoint: "/api/objects/properties",
	}),
};

export const propertyQueryOptions = createInfiniteQueryOptions<
	Property,
	PropertyMeta
>({
	objectType: "property",
	searchParamsSerializer,
	apiEndpoint: "/api/objects/properties/infinite",
	queryKeyPrefix: "properties-infinite",
});
