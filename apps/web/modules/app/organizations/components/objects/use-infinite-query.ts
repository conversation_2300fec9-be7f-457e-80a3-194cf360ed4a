import { useInfiniteQuery as useInfiniteTanstackQuery } from "@tanstack/react-query";
import { useQueryStates } from "nuqs";
import {
	companiesDataOptions,
	contactsDataOptions,
	propertiesDataOptions,
} from "./query-options";
import { searchParamsParser } from "./search-params";
import { ObjectType } from "@repo/database";

export function useInfiniteContactsQuery(orgId: string) {
	const [search] = useQueryStates(searchParamsParser);
	const query = useInfiniteTanstackQuery(contactsDataOptions(search, orgId));
	return query;
}

export function useInfiniteCompaniesQuery(orgId: string) {
	const [search] = useQueryStates(searchParamsParser);
	const query = useInfiniteTanstackQuery(companiesDataOptions(search, orgId));
	return query;
}

export function useInfinitePropertiesQuery(orgId: string) {
	const [search] = useQueryStates(searchParamsParser);
	const query = useInfiniteTanstackQuery(
		propertiesDataOptions(search, orgId),
	);
	return query;
}

export function useInfiniteObjectsQuery(
	objectType: ObjectType,
	orgId: string,
) {
	const [search] = useQueryStates(searchParamsParser);

	const queryOptions = (() => {
		switch (objectType) {
			case "contact":
				return contactsDataOptions(search, orgId);
			case "company":
				return companiesDataOptions(search, orgId);
			case "property":
				return propertiesDataOptions(search, orgId);
			default:
				throw new Error(`Unsupported object type: ${objectType}`);
		}
	})();

	const query = useInfiniteTanstackQuery(queryOptions);
	return query;
}
