import { fetchFavorites, useToggleFavorite } from "@app/favorites/lib/api";
import {
	useDeleteObjectView,
	useDuplicateObjectView,
	useRemoveUserDefaultView,
	useSetUserDefaultView,
	useUserDefaultView,
} from "@app/object-views/lib/api";
import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import AlertDialog from "@app/shared/components/AlertDialog";
import { EllipsisDropdown } from "@shared/components/EllipsisDropdown";
import {
	IconCheck,
	IconChevronDown,
	IconCopy,
	IconDatabaseHeart,
	IconEdit,
	IconHomeHeart,
	IconLayoutKanban,
	IconLock,
	IconMapPin2,
	IconPlus,
	IconSquareRoundedCheckFilled,
	IconStar,
	IconStarOff,
	IconTable,
	IconTrash,
	IconWorld,
	IconWorldOff,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { type FC, useState } from "react";
import { toast } from "sonner";
import { RenameViewModal } from "./RenameViewModal";
import { ObjectType } from "@repo/database";

/**
 * ObjectViewsDropdown Component
 *
 * This component allows users to:
 * - Select different views for object types (contacts, companies, properties)
 * - Set personal default views that are specific to each user
 * - Manage view settings (rename, duplicate, delete)
 * - Create new views
 *
 * User Default View Feature:
 * - Each user can set their own default view per object type
 * - User defaults take precedence over organization defaults
 * - Shows "Your default" badge for user-specific defaults
 * - Provides "Set as default" / "Remove as default" options in the dropdown
 */
interface ObjectViewsDropdownProps {
	view: any;
	views: any[];
	onViewChange: (viewId: string) => void;
	onRename?: (viewId: string) => void;
	onDuplicate?: (viewId: string) => void;
	onDelete?: (viewId: string) => void;
	onCreateNew?: () => void;
	onViewRenamed?: (updatedView: any) => void;
	onViewsChanged?: (views: any[]) => void;
	className?: string;
	objectType?: ObjectType;
}

export const ObjectViewsDropdown: FC<ObjectViewsDropdownProps> = ({
	view,
	views,
	onViewChange,
	onRename,
	onDuplicate,
	onDelete,
	onCreateNew,
	onViewRenamed,
	onViewsChanged,
	className,
	objectType,
}) => {
	const [open, setOpen] = useState(false);
	const [renameModalOpen, setRenameModalOpen] = useState(false);
	const [viewToRename, setViewToRename] = useState<
		{ id: string; name: string } | undefined
	>();
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [viewToDelete, setViewToDelete] = useState<
		{ id: string; name: string } | undefined
	>();
	const { activeOrganization } = useActiveOrganization();

	// Get current table filter state (only if we're in a DataTable context)
	let currentTableFilters: Array<{ id: string; value: any }> = [];
	try {
		const { table } = useDataTable();
		currentTableFilters = table?.getState().columnFilters || [];
	} catch {
		// Not in DataTable context, use empty filters
		currentTableFilters = [];
	}

	// User default view hooks
	const { data: userDefaultView } = useUserDefaultView(
		objectType as ObjectType,
		activeOrganization?.id,
	);
	const setUserDefaultMutation = useSetUserDefaultView();
	const removeUserDefaultMutation = useRemoveUserDefaultView();

	// Duplicate view hook
	const duplicateViewMutation = useDuplicateObjectView();

	// Delete view hook
	const deleteViewMutation = useDeleteObjectView();

	// Favorites API hook
	const toggleFavorite = useToggleFavorite(activeOrganization?.id);

	// Fetch favorites data to check if views are favorited
	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", activeOrganization?.id],
		queryFn: () =>
			activeOrganization?.id
				? fetchFavorites(activeOrganization.id)
				: Promise.resolve([]),
		enabled: !!activeOrganization?.id,
	});

	const handleSetAsDefault = async (viewId: string) => {
		try {
			await setUserDefaultMutation.mutateAsync({
				objectType: objectType as ObjectType,
				viewId,
				organizationId: activeOrganization?.id,
			});
			toast.success("View set as default");

			// Update local views to ensure only one default exists
			if (onViewsChanged) {
				const updatedViews = views.map((v) => ({
					...v,
					isDefault: v.id === viewId, // Only the selected view is default, all others are false
				}));
				onViewsChanged(updatedViews);
			}
		} catch (error) {
			toast.error("Failed to set view as default");
		}
	};

	const handleRemoveDefault = async () => {
		try {
			await removeUserDefaultMutation.mutateAsync({
				objectType: objectType as ObjectType,
				organizationId: activeOrganization?.id,
			});
			toast.success("Default view removed");

			// Update local views to remove default status from the user's previous default
			if (onViewsChanged && userDefaultView?.id) {
				const updatedViews = views.map((v) => ({
					...v,
					isDefault:
						v.id === userDefaultView.id ? false : v.isDefault,
				}));
				onViewsChanged(updatedViews);
			}
		} catch (error) {
			toast.error("Failed to remove default view");
		}
	};

	const handleToggleFavorite = (viewId: string) => {
		if (!activeOrganization?.id) return;

		toggleFavorite.mutate({
			objectId: viewId,
			objectType: "view",
			organizationId: activeOrganization.id,
		});
	};

	const handleDuplicateView = (viewId: string) => {
		duplicateViewMutation.mutate(
			{ viewId, currentFilters: currentTableFilters },
			{
				onSuccess: () => {
					const hasFilters = currentTableFilters.length > 0;
					const message = hasFilters
						? "View duplicated successfully with current filters"
						: "View duplicated successfully";
					toast.success(message);
					setOpen(false);
				},
				onError: (error) => {
					toast.error(error.message || "Failed to duplicate view");
				},
			},
		);
	};

	const handleDeleteView = (viewId: string) => {
		const selectedView = views.find((v) => v.id === viewId);
		if (selectedView) {
			setViewToDelete({ id: selectedView.id, name: selectedView.name });
			setDeleteDialogOpen(true);
			setOpen(false); // Close the dropdown
		}
	};

	const handleConfirmDelete = () => {
		if (!viewToDelete) return;

		deleteViewMutation.mutate(viewToDelete.id, {
			onSuccess: () => {
				toast.success(
					`View "${viewToDelete.name}" deleted successfully`,
				);
				setDeleteDialogOpen(false);
				setViewToDelete(undefined);

				// If we deleted the current view, trigger view change to default
				if (view?.id === viewToDelete.id) {
					const defaultView = views.find((v) => v.isDefault);
					if (defaultView) {
						onViewChange(defaultView.id);
					}
				}
			},
			onError: (error) => {
				toast.error(error.message || "Failed to delete view");
			},
		});
	};

	const isUserDefault = (viewId: string) => {
		return userDefaultView?.id === viewId;
	};

	const isFavorited = (viewId: string) => {
		return favorites.some(
			(fav) => fav.objectId === viewId && fav.objectType === "view",
		);
	};

	const handleRenameView = (viewId: string) => {
		const selectedView = views.find((v) => v.id === viewId);
		if (selectedView) {
			setViewToRename({ id: selectedView.id, name: selectedView.name });
			setRenameModalOpen(true);
		}
	};

	const handleViewRenamed = (updatedView: any) => {
		// Call the parent callback if provided
		if (onViewRenamed) {
			onViewRenamed(updatedView);
		}
	};

	return (
		<div className="flex items-center gap-2">
			{/* View Selection Combobox */}
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<Button
						variant="relio"
						role="combobox"
						aria-expanded={open}
						className="justify-between w-fit"
						size="sm"
					>
						<div className="flex items-center gap-2 text-sm">
							<div
								className={`flex items-center rounded-md p-1 ${
									view?.viewType === "kanban"
										? "bg-orange-500"
										: view?.viewType === "map"
											? "bg-red-500"
											: "bg-emerald-500"
								}`}
							>
								{view?.viewType === "kanban" ? (
									<IconLayoutKanban className="h-3 w-3 text-white" />
								) : view?.viewType === "map" ? (
									<IconMapPin2 className="h-3 w-3 text-white" />
								) : (
									<IconTable className="h-3 w-3 text-white" />
								)}
							</div>
							<span>{view?.name || "Select a view"}</span>
							{view?.isDefault && (
								<IconDatabaseHeart className="w-4 h-4 text-muted-foreground" />
							)}
							{!view?.isPublic && (
								<IconLock className="w-4 h-4 text-muted-foreground" />
							)}
							{view && isUserDefault(view.id) && (
								<IconDatabaseHeart className="w-4 h-4 text-muted-foreground" />
							)}
						</div>
						<IconChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="min-w-[200px] p-0" align="start">
					<Command>
						<CommandInput placeholder="Search views..." />
						<CommandList>
							<CommandEmpty>No views found.</CommandEmpty>
							<CommandGroup>
								{views.map((v: any) => (
									<div
										key={v.id}
										className="flex items-center group"
									>
										<CommandItem
											value={v.name}
											onSelect={() => {
												onViewChange(v.id);
												setOpen(false);
											}}
											className="flex-1 flex items-center justify-between cursor-pointer border-none"
										>
											<div className="flex items-center gap-2 flex-1">
												<div
													className={`flex items-center rounded-md p-0.5 ${
														v.viewType === "kanban"
															? "bg-orange-500"
															: v.viewType ===
																	"map"
																? "bg-red-500"
																: "bg-emerald-500"
													}`}
												>
													{v.viewType === "kanban" ? (
														<IconLayoutKanban className="text-white" />
													) : v.viewType === "map" ? (
														<IconMapPin2 className="text-white" />
													) : (
														<IconTable className="text-white" />
													)}
												</div>
												<span
													className={cn(
														view?.id === v.id
															? "text-blue-500"
															: "",
													)}
												>
													{v.name}
												</span>
												{v.isDefault && (
													<IconDatabaseHeart className="w-4 h-4 text-muted-foreground" />
												)}
												{!v.isPublic && (
													<IconLock className="w-4 h-4 text-muted-foreground" />
												)}
												{isUserDefault(v.id) && (
													<IconDatabaseHeart className="h-4 w-4 text-muted-foreground" />
												)}
											</div>
											<div
												onClick={(e) =>
													e.stopPropagation()
												}
											>
												<EllipsisDropdown
													className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6"
													side="right"
													align="start"
												>
													{isFavorited(v.id) ? (
														<EllipsisDropdown.Item
															onClick={(e) => {
																e.stopPropagation();
																handleToggleFavorite(
																	v.id,
																);
															}}
															className="flex items-center gap-2"
														>
															<IconStarOff className="w-4 h-4" />
															Remove from
															favorites
														</EllipsisDropdown.Item>
													) : (
														<EllipsisDropdown.Item
															onClick={(e) => {
																e.stopPropagation();
																handleToggleFavorite(
																	v.id,
																);
															}}
															className="flex items-center gap-2"
														>
															<IconStar className="w-4 h-4" />
															Add to favorites
														</EllipsisDropdown.Item>
													)}

													<EllipsisDropdown.Item
														onClick={(e) => {
															e.stopPropagation();
															handleRenameView(
																v.id,
															);
														}}
														className="flex items-center gap-2"
													>
														<IconEdit className="w-4 h-4" />
														Rename view
													</EllipsisDropdown.Item>

													<EllipsisDropdown.Item
														onClick={(e) => {
															e.stopPropagation();
															handleDuplicateView(
																v.id,
															);
														}}
														className="flex items-center gap-2"
													>
														<IconCopy className="w-4 h-4" />
														Duplicate
													</EllipsisDropdown.Item>

													<Separator className="my-1" />

													{!v.isDefault && (
														<EllipsisDropdown.Item
															onClick={(e) => {
																e.stopPropagation();
																handleDeleteView(
																	v.id,
																);
															}}
															className="flex items-center gap-2 text-red-500 hover:!text-red-600"
														>
															<IconTrash className="w-4 h-4" />
															Delete
														</EllipsisDropdown.Item>
													)}

													{/* Set as default option */}
													{isUserDefault(v.id) ? (
														<EllipsisDropdown.Item
															onClick={(e) => {
																e.stopPropagation();
																handleRemoveDefault();
															}}
															className="flex items-center gap-2"
														>
															<IconHomeHeart className="w-4 h-4" />
															Remove as default
														</EllipsisDropdown.Item>
													) : (
														<EllipsisDropdown.Item
															onClick={(e) => {
																e.stopPropagation();
																handleSetAsDefault(
																	v.id,
																);
															}}
															className="flex items-center gap-2"
														>
															<IconHomeHeart className="w-4 h-4" />
															Set as default
														</EllipsisDropdown.Item>
													)}
												</EllipsisDropdown>
											</div>
										</CommandItem>
									</div>
								))}

								<Separator className="my-1" />

								{/* Create new view option */}
								<CommandItem
									onSelect={() => {
										onCreateNew?.();
										setOpen(false);
									}}
									className="cursor-pointer"
								>
									<div className="flex items-center gap-2">
										<IconPlus className="w-4 h-4" />
										<span>Create new view</span>
									</div>
								</CommandItem>
							</CommandGroup>
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>

			{/* Rename View Modal */}
			<RenameViewModal
				open={renameModalOpen}
				onOpenChange={(open) => {
					setRenameModalOpen(open);
					if (!open) {
						setViewToRename(undefined);
					}
				}}
				view={viewToRename}
				organizationId={activeOrganization?.id}
				objectType={objectType}
				onViewRenamed={handleViewRenamed}
			/>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={deleteDialogOpen}
				onOpenChange={(open) => {
					setDeleteDialogOpen(open);
					if (!open) {
						setViewToDelete(undefined);
					}
				}}
				title="Delete View"
				description={`Are you sure you want to delete "${viewToDelete?.name}"? This action cannot be undone.`}
				confirmLabel="Delete"
				cancelLabel="Cancel"
				confirmClassName="bg-red-500 hover:bg-red-600 text-white"
				onConfirm={handleConfirmDelete}
				loading={deleteViewMutation.isPending}
			/>
		</div>
	);
};
