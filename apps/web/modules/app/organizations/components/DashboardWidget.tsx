"use client";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { cn } from "@ui/lib";
import React, { useCallback, useEffect, useRef, useState } from "react";
import type { WidgetConfig } from "../hooks/use-dashboard-layout";

interface DashboardWidgetProps {
	widget: WidgetConfig;
	children: React.ReactNode;
	editable?: boolean;
	onResize?: (newSize: { width: number; height: number }) => void;
}

export function DashboardWidget({
	widget,
	children,
	editable = false,
	onResize,
}: DashboardWidgetProps) {
	const [isResizing, setIsResizing] = useState(false);
	const [resizeDirection, setResizeDirection] = useState<string>("");
	const widgetRef = useRef<HTMLDivElement>(null);
	const initialMousePos = useRef({ x: 0, y: 0 });
	const initialSize = useRef({ width: 0, height: 0 });

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: widget.id,
		data: {
			type: "widget",
			widget,
		},
		disabled: !editable || isResizing,
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition: isResizing ? "none" : transition,
		gridColumn: `span ${widget.size.width}`,
		gridRow: `span ${widget.size.height}`,
	};

	const handleResizeStart = useCallback(
		(e: React.MouseEvent, direction: string) => {
			e.preventDefault();
			e.stopPropagation();

			setIsResizing(true);
			setResizeDirection(direction);
			initialMousePos.current = { x: e.clientX, y: e.clientY };
			initialSize.current = { ...widget.size };
		},
		[widget.size],
	);

	const handleResizeMove = useCallback(
		(e: MouseEvent) => {
			if (!isResizing || !onResize || !resizeDirection) return;

			const deltaX = e.clientX - initialMousePos.current.x;
			const deltaY = e.clientY - initialMousePos.current.y;

			// More sensitive grid unit calculation (every 80px = 1 grid unit)
			const gridUnitX = Math.round(deltaX / 80);
			const gridUnitY = Math.round(deltaY / 80);

			let newWidth = initialSize.current.width;
			let newHeight = initialSize.current.height;

			if (resizeDirection.includes("right")) {
				newWidth = Math.max(
					widget.minSize.width,
					Math.min(
						widget.maxSize.width,
						initialSize.current.width + gridUnitX,
					),
				);
			}
			if (resizeDirection.includes("left")) {
				newWidth = Math.max(
					widget.minSize.width,
					Math.min(
						widget.maxSize.width,
						initialSize.current.width - gridUnitX,
					),
				);
			}
			if (resizeDirection.includes("bottom")) {
				newHeight = Math.max(
					widget.minSize.height,
					Math.min(
						widget.maxSize.height,
						initialSize.current.height + gridUnitY,
					),
				);
			}
			if (resizeDirection.includes("top")) {
				newHeight = Math.max(
					widget.minSize.height,
					Math.min(
						widget.maxSize.height,
						initialSize.current.height - gridUnitY,
					),
				);
			}

			if (
				newWidth !== widget.size.width ||
				newHeight !== widget.size.height
			) {
				onResize({ width: newWidth, height: newHeight });
			}
		},
		[
			isResizing,
			resizeDirection,
			onResize,
			widget.size,
			widget.minSize,
			widget.maxSize,
		],
	);

	const handleResizeEnd = useCallback(() => {
		setIsResizing(false);
		setResizeDirection("");
	}, []);

	// Add and remove event listeners
	useEffect(() => {
		if (isResizing) {
			document.addEventListener("mousemove", handleResizeMove);
			document.addEventListener("mouseup", handleResizeEnd);

			return () => {
				document.removeEventListener("mousemove", handleResizeMove);
				document.removeEventListener("mouseup", handleResizeEnd);
			};
		}
	}, [isResizing, handleResizeMove, handleResizeEnd]);

	const ResizeHandle = ({
		direction,
		className,
	}: {
		direction: string;
		className: string;
	}) => (
		<div
			className={cn(
				"absolute bg-transparent transition-all duration-200 z-20",
				"hover:bg-blue-500/30 group-hover:opacity-100 rounded-xl",
				isResizing && resizeDirection === direction
					? "bg-blue-500/50 rounded-xl"
					: "opacity-0",
				className,
			)}
			onMouseDown={(e) => handleResizeStart(e, direction)}
			style={{ cursor: getCursor(direction) }}
		/>
	);

	const getCursor = (direction: string) => {
		if (direction.includes("top") && direction.includes("left"))
			return "nw-resize";
		if (direction.includes("top") && direction.includes("right"))
			return "ne-resize";
		if (direction.includes("bottom") && direction.includes("left"))
			return "sw-resize";
		if (direction.includes("bottom") && direction.includes("right"))
			return "se-resize";
		if (direction.includes("top") || direction.includes("bottom"))
			return "ns-resize";
		if (direction.includes("left") || direction.includes("right"))
			return "ew-resize";
		return "default";
	};

	return (
		<div
			ref={(node) => {
				setNodeRef(node);
				if (widgetRef.current !== node) {
					widgetRef.current = node;
				}
			}}
			style={style}
			className={cn(
				"relative transition-all duration-200 group",
				isDragging && "opacity-50 scale-105 z-50",
				editable &&
					"hover:border-blue-300 dark:hover:border-blue-600 rounded-xl cursor-grab active:cursor-grabbing",
				isResizing &&
					"border-blue-500 dark:border-blue-400 border-2 rounded-xl",
			)}
			{...(editable ? attributes : {})}
			{...(editable ? listeners : {})}
		>
			{/* Corner and edge resize handles - made larger for better interaction */}
			{editable && (
				<>
					{/* Corner handles - 8x8px */}
					<ResizeHandle
						direction="top-left"
						className="top-0 left-0 w-2 h-2"
					/>
					<ResizeHandle
						direction="top-right"
						className="top-0 right-0 w-2 h-2"
					/>
					<ResizeHandle
						direction="bottom-right"
						className="bottom-0 right-0 w-2 h-2"
					/>
					<ResizeHandle
						direction="bottom-left"
						className="bottom-0 left-0 w-2 h-2"
					/>

					{/* Edge handles - made thicker for easier interaction */}
					<ResizeHandle
						direction="top"
						className="top-0 left-2 right-2 h-1"
					/>
					<ResizeHandle
						direction="right"
						className="top-2 bottom-2 right-0 w-1"
					/>
					<ResizeHandle
						direction="bottom"
						className="bottom-0 left-2 right-2 h-1"
					/>
					<ResizeHandle
						direction="left"
						className="top-2 bottom-2 left-0 w-1"
					/>
				</>
			)}

			<div className="h-full pointer-events-auto">{children}</div>

			{/* Visual feedback during resize */}
			{isResizing && (
				<div className="absolute inset-0 pointer-events-none border-2 border-blue-500 bg-blue-500/10 rounded-xl" />
			)}
		</div>
	);
}
