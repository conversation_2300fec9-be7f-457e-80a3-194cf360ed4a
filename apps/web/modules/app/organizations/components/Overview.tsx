import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Chart<PERSON>ontainer,
	ChartTooltip,
	ChartTooltipContent,
} from "@ui/components/chart";
import {
	eachDayOfInterval,
	eachMonthOfInterval,
	endOfMonth,
	endOfWeek,
	endOfYear,
	format,
	startOfMonth,
	startOfWeek,
	startOfYear,
} from "date-fns";
import * as React from "react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";

const chartConfig: any = {
	calls: {
		label: "Calls",
		color: "hsl(var(--chart-1))",
	},
	notes: {
		label: "Notes",
		color: "hsl(var(--chart-2))",
	},
	tasks: {
		label: "Tasks",
		color: "hsl(var(--chart-3))",
	},
};

interface ChartDataItem {
	date: string;
	calls: number;
	notes: number;
	tasks: number;
}

const Overview = ({
	callData,
	noteData,
	taskData,
	selectedFilter,
	timeSeriesData,
}: any) => {
	const [activeChart, setActiveChart] = React.useState("calls");
	const [chartData, setChartData] = React.useState<ChartDataItem[]>([]);

	function generateChartData(
		selectedFilter: string,
		callData: number,
		noteData: number,
		taskData: number,
		timeSeriesData?: any[],
	) {
		// If we have real time series data from the API, use it
		if (timeSeriesData && timeSeriesData.length > 0) {
			return timeSeriesData.map((item) => ({
				date: item.date,
				calls: item.calls || 0,
				notes: item.notes || 0,
				tasks: item.tasks || 0,
			}));
		}

		// Fallback to artificial distribution for backward compatibility
		const today = new Date();
		let chartData: ChartDataItem[] = [];

		// Ensure data values are valid numbers
		const safeCallData = Math.max(0, callData || 0);
		const safeNoteData = Math.max(0, noteData || 0);
		const safeTaskData = Math.max(0, taskData || 0);

		switch (selectedFilter) {
			case "Today":
			case "Yesterday":
				chartData.push({
					date: selectedFilter,
					calls: safeCallData,
					notes: safeNoteData,
					tasks: safeTaskData,
				});
				break;
			case "This Week": {
				const weekStart = startOfWeek(today, { weekStartsOn: 0 });
				const weekEnd = endOfWeek(today, { weekStartsOn: 0 });
				const weekDays = eachDayOfInterval({
					start: weekStart,
					end: weekEnd,
				});

				chartData = distributeWeeklyData(
					weekDays,
					safeCallData,
					safeNoteData,
					safeTaskData,
					today,
				);
				break;
			}
			case "This Month": {
				const monthStart = startOfMonth(today);
				const monthEnd = endOfMonth(today);
				const monthDays = eachDayOfInterval({
					start: monthStart,
					end: monthEnd,
				});

				chartData = distributeMonthlyData(
					monthDays,
					safeCallData,
					safeNoteData,
					safeTaskData,
					today,
				);
				break;
			}
			case "This Year": {
				const yearStart = startOfYear(today);
				const yearEnd = endOfYear(today);
				const yearMonths = eachMonthOfInterval({
					start: yearStart,
					end: yearEnd,
				});

				chartData = distributeYearlyData(
					yearMonths,
					safeCallData,
					safeNoteData,
					safeTaskData,
					today,
				);
				break;
			}
		}

		return chartData;
	}

	function distributeWeeklyData(
		weekDays: Date[],
		callData: number,
		noteData: number,
		taskData: number,
		today: Date,
	): ChartDataItem[] {
		const currentDayIndex = weekDays.findIndex(
			(date) =>
				date.getDate() === today.getDate() &&
				date.getMonth() === today.getMonth(),
		);

		// Create different distribution patterns for each metric
		const callsPattern = [0.05, 0.15, 0.25, 0.35, 0.15, 0.03, 0.02]; // Peak mid-week
		const notesPattern = [0.1, 0.2, 0.3, 0.2, 0.15, 0.03, 0.02]; // More consistent
		const tasksPattern = [0.2, 0.25, 0.2, 0.15, 0.15, 0.03, 0.02]; // Front-loaded

		return weekDays.map((date, index) => {
			if (index <= currentDayIndex) {
				const calls = Math.round(callData * callsPattern[index] || 0);
				const notes = Math.round(noteData * notesPattern[index] || 0);
				const tasks = Math.round(taskData * tasksPattern[index] || 0);

				return {
					date: formatDate(date, weekDays),
					calls,
					notes,
					tasks,
				};
			}
			return {
				date: formatDate(date, weekDays),
				calls: 0,
				notes: 0,
				tasks: 0,
			};
		});
	}

	function distributeMonthlyData(
		monthDays: Date[],
		callData: number,
		noteData: number,
		taskData: number,
		today: Date,
	): ChartDataItem[] {
		const currentDayIndex = monthDays.findIndex(
			(date) => date.getDate() === today.getDate(),
		);

		// Handle edge cases
		if (currentDayIndex === -1) {
			return monthDays.map((date) => ({
				date: formatDate(date, monthDays),
				calls: 0,
				notes: 0,
				tasks: 0,
			}));
		}

		const totalDays = currentDayIndex + 1;

		// For small datasets, distribute values more strategically to avoid all zeros
		const distributeSmallValues = (total: number, days: number) => {
			if (total === 0) return new Array(days).fill(0);

			const result = new Array(days).fill(0);

			if (total <= days) {
				// For very small totals, place them deterministically with emphasis on recent days
				// Use a pattern that distributes values towards the end (more recent activity)
				const step = Math.max(1, Math.floor(days / total));
				const currentIndex = Math.max(0, days - total * step);

				for (let i = 0; i < total; i++) {
					const index = Math.min(currentIndex + i * step, days - 1);
					result[index]++;
				}
			} else {
				// For larger totals, distribute with variation ensuring total is preserved
				let remaining = total;

				for (let i = 0; i < days; i++) {
					const progress = days === 1 ? 0.5 : i / (days - 1);
					const variation = Math.sin(progress * Math.PI) * 0.5 + 0.7;
					const baseValue = total / days;
					const value = Math.round(baseValue * variation);
					const actualValue = Math.min(value, remaining);
					result[i] = Math.max(0, actualValue);
					remaining -= result[i];
				}

				// Distribute any remaining values deterministically
				let index = days - 1;
				while (remaining > 0 && index >= 0) {
					result[index]++;
					remaining--;
					index--;
				}
			}

			return result;
		};

		const callsDistribution = distributeSmallValues(callData, totalDays);
		const notesDistribution = distributeSmallValues(noteData, totalDays);
		const tasksDistribution = distributeSmallValues(taskData, totalDays);

		return monthDays.map((date, index) => {
			if (index <= currentDayIndex) {
				return {
					date: formatDate(date, monthDays),
					calls: callsDistribution[index] || 0,
					notes: notesDistribution[index] || 0,
					tasks: tasksDistribution[index] || 0,
				};
			}
			return {
				date: formatDate(date, monthDays),
				calls: 0,
				notes: 0,
				tasks: 0,
			};
		});
	}

	function distributeYearlyData(
		yearMonths: Date[],
		callData: number,
		noteData: number,
		taskData: number,
		today: Date,
	): ChartDataItem[] {
		const currentMonthIndex = yearMonths.findIndex(
			(date) => date.getMonth() === today.getMonth(),
		);

		// Handle edge cases
		if (currentMonthIndex === -1) {
			return yearMonths.map((date) => ({
				date: formatDate(date, yearMonths),
				calls: 0,
				notes: 0,
				tasks: 0,
			}));
		}

		const totalMonths = currentMonthIndex + 1;

		// Use the same smart distribution for yearly data
		const distributeSmallValues = (total: number, months: number) => {
			if (total === 0) return new Array(months).fill(0);

			const result = new Array(months).fill(0);

			if (total <= months) {
				// For very small totals, distribute towards recent months
				const step = Math.max(1, Math.floor(months / total));
				const currentIndex = Math.max(0, months - total * step);

				for (let i = 0; i < total; i++) {
					const index = Math.min(currentIndex + i * step, months - 1);
					result[index]++;
				}
			} else {
				// For larger totals, distribute with variation
				let remaining = total;

				for (let i = 0; i < months; i++) {
					const progress = months === 1 ? 0.5 : i / (months - 1);
					const variation =
						Math.sin(progress * Math.PI * 0.8) * 0.4 + 0.8;
					const baseValue = total / months;
					const value = Math.round(baseValue * variation);
					const actualValue = Math.min(value, remaining);
					result[i] = Math.max(0, actualValue);
					remaining -= result[i];
				}

				// Distribute remaining values
				let index = months - 1;
				while (remaining > 0 && index >= 0) {
					result[index]++;
					remaining--;
					index--;
				}
			}

			return result;
		};

		const callsDistribution = distributeSmallValues(callData, totalMonths);
		const notesDistribution = distributeSmallValues(noteData, totalMonths);
		const tasksDistribution = distributeSmallValues(taskData, totalMonths);

		return yearMonths.map((date, index) => {
			if (index <= currentMonthIndex) {
				return {
					date: formatDate(date, yearMonths),
					calls: callsDistribution[index] || 0,
					notes: notesDistribution[index] || 0,
					tasks: tasksDistribution[index] || 0,
				};
			}
			return {
				date: formatDate(date, yearMonths),
				calls: 0,
				notes: 0,
				tasks: 0,
			};
		});
	}

	function formatDate(date: Date, intervalArray: Date[]): string {
		if (intervalArray.length <= 7) {
			return format(date, "EEE");
		}
		if (intervalArray.length <= 31) {
			return format(date, "d");
		}
		return format(date, "MMM");
	}

	React.useEffect(() => {
		const newChartData = generateChartData(
			selectedFilter,
			callData,
			noteData,
			taskData,
			timeSeriesData,
		);
		setChartData(newChartData);
	}, [selectedFilter, callData, noteData, taskData, timeSeriesData]);

	const total = React.useMemo(
		() => ({
			calls: callData,
			notes: noteData,
			tasks: taskData,
		}),
		[callData, noteData, taskData],
	);

	return (
		<Card className="@container/card h-full rounded-2xl border border-border bg-neutral-100 dark:bg-sidebar shadow-sm overflow-hidden">
			<CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row">
				<div className="flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6">
					<CardTitle>Analytics Overview</CardTitle>
					<CardDescription>
						Showing the total number of calls, notes, and tasks for{" "}
						{selectedFilter.toLowerCase()}.
					</CardDescription>
				</div>
				<div className="flex">
					{["calls", "notes", "tasks"].map((key) => {
						const chart = key;
						return (
							<button
								key={chart}
								data-active={activeChart === chart}
								className="relative flex flex-1 flex-col justify-center items-center gap-1 border-t px-4 py-4 text-left even:border-l data-[active=true]:bg-muted/50 sm:border-l sm:border-t-0 sm:px-6 sm:py-6 transition-colors hover:bg-muted/30"
								onClick={() => setActiveChart(chart)}
							>
								<span className="text-xs text-muted-foreground">
									{chartConfig[chart].label}
								</span>
								<span className="text-lg font-bold leading-none sm:text-2xl code-font">
									{total[
										key as keyof typeof total
									].toLocaleString()}
								</span>
								{activeChart === chart && (
									<div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />
								)}
							</button>
						);
					})}
				</div>
			</CardHeader>
			<CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
				<ChartContainer
					config={chartConfig}
					className="aspect-auto h-[250px] w-full"
				>
					<AreaChart
						data={chartData}
						margin={{
							left: 12,
							right: 12,
						}}
					>
						<defs>
							<linearGradient
								id="fillCalls"
								x1="0"
								y1="0"
								x2="0"
								y2="1"
							>
								<stop
									offset="5%"
									stopColor="#2b7fff"
									stopOpacity={0.8}
								/>
								<stop
									offset="95%"
									stopColor="#2b7fff"
									stopOpacity={0.1}
								/>
							</linearGradient>
							<linearGradient
								id="fillNotes"
								x1="0"
								y1="0"
								x2="0"
								y2="1"
							>
								<stop
									offset="5%"
									stopColor="#2b7fff"
									stopOpacity={0.8}
								/>
								<stop
									offset="95%"
									stopColor="#2b7fff"
									stopOpacity={0.1}
								/>
							</linearGradient>
							<linearGradient
								id="fillTasks"
								x1="0"
								y1="0"
								x2="0"
								y2="1"
							>
								<stop
									offset="5%"
									stopColor="#2b7fff"
									stopOpacity={0.8}
								/>
								<stop
									offset="95%"
									stopColor="#2b7fff"
									stopOpacity={0.1}
								/>
							</linearGradient>
						</defs>
						<CartesianGrid vertical={false} />
						<XAxis
							dataKey="date"
							tickLine={false}
							axisLine={false}
							tickMargin={8}
							minTickGap={32}
							tick={{ fontSize: 12 }}
							tickFormatter={(value) => {
								// Simple tick formatting for our date strings
								return value;
							}}
						/>
						<YAxis
							tickLine={false}
							axisLine={false}
							tickMargin={8}
							tickCount={7}
							tick={{ fontSize: 12 }}
							tickFormatter={(value) => {
								if (value >= 100) {
									return `${(value / 10000).toFixed(1)}M`;
								}
								if (value >= 1000) {
									return `${(value / 100).toFixed(1)}K`;
								}
								return value.toString();
							}}
						/>
						<ChartTooltip
							cursor={false}
							content={
								<ChartTooltipContent
									labelFormatter={(value) => value}
									indicator="dot"
									hideLabel={false}
								/>
							}
						/>
						<Area
							dataKey={activeChart}
							type="natural"
							fill={`url(#fill${activeChart.charAt(0).toUpperCase() + activeChart.slice(1)})`}
							fillOpacity={1}
							stroke={`hsl(var(--chart-${activeChart === "calls" ? "1" : activeChart === "notes" ? "2" : "3"}))`}
							strokeWidth={2}
							dot={false}
						/>
					</AreaChart>
				</ChartContainer>
			</CardContent>
		</Card>
	);
};

export default Overview;
