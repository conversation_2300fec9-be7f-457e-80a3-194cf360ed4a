import type { FilterFn } from "@tanstack/react-table";
import { isAfter, isBefore, isSameDay } from "date-fns";
import { isArrayOfDates } from "./is-array";

interface Tag {
	id: string;
	name: string;
	color: string;
}

export const inDateRange: FilterFn<any> = (row, columnId, value) => {
	const date = new Date(row.getValue(columnId));
	const [start, end] = value as Date[];

	if (isNaN(date.getTime())) {
		return false;
	}

	// if no end date, check if it's the same day
	if (!end) {
		return isSameDay(date, start);
	}

	return isAfter(date, start) && isBefore(date, end);
};

inDateRange.autoRemove = (val: any) =>
	!Array.isArray(val) || !val.length || !isArrayOfDates(val);

export const arrSome: FilterFn<any> = (row, columnId, filterValue) => {
	// Return true if no filter value is provided
	if (!filterValue || !filterValue.value) return true;

	const rowValue = row.getValue(columnId);
	if (!rowValue) return false;

	// Handle array of tag objects
	if (Array.isArray(rowValue) && rowValue.length > 0 && typeof rowValue[0] === 'object') {
		const rowTags = (rowValue as Tag[]).map(tag => tag.name?.toLowerCase());
		const filterTags = Array.isArray(filterValue.value) 
			? filterValue.value.map((v: string) => v.toLowerCase())
			: [String(filterValue.value).toLowerCase()];

		switch (filterValue.operator) {
			case "is":
			case "is any of":
				return filterTags.some((tag: string) => rowTags.includes(tag));
			case "is not":
				return !filterTags.some((tag: string) => rowTags.includes(tag));
			default:
				return true;
		}
	}

	// Handle array of strings
	if (Array.isArray(rowValue)) {
		const rowValues = rowValue.map((v: string) => v.toLowerCase());
		const filterValues = Array.isArray(filterValue.value) 
			? filterValue.value.map((v: string) => v.toLowerCase())
			: [String(filterValue.value).toLowerCase()];

		switch (filterValue.operator) {
			case "is":
			case "is any of":
				return filterValues.some((val: string) => rowValues.includes(val));
			case "is not":
				return !filterValues.some((val: string) => rowValues.includes(val));
			default:
				return true;
		}
	}

	// Handle single value
	const rowVal = String(rowValue).toLowerCase();
	const filterValues = Array.isArray(filterValue.value)
		? filterValue.value.map((v: string) => v.toLowerCase())
		: [String(filterValue.value).toLowerCase()];

	switch (filterValue.operator) {
		case "is":
		case "is any of":
			return filterValues.includes(rowVal);
		case "is not":
			return !filterValues.includes(rowVal);
		default:
			return true;
	}
};

arrSome.autoRemove = (val: any) => {
	if (!val || !val.value) return true;
	if (Array.isArray(val.value)) return val.value.length === 0;
	return false;
};

function testFalsey(val: any) {
	return val === undefined || val === null || val === "";
}
