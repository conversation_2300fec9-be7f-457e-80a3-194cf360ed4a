"use client";

import { useEffect, useState } from "react";

const STORAGE_KEY = "dashboard-layout";
const LAYOUT_VERSION_KEY = "dashboard-layout-version";
const CURRENT_VERSION = "1.2"; // Increment when adding new widgets

export interface WidgetConfig {
	id: string;
	type:
		| "tasks-summary"
		| "call-results"
		| "overview"
		| "recently-viewed"
		| "tasks-progress"
		| "ai-analytics";
	title: string;
	position: { x: number; y: number };
	size: { width: number; height: number };
	minSize: { width: number; height: number };
	maxSize: { width: number; height: number };
	visible: boolean;
}

export function useDashboardLayout(defaultLayout: WidgetConfig[]) {
	const [layout, setLayout] = useState<WidgetConfig[]>(defaultLayout);

	useEffect(() => {
		const saved = localStorage.getItem(STORAGE_KEY);
		const savedVersion = localStorage.getItem(LAYOUT_VERSION_KEY);

		if (saved && savedVersion === CURRENT_VERSION) {
			try {
				const parsedLayout = JSON.parse(saved);
				setLayout(parsedLayout);
			} catch (error) {
				console.error("Failed to parse saved layout:", error);
				setLayout(defaultLayout);
			}
		} else if (saved) {
			try {
				const parsedLayout = JSON.parse(saved);
				// Merge new widgets from default layout
				const mergedLayout = mergeLayouts(parsedLayout, defaultLayout);
				setLayout(mergedLayout);
				// Save the merged layout with current version
				localStorage.setItem(STORAGE_KEY, JSON.stringify(mergedLayout));
				localStorage.setItem(LAYOUT_VERSION_KEY, CURRENT_VERSION);
			} catch (error) {
				console.error("Failed to parse saved layout:", error);
				setLayout(defaultLayout);
				localStorage.setItem(
					STORAGE_KEY,
					JSON.stringify(defaultLayout),
				);
				localStorage.setItem(LAYOUT_VERSION_KEY, CURRENT_VERSION);
			}
		} else {
			// No saved layout, use default and save it
			localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultLayout));
			localStorage.setItem(LAYOUT_VERSION_KEY, CURRENT_VERSION);
		}
	}, [defaultLayout]);

	const mergeLayouts = (
		savedLayout: WidgetConfig[],
		defaultLayout: WidgetConfig[],
	): WidgetConfig[] => {
		const savedIds = new Set(savedLayout.map((w) => w.id));
		const newWidgets = defaultLayout.filter((w) => !savedIds.has(w.id));

		// Add new widgets to the saved layout
		return [...savedLayout, ...newWidgets];
	};

	const updateLayout = (newLayout: WidgetConfig[]) => {
		setLayout(newLayout);
		localStorage.setItem(STORAGE_KEY, JSON.stringify(newLayout));
		localStorage.setItem(LAYOUT_VERSION_KEY, CURRENT_VERSION);
	};

	const resetLayout = () => {
		setLayout(defaultLayout);
		localStorage.removeItem(STORAGE_KEY);
		localStorage.removeItem(LAYOUT_VERSION_KEY);
	};

	return {
		layout,
		updateLayout,
		resetLayout,
	};
}
