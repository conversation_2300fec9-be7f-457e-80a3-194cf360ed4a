import { apiClient } from "@shared/lib/api-client";
import { headers } from "next/headers";
import { cache } from "react";

export const getPurchases = cache(async (organizationId?: string) => {
	const response = await apiClient.payments.purchases.$get(
		{
			query: {
				organizationId,
			},
		},
		{
			headers: Object.fromEntries((await headers()).entries()),
		},
	);

	if (!response.ok) {
		throw new Error("Failed to fetch purchases");
	}

	return response.json();
});
