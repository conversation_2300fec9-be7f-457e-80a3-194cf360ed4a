import { useEmailCompose } from "../components/EmailComposeProvider";

/**
 * Convenience hook for using global email compose functionality
 * from anywhere in the app
 */
export function useGlobalEmailCompose() {
	const { openCompose, ...rest } = useEmailCompose();

	/**
	 * Open email compose modal with optional prefilled data
	 */
	const composeEmail = (toEmail?: string, subject?: string) => {
		openCompose(toEmail, subject);
	};

	/**
	 * Open email compose modal with contact's email
	 */
	const composeToContact = (contactEmail: string, contactName?: string) => {
		const subject = contactName ? `Re: ${contactName}` : undefined;
		openCompose(contactEmail, subject);
	};

	return {
		...rest,
		composeEmail,
		composeToContact,
	};
} 