export interface PlacesAutocompleteParams {
  input: string;
  components?: string;
  types?: string;
  language?: string;
}

export interface PlaceDetailsParams {
  placeId: string;
  fields: string[];
  language?: string;
}

export interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface PlacesPrediction {
  placeId: string;
  description: string;
  mainText: string;
  secondaryText: string;
  types: string[];
}

export interface PlaceDetails {
  address_components: AddressComponent[];
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
}

export async function getPlacesAutocomplete(params: PlacesAutocompleteParams) {
  try {
    if (!params.input || params.input.length < 3) {
      return { predictions: [] };
    }

    const response = await fetch('/api/geocode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        action: 'autocomplete',
        ...params,
      }),
    });

    if (!response.ok) {
      throw new Error(`Places Autocomplete API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Transform Google Places API response to our format
    const predictions: PlacesPrediction[] = 
      data.predictions?.map((prediction: any) => ({
        placeId: prediction.place_id,
        description: prediction.description,
        mainText: prediction.structured_formatting?.main_text || prediction.description,
        secondaryText: prediction.structured_formatting?.secondary_text || '',
        types: prediction.types || [],
      })) || [];

    return { predictions };
  } catch (error) {
    console.error('Places Autocomplete error:', error);
    return { 
      error: error instanceof Error ? error.message : 'Internal server error', 
      predictions: [] 
    };
  }
}

export async function getPlaceDetails(params: PlaceDetailsParams) {
  try {
    if (!params.placeId) {
      throw new Error('Place ID is required');
    }

    const response = await fetch('/api/geocode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        action: 'details',
        ...params,
      }),
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // No place details found
      }
      throw new Error(`Place Details API error: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.error) {
      throw new Error(result.error);
    }

    return {
      result: result.result as PlaceDetails
    };
  } catch (error) {
    console.error('Place Details error:', error);
    return null;
  }
} 