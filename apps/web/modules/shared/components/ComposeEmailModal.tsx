"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useConnectedAccounts } from "@shared/hooks/useConnectedAccounts";
import { useSendEmail, sendEmailSchema, type SendEmailFormData, type SendEmailRequest, useUsersByEmails, type UserByEmailResult } from "@shared/hooks/useEmailSending";
import { useEmailCompose, type EmailDraft } from "./EmailComposeProvider";
import { UserChip } from "./UserAvatar";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { useSearch } from "@app/search";
import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@ui/components/command";
import { IconMail, IconSettings, IconMinus, IconMaximize, IconMailPlus, IconX, IconUser, IconBuilding, IconHome, IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import Icon from "@ui/icons/google-color";
import { Separator } from "@ui/components/separator";

interface ComposeEmailModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	toEmail?: string; // Pre-fill the to field
	toName?: string; // Pre-fill the recipient name
	toAvatarUrl?: string; // Pre-fill the recipient avatar
	toType?: string; // Pre-fill the recipient type
	toAllEmails?: string[]; // All available emails for the recipient
	subject?: string; // Pre-fill the subject
}

interface RecipientManagerProps {
	recipients: Array<{
		email: string;
		name: string;
		avatarUrl?: string;
		type?: string;
		allEmails?: string[];
	}>;
	onRecipientsChange: (recipients: Array<{
		email: string;
		name: string;
		avatarUrl?: string;
		type?: string;
		allEmails?: string[];
	}>) => void;
	placeholder?: string;
}

function RecipientManager({ recipients, onRecipientsChange, placeholder = "Enter email address..." }: RecipientManagerProps) {
	const [inputValue, setInputValue] = useState("");
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [selectedRecipient, setSelectedRecipient] = useState<string | null>(null);
	const { activeOrganization } = useActiveOrganization();

	// Use the same search hook as CommandMenu
	const shouldSearch = inputValue.length > 1 && !!activeOrganization?.id;
	const {
		data: searchResults,
		isLoading: searchLoading,
		error: searchError,
	} = useSearch(
		{
			query: inputValue,
			organizationId: activeOrganization?.id || "",
			type: "all",
			limit: 20,
		},
		shouldSearch,
	);

	// Helper function to extract all emails from a search result
	const getAllEmails = (result: any): string[] => {
		const emails: string[] = [];
		
		// Primary email
		const primaryEmail = getEmailFromResult(result);
		if (primaryEmail) emails.push(primaryEmail);

		// Additional emails for contacts
		if (result.type === "contact" || result.type === "contacts") {
			if (result.workEmail && !emails.includes(result.workEmail)) {
				emails.push(result.workEmail);
			}
			if (result.personalEmail && !emails.includes(result.personalEmail)) {
				emails.push(result.personalEmail);
			}
			if (result.otherEmail && !emails.includes(result.otherEmail)) {
				emails.push(result.otherEmail);
			}
		}

		return emails;
	};

	// Helper function to extract email from different object types
	const getEmailFromResult = (result: any): string | null => {
		// Direct email field
		if (result.email) return result.email;
		
		// For companies, might have a contact email or info email
		if (result.type === "company" || result.type === "companies") {
			return result.contactEmail || result.email || null;
		}
		
		// For properties, might have a listing email
		if (result.type === "property" || result.type === "properties") {
			return result.listingEmail || result.contactEmail || result.email || null;
		}
		
		return null;
	};

	// Helper function to get display name
	const getDisplayName = (result: any): string => {
		if (result.name) return result.name;
		if (result.title) return result.title;
		if (result.firstName && result.lastName) return `${result.firstName} ${result.lastName}`;
		if (result.firstName) return result.firstName;
		if (result.company) return result.company;
		return result.email || "Unknown";
	};

	// Helper function to get type icon and color
	const getTypeInfo = (type: string) => {
		switch (type) {
			case "contact":
			case "contacts":
				return { 
					icon: IconUser, 
					color: "text-blue-500", 
					bgColor: "bg-blue-500",
					label: "Contact"
				};
			case "company":
			case "companies":
				return { 
					icon: IconBuilding, 
					color: "text-purple-500", 
					bgColor: "bg-purple-500",
					label: "Company"
				};
			case "property":
			case "properties":
				return { 
					icon: IconHome, 
					color: "text-green-500", 
					bgColor: "bg-green-500",
					label: "Property"
				};
			case "user":
				return { 
					icon: IconUser, 
					color: "text-orange-500", 
					bgColor: "bg-orange-500",
					label: "User"
				};
			default:
				return { 
					icon: IconUser, 
					color: "text-gray-500", 
					bgColor: "bg-gray-500",
					label: "Contact"
				};
		}
	};

	const addRecipient = (email: string, name?: string, avatarUrl?: string, type?: string, allEmails?: string[]) => {
		const recipientName = name || email;
		// Ensure the primary email is included in allEmails
		const emails = allEmails || [];
		if (!emails.includes(email)) {
			emails.unshift(email); // Add primary email at the start if not present
		}
		
		const newRecipient = { 
			email, 
			name: recipientName, 
			avatarUrl, 
			type, 
			allEmails: emails 
		};
		
		if (email && !recipients.some(r => r.email === email)) {
			onRecipientsChange([...recipients, newRecipient]);
			setInputValue("");
			setIsDropdownOpen(false);
		}
	};

	const removeRecipient = (email: string) => {
		onRecipientsChange(recipients.filter(r => r.email !== email));
		setSelectedRecipient(null);
	};

	const handleInputChange = (value: string) => {
		setInputValue(value);
		if (value.length > 0) {
			setIsDropdownOpen(true);
		} else {
			setIsDropdownOpen(false);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && inputValue) {
			e.preventDefault();
			// When adding via keyboard, just use the email as both email and allEmails
			addRecipient(inputValue, undefined, undefined, undefined, [inputValue]);
		}
	};

	const SkeletonItem = () => (
		<div className="flex items-center space-x-2 p-2">
			<div className="w-6 h-6 bg-muted rounded-full animate-pulse" />
			<div className="flex-1 space-y-1">
				<div className="h-4 bg-muted rounded animate-pulse" />
				<div className="h-3 bg-muted/60 rounded animate-pulse w-2/3" />
			</div>
		</div>
	);

	return (
		<div className="space-y-2 flex-1">
			{/* Recipient chips */}
			<div className="flex flex-row items-center">
			{recipients.length > 0 && (
				<div className="flex flex-wrap gap-1">
					{recipients.map((recipient) => (
						<Popover key={recipient.email} open={selectedRecipient === recipient.email} onOpenChange={(open) => setSelectedRecipient(open ? recipient.email : null)}>
							<PopoverTrigger asChild>
								<div>
									<UserChip
										name={recipient.name}
										avatarUrl={recipient.avatarUrl}
										size="22"
										variant="default"
										interactive={true}
										showSuffixIcon={true}
										suffixIcon={<IconX className="w-3 h-3" />}
										onSuffixClick={() => removeRecipient(recipient.email)}
										type={recipient.type}
									/>
								</div>
							</PopoverTrigger>
							<PopoverContent className="w-64 p-0" align="start">
								<div className="p-1">
									{/* Email options */}
									<div>
										{(recipient.allEmails || [recipient.email]).map((email) => (
											<Button
												key={email}
												variant="ghost"
												size="sm"
												className={cn(
													"h-8 gap-2 flex justify-between border border-transparent hover:!border hover:!border-border hover:!bg-muted/50 cursor-pointer relative select-none items-center rounded-lg text-sm outline-hidden transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50 w-full",
												)}
												onClick={() => {
													// Replace current email with selected one
													const updatedRecipient = {
														...recipient,
														email: email
													};
													const otherRecipients = recipients.filter(r => r.email !== recipient.email);
													onRecipientsChange([...otherRecipients, updatedRecipient]);
												}}
											>
												<span className="text-xs">{email}</span>
												{email === recipient.email && (
													<IconSquareRoundedCheckFilled className="w-4 h-4 text-blue-500" />
												)}
											</Button>
										))}
									</div>

										<Separator className="my-1" />
									{/* Remove action */}
									<Button
										variant="ghost"
										size="sm"
										className="gap-2 flex justify-start border border-transparent hover:!border hover:!border-border hover:!bg-muted/50 h-8 cursor-pointer relative select-none items-center rounded-lg px-2 py-1.5 text-sm outline-hidden transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50 w-full text-red-500 hover:text-red-600"
										onClick={() => removeRecipient(recipient.email)}
									>
										<IconX className="w-4 h-4" />
										Remove recipient
									</Button>
								</div>
							</PopoverContent>
						</Popover>
					))}
				</div>
			)}

			<div className="relative">
				<Command shouldFilter={false}>
					<CommandInput
						placeholder={placeholder}
						value={inputValue}
						onValueChange={handleInputChange}
						onKeyDown={handleKeyDown}
						divClassName="!border-none -ml-2"
						icon={false}
					/>
					{isDropdownOpen && (
						<CommandList className="absolute top-full left-0 right-0 z-50 bg-popover border border-border rounded-md shadow-lg max-h-60 overflow-auto">
							{searchLoading ? (
								<CommandGroup>
									{[...Array(3)].map((_, i) => (
										<div key={i}>
											<SkeletonItem />
										</div>
									))}
								</CommandGroup>
							) : (
								<>
									{/* Direct email entry */}
									{inputValue && inputValue.includes('@') && (
										<CommandGroup heading="Email address">
											<CommandItem onSelect={() => addRecipient(inputValue)}>
												<div className="flex items-center space-x-2">
													<div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center text-xs">
														@
													</div>
													<span>{inputValue}</span>
												</div>
											</CommandItem>
										</CommandGroup>
									)}

									{/* Search results */}
									{searchResults && searchResults.results.length > 0 && (
										<CommandGroup heading={`Results (${searchResults.results.length})`}>
											{searchResults.results
												.filter(result => {
													// Only show results that have an email
													const email = getEmailFromResult(result);
													return email && !recipients.some(r => r.email === email);
												})
												.map((result) => {
													const email = getEmailFromResult(result);
													const displayName = getDisplayName(result);
													const typeInfo = getTypeInfo(result.type);
													
													if (!email) return null;

													return (
														<CommandItem
															key={`${result.type}-${result.id}`}
															onSelect={() => addRecipient(email, displayName, result.avatarUrl, result.type, getAllEmails(result))}
														>
															<div className="flex items-center justify-between w-full">
																<div className="flex items-center space-x-2">
																	{result.type === "contact" || result.type === "user" ? (
																		<ContactAvatar
																			name={displayName}
																			avatarUrl={result.avatarUrl}
																			className="w-6 h-6"
																		/>
																	) : (
																		<div className={cn(
																			"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium",
																			typeInfo.bgColor
																		)}>
																			<typeInfo.icon className="w-3 h-3" />
																		</div>
																	)}
																	<div>
																		<div className="font-medium text-sm">{displayName}</div>
																		<div className="text-xs text-muted-foreground">{email}</div>
																	</div>
																</div>
																<Badge className="flex items-center gap-1 text-xs !px-2 !py-0.5">
																	<typeInfo.icon className="h-3 w-3" />
																	{typeInfo.label}
																</Badge>
															</div>
														</CommandItem>
													);
												})}
										</CommandGroup>
									)}

									{/* No results */}
									{!searchLoading && 
										(!searchResults || searchResults.results.filter(result => getEmailFromResult(result)).length === 0) && 
										inputValue.length > 1 && (
										<CommandEmpty>
											{inputValue.includes('@') 
												? "Press Enter to add this email address"
												: "No contacts found with email addresses"
											}
										</CommandEmpty>
									)}
								</>
							)}
						</CommandList>
					)}
				</Command>
			</div>

			</div>
		</div>
	);
}

// Use the form data type from the email sending hook

export function ComposeEmailModal({
	open,
	onOpenChange,
	toEmail,
	toName,
	toAvatarUrl,
	toType,
	toAllEmails,
	subject,
}: ComposeEmailModalProps) {
	const router = useRouter();
	const { activeOrganization } = useActiveOrganization();
	
	// Use global email compose context
	const { 
		savedDraft, 
		saveDraft, 
		clearDraft, 
		minimizeCompose 
	} = useEmailCompose();

	// Use the hooks
	const { data: connectedAccountsData, isLoading: accountsLoading, error: accountsError } = useConnectedAccounts();
	const sendEmailMutation = useSendEmail();

	const connectedAccounts = connectedAccountsData?.accounts || [];
	// Filter accounts that have refresh tokens (can actually send emails)
	const usableAccounts = connectedAccounts.filter(account => account.refreshToken);
	const defaultAccount = usableAccounts[0]; // Use first usable account as default

	const form = useForm<SendEmailFormData>({
		resolver: zodResolver(sendEmailSchema),
		defaultValues: {
			organizationId: activeOrganization?.id || "",
			fromAccountId: defaultAccount?.id || "",
			to: toEmail || "",
			cc: "",
			bcc: "",
			subject: subject || "",
			body: "",
		},
	});

	// State for managing recipients
	const [toRecipients, setToRecipients] = useState<Array<{
		email: string;
		name: string;
		avatarUrl?: string;
		type?: string;
		allEmails?: string[];
	}>>([]);

	// Extract emails from saved draft for user data fetching
	const draftEmails = React.useMemo(() => {
		if (!savedDraft?.to) return [];
		const emails = typeof savedDraft.to === 'string' ? savedDraft.to.split(',') : [savedDraft.to];
		return emails.map(email => email.trim()).filter(Boolean);
	}, [savedDraft]);

	// Fetch user data for draft emails
	const { data: draftUserData, isLoading: isLoadingDraftUsers } = useUsersByEmails(
		draftEmails,
		activeOrganization?.id || "",
		Boolean(open && savedDraft && draftEmails.length > 0)
	);

	// Update form when accounts load or props change
	useEffect(() => {
		if (open) {
			// If we have a saved draft, restore it
			if (savedDraft) {
				const draftCC = Array.isArray(savedDraft.cc) ? savedDraft.cc.join(', ') : savedDraft.cc;
				const draftBCC = Array.isArray(savedDraft.bcc) ? savedDraft.bcc.join(', ') : savedDraft.bcc;
				
				form.reset({
					organizationId: activeOrganization?.id || "",
					fromAccountId: savedDraft.fromAccountId,
					to: savedDraft.to,
					cc: draftCC,
					bcc: draftBCC,
					subject: savedDraft.subject,
					body: savedDraft.body,
				});
				// Parse recipients from saved draft - use fetched user data if available
				if (savedDraft.to) {
					const emails = typeof savedDraft.to === 'string' ? savedDraft.to.split(',') : [savedDraft.to];
					const recipients = emails.map(email => {
						const trimmedEmail = email.trim();
						// Look for user data for this email
						const userData = draftUserData?.find(user => user.email === trimmedEmail);
						
						return {
							email: trimmedEmail,
							name: userData?.name || trimmedEmail,
							avatarUrl: userData?.avatarUrl || undefined,
							type: userData?.type,
							allEmails: userData?.allEmails || [trimmedEmail],
						};
					});
					setToRecipients(recipients);
				}
			} else {
				form.reset({
					organizationId: activeOrganization?.id || "",
					fromAccountId: defaultAccount?.id || "",
					to: toEmail || "",
					cc: "",
					bcc: "",
					subject: subject || "",
					body: "",
				});
				// Set initial recipients
				if (toEmail) {
					setToRecipients([{
						email: toEmail,
						name: toName || toEmail,
						avatarUrl: toAvatarUrl,
						type: toType,
						allEmails: toAllEmails || [toEmail],
					}]);
				}
			}
		}
	}, [open, toEmail, toName, toAvatarUrl, toType, toAllEmails, subject, form, activeOrganization?.id, defaultAccount?.id, savedDraft, draftUserData]);

	// Update form when recipients change - set a single email or empty string for validation
	useEffect(() => {
		form.setValue('to', toRecipients.length > 0 ? toRecipients[0].email : '');
	}, [toRecipients, form]);

	// Check if form has content for draft saving
	const hasContent = useCallback(() => {
		const values = form.getValues();
		return Boolean(
			values.to || 
			values.cc || 
			values.bcc || 
			values.subject || 
			values.body
		);
	}, [form]);

	// Save draft when modal closes with content
	const handleModalClose = useCallback((newOpen: boolean) => {
		if (!newOpen && (hasContent() || toRecipients.length > 0)) {
			const values = form.getValues();
			const draft: EmailDraft = {
				to: toRecipients.map(r => r.email).join(', '), // Save all recipients for draft restoration
				cc: values.cc || "",
				bcc: values.bcc || "",
				subject: values.subject,
				body: values.body,
				fromAccountId: values.fromAccountId,
			};
			saveDraft(draft);
		} else if (!newOpen && !hasContent() && toRecipients.length === 0) {
			// Clear any existing draft if no content
			clearDraft();
		}
		onOpenChange(newOpen);
	}, [hasContent, form, onOpenChange, saveDraft, clearDraft, toRecipients]);

	// Fetch watermark settings
	const { data: watermarkSettings } = useQuery({
		queryKey: ['watermark-settings', activeOrganization?.id],
		queryFn: async () => {
			const response = await fetch(`/api/organizations/watermark/${activeOrganization?.id}`);
			if (!response.ok) throw new Error('Failed to fetch watermark settings');
			return response.json();
		},
		enabled: !!activeOrganization?.id,
	});

	const onSubmit = async (data: SendEmailFormData) => {
		if (!data.fromAccountId || !activeOrganization?.id) {
			toast.error("Please select an email account");
			return;
		}

		// Check if we have recipients
		if (toRecipients.length === 0) {
			toast.error("Please add at least one recipient");
			return;
		}

		// Add watermark if enabled
		let emailBody = data.body;
		if (watermarkSettings?.enabled) {
			const watermark = "\n\n---\nSent with Relio";
			emailBody = emailBody + watermark;
		}

		// Prepare email data with proper recipient handling
		// First recipient goes to 'to', additional ones go to 'cc'
		const primaryRecipient = toRecipients[0].email;
		const additionalRecipients = toRecipients.slice(1).map(r => r.email);
		
		// Convert recipients to arrays
		const ccArray = [...(data.cc ? data.cc.split(',') : []), ...additionalRecipients]
			.map(email => email.trim())
			.filter(Boolean);
		
		const bccArray = data.bcc ? data.bcc.split(',').map(email => email.trim()).filter(Boolean) : [];

		const emailData: SendEmailRequest = {
			organizationId: data.organizationId,
			fromAccountId: data.fromAccountId,
			to: primaryRecipient,
			cc: ccArray,
			bcc: bccArray,
			subject: data.subject,
			body: emailBody,
		};

		try {
			await sendEmailMutation.mutateAsync(emailData);
			// Clear the draft after successful send
			clearDraft();
			onOpenChange(false);
		} catch (error) {
			// Error is handled by the mutation
		}
	};

	const handleConfigureMailbox = () => {
		onOpenChange(false);
		router.push(`/app/${activeOrganization?.slug}/settings/account/email-calendar`);
	};

	// Custom controls for minimize button
	const customControls = (
		<Button
			variant="ghost"
			size="sm"
			onClick={() => {
				// Save draft before minimizing if there's content
				if (hasContent() || toRecipients.length > 0) {
					const values = form.getValues();
					const draft = {
						to: toRecipients.map(r => r.email).join(', '), // Save all recipients for draft restoration
						cc: values.cc || "",
						bcc: values.bcc || "",
						subject: values.subject,
						body: values.body,
						fromAccountId: values.fromAccountId,
					};
					saveDraft(draft);
				}
				// Close the modal via props
				onOpenChange(false);
				// Set minimized state in global context
				minimizeCompose();
			}}
			className="h-auto p-1"
		>
			<IconMinus className="h-4 w-4" />
		</Button>
	);

	// Show loading state
	if (accountsLoading || (savedDraft && isLoadingDraftUsers)) {
		return (
			<StandardizedModal
				open={open}
				onOpenChange={handleModalClose}
				title="Loading..."
				icon={<IconMail className="h-5 w-5" />}
				position="bottom-right"
				maxWidth="lg"
				overlay={false}
			>
				<div className="flex flex-col items-center justify-center py-8 space-y-4">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
					<p className="text-muted-foreground">Loading email accounts...</p>
				</div>
			</StandardizedModal>
		);
	}

	// Show no mailbox configured state or accounts need reconnection
	if (accountsError || connectedAccounts.length === 0 || usableAccounts.length === 0) {
		const needsReconnection = connectedAccounts.length > 0 && usableAccounts.length === 0;
		return (
			<StandardizedModal
				overlay={false}
				open={open}
				onOpenChange={handleModalClose}
				title="Compose email"
				icon={<IconMailPlus className="h-5 w-5" />}
				position="bottom-right"
				maxWidth="lg"
				customControls={customControls}
				footer={
					<StandardizedModalFooter>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => handleModalClose(false)}
						>
							Cancel
						</Button>
						<Button
							type="button"
							variant="primary"
							size="sm"
							onClick={handleConfigureMailbox}
						>
							{needsReconnection ? "Reconnect email accounts" : "Connect email account"}
						</Button>
					</StandardizedModalFooter>
				}
			>
				<div className="flex flex-col items-center justify-center py-8 space-y-6">
					{/* Email illustration */}
					<div className="flex items-center space-x-2">
						{/* Left hexagon */}
						<div className="w-16 h-16 flex items-center justify-center">
							<svg className="w-12 h-12 text-muted-foreground/40" fill="currentColor" viewBox="0 0 24 24">
								<path d="M17.5 3.5L22 12l-4.5 8.5h-11L2 12l4.5-8.5h11z" />
							</svg>
						</div>
						
						{/* Dashed lines */}
						<div className="w-8 h-0.5 border-t-2 border-dashed border-muted-foreground/30"></div>
						
						{/* Center email icon with connection indicator */}
						<div className="w-16 h-16 flex items-center justify-center relative">
							<div className="w-12 h-12 rounded-lg border-2 border-red-500/30 bg-red-500/10 flex items-center justify-center">
								<svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
								</svg>
							</div>
							{/* Disconnected indicator */}
							<div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
								<svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
								</svg>
							</div>
						</div>
						
						{/* Dashed lines */}
						<div className="w-8 h-0.5 border-t-2 border-dashed border-muted-foreground/30"></div>
						
						{/* Right hexagon */}
						<div className="w-16 h-16 flex items-center justify-center">
							<svg className="w-12 h-12 text-muted-foreground/40" fill="currentColor" viewBox="0 0 24 24">
								<path d="M17.5 3.5L22 12l-4.5 8.5h-11L2 12l4.5-8.5h11z" />
							</svg>
						</div>
					</div>

					<div className="text-center space-y-2">
						<h3 className="text-lg font-semibold">
							{needsReconnection ? "Email accounts need reconnection" : "No email accounts connected"}
						</h3>
						<p className="text-muted-foreground max-w-md">
							{needsReconnection 
								? "Your email accounts need to be reconnected to enable email sending. This is required after recent security updates."
								: "Connect your email account to send emails from the CRM."
							}
						</p>
					</div>
				</div>
			</StandardizedModal>
		);
	}

	// Show compose email form
	return (
		<StandardizedModal
			open={open}
			overlay={false}
			onOpenChange={handleModalClose}
			title={savedDraft ? "Compose email (Draft)" : "Compose email"}
			icon={<IconMailPlus className="h-5 w-5" />}
			position="bottom-right"
			maxWidth="3xl"
			customControls={customControls}
			preventOutsideClick={false}
			footer={
				<StandardizedModalFooter>
					<div className="flex items-center justify-between w-full">
						<div className="flex items-center space-x-2">
							<label className="relative inline-flex items-center cursor-pointer">
								<input type="checkbox" className="sr-only peer" />
								<div className="relative w-9 h-5 bg-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
							</label>
							<span className="text-sm text-muted-foreground">Enable mass sending</span>
							<button className="text-muted-foreground hover:text-foreground">
								<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</button>
						</div>
						<div className="flex items-center space-x-2">
							<Button
								type="button"
								variant="ghost"
								size="sm"
								onClick={() => handleModalClose(false)}
								disabled={sendEmailMutation.isPending}
							>
								Cancel
							</Button>
							<Button
								type="submit"
								variant="primary"
								size="sm"
								onClick={form.handleSubmit(onSubmit)}
								disabled={sendEmailMutation.isPending || !form.formState.isValid || toRecipients.length === 0}
							>
								{sendEmailMutation.isPending ? (
									<div className="flex items-center gap-2">
										<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
										<span>Sending...</span>
									</div>
								) : (
									"Send email"
								)}
							</Button>
						</div>
					</div>
				</StandardizedModalFooter>
			}
		>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-4">
				{savedDraft && (
					<div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
						<div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
							<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
							</svg>
							<span className="text-sm font-medium">Draft restored</span>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								onClick={clearDraft}
								className="ml-auto h-auto p-1 text-blue-600 dark:text-blue-400"
							>
								<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
								</svg>
							</Button>
						</div>
					</div>
				)}

				<div className="flex flex-col space-y-1">
					{/* From field */}
					<div className="flex items-center gap-8">
						<Label className="text-muted-foreground text-sm">From</Label>
						{usableAccounts.length > 1 ? (
							<Select 
								value={form.watch("fromAccountId")} 
								onValueChange={(value) => form.setValue("fromAccountId", value)}
							>
								<SelectTrigger>
									<SelectValue placeholder="Select email account" />
								</SelectTrigger>
								<SelectContent>
									{usableAccounts.map((account) => (
										<SelectItem key={account.id} value={account.id}>
											<div className="flex items-center space-x-2">
												<span className="text-sm">{account.email}</span>
												<span className="text-xs text-muted-foreground">({account.provider})</span>
											</div>
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						) : (
							<div className="flex items-center space-x-2">
								<UserChip
									name={defaultAccount?.name || "No email account"}
									avatarUrl={defaultAccount?.picture}
									size="22"
									variant="default"
									className="mr-2"
								/>
								{/* {defaultAccount?.provider === "google" && (
									<Icon className="w-3 h-3" />
								)} */}
							</div>
						)}
					</div>

					{/* To field */}
					<div className="flex items-center gap-12.5">
						<Label className="text-muted-foreground text-sm">To</Label>
						<div className="flex-1">
							<RecipientManager
								recipients={toRecipients}
								onRecipientsChange={setToRecipients}
								placeholder=""
							/>
							{toRecipients.length > 1 && (
								<p className="text-xs text-muted-foreground mt-1">
									Additional recipients will be added to CC
								</p>
							)}
						</div>
						{form.formState.errors.to && (
							<p className="text-sm text-red-500">{form.formState.errors.to.message}</p>
						)}
					</div>

					{/* Subject field */}
					<div className="flex items-center gap-1 -mt-1">
						<Label htmlFor="subject" className="text-muted-foreground text-sm">Subject</Label>
						<Input
							{...form.register("subject")}
							placeholder="Enter subject..."
							className={cn(
								"!border-none focus:!ring-0 focus:!ring-offset-0",
								form.formState.errors.subject && "border-b-red-500"
							)}
						/>
						{form.formState.errors.subject && (
							<p className="text-sm text-red-500">{form.formState.errors.subject.message}</p>
						)}
					</div>
				</div>

				{/* Body field */}
				<div className="space-y-2">
					<Textarea
						{...form.register("body")}
						placeholder="Start typing your email, or create a template"
						rows={8}
						className={cn(
							"resize-none !border-none focus:!ring-0 focus:!ring-offset-0 !p-0",
							form.formState.errors.body && "border-red-500"
						)}
					/>
					{form.formState.errors.body && (
						<p className="text-sm text-red-500">{form.formState.errors.body.message}</p>
					)}
				</div>

				{/* Toolbar */}
				{/* <div className="flex items-center justify-between pt-2 border-t"> */}
					{/* <div className="flex items-center space-x-2 text-muted-foreground">
						<button type="button" className="p-1 hover:bg-muted rounded">
							<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
							</svg>
						</button>
						<button type="button" className="p-1 hover:bg-muted rounded">
							<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
							</svg>
						</button>
						<button type="button" className="p-1 hover:bg-muted rounded">
							<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
							</svg>
						</button>
						<button type="button" className="p-1 hover:bg-muted rounded">
							<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
							</svg>
						</button>
					</div> */}
					
					{/* <div className="text-xs text-muted-foreground flex items-center gap-4">
						<span>200 Emails left this month on free plan. <button className="text-blue-500 hover:underline">Upgrade</button> for unlimited email sending.</span>
						{watermarkSettings?.enabled && (
							<span className="text-orange-600">• "Sent with Relio" will be added</span>
						)}
					</div> */}
				{/* </div> */}
			</form>
		</StandardizedModal>
	);
} 