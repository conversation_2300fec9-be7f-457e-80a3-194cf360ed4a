import { useToggleFavorite } from "@app/favorites/lib/api";
import type { ObjectType } from "@repo/database";
import {
	IconDotsVertical,
	IconCopy,
	IconStar,
	IconStarOff,
	IconTrash,
	IconArrowMergeAltLeft,
	IconSettings,
} from "@tabler/icons-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import type { FC } from "react";
import { toast } from "sonner";
import { usePathname } from "next/navigation";
import { Button } from "@ui/components/button";

interface RecordMoreDropdownProps {
	record: any;
	isFavorite: boolean;
	organizationId?: string;
	objectType: ObjectType;
	className?: string;
	onDelete?: () => void;
	onConfigurePage?: () => void;
}

export const RecordMoreDropdown: FC<RecordMoreDropdownProps> = ({
	record,
	isFavorite,
	organizationId,
	objectType,
	className,
	onDelete,
	onConfigurePage,
}) => {
	const toggleFavorite = useToggleFavorite(organizationId || "");
	const pathname = usePathname();

	const handleToggleFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!organizationId) return;
		toggleFavorite.mutate({
			objectId: record.id,
			objectType,
			organizationId,
		});
	};

	const handleDelete = (e: React.MouseEvent) => {
		e.stopPropagation();
		onDelete?.();
	};

	const handleCopyUrl = async (e: React.MouseEvent) => {
		e.stopPropagation();
		try {
			const url = window.location.origin + pathname;
			await navigator.clipboard.writeText(url);
			toast.success("Page URL copied to clipboard");
		} catch (err) {
			console.error("Failed to copy URL:", err);
			toast.error("Failed to copy URL to clipboard");
		}
	};

	const handleCopyId = async (e: React.MouseEvent) => {
		e.stopPropagation();
		try {
			await navigator.clipboard.writeText(record.id);
			toast.success("Record ID copied to clipboard");
		} catch (err) {
			console.error("Failed to copy ID:", err);
			toast.error("Failed to copy ID to clipboard");
		}
	};

	const handleMergeRecords = (e: React.MouseEvent) => {
		e.stopPropagation();
		// TODO: Implement merge records functionality
		toast.info("Merge records functionality coming soon");
	};

	const handleConfigurePage = (e: React.MouseEvent) => {
		e.stopPropagation();
		onConfigurePage?.();
	};

	return (
		<span onClick={(e) => e.stopPropagation()}>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={cn(
							"cursor-pointer",
              "h-8 w-8",
							"flex items-center justify-center rounded-md p-1 hover:bg-muted/60 transition",
							className,
						)}
						aria-label="Open menu"
					>
						<IconDotsVertical className="w-4 h-4 text-muted-foreground" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					sideOffset={8}
					className={cn(
						"z-50 min-w-[180px] rounded-xl border border-border bg-popover text-popover-foreground shadow-lg focus:outline-none animate-in fade-in-0 slide-in-from-top-1",
						"bg-sidebar",
					)}
					align="end"
				>
					{/* Configure page action */}
					{/* <DropdownMenuItem
						className="flex items-center gap-2"
						onClick={handleConfigurePage}
					>
						<IconSettings className="w-4 h-4" />
						Configure page
					</DropdownMenuItem> */}

					{/* <DropdownMenuSeparator className="my-0.5" /> */}

					{/* Copy actions */}
					<DropdownMenuItem
						className="flex items-center gap-2"
						onClick={handleCopyUrl}
					>
						<IconCopy className="w-4 h-4" />
						Copy page URL
					</DropdownMenuItem>
					<DropdownMenuItem
						className="flex items-center gap-2"
						onClick={handleCopyId}
					>
						<IconCopy className="w-4 h-4" />
						Copy record ID
					</DropdownMenuItem>

					<DropdownMenuSeparator className="my-0.5" />

					{/* Favorite actions */}
					<DropdownMenuItem
						className="flex items-center gap-2"
						onClick={handleToggleFavorite}
					>
						{isFavorite ? (
							<>
								<IconStarOff className="w-4 h-4" />
								Remove from favorites
							</>
						) : (
							<>
								<IconStar className="w-4 h-4" />
								Add to favorites
							</>
						)}
					</DropdownMenuItem>

					{/* Merge action */}
					<DropdownMenuItem
						className="flex items-center gap-2"
						onClick={handleMergeRecords}
					>
						<IconArrowMergeAltLeft className="w-4 h-4" />
						Merge records
					</DropdownMenuItem>

					<DropdownMenuSeparator className="my-0.5" />

					{/* Delete action */}
					<DropdownMenuItem
						className="flex items-center gap-2 text-red-500 hover:!text-red-600"
						onClick={handleDelete}
					>
						<IconTrash className="w-4 h-4" />
						Delete record
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</span>
	);
}; 