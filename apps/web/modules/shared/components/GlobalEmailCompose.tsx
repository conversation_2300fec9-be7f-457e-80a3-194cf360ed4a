"use client";

import React from "react";
import { useEmailCompose } from "./EmailComposeProvider";
import { ComposeEmailModal } from "./ComposeEmailModal";
import { GlobalMinimizedEmail } from "./GlobalMinimizedEmail";

export function GlobalEmailCompose() {
	const { isOpen, closeCompose, initialToEmail, initialSubject } = useEmailCompose();

	return (
		<>
			<ComposeEmailModal
				open={isOpen}
				onOpenChange={closeCompose}
				toEmail={initialToEmail}
				subject={initialSubject}
			/>
			<GlobalMinimizedEmail />
		</>
	);
} 