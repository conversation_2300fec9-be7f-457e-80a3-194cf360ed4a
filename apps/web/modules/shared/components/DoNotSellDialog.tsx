"use client";

import { Close as DialogPrimitiveClose } from "@radix-ui/react-dialog";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { Switch } from "@ui/components/switch";
import React, { useEffect, useState } from "react";

interface DoNotSellDialogProps {
	trigger: React.ReactNode;
}

export function DoNotSellDialog({ trigger }: DoNotSellDialogProps) {
	const [optOut, setOptOut] = useState(false);

	useEffect(() => {
		// Load the user's preference from localStorage only on the client
		if (typeof window !== "undefined") {
			const savedPreference = localStorage.getItem("doNotSellPreference");
			if (savedPreference) {
				setOptOut(JSON.parse(savedPreference));
			}
		}
	}, []);

	const handleOptOutChange = (checked: boolean) => {
		setOptOut(checked);
		if (typeof window !== "undefined") {
			localStorage.setItem(
				"doNotSellPreference",
				JSON.stringify(checked),
			);
		}
	};

	return (
		<Dialog>
			<DialogTrigger asChild>{trigger}</DialogTrigger>
			<DialogContent className="sm:max-w-[525px]">
				<DialogHeader>
					<DialogTitle>
						Do Not Sell or Share My Personal Information
					</DialogTitle>
					<DialogDescription>
						Control how we use and share your personal information
						for marketing purposes.
					</DialogDescription>
				</DialogHeader>
				<div className="py-4">
					<div className="flex items-center justify-between">
						<div>
							<h3 className="text-lg font-medium">
								Opt-out of sale or sharing
							</h3>
							<p className="text-sm text-zinc-500 dark:text-zinc-400">
								When enabled, we will not sell or share your
								personal information for targeted advertising
								purposes.
							</p>
						</div>
						<Switch
							checked={optOut}
							onCheckedChange={handleOptOutChange}
							aria-label="Opt-out of sale or sharing"
							id="do-not-sell-switch"
						/>
					</div>
					<div className="mt-6">
						<p className="text-sm text-zinc-500 dark:text-zinc-400">
							Please note that opting out may affect your
							experience on our website and limit certain
							personalized features.
						</p>
					</div>
				</div>
				<DialogFooter>
					<DialogPrimitiveClose asChild>
						<Button type="button" variant="outline">
							Close
						</Button>
					</DialogPrimitiveClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
