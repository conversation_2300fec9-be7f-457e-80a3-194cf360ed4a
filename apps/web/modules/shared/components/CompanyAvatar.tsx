import { config } from "@repo/config";
import { IconBuildingFactory2 } from "@tabler/icons-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { forwardRef, useMemo } from "react";

const CompanyIcon = ({ className }: { className?: string }) => (
	<IconBuildingFactory2 className={className} />
);

export const CompanyAvatar = forwardRef<
	HTMLSpanElement,
	{
		name: string;
		logoUrl?: string | null;
		className?: string;
	}
>(({ name, logoUrl, className }, ref) => {
	const initials = useMemo(() => {
		if (!name || name.trim() === "") return "";
		const parts = name.trim().split(" ").filter(Boolean);
		return parts
			.slice(0, 2)
			.map((n) => n[0]?.toUpperCase())
			.join("");
	}, [name]);

	const avatarSrc = useMemo(
		() =>
			logoUrl
				? logoUrl.startsWith("http")
					? logoUrl
					: `/image-proxy/${config.storage.bucketNames.avatars}/${logoUrl}`
				: undefined,
		[logoUrl],
	);

	return (
		<Avatar ref={ref} className={className}>
			{avatarSrc && <AvatarImage src={avatarSrc} />}
			<AvatarFallback className="cursor-default bg-secondary border border-input rounded-full flex items-center justify-center">
				<CompanyIcon className="h-full w-full" />
			</AvatarFallback>
		</Avatar>
	);
});

CompanyAvatar.displayName = "CompanyAvatar";
