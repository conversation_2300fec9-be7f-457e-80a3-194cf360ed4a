import { config } from "@repo/config";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { forwardRef, useMemo } from "react";
import { cn } from "@ui/lib";
import { ContactIcon } from "@ui/icons/contact";

export const UserAvatar = forwardRef<
	HTMLSpanElement,
	{
		name: string;
		avatarUrl?: string | null;
		className?: string;
	}
>(({ name, avatarUrl, className }, ref) => {
	const initials = useMemo(
		() =>
			name
				.split(" ")
				.slice(0, 2)
				.map((n) => n[0])
				.join(""),
		[name],
	);

	const avatarSrc = useMemo(
		() =>
			avatarUrl
				? avatarUrl.startsWith("http")
					? avatarUrl
					: `/image-proxy/${config.storage.bucketNames.avatars}/${avatarUrl}`
				: undefined,
		[avatarUrl],
	);

	return (
		<Avatar ref={ref} className={className}>
			<AvatarImage src={avatarSrc} className="rounded-full" />
			<AvatarFallback className="cursor-default bg-secondary/10 text-muted-foreground border border-input dark:border-zinc-700 rounded-full">
				{initials}
			</AvatarFallback>
		</Avatar>
	);
});

UserAvatar.displayName = "UserAvatar";

interface UserChipProps {
	name: string;
	avatarUrl?: string | null;
	size?: "18" | "22" | "26";
	variant?: "default" | "secondary";
	interactive?: boolean;
	showSuffixIcon?: boolean;
	suffixIcon?: React.ReactNode;
	onSuffixClick?: (e: React.MouseEvent) => void;
	className?: string;
	onClick?: () => void;
	type?: string;
}

export const UserChip = forwardRef<HTMLDivElement, UserChipProps>(
	({ 
		name, 
		avatarUrl, 
		size = "22", 
		variant = "default", 
		interactive = false, 
		showSuffixIcon = false,
		suffixIcon,
		onSuffixClick,
		className, 
		onClick,
		type,
	}, ref) => {
		const initials = useMemo(
			() =>
				name
					.split(" ")
					.slice(0, 2)
					.map((n) => n[0])
					.join(""),
			[name],
		);

		const avatarSrc = useMemo(
			() =>
				avatarUrl
					? avatarUrl.startsWith("http")
						? avatarUrl
						: `/image-proxy/${config.storage.bucketNames.avatars}/${avatarUrl}`
					: undefined,
			[avatarUrl],
		);

		const sizeStyles = {
			"18": {
				container: "h-[18px] rounded-[7px] px-1 gap-1",
				avatar: "w-3 h-3",
				text: "text-xs leading-4 font-medium",
				icon: "w-3 h-3",
			},
			"22": {
				container: "h-[22px] rounded-[9px] px-1.5 gap-1",
				avatar: "w-3.5 h-3.5",
				text: "text-sm leading-5 font-medium",
				icon: "w-3.5 h-3.5",
			},
			"26": {
				container: "h-[26px] rounded-[11px] px-2 gap-1.5",
				avatar: "w-4 h-4",
				text: "text-sm leading-6 font-medium",
				icon: "w-4 h-4",
			},
		};

		const variantStyles = {
			default: "bg-transparent border border-border text-foreground hover:bg-muted/50",
			secondary: "bg-muted text-muted-foreground hover:bg-muted/80",
		};

		const defaultSuffixIcon = (
			<svg className={sizeStyles[size].icon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
			</svg>
		);

		const isContact = type === "contact" || type === "contacts";

		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex items-center transition-all duration-200 my-0.5 mr-1 cursor-default",
					sizeStyles[size].container,
					variantStyles[variant],
					interactive && "cursor-pointer",
					className
				)}
				onClick={onClick}
				role={interactive ? "button" : undefined}
				tabIndex={interactive ? 0 : undefined}
			>
				{/* Avatar */}
				<div className={cn("relative flex items-center justify-center rounded-full overflow-hidden flex-shrink-0", sizeStyles[size].avatar)}>
					{avatarSrc ? (
						<img
							src={avatarSrc}
							alt={name}
							className="w-full h-full object-cover"
						/>
					) : isContact ? (
						<ContactIcon className={sizeStyles[size].avatar} />
					) : (
						<div className="w-full h-full bg-muted-foreground/20 flex items-center justify-center text-[10px] font-medium text-muted-foreground">
							{initials}
						</div>
					)}
				</div>

				{/* Name with hidden/visible text technique for sizing */}
				<div className="flex-1 min-w-0 overflow-hidden relative">
					{/* Hidden text for sizing */}
					<div className={cn(
						"opacity-0 pointer-events-none overflow-hidden whitespace-nowrap",
						sizeStyles[size].text
					)}>
						{name}
					</div>
					{/* Visible text */}
					<div className="absolute inset-0 flex items-center">
						<div className={cn(
							"text-ellipsis whitespace-nowrap overflow-hidden",
							sizeStyles[size].text
						)}>
							{name}
						</div>
					</div>
				</div>

				{/* Suffix Icon */}
				{showSuffixIcon && (
					<button
						type="button"
						className={cn(
							"flex items-center justify-center text-muted-foreground hover:text-foreground transition-colors flex-shrink-0",
							sizeStyles[size].icon
						)}
						onClick={(e) => {
							e.stopPropagation();
							onSuffixClick?.(e);
						}}
					>
						{suffixIcon || defaultSuffixIcon}
					</button>
				)}
			</div>
		);
	}
);

UserChip.displayName = "UserChip";
