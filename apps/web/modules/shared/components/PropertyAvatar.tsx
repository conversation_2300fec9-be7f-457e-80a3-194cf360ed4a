import { config } from "@repo/config";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { forwardRef, useMemo } from "react";

const PropertyIcon = ({ className }: { className?: string }) => (
<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none" className={className}>
<g clipPath="url(#clip0_128_199056)">
<rect y="0.000244141" width="14" height="14" rx="5" fill="#F4F5F6"/>
<path fillRule="evenodd" clipRule="evenodd" d="M6.52889 3.09468C6.81713 2.91453 7.18287 2.91453 7.47111 3.09468L10.201 4.80086C10.698 5.11151 11 5.65631 11 6.24245V14.6071H3V6.24245C3 5.65631 3.30195 5.11151 3.799 4.80086L6.52889 3.09468ZM7 14.6071H10V6.1164C10 5.80093 9.8328 5.5091 9.56065 5.34956L7.40153 4.08384C7.22376 3.97962 7 4.10782 7 4.31389V14.6071Z" fill="#9FA1A7"/>
</g>
<rect x="0.5" y="0.500244" width="13" height="13" rx="4.5" stroke="var(--color)" strokeOpacity="0.04"/>
<defs>
<clipPath id="clip0_128_199056">
<rect y="0.000244141" width="14" height="14" rx="5" fill="white"/>
</clipPath>
</defs>
</svg>
);

export const PropertyAvatar = forwardRef<
	HTMLSpanElement,
	{
		name: string;
		avatarUrl?: string | null;
		className?: string;
	}
>(({ name, avatarUrl, className }, ref) => {
	const initials = useMemo(() => {
		if (!name || name.trim() === "") return "";
		const parts = name.trim().split(" ").filter(Boolean);
		return parts
			.slice(0, 2)
			.map((n) => n[0]?.toUpperCase())
			.join("");
	}, [name]);

	const avatarSrc = useMemo(
		() =>
			avatarUrl
				? avatarUrl.startsWith("http")
					? avatarUrl
					: `/image-proxy/${config.storage.bucketNames.avatars}/${avatarUrl}`
				: undefined,
		[avatarUrl],
	);

	return (
		<Avatar ref={ref} className={className}>
			{avatarSrc && <AvatarImage src={avatarSrc} />}
			<AvatarFallback className="cursor-default bg-secondary border border-input rounded-lg flex items-center justify-center">
				<PropertyIcon className="h-full w-full" />
			</AvatarFallback>
		</Avatar>
	);
});

PropertyAvatar.displayName = "PropertyAvatar";
