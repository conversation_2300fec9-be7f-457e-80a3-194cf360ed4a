import {
	IconBugOff,
	IconGitCommit,
	IconPencilStar,
	IconSparkles,
} from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import type { CarouselApi } from "@ui/components/carousel";
import {
	Carousel,
	CarouselContent,
	CarouselItem,
} from "@ui/components/carousel";
import { cn } from "@ui/lib";
import { formatDate } from "date-fns";
import Autoplay from "embla-carousel-autoplay";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import type { ChangelogItem, ChangeType } from "../types";

const changeTypeIcons: Record<ChangeType, React.ReactNode> = {
	feature: <IconSparkles size={14} strokeWidth={1.5} />,
	improvement: <IconPencilStar size={14} strokeWidth={1.5} />,
	fix: <IconBugOff size={14} strokeWidth={1.5} />,
	release: <IconGitCommit size={14} strokeWidth={1.5} />,
};

export function ChangelogEntry({
	item,
	className,
}: {
	item: ChangelogItem;
	className?: string;
}) {
	const [currentSlide, setCurrentSlide] = useState(0);
	const [api, setApi] = useState<CarouselApi>();

	const changesByType = item.changes?.reduce<Record<ChangeType, string[]>>(
		(acc, change) => {
			if (!acc[change.type]) {
				acc[change.type] = [];
			}
			acc[change.type].push(change.description);
			return acc;
		},
		{} as Record<ChangeType, string[]>,
	);

	const media = (
		item.previewImages && item.previewImages.length > 0
			? item.previewImages
			: item.previewImage
				? [item.previewImage]
				: []
	)
		.filter(Boolean)
		.map((src) => ({ src, alt: item.title }));

	useEffect(() => {
		if (!api) {
			return;
		}

		api.on("select", () => {
			setCurrentSlide(api.selectedScrollSnap());
		});
	}, [api]);

	return (
		<div
			className={cn(
				"grid border-l border-muted lg:grid-cols-12 lg:gap-8 pb-32",
				className,
			)}
		>
			<div className="col-span-12 mb-8 self-start lg:sticky lg:top-0 lg:col-span-4 lg:-mt-32 lg:pt-32">
				<div className="flex w-full items-baseline gap-6">
					<div className="bg-border border-muted text-foreground-lighter -ml-2.5 flex h-5 w-5 items-center justify-center rounded border drop-shadow-sm">
						{changeTypeIcons[item.type[0] as ChangeType] || (
							<IconGitCommit size={14} strokeWidth={1.5} />
						)}
					</div>
					<div className="flex w-full flex-col gap-1">
						{item.url ? (
							<Link href={item.url}>
								<h3 className="text-foreground text-2xl">
									{item.title}
								</h3>
							</Link>
						) : (
							<h3 className="text-foreground text-2xl">
								{item.title}
							</h3>
						)}
						<div className="flex items-center gap-2 mb-2">
							<span className="inline-block rounded-full bg-zinc-100 dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 px-3 py-1 text-xs font-semibold tracking-wide border border-zinc-200 dark:border-zinc-700 shadow-sm">
								v{item.version}
							</span>
						</div>
						<div className="flex items-center gap-2">
							<p className="text-zinc-400 dark:text-zinc-600 text-lg">
								{formatDate(new Date(item.date), "MMM d, yyyy")}
							</p>
						</div>
						{item.description && (
							<p className="text-muted-foreground dark:text-muted-foreground mt-2">
								{item.description}
							</p>
						)}
						<div className="flex flex-wrap gap-2 mt-2">
							{item.type.map((type, i) => (
								<Badge
									key={i}
									status={type}
									className="w-fit font-mono text-xs font-normal capitalize"
								>
									{type}
								</Badge>
							))}
						</div>
					</div>
				</div>
			</div>
			<div className="col-span-8 ml-8 lg:ml-0 max-w-[calc(100vw-80px)]">
				{media.length > 0 && (
					<div className="mb-8 space-y-4">
						{media.length === 1 ? (
							// Single image - no carousel needed
							<div className="overflow-hidden rounded-lg border border-border">
								<Image
									src={media[0].src}
									alt={media[0].alt}
									width={800}
									height={400}
									className="w-full"
								/>
							</div>
						) : (
							// Multiple images - use carousel
							<div className="relative">
								<Carousel
									className="w-full"
									setApi={setApi}
									opts={{
										loop: true,
									}}
									plugins={[
										Autoplay({
											delay: 4000,
											stopOnInteraction: true,
										}),
									]}
								>
									<CarouselContent>
										{media.map((image, index) => (
											<CarouselItem key={index}>
												<div className="relative overflow-hidden rounded-lg border border-border">
													<Image
														src={image.src}
														alt={image.alt}
														width={800}
														height={400}
														className="w-full object-cover"
														priority={index === 0}
													/>
												</div>
											</CarouselItem>
										))}
									</CarouselContent>
								</Carousel>

								{/* Slide indicator */}
								<div className="flex justify-center items-center gap-2 mt-3">
									<span className="text-sm text-muted-foreground">
										{currentSlide + 1} of {media.length}
									</span>
									<div className="flex gap-1.5">
										{media.map((_, index) => (
											<button
												key={index}
												onClick={() =>
													api?.scrollTo(index)
												}
												aria-label={`Go to image ${index + 1}`}
												className="p-1"
											>
												<div
													className={cn(
														"h-1.5 w-1.5 rounded-full transition-colors",
														index === currentSlide
															? "bg-primary"
															: "bg-primary/30 hover:bg-primary/60",
													)}
												/>
											</button>
										))}
									</div>
								</div>
							</div>
						)}
					</div>
				)}
				{item.content && (
					<div className="prose dark:prose-invert max-w-none">
						{item.content}
					</div>
				)}
				{changesByType && (
					<div className="mt-4 space-y-6">
						{Object.entries(changesByType).map(
							([type, changes]) => (
								<div key={type} className="space-y-2">
									<h4 className="text-xs font-mono capitalize text-muted-foreground">
										{type.replace(/([A-Z])/g, " $1").trim()}
										s
									</h4>
									<ul className="list-disc pl-5 space-y-1">
										{changes.map((change, i) => (
											<li
												key={i}
												className="text-foreground/80"
											>
												{change}
											</li>
										))}
									</ul>
								</div>
							),
						)}
					</div>
				)}
			</div>
		</div>
	);
}
