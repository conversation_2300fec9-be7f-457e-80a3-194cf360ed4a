export type ChangeType = "feature" | "release" | "fix" | "improvement";

export interface ChangeItem {
	type: ChangeType;
	description: string;
}

export interface ChangelogItem {
	version: string;
	date: string;
	title: string;
	type: ChangeType[];
	description?: string;
	/**
	 * Use previewImages for multiple images (up to 3). Deprecated: previewImage.
	 */
	previewImages?: string[];
	/**
	 * @deprecated Use previewImages instead.
	 */
	previewImage?: string;
	changes?: ChangeItem[];
	content?: string;
	url?: string;
}
