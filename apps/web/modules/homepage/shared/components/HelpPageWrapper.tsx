"use client";

import { MDXContent as MDXContentType } from "@content-collections/mdx/react";
import { File, Files, Folder } from "fumadocs-ui/components/files";
import { ImageZoom } from "fumadocs-ui/components/image-zoom";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Tab, Tabs } from "fumadocs-ui/components/tabs";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { createElement } from "react";

export function HelpPageWrapper({ code }: { code: string }) {
	// @ts-ignore - MDX components type issue
	const components = {
		...defaultMdxComponents,
		Tabs,
		Tab,
		Steps,
		Step,
		File,
		Files,
		Folder,
		img: (props: any) =>
			createElement(ImageZoom, {
				...props,
				className: "rounded-lg border-4 border-secondary/10",
			}),
	};

	// @ts-ignore - MDXContent type issue
	return <MDXContentType code={code} components={components} />;
}
