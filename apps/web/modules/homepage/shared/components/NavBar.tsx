"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { LocaleLink, useLocalePathname } from "@i18n/routing";
import { config } from "@repo/config";
import { ColorModeToggle } from "@shared/components/ColorModeToggle";
import { LocaleSwitch } from "@shared/components/LocaleSwitch";
import { Logo } from "@shared/components/Logo";
import { IconChevronRight } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";
import { Suspense, useEffect, useState } from "react";
import { useDebounceCallback } from "usehooks-ts";

export default function NavBar() {
	const t = useTranslations();
	const { user } = useSession();
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const [openDropdown, setOpenDropdown] = useState<string | null>(null);
	const localePathname = useLocalePathname();
	const [isTop, setIsTop] = useState(true);

	const debouncedScrollHandler = useDebounceCallback(
		() => {
			setIsTop(window.scrollY <= 10);
		},
		150,
		{
			maxWait: 150,
		},
	);

	useEffect(() => {
		window.addEventListener("scroll", debouncedScrollHandler);
		debouncedScrollHandler();
		return () => {
			window.removeEventListener("scroll", debouncedScrollHandler);
		};
	}, [debouncedScrollHandler]);

	useEffect(() => {
		setMobileMenuOpen(false);
	}, [localePathname]);

	const isHelpPage = localePathname.startsWith("/help");

	const menuItems: {
		label: string;
		href: string;
		dropdownItems?: Array<{
			title: string;
			href: string;
			description: string;
		}>;
	}[] = [
		{
			label: t("common.menu.pricing"),
			href: "/pricing",
		},
		{
			label: t("common.menu.faq"),
			href: "/faq",
		},
		{
			label: t("common.menu.blog"),
			href: "/blog",
		},
		{
			label: t("common.menu.changelog"),
			href: "/changelog",
		},
		...(config.contactForm.enabled
			? [
					{
						label: t("common.menu.contact"),
						href: "/contact",
					},
				]
			: []),
		// {
		// 	label: t("common.menu.help"),
		// 	href: "/help",
		// },
	];

	const isMenuItemActive = (href: string) => localePathname.startsWith(href);

	return (
		<nav
			className={cn(
				"fixed top-0 left-0 z-50 w-full transition-shadow duration-200",
				!isTop || isHelpPage
					? "bg-card/80 shadow-sm backdrop-blur-lg"
					: "shadow-none",
				isHelpPage && "!border-b",
			)}
			data-test="navigation"
		>
			<div
				className={cn(
					"container",
					mobileMenuOpen
						? "bg-card transition-colors duration-300"
						: "",
				)}
			>
				<div
					className={cn(
						"flex items-center justify-stretch gap-6 transition-[padding] duration-200",
						!isTop || isHelpPage ? "py-4" : "py-6",
					)}
				>
					<div className="flex justify-start items-center gap-8">
						<div className="flex flex-1 justify-start">
							<LocaleLink
								href="/"
								className="block hover:no-underline active:no-underline"
							>
								<Logo />
							</LocaleLink>
						</div>

						<div className="hidden flex-1 items-center gap-2 justify-start lg:flex">
							{menuItems.map((menuItem) => (
								<LocaleLink
									key={menuItem.href}
									href={menuItem.href}
									className={cn(
										"block px-3 py-2 font-medium text-foreground/80 text-md hover:bg-accent hover:text-foreground rounded-lg",
									)}
								>
									{menuItem.label}
								</LocaleLink>
							))}
						</div>
					</div>

					<div className="flex flex-1 items-center justify-end gap-3">
						<ColorModeToggle />
						{config.i18n.enabled && (
							<Suspense>
								<LocaleSwitch />
							</Suspense>
						)}

						<button
							className="text-muted-foreground relative flex size-8 lg:hidden"
							onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
						>
							<span className="sr-only">Open main menu</span>
							<div className="absolute top-1/2 left-1/2 block w-[18px] -translate-x-1/2 -translate-y-1/2">
								<span
									aria-hidden="true"
									className={`absolute block h-0.5 w-full rounded-full bg-current transition duration-500 ease-in-out ${mobileMenuOpen ? "rotate-45" : "-translate-y-1.5"}`}
								/>
								<span
									aria-hidden="true"
									className={`absolute block h-0.5 w-full rounded-full bg-current transition duration-500 ease-in-out ${mobileMenuOpen ? "opacity-0" : ""}`}
								/>
								<span
									aria-hidden="true"
									className={`absolute block h-0.5 w-full rounded-full bg-current transition duration-500 ease-in-out ${mobileMenuOpen ? "-rotate-45" : "translate-y-1.5"}`}
								/>
							</div>
						</button>

						<div
							className={cn(
								"absolute inset-0 top-full container flex h-[calc(100vh-64px)] flex-col transition-all duration-300 ease-in-out lg:hidden",
								mobileMenuOpen
									? "visible translate-x-0 opacity-100"
									: "invisible translate-x-full opacity-0",
								"bg-card",
							)}
						>
							<div className="mt-8 space-y-2">
                <LocaleLink
                  href="/auth/signup"
                  className="block"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Button size="sm" className="w-full">
                    {t("common.menu.signup")}
                  </Button>
                </LocaleLink>
                <LocaleLink
                  href="/auth/login"
                  className="block"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Button size="sm" className="w-full" variant="primary">
                    {t("common.menu.login")}
                  </Button>
                </LocaleLink>
              </div>
							<nav className="mt-3 flex flex-1 flex-col gap-6">
								{menuItems.map((link) =>
									link.dropdownItems ? (
										<div key={link.label} className="">
											<button
												onClick={() =>
													setOpenDropdown(
														openDropdown ===
															link.label
															? null
															: link.label,
													)
												}
												className="text-primary flex w-full items-center justify-between text-lg tracking-[-0.36px]"
												aria-label={`${link.label} menu`}
												aria-expanded={
													openDropdown === link.label
												}
											>
												{link.label}
												<IconChevronRight
													className={cn(
														"h-4 w-4 transition-transform",
														openDropdown ===
															link.label
															? "rotate-90"
															: "",
													)}
													aria-hidden="true"
												/>
											</button>
											<div
												className={cn(
													"ml-4 space-y-3 overflow-hidden transition-all",
													openDropdown === link.label
														? "mt-3 max-h-[1000px] opacity-100"
														: "max-h-0 opacity-0",
												)}
											>
												{link.dropdownItems?.map(
													(item) => (
														<LocaleLink
															key={item.title}
															href={item.href}
															className="hover:bg-accent flex items-start gap-3 rounded-md p-2"
															onClick={() => {
																setMobileMenuOpen(
																	false,
																);
																setOpenDropdown(
																	null,
																);
															}}
														>
															<div>
																<div className="text-primary font-medium">
																	{item.title}
																</div>
																<p className="text-muted-foreground text-sm">
																	{
																		item.description
																	}
																</p>
															</div>
														</LocaleLink>
													),
												)}
											</div>
										</div>
									) : (
										<LocaleLink
											key={link.label}
											href={link.href}
											className={cn(
												"text-primary text-lg tracking-[-0.36px]",
												localePathname === link.href &&
													"text-muted-foreground",
											)}
											onClick={() =>
												setMobileMenuOpen(false)
											}
										>
											{link.label}
										</LocaleLink>
									),
								)}
							</nav>
						</div>

						{config.ui.saas.enabled &&
							(user ? (
								<Button
									key="dashboard"
									className="hidden lg:flex"
									asChild
									variant="relio"
								>
									<LocaleLink href="/app">
										{t("common.menu.dashboard")}
									</LocaleLink>
								</Button>
							) : (
								<div className="flex items-center gap-2">
								<Button
									key="login"
									className="hidden lg:flex"
									asChild
									variant="relio"
								>
									<LocaleLink href="/auth/login">
										{t("common.menu.login")}
									</LocaleLink>
								</Button>
								{/* <Button
									key="signup"
									className="hidden lg:flex"
									asChild
									variant="primary"
								>
									<LocaleLink href="/auth/signup">
										{t("common.menu.signup")}
									</LocaleLink>
								</Button> */}
								</div>
							))}
					</div>
				</div>
			</div>
		</nav>
	);
}
