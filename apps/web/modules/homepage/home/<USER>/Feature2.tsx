import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@ui/components/card";
import { cn } from "@ui/lib";
import Image from "next/image";
import type { AbstractIntlMessages } from "next-intl";
import { useTranslations } from "next-intl";

type FadeDirection = "left" | "right" | "bottom" | "top";

type ItemType = {
	title: string;
	description: string;
	image: {
		src: string;
		alt: string;
		className: string;
	};
	fade: FadeDirection[];
};

const getItems = (t: ReturnType<typeof useTranslations>): ItemType[] => [
	{
		title: t("one.title"),
		description: t("one.description"),
		image: {
			src: "/images/features/assistant.png",
			alt: t("one.description"),
			className:
				"rounded-lg lg:translate-x-20 translate-x-6 md:translate-x-10",
		},
		fade: ["left"],
	},
	{
		title: t("two.title"),
		description: t("two.description"),
		image: {
			src: "/images/features/properties.png",
			alt: t("two.description"),
			className:
				"rounded-lg lg:translate-x-20 translate-x-6 md:translate-x-10",
		},
		fade: ["right", "bottom"],
	},
	{
		title: t("three.title"),
		description: t("three.description"),
		image: {
			src: "/images/dashboard.png",
			alt: t("three.description"),
			className:
				"rounded-lg translate-x-10 md:translate-x-6 pb-6 object-right-top",
		},
		fade: ["bottom"],
	},
	{
		title: t("four.title"),
		description: t("four.description"),
		image: {
			src: "/images/features/import.png",
			alt: t("four.description"),
			className: "rounded-lg px-6 mt-4 object-cover",
		},
		fade: ["bottom"],
	},
	{
		title: t("five.title"),
		description: t("five.description"),
		image: {
			src: "/images/features/contact.png",
			alt: t("five.description"),
			className: "rounded-lg pb-6 object-center object-cover",
		},
		fade: ["bottom"],
	},
];

export const Feature2 = () => {
	const t = useTranslations("home.feature2");
	const items = getItems(t);

	return (
		<section id="feature2" className="bg-muted py-16 md:py-28 lg:py-32">
			<div className="container">
				<h2 className="text-center text-3xl font-semibold tracking-tight text-balance sm:text-4xl md:text-5xl lg:text-6xl">
					{t("title")}
				</h2>

				<div className="mt-8 grid grid-cols-1 gap-4 md:mt-12 md:grid-cols-6 md:grid-rows-5 lg:mt-20">
					{items.map((item, i) => {
						const gridClasses = {
							0: "md:col-span-3 md:row-span-3",
							1: "md:col-span-3 md:row-span-3 md:col-start-4",
							2: "md:col-span-2 md:row-span-2 md:row-start-4",
							3: "md:col-span-2 md:row-span-2 md:col-start-3 md:row-start-4",
							4: "md:col-span-2 md:row-span-2 md:col-start-5 md:row-start-4",
						}[i];
						return (
							<Item key={i} {...item} className={gridClasses} />
						);
					})}
				</div>
			</div>
		</section>
	);
};

const Item = ({
	title,
	description,
	image,
	fade = [],
	className,
}: ItemType & { className?: string }) => {
	return (
		<Card
			className={cn(
				"relative flex flex-col overflow-hidden border-none px-0 text-lg shadow-none max-md:min-h-[400px]",
				className,
			)}
		>
			<CardHeader className="mb-2">
				<h3 className="inline leading-tight font-semibold text-balance">
					{title}{" "}
					<span className="text-muted-foreground font-medium">
						{description}
					</span>
				</h3>
			</CardHeader>

			<CardContent className="relative min-h-40 flex-1 overflow-hidden p-0 lg:min-h-48">
				{fade.includes("right") && (
					<div className="to-background absolute inset-0 z-10 bg-linear-to-r from-transparent via-transparent" />
				)}
				{fade.includes("bottom") && (
					<div className="to-background absolute inset-0 z-10 bg-linear-to-b from-transparent via-transparent" />
				)}
				{fade.includes("top") && (
					<div className="to-background absolute inset-0 z-10 bg-linear-to-t from-transparent via-transparent" />
				)}
				{fade.includes("left") && (
					<div className="to-background absolute inset-0 z-10 bg-linear-to-l from-transparent via-transparent" />
				)}
				<Image
					src={image.src}
					alt={image.alt}
					fill
					className={cn(
						"object-cover object-left-top",
						image.className,
					)}
				/>
			</CardContent>
		</Card>
	);
};
