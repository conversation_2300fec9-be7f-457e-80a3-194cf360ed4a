import {
	Accordion,
	Accordion<PERSON>ontent,
	Accordion<PERSON><PERSON>,
	AccordionTrigger,
} from "@ui/components/accordion";
import { useTranslations } from "next-intl";

export const FAQ = () => {
	const t = useTranslations();

	const leftQuestions = [
		{
			question: t("faq.left.one.question"),
			answer: t("faq.left.one.answer"),
		},
		{
			question: t("faq.left.two.question"),
			answer: t("faq.left.two.answer"),
		},
		{
			question: t("faq.left.three.question"),
			answer: (
				<>
					{t("faq.left.three.answerOne")}{" "}
					<a
						href="mailto:<EMAIL>"
						className="text-sky-500 hover:underline"
					>
						<EMAIL>
					</a>{" "}
					{t("faq.left.three.answerTwo")}
				</>
			),
		},
		{
			question: t("faq.left.four.question"),
			answer: t("faq.left.four.answer"),
		},
		{
			question: t("faq.left.five.question"),
			answer: t("faq.left.five.answer"),
		},
	];

	const rightQuestions = [
		{
			question: t("faq.right.one.question"),
			answer: t("faq.right.one.answer"),
		},
		{
			question: t("faq.right.two.question"),
			answer: t("faq.right.two.answer"),
		},
		{
			question: t("faq.right.three.question"),
			answer: t("faq.right.three.answer"),
		},
		{
			question: t("faq.right.four.question"),
			answer: t("faq.right.four.answer"),
		},
	];

	return (
		<section className={"pb-16 md:pb-28 lg:pb-32"}>
			<div className="container mx-auto lg:max-w-5xl">
				<div className="mb-12 text-center">
					<h1 className="mb-2 font-bold text-5xl">
						{t("faq.title")}
					</h1>
					<p className="text-lg opacity-50">{t("faq.description")}</p>
				</div>

				<div className="mt-6 grid gap-x-12 md:mt-10 md:grid-cols-2 lg:mt-14">
					<Accordion
						type="single"
						collapsible
						className="text-muted-foreground border-t"
					>
						{leftQuestions.map((item, i) => (
							<AccordionItem key={i} value={`left-${i}`}>
								<AccordionTrigger>
									{item.question}
								</AccordionTrigger>
								<AccordionContent className="text-foreground">
									{item.answer}
								</AccordionContent>
							</AccordionItem>
						))}
					</Accordion>

					<Accordion
						collapsible
						type="single"
						className="text-muted-foreground md:border-t"
					>
						{rightQuestions.map((item, i) => (
							<AccordionItem key={i} value={`right-${i}`}>
								<AccordionTrigger>
									{item.question}
								</AccordionTrigger>
								<AccordionContent className="text-foreground">
									{item.answer}
								</AccordionContent>
							</AccordionItem>
						))}
					</Accordion>
				</div>
			</div>
		</section>
	);
};
