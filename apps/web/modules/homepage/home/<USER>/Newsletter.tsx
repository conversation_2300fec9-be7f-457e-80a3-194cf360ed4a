"use client";

import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { ArrowRight, CheckCircle2, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import type React from "react";
import { useState } from "react";

export function Newsletter() {
	const t = useTranslations();
	const [email, setEmail] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSuccess, setIsSuccess] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);
		setError(null);

		try {
			// Basic email validation
			if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
				throw new Error("Please enter a valid email address");
			}

			const response = await fetch("/api/early-access", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ email }),
			});

			const data = await response.json();

			if (!data.success) {
				throw new Error(data.message || "Failed to subscribe");
			}

			setIsSuccess(true);
			setEmail("");
		} catch (err) {
			setError(
				err instanceof Error
					? err.message
					: "Something went wrong. Please try again.",
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<section
			id="earlyAccess"
			className="w-full py-12 md:py-24 lg:py-32 bg-zinc-50 dark:bg-sidebar"
		>
			<div className="container px-4 md:px-6">
				<div className="flex flex-col items-center justify-center space-y-4 text-center">
					<div className="space-y-2">
						<h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
							{t("newsletter.title")}
						</h2>
						<p className="max-w-[700px] text-zinc-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-zinc-400">
							{t("newsletter.subtitle")}
						</p>
					</div>
					<div className="w-full max-w-sm space-y-2">
						{isSuccess ? (
							<div className="flex items-center justify-center space-x-2 text-emerald-600 dark:text-emerald-500">
								<CheckCircle2 className="h-5 w-5" />
								<span>{t("newsletter.waitlist.success")}</span>
							</div>
						) : (
							<form
								className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2"
								onSubmit={handleSubmit}
							>
								<div className="flex items-start">
									<Input
										type="email"
										required
										value={email}
										onChange={(e) =>
											setEmail(e.target.value)
										}
										placeholder={t("newsletter.email")}
									/>

									<Button
										type="submit"
										variant="primary"
										className="ml-4"
										loading={isSubmitting}
									>
										{t("newsletter.submit")}
									</Button>
								</div>
							</form>
						)}
						{error && (
							<p className="text-sm text-red-500">{error}</p>
						)}
					</div>
				</div>
			</div>
		</section>
	);
}
