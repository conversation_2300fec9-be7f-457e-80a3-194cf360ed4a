"use client";

import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";

const ITEMS = [
	{
		name: "CBRE",
		src: "/images/logos/cbre.png",
		width: 143,
		height: 28,
		href: "https://cbre.com",
	},
	{
		name: "<PERSON>s",
		src: "/images/logos/colliers.png",
		width: 154,
		height: 28,
		href: "https://colliers.com",
	},
	{
		name: "Remax",
		src: "/images/logos/remax.png",
		width: 113,
		height: 28,
		href: "https://remax.com",
	},
	{
		name: "<PERSON><PERSON><PERSON>",
		src: "/images/logos/cw.png",
		width: 112,
		height: 28,
		href: "https://cushmanwakefield.com",
	},
	{
		name: "Exp Realty",
		src: "/images/logos/exp.png",
		width: 141,
		height: 28,
		href: "https://exprealty.com",
	},
	{
		name: "<PERSON><PERSON>",
		src: "/images/logos/jll.png",
		width: 104,
		height: 28,
		href: "https://jll.com",
	},
	{
		name: "<PERSON>",
		src: "/images/logos/kw.png",
		width: 105,
		height: 28,
		href: "https://kellerwilliams.com",
	},
	{
		name: "Marcus Millichap",
		src: "/images/logos/mm.jpeg",
		width: 128,
		height: 28,
		href: "https://marcusmillichap.com",
	},
	{
		name: "Newmark",
		src: "/images/logos/newmark.png",
		width: 90,
		height: 28,
		href: "https://www.nmrk.com/",
	},
];

export default function Logos() {
	const t = useTranslations("home.logos");

	return (
		<section className="bg-sand-100 overflow-hidden py-12 md:py-20 lg:py-24">
			<div className="container text-center">
				<h2 className="text-xl font-semibold tracking-tight text-balance lg:text-3xl">
					{t("title")}
					<br />
					<span className="text-muted-foreground">
						{t("description")}
					</span>
				</h2>
			</div>

			<div className="relative mt-10">
				<div className="flex w-full">
					{/* First marquee group */}
					<div className="animate-marquee flex shrink-0 items-center gap-12">
						{ITEMS.map((logo, index) => (
							<Link
								href={logo.href}
								target="_blank"
								key={index}
								className="p-6"
							>
								<Image
									src={logo.src}
									alt={logo.name}
									width={logo.width}
									height={logo.height}
									className="h-8 w-auto object-contain transition-opacity hover:opacity-70 grayscale hover:grayscale-0"
								/>
							</Link>
						))}
					</div>
					{/* Second marquee group */}
					<div className="animate-marquee flex shrink-0 items-center gap-12">
						{ITEMS.map((logo, index) => (
							<Link
								href={logo.href}
								target="_blank"
								key={index}
								className="p-6"
							>
								<Image
									src={logo.src}
									alt={logo.name}
									width={logo.width}
									height={logo.height}
									className="h-8 w-auto object-contain transition-opacity hover:opacity-70 grayscale hover:grayscale-0"
								/>
							</Link>
						))}
					</div>
				</div>
			</div>
		</section>
	);
}
