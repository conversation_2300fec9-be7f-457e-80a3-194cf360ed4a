// apps/web/modules/marketing/home/<USER>/Pricing.tsx
"use client";

import { config } from "@repo/config";
import { useLocaleCurrency } from "@shared/hooks/locale-currency";
import { CountingNumber } from "@ui/components/counting-number";
import { AnimatePresence, motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useState } from "react";

type Plan = {
	isFree?: boolean;
	recommended?: boolean;
	isEnterprise?: boolean;
	prices?: Array<{
		type: string;
		productId: string;
		interval?: "month" | "year";
		amount: number;
		currency: string;
		seatBased?: boolean;
		trialPeriodDays?: number;
	}>;
};

import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { Tabs, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import { Check, Star } from "lucide-react";

export default function Pricing({
	headerTag = "h2",
}: {
	headerTag?: "h1" | "h2";
}) {
	const t = useTranslations();
	const [isAnnual, setIsAnnual] = useState(true);
	const localeCurrency = useLocaleCurrency();
	const plans = config.payments.plans;

	const getFeaturesForPlan = (planId: string) => {
		// Define features for each plan
		const planFeatures: Record<string, string[]> = {
			free: [
				"Single member",
				"Unlimited tasks",
				"500 contacts",
				"1000 properties",
				"1 pipeline",
			],
			growth: [
				"All free plan features and...",
				"Unlimited contacts",
				"Unlimited properties",
				"Unlimited pipelines",
				"Relio AI",
				"Organization collaboration and sharing",
				"Relio Insights",
				"Admin roles",
			],
			enterprise: [
				"All growth plan features and...",
				"Priority support",
				"Custom integrations",
				"Dedicated account manager",
				"SLA 99.9% uptime",
				"Unlimited members",
			],
		};

		return planFeatures[planId] || [];
	};

	const filteredPlans = Object.entries(plans).map(([planId, planData]) => ({
		id: planId,
		...(planData as Plan),
	}));

	return (
		<section>
			<div className="container">
				<div className="mx-auto max-w-6xl space-y-4 text-center">
					<div className="flex justify-center">
						<Tabs
							value={isAnnual ? "yearly" : "monthly"}
							onValueChange={(value) =>
								setIsAnnual(value === "yearly")
							}
							className="w-fit"
						>
							<TabsList>
								<TabsTrigger value="monthly">
									{t("pricing.monthly")}
								</TabsTrigger>
								<TabsTrigger value="yearly">
									{t("pricing.yearly")}
								</TabsTrigger>
							</TabsList>
						</Tabs>
					</div>
				</div>

				<div className="mt-8 grid gap-8 sm:grid-cols-2 md:mt-12 lg:mt-20 lg:grid-cols-3">
					{filteredPlans.map((plan) => {
						const planId = plan.id;
						const price = plan.prices?.find(
							(price) =>
								price.currency === localeCurrency &&
								(isAnnual
									? price.interval === "year"
									: price.interval === "month"),
						);

						const features = getFeaturesForPlan(planId);

						return (
							<div
								key={planId}
								className={cn(
									plan.recommended &&
										"from-mint/70 to-sand-100 scale-[1.075] rounded-3xl bg-linear-to-b p-3",
								)}
							>
								<Card
									className={cn(
										"h-full border-none bg-zinc-100 dark:bg-sidebar relative",
										plan.recommended &&
											"bg-background ring-2 ring-black",
									)}
								>
									{plan.recommended && !plan.isEnterprise && (
										<div className="absolute -top-3 left-1/2 -translate-x-1/2">
											<div className="flex items-center gap-1.5 rounded-full bg-primary px-3 py-1 text-xs font-semibold text-primary-foreground">
												<Star className="size-3 fill-current" />
												{t("pricing.recommended")}
											</div>
										</div>
									)}
									<CardHeader>
										<h3
											className={cn(
												"text-2xl font-semibold",
												plan.recommended && "mt-2",
											)}
										>
											{planId === "free"
												? t(
														"pricing.products.free.title",
													)
												: planId === "growth"
													? t(
															"pricing.products.growth.title",
														)
													: t(
															"pricing.products.enterprise.title",
														)}
										</h3>
										<div className="mt-2">
											{plan.isFree ? (
												<p className="text-muted-foreground text-lg font-medium">
													{t(
														"pricing.products.free.title",
													)}
												</p>
											) : plan.isEnterprise ? (
												<p className="text-muted-foreground text-lg font-medium">
													{t("pricing.contactSales")}
												</p>
											) : price ? (
												<div className="relative min-h-[5rem]">
													<AnimatePresence mode="wait">
														<motion.div
															key={`${price.amount}-${isAnnual ? "yearly" : "monthly"}`}
															className="flex flex-col items-start"
															initial={{
																y: isAnnual
																	? 10
																	: -10,
																opacity: 0,
															}}
															animate={{
																y: 0,
																opacity: 1,
															}}
															exit={{
																y: isAnnual
																	? -10
																	: 10,
																opacity: 0,
															}}
															transition={{
																duration: 0.15,
															}}
														>
															<div className="flex items-baseline gap-1">
																<span className="text-primary text-2xl -mb-1">
																	$
																</span>
																<CountingNumber
																	number={
																		price.amount
																	}
																	decimalPlaces={
																		0
																	}
																	className="text-5xl text-primary font-bold leading-none"
																	transition={{
																		duration: 0.4,
																		stiffness: 300,
																		damping: 25,
																		mass: 0.6,
																	}}
																/>
																{isAnnual && (
																	<Badge
																		status="feature"
																		className="ml-2 text-[10px] font-mono"
																	>
																		{Math.round(
																			(1 -
																				790 /
																					(79 *
																						12)) *
																				100,
																		)}
																		% off
																	</Badge>
																)}
															</div>
															<div className="text-muted-foreground text-[10px] font-medium mt-1 ml-1 font-mono">
																per user/month,
																billed{" "}
																{isAnnual
																	? "annually"
																	: "monthly"}
															</div>
														</motion.div>
													</AnimatePresence>
												</div>
											) : (
												<p className="text-muted-foreground text-lg font-medium">
													{t("pricing.contactSales")}
												</p>
											)}
										</div>
									</CardHeader>
									<CardContent className="flex flex-col space-y-6">
										<Button
											variant={
												plan.isEnterprise
													? "outline"
													: plan.recommended
														? "primary"
														: "outline"
											}
											size="lg"
											className={cn(
												"w-full transition-colors",
												!plan.recommended &&
													"bg-background hover:bg-muted text-foreground border border-border",
												plan.recommended &&
													"bg-primary hover:bg-primary/90 text-primary-foreground",
											)}
											asChild={plan.isEnterprise}
										>
											{plan.isEnterprise ? (
												<a href="/#earlyAccess">
													{t("pricing.contactSales")}
												</a>
											) : plan.isFree ? (
												<a href="/#earlyAccess">
													{t("pricing.getStarted")}
												</a>
											) : plan.recommended &&
												plan.prices?.[0]
													?.trialPeriodDays ? (
												<a href="/#earlyAccess">
													{t("pricing.trialPeriod", {
														days: plan.prices[0]
															.trialPeriodDays,
													})}
												</a>
											) : (
												t("pricing.choosePlan")
											)}
										</Button>

										<div className="space-y-3.5">
											{plan.isFree && (
												<div className="text-accent-foreground text-xs mb-2 font-mono">
													For single users
												</div>
											)}
											{plan.recommended && (
												<div className="text-accent-foreground text-xs mb-2 font-mono">
													For growing teams
												</div>
											)}
											{plan.isEnterprise && (
												<div className="text-accent-foreground text-xs mb-2 font-mono">
													For large organizations
												</div>
											)}
											{features.map((feature, index) => (
												<div
													key={index}
													className="flex items-start gap-3"
												>
													<IconSquareRoundedCheckFilled className="size-4 shrink-0 mt-0.5 text-primary/50" />
													<span className="text-foreground text-sm leading-5">
														{feature}
													</span>
												</div>
											))}
										</div>
									</CardContent>
								</Card>
							</div>
						);
					})}
				</div>
			</div>
		</section>
	);
}
