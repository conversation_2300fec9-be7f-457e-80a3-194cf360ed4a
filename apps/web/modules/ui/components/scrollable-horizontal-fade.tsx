import { cn } from "@ui/lib";
import { useEffect, useRef, useState } from "react";

interface ScrollableHorizontalFadeProps {
	children: React.ReactNode;
	className?: string;
}

export function ScrollableHorizontalFade({
	children,
	className,
}: ScrollableHorizontalFadeProps) {
	const scrollRef = useRef<HTMLDivElement>(null);
	const [showLeftFade, setShowLeftFade] = useState(false);
	const [showRightFade, setShowRightFade] = useState(false);

	const checkScroll = () => {
		const element = scrollRef.current;
		if (!element) return;

		const { scrollLeft, scrollWidth, clientWidth } = element;

		setShowLeftFade(scrollLeft > 0);
		setShowRightFade(scrollLeft < scrollWidth - clientWidth - 1);
	};

	useEffect(() => {
		const element = scrollRef.current;
		if (!element) return;

		checkScroll();
		element.addEventListener("scroll", checkScroll);

		const resizeObserver = new ResizeObserver(checkScroll);
		resizeObserver.observe(element);

		return () => {
			element.removeEventListener("scroll", checkScroll);
			resizeObserver.disconnect();
		};
	}, [children]);

	return (
		<div className={cn("relative", className)}>
			{/* Left fade */}
			{showLeftFade && (
				<div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-background to-transparent z-10 pointer-events-none" />
			)}

			{/* Right fade */}
			{showRightFade && (
				<div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-background to-transparent z-10 pointer-events-none" />
			)}

			{/* Scrollable content */}
			<div
				ref={scrollRef}
				className="flex gap-3 overflow-x-auto scrollbar-hide"
				style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
			>
				{children}
			</div>
		</div>
	);
}
