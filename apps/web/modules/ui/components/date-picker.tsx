"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Calendar } from "@ui/components/calendar";
import {
	Command,
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { parseDate } from "chrono-node";
import * as React from "react";
import { Card, CardContent, CardFooter } from "./card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "./select";
import { IconCalendar } from "@tabler/icons-react";

function formatDate(date: Date | undefined) {
	if (!date) {
		return "";
	}

	return date.toLocaleDateString("en-US", {
		day: "2-digit",
		month: "long",
		year: "numeric",
	});
}

interface DatePickerProps {
	value?: Date;
	onChange?: (date: Date | undefined) => void;
	onClose?: () => void;
}

export function DatePicker({ value, onChange, onClose }: DatePickerProps) {
	const [open, setOpen] = React.useState(false);
	const [inputValue, setInputValue] = React.useState("In 2 days");
	const [internalDate, setInternalDate] = React.useState<Date | undefined>(
		parseDate("In 2 days") || undefined,
	);
	const [month, setMonth] = React.useState<Date | undefined>(
		value ?? internalDate,
	);
	const [selectedTime, setSelectedTime] = React.useState<string | null>(
		"10:00",
	);
	const [captionLayout, setCaptionLayout] =
		React.useState<React.ComponentProps<typeof Calendar>["captionLayout"]>(
			"dropdown",
		);

	// Use controlled or uncontrolled value
	const date = value !== undefined ? value : internalDate;

	// Generate time slots from 09:00 to 18:00 in 15 min increments
	const timeSlots = Array.from({ length: 37 }, (_, i) => {
		const totalMinutes = i * 15;
		const hour = Math.floor(totalMinutes / 60) + 9;
		const minute = totalMinutes % 60;
		return `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
	});

	return (
		<div className="!rounded-2xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 sm:max-w-3xl m-0 bg-clip-padding overflow-y-auto">
			<div
				data-slot="command-input-wrapper"
				className="flex !h-12 items-center justify-between w-full gap-2 border-b px-3"
			>
				<Input
					id="date"
					value={inputValue}
					placeholder="Tomorrow or next week"
					className="!border-none bg-background pr-10 border-b h-12 text-sm font-normal rounded-none shadow-none focus-visible:ring-0 focus-visible:border-primary"
					onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
						setInputValue(e.target.value);
						const parsed = parseDate(e.target.value);
						if (onChange) onChange(parsed || undefined);
						else setInternalDate(parsed || undefined);
						if (parsed) setMonth(parsed);
					}}
					onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
						if (e.key === "ArrowDown") {
							e.preventDefault();
							setOpen(true);
						}
					}}
				/>
				<Button
					id="date-picker"
					variant="ghost"
					size="icon"
					onClick={() => setOpen(true)}
				>
					<IconCalendar className="size-3.5 text-muted-foreground" />
					<span className="sr-only">Select date</span>
				</Button>
				<CommandDialog
					open={open}
					onOpenChange={(nextOpen) => {
						setOpen(nextOpen);
						if (!nextOpen && onClose) onClose();
					}}
					className="!rounded-2xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 sm:max-w-3xl m-0 bg-clip-padding overflow-y-auto"
				>
					<Command>
						<CommandList className="max-h-[438px] min-h-[438px] overflow-hidden no-scrollbar bg-secondary/20 px-1 pb-16">
							<div className="flex flex-col md:flex-row gap-0 p-2">
								<div className="flex-1 min-w-0 border-b md:border-b-0 md:border-r border-border flex flex-col items-center justify-center">
									<Calendar
										mode="single"
										selected={date}
										onSelect={(selected) => {
											if (onChange) onChange(selected);
											else setInternalDate(selected);
											if (selected) setMonth(selected);
										}}
										defaultMonth={month}
										onMonthChange={setMonth}
										showOutsideDays={false}
										className="bg-transparent p-0 [--cell-size:--spacing(10)] md:[--cell-size:--spacing(12)]"
										formatters={{
											formatWeekdayName: (date: Date) => {
												return date.toLocaleString(
													"en-US",
													{ weekday: "short" },
												);
											},
										}}
										captionLayout={captionLayout}
									/>
								</div>
								<div className="md:w-56 flex-shrink-0 border-t md:border-t-0 md:border-l border-border flex flex-col">
									<div className="grid gap-2 p-6 max-h-[370px] overflow-y-auto">
										{timeSlots.map((time) => (
											<CommandItem
												key={time}
												value={time}
												onSelect={() =>
													setSelectedTime(time)
												}
												className={`w-full shadow-none ${selectedTime === time ? "bg-primary text-primary-foreground" : ""}`}
											>
												{time}
											</CommandItem>
										))}
									</div>
								</div>
							</div>
							<CommandEmpty>No times found.</CommandEmpty>
						</CommandList>
						<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
							<div className="flex items-center gap-2">
								<span className="text-xs text-muted-foreground">
									Select a date and time for your meeting.
								</span>
							</div>
							<div className="flex items-center gap-2">
								<Button
									disabled={!date || !selectedTime}
									className="w-full md:ml-auto md:w-auto"
									variant="outline"
									onClick={() => setOpen(false)}
								>
									Continue
								</Button>
							</div>
						</div>
					</Command>
				</CommandDialog>
			</div>

			<div className="text-muted-foreground text-xs px-4 py-2 text-right bg-secondary/50">
				Type natural language like "tomorrow" or "next week" to quickly
				set the date.
			</div>
		</div>
	);
}

export function DatePickerAlternative({ value, onChange, onClose }: DatePickerProps) {
	const [open, setOpen] = React.useState(false);
	const [inputValue, setInputValue] = React.useState("In 2 days");
	const [internalDate, setInternalDate] = React.useState<Date | undefined>(
		parseDate("In 2 days") || undefined,
	);
	const [month, setMonth] = React.useState<Date | undefined>(
		value ?? internalDate,
	);
	const [selectedTime, setSelectedTime] = React.useState<string | null>(
		"10:00",
	);
	const [captionLayout, setCaptionLayout] =
		React.useState<React.ComponentProps<typeof Calendar>["captionLayout"]>(
			"dropdown",
		);

	// Use controlled or uncontrolled value
	const date = value !== undefined ? value : internalDate;

	// Generate time slots from 09:00 to 18:00 in 15 min increments
	const timeSlots = Array.from({ length: 37 }, (_, i) => {
		const totalMinutes = i * 15;
		const hour = Math.floor(totalMinutes / 60) + 9;
		const minute = totalMinutes % 60;
		return `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
	});

	return (
		<div>
			<div
				data-slot="command-input-wrapper"
				className="flex items-center justify-between w-full gap-2 border border-border rounded-lg"
			>
				<Input
					id="date"
					value={inputValue}
					placeholder="Tomorrow or next week"
					className="!border-none bg-background pr-10 border-b h-12 text-sm font-normal rounded-none shadow-none focus-visible:ring-0 focus-visible:border-primary"
					onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
						setInputValue(e.target.value);
						const parsed = parseDate(e.target.value);
						if (onChange) onChange(parsed || undefined);
						else setInternalDate(parsed || undefined);
						if (parsed) setMonth(parsed);
					}}
					onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
						if (e.key === "ArrowDown") {
							e.preventDefault();
							setOpen(true);
						}
					}}
				/>
				<Button
					id="date-picker"
					variant="ghost"
					size="icon"
					type="button"
					onClick={() => setOpen(true)}
				>
					<IconCalendar className="size-3.5 text-muted-foreground" />
					<span className="sr-only">Select date</span>
				</Button>
				<CommandDialog
					open={open}
					onOpenChange={(nextOpen) => {
						setOpen(nextOpen);
						if (!nextOpen && onClose) onClose();
					}}
					className="!rounded-2xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 sm:max-w-3xl m-0 bg-clip-padding overflow-y-auto"
				>
					<Command>
						<CommandList className="max-h-[438px] min-h-[438px] overflow-hidden no-scrollbar bg-secondary/20 px-1 pb-16">
							<div className="flex flex-col md:flex-row gap-0 p-2">
								<div className="flex-1 min-w-0 border-b md:border-b-0 md:border-r border-border flex flex-col items-center justify-center">
									<Calendar
										mode="single"
										selected={date}
										onSelect={(selected) => {
											if (onChange) onChange(selected);
											else setInternalDate(selected);
											if (selected) setMonth(selected);
										}}
										defaultMonth={month}
										onMonthChange={setMonth}
										showOutsideDays={false}
										className="bg-transparent p-0 [--cell-size:--spacing(10)] md:[--cell-size:--spacing(12)]"
										formatters={{
											formatWeekdayName: (date: Date) => {
												return date.toLocaleString(
													"en-US",
													{ weekday: "short" },
												);
											},
										}}
										captionLayout={captionLayout}
									/>
								</div>
								<div className="md:w-56 flex-shrink-0 border-t md:border-t-0 md:border-l border-border flex flex-col">
									<div className="grid gap-2 p-6 max-h-[370px] overflow-y-auto">
										{timeSlots.map((time) => (
											<CommandItem
												key={time}
												value={time}
												onSelect={() =>
													setSelectedTime(time)
												}
												className={`w-full shadow-none ${selectedTime === time ? "bg-primary text-primary-foreground" : ""}`}
											>
												{time}
											</CommandItem>
										))}
									</div>
								</div>
							</div>
							<CommandEmpty>No times found.</CommandEmpty>
						</CommandList>
						<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
							<div className="flex items-center gap-2">
								<span className="text-xs text-muted-foreground">
									Select a date and time for your meeting.
								</span>
							</div>
							<div className="flex items-center gap-2">
								<Button
									disabled={!date || !selectedTime}
									className="w-full md:ml-auto md:w-auto"
									variant="outline"
									onClick={() => setOpen(false)}
								>
									Continue
								</Button>
							</div>
						</div>
					</Command>
				</CommandDialog>
			</div>
		</div>
	);
}
