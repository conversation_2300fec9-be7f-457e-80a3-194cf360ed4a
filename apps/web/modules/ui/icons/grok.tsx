import type { SVGProps } from "react";

const Icon = (props: SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width={64}
		height={64}
		viewBox="0 0 64 64"
		fill="currentColor"
		{...props}
	>
		<g clipPath="url(#grok)">
			<path
				fillRule="evenodd"
				d="m24.72 40.773 21.275-15.725c1.042-.773 2.533-.472 3.032.725 2.613 6.318 1.445 13.907-3.76 19.118-5.203 5.21-12.446 6.352-19.064 3.749l-7.23 3.352c10.371 7.096 22.963 5.341 30.832-2.541 6.243-6.251 8.176-14.771 6.368-22.454l.016.019c-2.621-11.285.646-15.797 7.334-25.021q.24-.327.477-.662l-8.803 8.814v-.027L24.712 40.779m-4.384 3.816c-7.445-7.12-6.16-18.136.19-24.491 4.695-4.701 12.391-6.621 19.109-3.8l7.213-3.333c-1.5-1.103-3.14-2-4.877-2.667a23.93 23.93 0 0 0-26.006 5.243c-6.754 6.762-8.88 17.162-5.232 26.037 2.726 6.632-1.741 11.323-6.24 16.059C2.888 59.323 1.288 61 0 62.776l20.32-18.173"
				clipRule="evenodd"
			/>
		</g>
		<defs>
			<clipPath id="grok">
				<path fill="#fff" d="M0 0h64v64H0z" />
			</clipPath>
		</defs>
	</svg>
);
export default Icon;
