export const ContactIcon = ({ className }: { className?: string }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="14"
		height="14"
		viewBox="0 0 14 14"
		fill="none"
		className={className}
	>
		<g clipPath="url(#clip0_contact)">
			<rect y="0" width="14" height="14" rx="7" fill="#E4E7EB" />
			<path
				d="M8.29452 9.5H5.70548C5.41148 9.5 5.26449 9.5 5.14055 9.51094C3.74118 9.63168 2.63144 10.7414 2.51069 12.1408C2.5 12.2647 2.5 12.4117 2.5 12.7057C2.5 12.8245 2.5 12.8838 2.50432 12.9339C2.55308 13.499 3.00125 13.9472 3.56637 13.9959C3.61643 14.0002 3.67579 14.0002 3.79452 14.0002H10.2055C10.3242 14.0002 10.3836 14.0002 10.4336 13.9959C10.9988 13.9472 11.4469 13.499 11.4957 12.9339C11.5 12.8838 11.5 12.8245 11.5 12.7057C11.5 12.4117 11.5 12.2647 11.4893 12.1408C11.3686 10.7414 10.2588 9.63168 8.85945 9.51094C8.73551 9.5 8.58852 9.5 8.29452 9.5Z"
				fill="#6B7280"
			/>
			<circle cx="7" cy="5.5" r="2.5" fill="#6B7280" />
		</g>
		<rect
			x="0.5"
			y="0.5"
			width="13"
			height="13"
			rx="6.5"
			stroke="currentColor"
			strokeOpacity="0.1"
		/>
		<defs>
			<clipPath id="clip0_contact">
				<rect y="0" width="14" height="14" rx="7" fill="white" />
			</clipPath>
		</defs>
	</svg>
);