import type { Messages } from "@repo/i18n";
import type { JSX as Jsx } from "react/jsx-runtime";

// temporary fix for mdx types and JSX with React 19
// TODO: remove once mdx and other packages have full compatibility with react 19
declare global {
	namespace JSX {
		type ElementClass = Jsx.ElementClass;
		type Element = Jsx.Element;
		type IntrinsicElements = Jsx.IntrinsicElements;
	}
}

// Override React types to fix React 19 compatibility issues
declare global {
	namespace React {
		// Override ReactNode to accept any type for React 19 compatibility
		// biome-ignore lint/suspicious/noExplicitAny: Required for React 19 compatibility
		type ReactNode = any;
	}

	// Override JSX types to be more permissive
	namespace JSX {
		// Allow any element to return any type
		// biome-ignore lint/suspicious/noExplicitAny: Required for React 19 compatibility
		type Element = any;
	}
}

declare global {
	interface IntlMessages extends Messages {}
}
