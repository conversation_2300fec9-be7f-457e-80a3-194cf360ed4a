---
title: <PERSON><PERSON> wins Best Start Up
date: 2025-06-11
image: /images/blog/convex-hackathon.png
authorName: <PERSON>
authorImage: /images/blog/bobby.png
excerpt: Celebrating the incredible projects and winners from the second Convex Devpost hackathon
tags: [convex, devpost, hackathon]
published: true
---

<Alert>
  We're beyond thrilled to announce the winners of Convex's second hackathon on DevPost! After reviewing hundreds of incredible submissions, we're honored to recognize the most innovative and impactful projects built on Convex.
</Alert>

The second Convex hackathon on Devpost has come to a close, and we're still reeling from the sheer volume of creativity and innovation on display. From startups tackling real estate and job listings to AI-powered tools for developers, the submissions showcased the versatility and power of Convex.

We were blown away by the caliber of projects submitted, and we're thrilled to see the Convex community growing and pushing the boundaries of what's possible. After careful consideration, our judges have selected the winners of the hackathon, and we're excited to share the results with you!

## Winners and Prizes

### Best Fullstack App (Overall)

![fireview](/images/blog/fireview.png)

Winner: Fireview [live app](https://fireview.dev/)

Description: Notion for your application data

Why we chose it: One of our judges responses was "amazing" and "one of the more complex use of Convex I've seen". The timeline for Fireview works as they made more than a significant amount of changes to their app at the start of the hackathon. We're fans of Fireview and the problems it solves for developers. Great job team!

Check it out their [demo](https://www.youtube.com/watch?v=XjH9xoj0mtc) and submission [here](https://convexhackathon2.devpost.com/submissions/551936-fireview).

### Best Startup Idea

Winner: Relio CRM [live app](https://reliocrm.com)

Description: Relio CRM and it's a tool based for commercial real estate brokers. Talk to your data with AI and collaborate with your brokers and agents in real time.

Why we chose it: There are CRMs, and then there are CRMs built on Convex for markets with massive potential. That's like putting ice cream on cake—it's got all the ingredients for a winning startup! Relio CRM taps into Convex features like auth on Next.js, indexes, vector search, and relationships to bring their idea to life.

Check it out their [demo](https://www.youtube.com/watch?v=B9se3lla30E) and submission [here](https://devpost.com/software/aibase).


### Best Use of AI with Convex in your App

Winner: AIbase [live app](https://www.myaibase.com/)

Description: Never rewrite your prompts again, build a library of custom prompted AI helpers and quickly use them anywhere.

Why we chose it: AIbase stood out as one of the most polished full-stack apps submitted, making great use of AI with Convex. What better way to showcase AI with Convex than by building an app designed to save time on prompting? On top of that, Aibase made excellent use of Convex’s features—auth, schema, relationships, and indexes—all seamlessly integrated. Impressive work!

Check it out their [demo](https://www.youtube.com/watch?v=hIvibvkEdWg) and submission [here](https://devpost.com/software/aibase).

### Most Viral App on X or LinkedIn

Winner: DirectReach AI [live app](https://direct-reach-8h7m.vercel.app/)

Description: Unlock Recruiter Connections Instantly – Generate Polished AI Emails and Land Your Dream Job 10x Faster!

Why we chose DirectReach AI : Going viral is no easy feat these days, but Nishant and the DirectReach AI team pulled it off by building a much needed platform that helps job seekers connect with recruiters using AI and Convex. What's viral, you ask? Oh, just [200k+ impressions](https://www.linkedin.com/posts/nishantkumar_ai-ai-generated-emails-activity-7004364517413439488-JLZG?utm_source=linkedin_share&utm_medium=member_desktop_web) and [2000+ engagements](https://www.linkedin.com/posts/nishantkumar_ai-ai-generated-emails-activity-7004364517413439488-JLZG?utm_source=linkedin_share&utm_medium=member_desktop_web). Mind = blown, Nishant!

Check it out their [demo](https://www.youtube.com/watch?v=4vWXXuTAtGo) and submission [here](https://devpost.com/software/directreach-ai).

## Convex Hackathon Judges

<div className="grid gap-4 md:grid-cols-2">
  {[
    { name: 'Anjana Vakil', role: 'Independent Developer & Educator' },
    { name: 'Ian Macartney', role: 'Developer Experience Engineer at Convex' },
    { name: 'Jamie Turner', role: 'Co-Founder / CEO at Convex' },
    { name: 'Shawn Erquhart', role: 'Product Engineer' },
    { name: 'Sunny Rochiramani', role: 'VP / Head of Engineering at Descript' },
    { name: 'Tom Redman', role: 'Head of DX at Convex' },
    { name: 'Wayne Sutton', role: 'Head of Community and Events at Convex' }
  ].map((judge) => (
    <div
      key={judge.name}
      className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm"
    >
      <h4 className="font-semibold">{judge.name}</h4>
      <p className="text-sm text-muted-foreground">{judge.role}</p>
    </div>
  ))}
</div>

<Alert variant="primary" className="mt-4">
  The Relio team is incredibly grateful to all participants for their commitment and feedback throughout the hackathon. We hope to see you in our Discord as you build your apps or launch new ones with Relio!
</Alert>

<p className="text-center flex flex-col gap-2">
  <a href="https://convex.dev" target="_blank" rel="noopener noreferrer" className="text-link">Learn more about Convex</a>
  {" "}
  <a href="https://convexhackathon2.devpost.com/?_gl=1*ic21hn*_gcl_au*MTA5OTEyNjU0Ni4xNzQ5Njg4MDgy*_ga*MTk0MjMxNTM1Ni4xNzQ5Njg4MDgy*_ga_0YHJK3Y10M*czE3NDk2ODgwODIkbzEkZzEkdDE3NDk2ODgwODckajU1JGwwJGhw" target="_blank" rel="noopener noreferrer" className="text-link">See the Convex Hackathon 2 submissions</a>
</p>
