"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	STORAGE_KEYS,
	getLocalStorage,
	setLocalStorage,
} from "@app/shared/lib/local-storage";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
	type ColumnDef,
	type ColumnFiltersState,
	getCoreRowModel,
	getFacetedRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	type SortingState,
	useReactTable,
	type VisibilityState,
} from "@tanstack/react-table";
import type { Filter } from "@ui/components/filters";
import React, { useEffect, useMemo, useState } from "react";
import FileList from "./list";
import { columns } from "./list/_components/Columns";

interface File {
	id: string;
	name: string;
	originalName: string;
	size: number;
	mimeType: string;
	fileType: string;
	extension?: string;
	url: string;
	thumbnailUrl?: string;
	folderId: string | null;
	isPublic: boolean;
	isDeleted: boolean;
	createdAt: string;
	updatedAt: string;
	uploader: {
		id: string;
		name: string;
		image?: string;
	};
	folder?: {
		id: string;
		name: string;
	};
}

interface Folder {
	id: string;
	name: string;
	parentId: string | null;
	isPublic: boolean;
	createdAt: string;
	creator: {
		id: string;
		name: string;
		image?: string;
	};
	_count: {
		files: number;
		children: number;
	};
}

const Files = () => {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	// State management
	const [isMounted, setIsMounted] = useState(false);
	const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
	const [viewMode, setViewMode] = useState<"list" | "grid">("list");
	const [searchQuery, setSearchQuery] = useState("");

	// Table state
	const [sorting, setSorting] = useState<SortingState>([]);
	const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
	const [rowSelection, setRowSelection] = useState({});
	const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
	const [filters, setFilters] = useState<Filter[]>([]);

	// Fetch files
	const { data: files = [], isLoading: isFilesLoading } = useQuery<File[]>({
		queryKey: ["files", activeOrganization?.id, currentFolderId, searchQuery],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const params = new URLSearchParams({
				organizationId: activeOrganization.id,
			});
			if (currentFolderId) {
				params.append("folderId", currentFolderId);
			} else {
				params.append("folderId", "null");
			}
			if (searchQuery) {
				params.append("search", searchQuery);
			}
			const response = await fetch(`/api/files?${params}`);
			if (!response.ok) throw new Error("Failed to fetch files");
			return response.json();
		},
		enabled: !!activeOrganization?.id,
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		refetchOnWindowFocus: false,
		refetchOnMount: false,
	});

	// Fetch folders
	const { data: folders = [], isLoading: isFoldersLoading } = useQuery<Folder[]>({
		queryKey: ["folders", activeOrganization?.id, currentFolderId],
		queryFn: async () => {
			if (!activeOrganization?.id) return [];
			const params = new URLSearchParams({
				organizationId: activeOrganization.id,
			});
			if (currentFolderId) {
				params.append("parentId", currentFolderId);
			} else {
				params.append("parentId", "null");
			}
			const response = await fetch(`/api/folders?${params}`);
			if (!response.ok) throw new Error("Failed to fetch folders");
			return response.json();
		},
		enabled: !!activeOrganization?.id,
		staleTime: 2 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
		refetchOnWindowFocus: false,
		refetchOnMount: false,
	});

	// Combine files and folders for table display
	const tableData = useMemo(() => {
		const folderItems = folders.map(folder => ({
			...folder,
			type: "folder" as const,
			size: 0,
			mimeType: "folder",
			fileType: "folder",
		}));
		
		const fileItems = files.map(file => ({
			...file,
			type: "file" as const,
		}));

		return [...folderItems, ...fileItems];
	}, [files, folders]);

	// Load preferences from localStorage
	useEffect(() => {
		const storedViewMode = getLocalStorage("files_view_mode");
		if (storedViewMode === "list" || storedViewMode === "grid") {
			setViewMode(storedViewMode);
		}
	}, []);

	// Save preferences to localStorage
	useEffect(() => {
		setLocalStorage("files_view_mode", viewMode);
	}, [viewMode]);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	const table = useReactTable({
		data: tableData as unknown as any[],
		columns: columns(filters, handleFilesUpdated) as ColumnDef<any>[],
		state: {
			sorting,
			columnVisibility,
			rowSelection,
			columnFilters,
		},
		enableRowSelection: true,
		enableSorting: true,
		enableMultiSort: false,
		onRowSelectionChange: setRowSelection,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
	});

	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		if (tableData !== undefined && isMounted) {
			setIsLoading(false);
		}
	}, [tableData, isMounted]);

	useEffect(() => {
		if (table && isMounted) {
			const selectedRows = table.getFilteredSelectedRowModel().rows;
			const selectedFilesData = selectedRows.map(
				(row) => row.original as any,
			);
			setSelectedFiles(selectedFilesData);
		}
	}, [rowSelection, table, isMounted]);

	const handleFolderChange = (folderId: string | null) => {
		setCurrentFolderId(folderId);
	};

	const handleViewModeChange = (mode: "list" | "grid") => {
		setViewMode(mode);
	};

	const handleFilesUpdated = () => {
		queryClient.invalidateQueries({
			queryKey: ["files", activeOrganization?.id],
		});
		queryClient.invalidateQueries({
			queryKey: ["folders", activeOrganization?.id],
		});
	};

	if (!isMounted) {
		return null;
	}

	return (
		<>
			<div className="border-b border-muted p-2 w-full flex justify-between items-center">
				<div className="flex items-center gap-4">
					<h1 className="text-lg font-semibold">Files</h1>
					{/* TODO: Add breadcrumb navigation */}
				</div>
				<div className="flex justify-end space-x-2 flex-row items-center">
					{/* TODO: Add file management buttons */}
				</div>
			</div>

			<div className="pt-4">
				<FileList
					columns={columns(filters, handleFilesUpdated)}
					table={table}
					isLoading={isLoading}
					viewMode={viewMode}
					currentFolderId={currentFolderId}
					onViewModeChange={handleViewModeChange}
					onFolderChange={handleFolderChange}
					onFilesUpdated={handleFilesUpdated}
				/>
			</div>
		</>
	);
};

export default Files;
