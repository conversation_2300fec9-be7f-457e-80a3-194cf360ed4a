"use client";

import { But<PERSON> } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import {
	IconChevronDown,
	IconChevronUp,
	IconDotsHorizontal,
	IconDownload,
	IconEdit,
	IconExternalLink,
	IconFile,
	IconFolder,
	IconShare,
	IconTrash,
} from "@tabler/icons-react";
import type { ColumnDef } from "@tanstack/react-table";
import type { Filter } from "@ui/components/filters";
import { formatDistanceToNow } from "date-fns";
import React from "react";
import FileActions from "./FileActions";

interface FileItem {
	id: string;
	name: string;
	originalName?: string;
	size: number;
	mimeType: string;
	fileType: string;
	type: "file" | "folder";
	url?: string;
	thumbnailUrl?: string;
	isPublic: boolean;
	createdAt: string;
	uploader?: {
		id: string;
		name: string;
		image?: string;
	};
	creator?: {
		id: string;
		name: string;
		image?: string;
	};
	_count?: {
		files: number;
		children: number;
	};
}

const formatFileSize = (bytes: number) => {
	if (bytes === 0) return "0 Bytes";
	const k = 1024;
	const sizes = ["Bytes", "KB", "MB", "GB"];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const getFileIcon = (fileType: string, type: "file" | "folder") => {
	if (type === "folder") {
		return <IconFolder className="h-4 w-4 text-blue-500" />;
	}
	
	switch (fileType) {
		case "images":
			return "🖼️";
		case "documents":
			return "📄";
		case "videos":
			return "🎥";
		case "audio":
			return "🎵";
		case "archives":
			return "📦";
		default:
			return <IconFile className="h-4 w-4 text-muted-foreground" />;
	}
};

export const columns = (filters: Filter[], onFilesUpdated?: () => void): ColumnDef<FileItem>[] => [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && "indeterminate")
				}
				onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
				aria-label="Select all"
				className="translate-y-[2px]"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
				className="translate-y-[2px]"
			/>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "name",
		header: ({ column }) => {
			return (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					className="h-auto p-0 hover:bg-transparent"
				>
					Name
					{column.getIsSorted() === "asc" ? (
						<IconChevronUp className="ml-2 h-4 w-4" />
					) : column.getIsSorted() === "desc" ? (
						<IconChevronDown className="ml-2 h-4 w-4" />
					) : null}
				</Button>
			);
		},
		cell: ({ row }) => {
			const item = row.original;
			const isFolder = item.type === "folder";
			
			return (
				<div className="flex items-center gap-3">
					{item.thumbnailUrl && !isFolder ? (
						<img
							src={item.thumbnailUrl}
							alt={item.name}
							className="h-8 w-8 object-cover rounded border flex-shrink-0"
						/>
					) : (
						<div className="h-8 w-8 flex items-center justify-center flex-shrink-0">
							{getFileIcon(item.fileType, item.type)}
						</div>
					)}
					<div className="min-w-0 flex-1">
						<div className="font-medium truncate">{item.name}</div>
						{item.originalName && item.originalName !== item.name && (
							<div className="text-xs text-muted-foreground truncate">
								{item.originalName}
							</div>
						)}
						{isFolder && item._count && (
							<div className="text-xs text-muted-foreground">
								{item._count.files} files, {item._count.children} folders
							</div>
						)}
					</div>
				</div>
			);
		},
	},
	{
		accessorKey: "size",
		header: ({ column }) => {
			return (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					className="h-auto p-0 hover:bg-transparent"
				>
					Size
					{column.getIsSorted() === "asc" ? (
						<IconChevronUp className="ml-2 h-4 w-4" />
					) : column.getIsSorted() === "desc" ? (
						<IconChevronDown className="ml-2 h-4 w-4" />
					) : null}
				</Button>
			);
		},
		cell: ({ row }) => {
			const item = row.original;
			if (item.type === "folder") {
				return <span className="text-muted-foreground">—</span>;
			}
			return <span className="text-sm">{formatFileSize(item.size)}</span>;
		},
	},
	{
		accessorKey: "fileType",
		header: "Type",
		cell: ({ row }) => {
			const item = row.original;
			if (item.type === "folder") {
				return <span className="text-sm text-muted-foreground">Folder</span>;
			}
			return (
				<span className="text-sm capitalize">
					{item.fileType.replace(/s$/, "")}
				</span>
			);
		},
	},
	{
		accessorKey: "createdAt",
		header: ({ column }) => {
			return (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					className="h-auto p-0 hover:bg-transparent"
				>
					Created
					{column.getIsSorted() === "asc" ? (
						<IconChevronUp className="ml-2 h-4 w-4" />
					) : column.getIsSorted() === "desc" ? (
						<IconChevronDown className="ml-2 h-4 w-4" />
					) : null}
				</Button>
			);
		},
		cell: ({ row }) => {
			const item = row.original;
			return (
				<div className="text-sm">
					<div>{formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}</div>
					<div className="text-xs text-muted-foreground">
						by {item.uploader?.name || item.creator?.name || "Unknown"}
					</div>
				</div>
			);
		},
	},
	{
		accessorKey: "isPublic",
		header: "Visibility",
		cell: ({ row }) => {
			const item = row.original;
			return (
				<span
					className={cn(
						"inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
						item.isPublic
							? "bg-green-50 text-green-700 ring-1 ring-green-600/20"
							: "bg-gray-50 text-gray-700 ring-1 ring-gray-600/20"
					)}
				>
					{item.isPublic ? "Public" : "Private"}
				</span>
			);
		},
	},
	{
		id: "actions",
		enableHiding: false,
		cell: ({ row }) => {
			const item = row.original;
			return (
				<FileActions
					item={item}
					onFilesUpdated={onFilesUpdated}
				/>
			);
		},
	},
];
