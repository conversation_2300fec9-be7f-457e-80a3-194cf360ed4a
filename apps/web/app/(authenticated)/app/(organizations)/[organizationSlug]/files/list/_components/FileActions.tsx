"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	IconDotsHorizontal,
	IconDownload,
	IconEdit,
	IconExternalLink,
	IconShare,
	IconTrash,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import ShareFileModal from "@app/files/components/ShareFileModal";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";

interface FileActionsProps {
	item: {
		id: string;
		name: string;
		type: "file" | "folder";
		url?: string;
		isPublic: boolean;
	};
	onFilesUpdated?: () => void;
}

const FileActions = ({ item, onFilesUpdated }: FileActionsProps) => {
	const { organization } = useActiveOrganization();
	const queryClient = useQueryClient();
	const [showShareModal, setShowShareModal] = useState(false);
	const isFolder = item.type === "folder";

	// Toggle public access mutation
	const togglePublicMutation = useMutation({
		mutationFn: async (isPublic: boolean) => {
			const endpoint = isFolder ? `/api/folders/${item.id}` : `/api/files/${item.id}`;
			const response = await fetch(endpoint, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					isPublic,
					organizationId: organization?.id,
				}),
			});
			if (!response.ok) throw new Error("Failed to update visibility");
			return response.json();
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["files", organization?.id],
			});
			queryClient.invalidateQueries({
				queryKey: ["folders", organization?.id],
			});
			onFilesUpdated?.();
			toast.success(`${isFolder ? "Folder" : "File"} visibility updated`);
		},
		onError: (error) => {
			console.error("Failed to update visibility:", error);
			toast.error("Failed to update visibility");
		},
	});

	// Delete mutation
	const deleteMutation = useMutation({
		mutationFn: async () => {
			const endpoint = isFolder ? `/api/folders/${item.id}` : `/api/files/${item.id}`;
			const response = await fetch(endpoint, {
				method: "DELETE",
			});
			if (!response.ok) throw new Error("Failed to delete");
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ["files", organization?.id],
			});
			queryClient.invalidateQueries({
				queryKey: ["folders", organization?.id],
			});
			onFilesUpdated?.();
			toast.success(`${isFolder ? "Folder" : "File"} deleted successfully`);
		},
		onError: (error) => {
			console.error("Failed to delete:", error);
			toast.error(`Failed to delete ${isFolder ? "folder" : "file"}`);
		},
	});

	const handleDownload = () => {
		if (item.url) {
			window.open(item.url, "_blank");
		}
	};

	const handleOpen = () => {
		if (item.url) {
			window.open(item.url, "_blank");
		}
	};

	const handleShare = () => {
		setShowShareModal(true);
	};

	const handleDelete = () => {
		if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
			deleteMutation.mutate();
		}
	};

	const handlePublicToggle = (isPublic: boolean) => {
		togglePublicMutation.mutate(isPublic);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<IconDotsHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					{!isFolder && (
						<>
							<DropdownMenuItem onClick={handleOpen}>
								<IconExternalLink className="mr-2 h-4 w-4" />
								Open
							</DropdownMenuItem>
							<DropdownMenuItem onClick={handleDownload}>
								<IconDownload className="mr-2 h-4 w-4" />
								Download
							</DropdownMenuItem>
							<DropdownMenuSeparator />
						</>
					)}
					<DropdownMenuItem>
						<IconEdit className="mr-2 h-4 w-4" />
						Rename
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleShare}>
						<IconShare className="mr-2 h-4 w-4" />
						Share
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem 
						className="text-red-600"
						onClick={handleDelete}
						disabled={deleteMutation.isPending}
					>
						<IconTrash className="mr-2 h-4 w-4" />
						{deleteMutation.isPending ? "Deleting..." : "Delete"}
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{!isFolder && (
				<ShareFileModal
					isOpen={showShareModal}
					onClose={() => setShowShareModal(false)}
					fileId={item.id}
					fileName={item.name}
					isPublic={item.isPublic}
					onPublicToggle={handlePublicToggle}
				/>
			)}
		</>
	);
};

export default FileActions;
