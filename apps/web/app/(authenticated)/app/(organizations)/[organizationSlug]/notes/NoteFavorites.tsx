import { fetchFavorites } from "@app/favorites/lib/api";
import { useNotes } from "@app/notes/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useQuery } from "@tanstack/react-query";
import { cn } from "@ui/lib";
import { NoteCard } from "./NoteCard";

export const NoteFavorites = () => {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;

	const { data: favorites = [], isLoading: isLoadingFavorites } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	const { data: notes = [], isLoading: isLoadingNotes } =
		useNotes(organizationId);

	const noteFavorites = favorites.filter(
		(fav) => fav.objectType === "note" && fav.objectId,
	);

	const favoriteNotes = notes
		.filter((note) => noteFavorites.some((fav) => fav.objectId === note.id))
		.map((note) => {
			const fav = noteFavorites.find(
				(fav) => fav.objectId === note.id,
			) as any;
			return {
				...note,
				createdBy: fav?.user,
			};
		});

	if (isLoadingFavorites || isLoadingNotes) {
		return (
			<div className="flex flex-col gap-2 p-4 bg-zinc-100 dark:bg-sidebar">
				<div className="text-muted-foreground">
					<h1 className="text-xs">Favorites</h1>
				</div>
				<div className="border border-dashed border-muted rounded-lg p-2 w-full h-[150px] flex items-center justify-center">
					<span className="text-xs text-muted-foreground">
						Loading...
					</span>
				</div>
			</div>
		);
	}

	return (
		<div
			className={cn(
				"flex flex-col gap-2 p-4 bg-zinc-100 dark:bg-sidebar border-b border-border mt-4",
			)}
		>
			<div className="text-muted-foreground">
				<h1 className="text-xs">Favorites</h1>
			</div>
			<div
				className={cn(
					"w-full min-h-[150px] flex items-center justify-center",
					favoriteNotes.length === 0 &&
						"border border-dashed dark:border-zinc-700 border-zinc-200 rounded-lg p-2",
				)}
			>
				{favoriteNotes.length === 0 ? (
					<div className="flex flex-col gap-2 text-center">
						<span className="text-sm font-medium">Favorites</span>
						<span className="text-xs text-muted-foreground">
							Add a note to your favorites by clicking the star
							icon in the top right corner of the note.
						</span>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
						{favoriteNotes.map((note) => (
							<NoteCard key={note.id} note={note} />
						))}
					</div>
				)}
			</div>
		</div>
	);
};
