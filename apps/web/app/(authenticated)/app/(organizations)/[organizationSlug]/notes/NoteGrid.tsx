"use client";

import type { Note } from "@repo/database";
import React from "react";
import { NoteCard } from "./NoteCard";

interface NoteGridProps {
	notes: (Note & {
		createdBy?: {
			id: string;
			name: string;
			email: string;
			image: string | null;
		};
	})[];
	groupBy?: string;
}

const groupNotes = (
	notes: (Note & {
		createdBy?: {
			id: string;
			name: string;
			email: string;
			image: string | null;
		};
	})[],
	groupBy: string,
) => {
	if (!groupBy) return { "All Notes": notes };
	const groups: Record<string, typeof notes> = {};
	notes.forEach((note) => {
		let groupValue = (note as any)[groupBy];
		if (groupBy === "createdAt" && groupValue) {
			groupValue = new Date(groupValue).toLocaleDateString();
		} else if (groupBy === "createdBy") {
			groupValue = groupValue?.name || "Unknown";
		}
		if (!groupValue) groupValue = "Ungrouped";
		if (!groups[groupValue]) groups[groupValue] = [];
		groups[groupValue].push(note);
	});
	return groups;
};

export function NoteGrid({ notes, groupBy = "" }: NoteGridProps) {
	const grouped = groupNotes(notes, groupBy);

	// Check if we have any notes at all
	if (!notes || notes.length === 0) {
		return (
			<div className="w-full max-w-full overflow-hidden space-y-6 -mt-4">
				<div className="flex items-center justify-center min-h-[400px]">
					<div className="text-center">
						<h3 className="text-lg font-medium text-muted-foreground mb-2">
							No notes found
						</h3>
						<p className="text-sm text-muted-foreground">
							{notes?.length === 0 ? "Try adjusting your search or filters." : "Create your first note to get started."}
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="w-full max-w-full overflow-hidden space-y-6 -mt-4">
			{Object.entries(grouped).map(([group, groupNotes]) => (
				<div key={group} className="space-y-4">
					<div className="bg-zinc-100 dark:bg-sidebar px-4 py-2">
						<div className="flex items-center gap-x-2">
							<h2 className="text-xs font-medium">{group}</h2>
							<span className="rounded-sm bg-zinc-100 dark:bg-sidebar px-1 py-0 text-xs font-medium border border-input font-mono">
								{groupNotes.length}
							</span>
						</div>
					</div>
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4 lg:gap-5 px-4 auto-rows-max">
						{groupNotes.map((note) => (
							<div key={note.id} className="w-full min-w-0">
								<NoteCard note={note} />
							</div>
						))}
					</div>
				</div>
			))}
		</div>
	);
}
