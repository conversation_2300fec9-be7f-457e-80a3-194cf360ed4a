import type { Note } from "@repo/database";
import type { FC } from "react";
import { NoteCard } from "./NoteCard";

interface NoteListProps {
	notes: Note[];
}

export const NoteList: FC<NoteListProps> = ({ notes }) => {
	const favorites: Note[] = [];
	const createdToday: Note[] = notes;

	return (
		<div className="w-full px-8 pt-6">
			<div className="mb-8">
				<div className="text-sm text-white mb-2">Favorites</div>
				<div className="border border-dashed border-neutral-700 rounded-2xl p-6 flex items-center justify-center min-h-[100px] mb-2">
					{favorites.length === 0 ? (
						<div className="text-center w-full">
							<div className="font-semibold text-base text-white mb-1">
								Favorites
							</div>
							<div className="text-muted-foreground text-sm">
								Notes that you favorite will appear here
							</div>
						</div>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
							{favorites.map((note) => (
								<NoteCard key={note.id} note={note} />
							))}
						</div>
					)}
				</div>
			</div>
			<div className="mb-4">
				<div className="text-sm text-white mb-2">
					Created today{" "}
					<span className="bg-neutral-800 text-xs rounded px-2 py-0.5 ml-1">
						{createdToday.length}
					</span>
				</div>
				<div className="flex flex-wrap gap-4">
					{createdToday.map((note) => (
						<NoteCard key={note.id} note={note} />
					))}
				</div>
			</div>
		</div>
	);
};
