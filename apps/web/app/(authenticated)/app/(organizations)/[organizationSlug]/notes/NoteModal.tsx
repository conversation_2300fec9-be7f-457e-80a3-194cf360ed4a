"use client";

import { useToggleFavorite } from "@app/favorites/lib/api";
import Cover from "./NoteCover";
import Toolbar from "./NoteToolbar";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import type { Note, ObjectType, User } from "@repo/database";
import Editor, { type EditorRef } from "@shared/components/Editor";
import { EllipsisDropdown } from "@shared/components/EllipsisDropdown";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconLink,
	IconMaximize,
	IconStar,
	IconStarOff,
	IconTrash,
	IconWorld,
	IconWorldOff,
} from "@tabler/icons-react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@ui/components/command";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import { useRouter } from "next/navigation";
import * as React from "react";
import { useDebounce } from "@app/shared/hooks/useDebounce";
import { useDeleteNote, useUpdateNote } from "@app/notes/lib/api";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { CompanyAvatar } from "@shared/components/CompanyAvatar";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { CompanyBadge, ContactBadge, PropertyBadge } from "@ui/components/badge";
import { RelatedObjectSelector } from "@app/shared/components/RelatedObjectSelector";

interface NoteModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	note:
		| (Note & {
				createdBy?: {
					id: string;
					name: string;
					email: string;
					image: string | null;
				};
				objectRecord?: {
					id: string;
					name: string;
					email: string;
					image: string | null;
					type: ObjectType
				};
		  })
		| null;
	noteToEdit:
		| (Note & {
				createdBy?: {
					id: string;
					name: string;
					email: string;
					image: string | null;
				};
		  })
		| null;
	isFavorite: boolean;
}

export function NoteModal({
	open,
	onOpenChange,
	note,
	noteToEdit,
	isFavorite,
}: NoteModalProps) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = React.useState("");
	const [isCreating, setIsCreating] = React.useState(false);
	const queryClient = useQueryClient();
	const toggleFavorite = useToggleFavorite(activeOrganization?.id);
	const deleteNoteMutation = useDeleteNote(activeOrganization?.id);
	const updateNoteMutation = useUpdateNote(activeOrganization?.id);
	const router = useRouter();

	if (noteToEdit) {
		note = noteToEdit;
	}

	const [title, setTitle] = React.useState(note?.title || "Untitled note");
	const [isEditing, setIsEditing] = React.useState(false);
	const inputRef = React.useRef<HTMLInputElement>(null);
	const [isTitleDirty, setIsTitleDirty] = React.useState(false);
	const lastSavedTitleRef = React.useRef(note?.title || "Untitled note");

	const [content, setContent] = React.useState(note?.content || "");
	const debouncedContent = useDebounce(content, 500);
	const contentSaveTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
	const latestContentRef = React.useRef(content);
	const isUserInputRef = React.useRef(false);
	const editorRef = React.useRef<EditorRef>(null);

	const [icon, setIcon] = React.useState(note?.icon || "");

	React.useEffect(() => {
		latestContentRef.current = content;
	}, [content]);

	const noteData = React.useMemo(
		() => ({
			id: note?.id || null,
			content: note?.content || "",
			title: note?.title || "Untitled note",
			icon: note?.icon || "",
		}),
		[note?.id, note?.content, note?.title, note?.icon],
	);

	React.useEffect(() => {
		if (noteData.id && noteData.id !== noteIdRef.current) {
			setContent(noteData.content);
			latestContentRef.current = noteData.content;
			setTitle(noteData.title);
			setIsTitleDirty(false);
			lastSavedTitleRef.current = noteData.title;
			setIcon(noteData.icon);
		}
	}, [noteData.id]);

	React.useEffect(() => {
		if (
			noteData.id &&
			!isUserInputRef.current &&
			noteData.content !== latestContentRef.current
		) {
			setContent(noteData.content);
			latestContentRef.current = noteData.content;
		}
	}, [noteData.content]);

	const isEmptyDoc = (c: string) =>
		c === '{"type":"doc","content":[{"type":"paragraph"}]}';

	const noteIdRef = React.useRef(note?.id);
	const organizationIdRef = React.useRef(activeOrganization?.id);
	const updateMutationRef = React.useRef(updateNoteMutation.mutate);

	React.useEffect(() => {
		noteIdRef.current = note?.id;
		organizationIdRef.current = activeOrganization?.id;
		updateMutationRef.current = updateNoteMutation.mutate;
	}, [note?.id, activeOrganization?.id, updateNoteMutation.mutate]);

	const saveContent = React.useCallback((toSave: string) => {
		if (noteIdRef.current && toSave !== undefined) {
			updateMutationRef.current({
				id: noteIdRef.current,
				content: toSave,
				organizationId: organizationIdRef.current || "",
			});
		}
	}, []);

	const saveContentWithDebounce = React.useCallback(
		(toSave: string) => {
			if (contentSaveTimeoutRef.current) {
				clearTimeout(contentSaveTimeoutRef.current);
			}

			contentSaveTimeoutRef.current = setTimeout(() => {
				saveContent(toSave);
			}, 300);
		},
		[saveContent],
	);

	const handleContentChange = React.useCallback(
		(newContent: string) => {
			isUserInputRef.current = true;
			setContent(newContent);
			saveContentWithDebounce(newContent);

			setTimeout(() => {
				isUserInputRef.current = false;
			}, 100);
		},
		[saveContentWithDebounce],
	);

	const handleEditorBlur = React.useCallback(() => {
		if (noteIdRef.current && latestContentRef.current) {
			saveContent(latestContentRef.current);
		}
	}, [saveContent]);

	const handleTitleChange = React.useCallback((newTitle: string) => {
		setTitle(newTitle);
		setIsTitleDirty(true);
	}, []);

	const handleTitleBlur = React.useCallback(() => {
		setIsEditing(false);
		setIsTitleDirty(false);

		if (
			noteIdRef.current &&
			title.trim() !== "" &&
			title !== lastSavedTitleRef.current
		) {
			lastSavedTitleRef.current = title.trim();
			updateMutationRef.current({
				id: noteIdRef.current,
				title: title.trim(),
				organizationId: organizationIdRef.current || "",
			});
		}
	}, [title]);

	const debouncedTitle = useDebounce(title, 1000);

	React.useEffect(() => {
		if (
			noteIdRef.current &&
			isTitleDirty &&
			debouncedTitle !== undefined &&
			debouncedTitle !== lastSavedTitleRef.current &&
			!isEditing &&
			debouncedTitle.trim() !== ""
		) {
			lastSavedTitleRef.current = debouncedTitle.trim();
			updateMutationRef.current({
				id: noteIdRef.current,
				title: debouncedTitle.trim(),
				organizationId: organizationIdRef.current || "",
			});
			setIsTitleDirty(false);
		}
	}, [debouncedTitle, isEditing, isTitleDirty]);

	const [isFavoriteLocal, setIsFavoriteLocal] = React.useState(isFavorite);
	React.useEffect(() => {
		setIsFavoriteLocal(isFavorite);
	}, [isFavorite]);

	const handleToggleFavorite = () => {
		setIsFavoriteLocal((prev) => !prev);
		toggleFavorite.mutate(
			{
				objectId: note?.id || "",
				objectType: "note",
				organizationId: activeOrganization?.id || "",
			},
			{
				onError: () => setIsFavoriteLocal((prev) => !prev),
			},
		);
	};

	React.useEffect(() => {
		if (isEditing) inputRef.current?.focus();
	}, [isEditing]);

	const handleCopyLink = () => {
		const baseUrl = window.location.origin;
		const noteId = note?.id;
		
		if (note?.isPublished) {
			navigator.clipboard.writeText(`${baseUrl}/app/note/${noteId}`);
		} else {
			navigator.clipboard.writeText(`${baseUrl}/app/${activeOrganization?.slug}/notes/${noteId}`);
		}
	};

	const handleOpenChange = React.useCallback(
		(nextOpen: boolean) => {
			if (!nextOpen && noteIdRef.current && latestContentRef.current) {
				saveContent(latestContentRef.current);
			}
			onOpenChange(nextOpen);
		},
		[saveContent, onOpenChange],
	);

	React.useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "Escape" && open) {
				if (noteIdRef.current && latestContentRef.current) {
					saveContent(latestContentRef.current);
				}
				onOpenChange(false);
			}
		};

		if (open) {
			document.addEventListener("keydown", handleKeyDown);
			return () => document.removeEventListener("keydown", handleKeyDown);
		}
	}, [open, saveContent, onOpenChange]);

	React.useEffect(() => {
		return () => {
			const currentContent = latestContentRef.current;
			const noteContent = note?.content;
			if (currentContent && currentContent !== noteContent) {
				if (contentSaveTimeoutRef.current) {
					clearTimeout(contentSaveTimeoutRef.current);
				}
			}
		};
	}, [note?.id]);

	React.useEffect(() => {
		const handleWindowBlur = () => {
			if (open && noteIdRef.current && latestContentRef.current) {
				saveContent(latestContentRef.current);
			}
		};

		const handleBeforeUnload = () => {
			if (open && noteIdRef.current && latestContentRef.current) {
				saveContent(latestContentRef.current);
			}
		};

		if (open) {
			window.addEventListener("blur", handleWindowBlur);
			window.addEventListener("beforeunload", handleBeforeUnload);

			return () => {
				window.removeEventListener("blur", handleWindowBlur);
				window.removeEventListener("beforeunload", handleBeforeUnload);
			};
		}
	}, [open, saveContent]);

	const handleMaximize = () => {
		router.push(`/app/${activeOrganization?.slug}/notes/${note?.id}`);
	};

	const Header = React.memo(() => {
		return (
			<div className="flex items-center justify-between px-4 py-1.5 border-b border-border bg-sidebar">
				{note?.objectId ? (
					<div className="flex items-center gap-3">
						{note?.objectRecord?.type === "contact" ? (
							<ContactBadge value={note?.objectRecord?.name} avatar={note?.objectRecord?.image || undefined} />
						) : note?.objectRecord?.type === "company" ? (
							<CompanyBadge value={note?.objectRecord?.name} logo={note?.objectRecord?.image || undefined} />
						) : (
							<PropertyBadge value={note?.objectRecord?.name} avatar={note?.objectRecord?.image || undefined} />
						)}
					</div>
				) : (
					<div className="flex items-center gap-3">
						<span className="font-medium text-sm text-primary">
							Personal Note
						</span>
					</div>
				)}
				<div className="flex items-center gap-2 mr-4">
					<Button
						tooltip={{
							content: "Maximize",
							side: "bottom",
						}}
						onClick={handleMaximize}
						variant="ghost"
						size="icon"
						className="text-muted-foreground hover:bg-transparent hover:text-primary"
						aria-label="Maximize"
						tabIndex={2}
					>
						<IconMaximize className="size-4" />
					</Button>
					{/* TODO: Add pin to bottom button and create new right bottom button */}
					{/* <Button tooltip={{
            children: "Pin to bottom",
            side: "bottom",
          }} variant="ghost" size="icon" className="text-muted-foreground hover:bg-transparent hover:text-primary" aria-label="Pin to bottom">
            <IconSquareRoundedMinus className="size-4" />
          </Button> */}
				</div>
			</div>
		);
	});

	const InfoBar = React.memo(() => {
		return (
			<div className="flex items-center justify-between px-4 py-1.5 border-b border-border bg-sidebar">
				<div className="flex items-center gap-2">
					{note?.isPublished ? (
						<div className="flex items-center gap-1 text-blue-500">
							<IconWorld className="size-4" />
							<span className="text-xs text-muted-foreground">
								Published
							</span>
						</div>
					) : (
						<div className="flex items-center gap-1">
							<IconWorldOff className="size-4" />
							<span className="text-xs text-muted-foreground">
								Private
							</span>
						</div>
					)}
				</div>
				<div className="flex items-center gap-1">
					<UserAvatar
						name={note?.createdBy?.name || "Unknown"}
						avatarUrl={note?.createdBy?.image || null}
						className="h-6 w-6"
					/>
					<Button
						tooltip={{
							content: "Copy link",
							side: "bottom",
						}}
						variant="ghost"
						size="sm"
						className="gap-2 text-muted-foreground hover:bg-transparent hover:text-primary"
						aria-label="Copy link"
						onClick={handleCopyLink}
					>
						<IconLink className="size-4" />
						<span className="text-xs">Copy link</span>
					</Button>
					<EllipsisDropdown className="p-1">
						<EllipsisDropdown.Item
							className="flex items-center gap-2"
							onClick={handleToggleFavorite}
						>
							{isFavoriteLocal ? (
								<IconStarOff className="size-4" />
							) : (
								<IconStar className="size-4" />
							)}
							{isFavoriteLocal
								? "Remove from favorites"
								: "Add to favorites"}
						</EllipsisDropdown.Item>
						<EllipsisDropdown.Item
							className="flex items-center gap-2"
							onClick={() => {
								if (note?.isPublished) {
									updateNoteMutation.mutate({
										id: note?.id || "",
										isPublished: false,
										organizationId:
											activeOrganization?.id || "",
									});
								} else {
									updateNoteMutation.mutate({
										id: note?.id || "",
										isPublished: true,
										organizationId:
											activeOrganization?.id || "",
									});
								}
							}}
						>
							{note?.isPublished ? (
								<IconWorldOff className="size-4" />
							) : (
								<IconWorld className="size-4" />
							)}
							{note?.isPublished ? "Make private" : "Make public"}
						</EllipsisDropdown.Item>
						<Separator className="my-0.5" />
						<EllipsisDropdown.Item
							className="flex items-center gap-2 text-red-500 hover:!text-red-600"
							onClick={() => {
								deleteNoteMutation.mutate(note?.id || "", {
									onSuccess: () => {
										onOpenChange(false);
									},
								});
							}}
						>
							<IconTrash className="size-4" />
							Delete note
						</EllipsisDropdown.Item>
					</EllipsisDropdown>
				</div>
			</div>
		);
	});

	const TitleInput = React.useMemo(() => {
		return (
			<Input
				ref={inputRef}
				value={title}
				onChange={(e) => {
					handleTitleChange(e.target.value);
				}}
				onBlur={handleTitleBlur}
				onKeyDown={(e) => {
					if (e.key === "Enter") {
						handleTitleBlur();
					}
				}}
				className="!-ml-3 w-full bg-transparent !text-4xl font-bold text-primary !outline-none !border-none !ring-0 !shadow-none"
			/>
		);
	}, [title, handleTitleChange, handleTitleBlur]);

	const hasInitialFocus = React.useRef(false);

	React.useEffect(() => {
		if (open && !isEditing && !hasInitialFocus.current) {
			hasInitialFocus.current = true;

			if (title === "Untitled note") {
				setTimeout(() => {
					if (inputRef.current) {
						inputRef.current.focus();
						inputRef.current.select();
					}
				}, 100);
			} else {
				setTimeout(() => {
					if (editorRef.current) {
						editorRef.current.focus();
					}
				}, 100);
			}
		}

		if (!open) {
			hasInitialFocus.current = false;
		}
	}, [open, isEditing, title]);

	React.useEffect(() => {
		if (
			updateNoteMutation.isSuccess &&
			updateNoteMutation.data?.id === note?.id &&
			!isUserInputRef.current
		) {
			const serverData = updateNoteMutation.data;

			if (
				serverData.content !== undefined &&
				serverData.content !== latestContentRef.current
			) {
				setContent(serverData.content || "");
				latestContentRef.current = serverData.content || "";
			}

			if (
				serverData.title !== undefined &&
				!isEditing &&
				!isTitleDirty &&
				serverData.title !== lastSavedTitleRef.current
			) {
				setTitle(serverData.title || "Untitled note");
				lastSavedTitleRef.current = serverData.title || "Untitled note";
			}

			if (serverData.icon !== undefined) {
				setIcon(serverData.icon || "");
			}
		}
	}, [
		updateNoteMutation.isSuccess,
		updateNoteMutation.data,
		note?.id,
		isEditing,
		isTitleDirty,
	]);

	const onIconSelect = (newIcon: string) => {
		// Update local state immediately for instant UI feedback
		setIcon(newIcon);
		
		if (noteIdRef.current) {
			updateMutationRef.current({
				id: noteIdRef.current,
				icon: newIcon,
				organizationId: organizationIdRef.current || "",
			});
		}
	};

	const onRemoveIcon = () => {
		// Update local state immediately for instant UI feedback
		setIcon("");
		
		if (noteIdRef.current) {
			updateMutationRef.current({
				id: noteIdRef.current,
				icon: "",
				organizationId: organizationIdRef.current || "",
			});
		}
	};

	return (
		<>
			<CommandDialog
				overlay={false}
				open={open}
				onOpenChange={handleOpenChange}
				className="!rounded-3xl border border-input !ring-4 !ring-neutral-200/80 dark:!bg-neutral-900 dark:!ring-neutral-800 sm:max-w-3xl m-0 bg-clip-padding overflow-y-auto no-scrollbar"
			>
				<Command value={search} onValueChange={setSearch}>
					<CommandList className="max-h-[750px] min-h-[750px] !min-w-[750px] overflow-hidden bg-secondary/20 pb-16 no-scrollbar">
						{note ? (
							<div className="flex flex-col overflow-hidden">
								<Header />
								<InfoBar />
								<Cover
									modal={true}
									url={note.coverImage || undefined}
									note={{
										...note,
										icon: icon,
									}}
									onIconSelect={onIconSelect}
									onRemoveIcon={onRemoveIcon}
								/>
								<div className="flex flex-col px-12 pt-8 pb-6">
									<div className="mb-2">{TitleInput}</div>
									{/* Pills */}
									<div className="flex items-center gap-2 mb-6">
										<RelatedObjectSelector
											value={note?.objectRecord ? {
												id: note.objectRecord.id,
												name: note.objectRecord.name,
												recordType: note.objectRecord.type as "contact" | "company" | "property",
												subtitle: note.objectRecord.email || undefined,
											} : undefined}
											onValueChange={async (newObj) => {
												if (!note?.id) return;
												await updateNoteMutation.mutateAsync({
													id: note.id,
													objectId: newObj?.id || null,
													objectType: newObj?.recordType || null,
													organizationId: activeOrganization?.id || "",
												});
											}}
											displayMode="full"
											placeholder="Link a record"
										/>
									</div>
									{/* Prompt */}
									<div className="mb-8 h-[525px] overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
										<Editor
											ref={editorRef}
											initialContent={content}
											onChange={handleContentChange}
											editable={!isEditing}
											onBlur={handleEditorBlur}
											placeholder="Start typing, or create a template and link to #"
										/>
									</div>
								</div>
							</div>
						) : (
							<span>No note selected.</span>
						)}
					</CommandList>
				</Command>
			</CommandDialog>
		</>
	);
}
