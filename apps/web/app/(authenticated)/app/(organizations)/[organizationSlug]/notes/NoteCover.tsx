"use client";

import { CoverImageModal } from "@app/notes/components/CoverImageModal";
import { useCoverImage } from "@app/notes/hooks/useCoverImage";
import { useUpdateNote } from "@app/notes/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useEdgeStore } from "@repo/storage";
import {
	EmojiPicker,
	EmojiPickerContent,
	EmojiPickerFooter,
	EmojiPickerSearch,
} from "@shared/components/EmojiPicker";
import { IconX } from "@tabler/icons-react";
import { Button } from "@ui/components/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { ImageIcon, Smile, X } from "lucide-react";
import Image from "next/image";
import { useParams } from "next/navigation";
import React from "react";

interface CoverProps {
	url?: string;
	preview?: boolean;
	modal?: boolean;
	note?: any;
	onIconSelect?: (icon: string) => void;
	onRemoveIcon?: () => void;
}

const Cover = ({
	url,
	preview,
	modal,
	note,
	onIconSelect,
	onRemoveIcon,
}: CoverProps) => {
	const coverImage = useCoverImage();
	const { mutate: update } = useUpdateNote();
	const params = useParams();
	const { activeOrganization } = useActiveOrganization();
	const [isIconOpen, setIsIconOpen] = React.useState(false);
	const { edgestore } = useEdgeStore();

	const onRemove = async () => {
		if (params.id && typeof params.id === "string") {
			if (url) {
				try {
					await edgestore.publicFiles.delete({
						url: url,
					});
				} catch (error) {
					console.error(
						"Failed to delete image from EdgeStore:",
						error,
					);
				}
			}

			update({
				id: params.id,
				coverImage: "",
				organizationId: activeOrganization?.id || "",
			});
		}
	};

	return (
		<div
			className={cn(
				"relative w-full group",
				modal && "h-[12vh]",
				!modal && "h-[35vh]",
				!url && "h-[6vh]",
				url && "bg-muted",
			)}
		>
			{url && (
				<Image src={url} fill alt="Cover" className="object-cover" />
			)}

			{/* All controls together in bottom-right corner */}
			{!preview && (
				<div
					className={cn(
						"opacity-0 group-hover:opacity-100 absolute bottom-5 right-5 flex items-center gap-x-2",
						modal &&
							"bottom-0 right-4 mb-4 bg-sidebar/50 p-2 rounded-lg",
					)}
				>
					{/* Icon controls */}
					{modal && (
						<>
							{note?.icon ? (
								<div className="flex items-center gap-x-2">
									<Popover
										onOpenChange={setIsIconOpen}
										open={isIconOpen}
									>
										<PopoverTrigger asChild>
											<Button
												className="text-muted-foreground text-xs gap-2 p-2"
												variant="outline"
												size="sm"
											>
												<span className="text-lg">
													{note.icon}
												</span>
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-fit p-0">
											<EmojiPicker
												className="h-[342px]"
												onEmojiSelect={({ emoji }) => {
													setIsIconOpen(false);
													onIconSelect?.(emoji);
												}}
											>
												<EmojiPickerSearch />
												<EmojiPickerContent />
												<EmojiPickerFooter />
											</EmojiPicker>
										</PopoverContent>
									</Popover>
									<Button
										onClick={onRemoveIcon}
										className="text-muted-foreground text-xs gap-x-2"
										variant="outline"
										size="sm"
									>
										<IconX className="h-4 w-4" />
										Remove icon
									</Button>
								</div>
							) : (
								<Popover
									open={isIconOpen}
									onOpenChange={setIsIconOpen}
								>
									<PopoverTrigger asChild>
										<Button
											className="text-muted-foreground text-xs gap-2"
											variant="outline"
											size="sm"
										>
											<Smile className="size-4" />
											Add icon
										</Button>
									</PopoverTrigger>
									<PopoverContent className="w-fit p-0">
										<EmojiPicker
											className="h-[342px]"
											onEmojiSelect={({ emoji }) => {
												setIsIconOpen(false);
												onIconSelect?.(emoji);
											}}
										>
											<EmojiPickerSearch />
											<EmojiPickerContent />
											<EmojiPickerFooter />
										</EmojiPicker>
									</PopoverContent>
								</Popover>
							)}
						</>
					)}

					{/* Cover controls */}
					{url ? (
						// Show change/remove buttons when cover exists
						<>
							<Button
								onClick={() => coverImage.onReplace(url)}
								className="text-muted-foreground text-xs gap-x-2"
								variant="outline"
								size="sm"
							>
								<ImageIcon className="h-4 w-4" />
								Change cover
							</Button>
							<Button
								onClick={onRemove}
								className="text-muted-foreground text-xs gap-x-2"
								variant="outline"
								size="sm"
							>
								<X className="h-4 w-4" />
								Remove
							</Button>
						</>
					) : (
						// Show add cover button when no cover exists
						<Button
							onClick={() => coverImage.onOpen()}
							className="text-muted-foreground text-xs gap-x-2"
							variant="outline"
							size="sm"
						>
							<ImageIcon className="h-4 w-4" />
							Add cover
						</Button>
					)}
				</div>
			)}

			{/* Show icon when it exists and not editing */}
			{modal && note?.icon && !preview && (
				<div className="absolute top-4 left-4">
					<p
						className="text-6xl cursor-pointer hover:opacity-75 transition"
						onClick={() => setIsIconOpen(true)}
					>
						{note.icon}
					</p>
				</div>
			)}

			{modal && (
				<CoverImageModal
					noteId={note?.id}
					organizationId={activeOrganization?.id || ""}
					existingCoverUrl={note?.coverImage || undefined}
				/>
			)}
		</div>
	);
};

Cover.Skeleton = function CoverSkeleton() {
	return <Skeleton className="w-full h-[12vh]" />;
};

export default Cover;
