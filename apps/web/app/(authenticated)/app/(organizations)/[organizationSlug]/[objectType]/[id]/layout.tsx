import type { <PERSON><PERSON><PERSON> } from "next";
import type { ReactNode } from "react";
import React, { Suspense } from "react";
import { headers } from "next/headers";
import { auth, type Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { Skeleton } from "@ui/components/skeleton";

interface LayoutProps {
	children: ReactNode;
	params: Promise<{
		organizationSlug: string;
		objectType: string;
		id: string;
	}>;
}

async function getOrganizationIdFromSlug(slug: string): Promise<string | null> {
	try {
		const organization = await db.organization.findFirst({
			where: { slug },
			select: { id: true },
		});
		return organization?.id || null;
	} catch (error) {
		console.error('Error fetching organization ID:', error);
		return null;
	}
}

async function fetchRecordName(recordId: string, objectType: string, organizationSlug: string): Promise<string | null> {
	try {
		// Validate ObjectID format - MongoDB ObjectIDs must be exactly 24 hexadecimal characters
		if (!recordId || !/^[0-9a-fA-F]{24}$/.test(recordId)) {
			console.warn(`Invalid ObjectID format: ${recordId} (length: ${recordId?.length || 0})`);
			return null;
		}

		// Get the session for authentication
		const session: Session | null = await auth.api.getSession({
			headers: await headers(),
		});
		
		if (!session?.user?.id) {
			return null;
		}

		// Get organization ID from slug
		const organizationId = await getOrganizationIdFromSlug(organizationSlug);
		if (!organizationId) {
			return null;
		}

		// Fetch record based on object type
		switch (objectType) {
			case "contact": {
				const contact = await db.contact.findFirst({
					where: {
						id: recordId,
						organizationId,
						isDeleted: false,
					},
					select: {
						firstName: true,
						lastName: true,
					},
				});

				if (!contact) return null;

				return `${contact.firstName || ""} ${contact.lastName || ""}`.trim() || "Unknown Contact";
			}

			case "company": {
				const company = await db.company.findFirst({
					where: {
						id: recordId,
						organizationId,
						isDeleted: false,
					},
					select: {
						name: true,
					},
				});

				return company?.name || null;
			}

			case "property": {
				const property = await db.property.findFirst({
					where: {
						id: recordId,
						organizationId,
						isDeleted: false,
					},
					select: {
						name: true,
					},
				});

				return property?.name || null;
			}

			case "custom_object": {
				const customObject = await db.customObject.findFirst({
					where: {
						id: recordId,
						organizationId,
						isDeleted: false,
					},
					select: {
						title: true,
					},
				});

				return customObject?.title || null;
			}

			case "note": {
				const note = await db.note.findFirst({
					where: {
						id: recordId,
						orgId: organizationId,
						isDeleted: false,
					},
					select: {
						title: true,
					},
				});

				return note?.title || null;
			}

			default:
				return null;
		}
	} catch (error) {
		console.error(`Error fetching ${objectType} name for metadata:`, error);
		return null;
	}
}

export async function generateMetadata({ params }: LayoutProps): Promise<Metadata> {
	const { organizationSlug, objectType, id } = await params;
	
	// Fetch the record name dynamically based on object type
	const recordName = await fetchRecordName(id, objectType, organizationSlug);
	

	// Generate metadata based on object type
	switch (objectType) {
		case "contact":
			if (recordName) {
				return {
					title: `${recordName} - Contact - Relio`,
					description: `View ${recordName}'s contact details on Relio`,
				};
			}
			return {
				title: "Contact Details - Relio",
				description: "View contact information and details on Relio",
			};

		case "company":
			if (recordName) {
				return {
					title: `${recordName} - Company - Relio`,
					description: `View ${recordName}'s company details on Relio`,
				};
			}
			return {
				title: "Company Details - Relio",
				description: "View company information and details on Relio",
			};

		case "property":
			if (recordName) {
				return {
					title: `${recordName} - Property - Relio`,
					description: `View property details for ${recordName} on Relio`,
				};
			}
			return {
				title: "Property Details - Relio",
				description: "View property information and details on Relio",
			};

		case "note":
			if (recordName) {
				return {
					title: `${recordName} - Note - Relio`,
					description: `View note: ${recordName} on Relio`,
				};
			}
			return {
				title: "Note - Relio",
				description: "View and edit your note on Relio",
			};

		default:
			// Generic fallback for any other object types
			const objectTypeTitle = objectType.charAt(0).toUpperCase() + objectType.slice(1);
			if (recordName) {
				return {
					title: `${recordName} - ${objectTypeTitle} - Relio`,
					description: `View ${recordName} details on Relio`,
				};
			}
			return {
				title: `${objectTypeTitle} - Relio`,
				description: `View ${objectType} details on Relio`,
			};
	}
}

export default function ObjectLayout({ children }: LayoutProps) {
	return (
		<main className={"flex-1 p-1 h-full w-full"}>
			<Suspense fallback={<Skeleton className="h-full w-full" />}>
				<div
					className={
						"rounded-xl border border-zinc-200 dark:border-muted overflow-hidden h-full"
					}
				>
					{children}
				</div>
			</Suspense>
		</main>
	);
}