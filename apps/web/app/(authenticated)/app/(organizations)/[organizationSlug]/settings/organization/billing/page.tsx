import { getActiveOrganization } from "@app/auth/lib/server";
import { ActivePlan } from "@app/payments/components/ActivePlan";
import { ChangePlan } from "@app/payments/components/ChangePlan";
import { purchasesQueryKey } from "@app/payments/lib/api";
import { getPurchases } from "@app/payments/lib/server";
import { SettingsList } from "@app/shared/components/SettingsList";
import { createPurchasesHelper } from "@repo/payments/lib/helper";
import { getQueryClient } from "@shared/lib/server";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("settings.billing.title"),
	};
}

export default async function BillingSettingsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	const purchases = await getPurchases(organization.id);
	const queryClient = getQueryClient();

	await queryClient.prefetchQuery({
		queryKey: purchasesQueryKey(organization.id),
		queryFn: () => purchases,
	});

	const { activePlan } = createPurchasesHelper(purchases);

	return (
		<SettingsList>
			{activePlan && <ActivePlan organizationId={organization.id} />}
			<ChangePlan
				organizationId={organization.id}
				activePlanId={activePlan?.id as string | undefined}
			/>
		</SettingsList>
	);
}
