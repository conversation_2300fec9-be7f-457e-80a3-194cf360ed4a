"use client";

import { useSettingsHeader } from "@app/shared/hooks/use-settings-header";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Switch } from "@ui/components/switch";
import { Label } from "@ui/components/label";
import { Button } from "@ui/components/button";
import { useNotificationSettings, useUpdateNotificationSettings } from "@app/notifications/lib/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

export function NotificationsSettingsClient() {
	const t = useTranslations();
	const { data: settings, isLoading } = useNotificationSettings();
	const { mutateAsync: updateSettings, isPending } = useUpdateNotificationSettings();
	
	// Local state for settings
	const [emailMentions, setEmailMentions] = useState(true);
	const [emailComments, setEmailComments] = useState(false);
	const [emailActivities, setEmailActivities] = useState(false);
	const [pushMentions, setPushMentions] = useState(true);
	const [pushComments, setPushComments] = useState(false);
	const [pushActivities, setPushActivities] = useState(false);
	
	// Update local state when settings are loaded
	useEffect(() => {
		if (settings) {
			setEmailMentions(settings.emailMentions);
			setEmailComments(settings.emailComments);
			setEmailActivities(settings.emailActivities);
			setPushMentions(settings.pushMentions);
			setPushComments(settings.pushComments);
			setPushActivities(settings.pushActivities);
		}
	}, [settings]);
	
	useSettingsHeader(
		t("organizations.settings.notifications.title"),
		t("organizations.settings.notifications.subtitle")
	);
	
	const handleSave = async () => {
		try {
			await updateSettings({
				emailMentions,
				emailComments,
				emailActivities,
				pushMentions,
				pushComments,
				pushActivities
			});
			toast.success("Notification settings updated");
		} catch (error) {
			console.error("Failed to update notification settings:", error);
			toast.error("Failed to update notification settings");
		}
	};
	
	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Email Notifications</CardTitle>
					<CardDescription>
						Configure which notifications you receive via email
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{isLoading ? (
						<div className="flex justify-center py-4">
							<Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
						</div>
					) : (
						<>
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="email-mentions">Mentions</Label>
									<p className="text-sm text-muted-foreground">
										Receive an email when someone mentions you in a comment
									</p>
								</div>
								<Switch
									id="email-mentions"
									checked={emailMentions}
									onCheckedChange={setEmailMentions}
								/>
							</div>
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="email-comments">Comments</Label>
									<p className="text-sm text-muted-foreground">
										Receive an email when someone comments on your activities
									</p>
								</div>
								<Switch
									id="email-comments"
									checked={emailComments}
									onCheckedChange={setEmailComments}
								/>
							</div>
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="email-activities">Activities</Label>
									<p className="text-sm text-muted-foreground">
										Receive an email for important activities in your organization
									</p>
								</div>
								<Switch
									id="email-activities"
									checked={emailActivities}
									onCheckedChange={setEmailActivities}
								/>
							</div>
						</>
					)}
				</CardContent>
			</Card>
			
			<Card>
				<CardHeader>
					<CardTitle>Push Notifications</CardTitle>
					<CardDescription>
						Configure which notifications you receive in the app
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{isLoading ? (
						<div className="flex justify-center py-4">
							<Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
						</div>
					) : (
						<>
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="push-mentions">Mentions</Label>
									<p className="text-sm text-muted-foreground">
										Receive a notification when someone mentions you in a comment
									</p>
								</div>
								<Switch
									id="push-mentions"
									checked={pushMentions}
									onCheckedChange={setPushMentions}
								/>
							</div>
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="push-comments">Comments</Label>
									<p className="text-sm text-muted-foreground">
										Receive a notification when someone comments on your activities
									</p>
								</div>
								<Switch
									id="push-comments"
									checked={pushComments}
									onCheckedChange={setPushComments}
								/>
							</div>
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="push-activities">Activities</Label>
									<p className="text-sm text-muted-foreground">
										Receive a notification for important activities in your organization
									</p>
								</div>
								<Switch
									id="push-activities"
									checked={pushActivities}
									onCheckedChange={setPushActivities}
								/>
							</div>
						</>
					)}
				</CardContent>
			</Card>
			
			<div className="flex justify-end">
				<Button 
					variant="action" 
					onClick={handleSave}
					disabled={isLoading || isPending}
				>
					{isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
					Save Changes
				</Button>
			</div>
		</div>
	);
}