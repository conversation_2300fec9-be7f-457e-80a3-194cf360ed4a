import { OrganizationCreditsStatus } from "@app/ai/components/OrganizationCreditsStatus";
import { getActiveOrganization } from "@app/auth/lib/server";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { ArrowLeft, Calendar, CreditCard, TrendingUp } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: "AI Credits",
		header: {
			title: "AI Credits",
			subtitle: "View your credit balance and usage history",
		},
	};
}

export default async function AICreditsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return redirect("/app");
	}

	return (
		<>
			<div className="mb-6">
				<div className="flex items-center gap-4 mb-4">
					<Button variant="ghost" asChild size="sm">
						<Link href={`/app/${organizationSlug}/settings/ai`}>
							<ArrowLeft className="h-4 w-4 mr-2" />
							Back to AI Settings
						</Link>
					</Button>
				</div>
				<h1 className="text-2xl font-semibold">AI Credits</h1>
				<p className="text-muted-foreground">
					View your credit balance, usage history, and manage your AI
					spending
				</p>
			</div>

			<div className="grid gap-6">
				{/* Credits Overview */}
				<OrganizationCreditsStatus
					organizationId={organization.id}
					showUpgradePrompt={true}
				/>

				{/* Credits Information */}
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Calendar className="h-5 w-5" />
								Credit Cycle
							</CardTitle>
							<CardDescription>
								Information about your credit renewal
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-3">
								<div className="flex justify-between">
									<span className="text-sm font-medium">
										Billing Cycle
									</span>
									<Badge variant="views">Monthly</Badge>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">
										Credits Reset
									</span>
									<span className="text-sm text-muted-foreground">
										1st of each month
									</span>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">
										Next Reset
									</span>
									<span className="text-sm text-muted-foreground">
										{new Date(
											new Date().getFullYear(),
											new Date().getMonth() + 1,
											1,
										).toLocaleDateString()}
									</span>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<TrendingUp className="h-5 w-5" />
								Usage Insights
							</CardTitle>
							<CardDescription>
								How you're using your AI credits
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-3">
								<div className="flex justify-between">
									<span className="text-sm font-medium">
										Data Chat
									</span>
									<span className="text-sm text-muted-foreground">
										~60% of usage
									</span>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">
										Task Creation
									</span>
									<span className="text-sm text-muted-foreground">
										~25% of usage
									</span>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">
										Analytics
									</span>
									<span className="text-sm text-muted-foreground">
										~15% of usage
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Credit Actions */}
				<Card>
					<CardHeader>
						<CardTitle>Credit Management</CardTitle>
						<CardDescription>
							Purchase additional credits or upgrade your plan
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<Button
								variant="outline"
								asChild
								className="h-auto flex-col p-6"
							>
								<Link
									href={`/app/${organizationSlug}/settings/ai/purchase`}
								>
									<CreditCard className="h-8 w-8 mb-3" />
									<span className="font-medium text-base mb-1">
										Buy More Credits
									</span>
									<span className="text-xs text-muted-foreground text-center">
										Purchase additional credits for your
										organization
									</span>
								</Link>
							</Button>

							<Button
								variant="outline"
								asChild
								className="h-auto flex-col p-6"
							>
								<Link
									href={`/app/${organizationSlug}/settings`}
								>
									<TrendingUp className="h-8 w-8 mb-3" />
									<span className="font-medium text-base mb-1">
										Upgrade Plan
									</span>
									<span className="text-xs text-muted-foreground text-center">
										Get more credits and features with a
										paid plan
									</span>
								</Link>
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* FAQ Section */}
				<Card>
					<CardHeader>
						<CardTitle>Frequently Asked Questions</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-4">
							<div>
								<h4 className="font-medium mb-2">
									How are credits calculated?
								</h4>
								<p className="text-sm text-muted-foreground">
									Credits are consumed based on AI model
									usage, with different operations consuming
									different amounts. Chat messages typically
									consume 1-5 credits, while complex analysis
									may use 10-20 credits.
								</p>
							</div>
							<div>
								<h4 className="font-medium mb-2">
									Do unused credits roll over?
								</h4>
								<p className="text-sm text-muted-foreground">
									Free plan credits reset monthly and don't
									roll over. Purchased credits and paid plan
									credits have different policies - check your
									plan details.
								</p>
							</div>
							<div>
								<h4 className="font-medium mb-2">
									Can I purchase credits in advance?
								</h4>
								<p className="text-sm text-muted-foreground">
									Yes, you can purchase credit packages that
									don't expire and can be used across billing
									cycles.
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</>
	);
}
