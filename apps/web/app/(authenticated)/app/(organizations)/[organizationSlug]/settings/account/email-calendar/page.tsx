import { getSession } from "@app/auth/lib/server";
import { ConnectedEmailAccountsSection } from "@app/settings/components/ConnectedEmailAccountsSection";
import { ForwardingAddressSection } from "@app/settings/components/ForwardingAddressSection";
import { WatermarkSection } from "@app/settings/components/WatermarkSection";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { EmailCalendarSettingsClient } from "./client";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("settings.account.title"),
		header: {
			title: t("settings.account.title"),
			subtitle: t("settings.account.subtitle"),
		},
	};
}

export default async function EmailCalendarPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	return (
		<>
			<EmailCalendarSettingsClient />
			<div className="flex flex-col gap-12 mt-12 max-w-3xl mx-auto">
				<ConnectedEmailAccountsSection />
				<ForwardingAddressSection />
				<WatermarkSection />
			</div>
		</>
	);
}
