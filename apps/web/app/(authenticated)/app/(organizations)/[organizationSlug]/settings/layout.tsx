import { getActiveOrganization } from "@app/auth/lib/server";
import { SettingsPageHeader } from "@app/shared/components/SettingsPageHeader";
import { SidebarContentLayout } from "@app/shared/components/SidebarContentLayout";
import { redirect } from "next/navigation";

export default async function SettingsLayout(props: any) {
	const { children, params } = props;
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		redirect("/app");
	}

	return (
		<>
			<SettingsPageHeader />
			<SidebarContentLayout>{children}</SidebarContentLayout>
		</>
	);
}
