import { OrganizationCreditsStatus } from "@app/ai/components/OrganizationCreditsStatus";
import { getActiveOrganization } from "@app/auth/lib/server";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { BarChart3, Bot, CreditCard, Settings, Zap } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: "AI Settings",
		header: {
			title: "AI Settings",
			subtitle: "Manage your AI features and credits",
		},
	};
}

export default async function AISettingsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return redirect("/app");
	}

	return (
		<div className="p-6 space-y-6">
			<div>
				<h1 className="text-2xl font-semibold">AI Settings</h1>
				<p className="text-muted-foreground">
					Manage your AI features, credits, and usage analytics
				</p>
			</div>

			<div className="grid gap-6">
				{/* Credits Overview */}
				<OrganizationCreditsStatus
					organizationId={organization.id}
					showUpgradePrompt={true}
				/>

				{/* AI Features Overview */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Bot className="h-5 w-5" />
							AI Features
						</CardTitle>
						<CardDescription>
							Available AI capabilities for your organization
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="flex items-start space-x-3 p-3 border rounded-lg">
								<div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
									<Bot className="h-4 w-4 text-blue-600 dark:text-blue-400" />
								</div>
								<div className="flex-1">
									<h3 className="font-medium">Data Chat</h3>
									<p className="text-sm text-muted-foreground">
										Ask questions about your CRM data
									</p>
									<Badge variant="views" className="mt-1">
										Active
									</Badge>
								</div>
							</div>

							<div className="flex items-start space-x-3 p-3 border rounded-lg">
								<div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
									<BarChart3 className="h-4 w-4 text-green-600 dark:text-green-400" />
								</div>
								<div className="flex-1">
									<h3 className="font-medium">
										Analytics Insights
									</h3>
									<p className="text-sm text-muted-foreground">
										AI-powered business analytics
									</p>
									<Badge variant="views" className="mt-1">
										Active
									</Badge>
								</div>
							</div>

							<div className="flex items-start space-x-3 p-3 border rounded-lg">
								<div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
									<Settings className="h-4 w-4 text-purple-600 dark:text-purple-400" />
								</div>
								<div className="flex-1">
									<h3 className="font-medium">
										Task Creation
									</h3>
									<p className="text-sm text-muted-foreground">
										Create tasks through natural language
									</p>
									<Badge variant="views" className="mt-1">
										Active
									</Badge>
								</div>
							</div>

							<div className="flex items-start space-x-3 p-3 border rounded-lg">
								<div className="w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center">
									<Zap className="h-4 w-4 text-orange-600 dark:text-orange-400" />
								</div>
								<div className="flex-1">
									<h3 className="font-medium">
										Smart Search
									</h3>
									<p className="text-sm text-muted-foreground">
										Advanced contact and company search
									</p>
									<Badge variant="views" className="mt-1">
										Active
									</Badge>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Quick Actions */}
				<Card>
					<CardHeader>
						<CardTitle>Quick Actions</CardTitle>
						<CardDescription>
							Manage your AI settings and credits
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<Button
								variant="outline"
								asChild
								className="h-auto flex-col p-4"
							>
								<Link
									href={`/app/${organizationSlug}/settings/ai/credits`}
								>
									<CreditCard className="h-6 w-6 mb-2" />
									<span className="font-medium">
										View Credits
									</span>
									<span className="text-xs text-muted-foreground">
										Check your credit balance
									</span>
								</Link>
							</Button>

							<Button
								variant="outline"
								asChild
								className="h-auto flex-col p-4"
							>
								<Link
									href={`/app/${organizationSlug}/settings/ai/usage`}
								>
									<BarChart3 className="h-6 w-6 mb-2" />
									<span className="font-medium">
										Usage Analytics
									</span>
									<span className="text-xs text-muted-foreground">
										View detailed usage stats
									</span>
								</Link>
							</Button>

							<Button
								variant="outline"
								asChild
								className="h-auto flex-col p-4"
							>
								<Link
									href={`/app/${organizationSlug}/settings/ai/purchase`}
								>
									<Zap className="h-6 w-6 mb-2" />
									<span className="font-medium">
										Buy Credits
									</span>
									<span className="text-xs text-muted-foreground">
										Purchase additional credits
									</span>
								</Link>
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
