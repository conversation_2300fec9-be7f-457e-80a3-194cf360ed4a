import { getSession } from "@app/auth/lib/server";
import { SettingsList } from "@app/shared/components/SettingsList";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { NotificationsSettingsClient } from "./client";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("organizations.settings.notifications.title"),
	};
}

export default async function NotificationsSettingsPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	return (
		<>
			<NotificationsSettingsClient />
			<SettingsList>
			</SettingsList>
		</>
	);
}
