"use client";

import React from "react";
import { DataTable } from "@ui/components/settings-table";
import { useAllTags } from "@app/shared/hooks/useTags";
import { createColumns, filterableColumns } from "./columns";
import { useRouter, useSearchParams } from "next/navigation";
import { CreateTagDialog } from "./create-tag-dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Badge } from "@ui/components/badge";
import type { TaggableObjectType } from "@repo/database/src/types/object";

const TagsSettingsPageContent = () => {
  const router = useRouter();
  const { data: rawTags, isLoading } = useAllTags();

  // Transform tags to include _id field expected by settings table
  const tags = React.useMemo(() => 
    rawTags?.map((tag: any) => ({ ...tag, _id: tag.id })) || []
  , [rawTags]);

  const handleTagClick = (event: React.MouseEvent, tag: any) => {
    event.preventDefault();
    router.push(`${window.location.pathname}/${tag.id}`);
  };

  const columns = React.useMemo(() => createColumns(), []);

  if (isLoading) {
    return (
      <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
      <div className="flex flex-row justify-between items-start mb-6">
        <div>
          <h1 className="text-2xl font-semibold mb-2">Tags</h1>
          <p className="text-muted-foreground text-md">
            Manage tags and their permissions across your organization
          </p>
        </div>
        <CreateTagDialog />
      </div>

      <div className="space-y-8">
        <DataTable 
          data={tags || []} 
          columns={columns} 
          usePagination={false} 
          useTopBar={false} 
          filterableColumns={filterableColumns}
          searchableColumns={["name", "objectType"]}
          onRowClick={handleTagClick}
        />
      </div>
    </div>
  );
};

export default TagsSettingsPageContent;