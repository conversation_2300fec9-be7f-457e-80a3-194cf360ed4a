import React from "react";
import { cn } from "@ui/lib";

interface CustomRadioButtonProps {
  value: string;
  checked: boolean;
  onChange: (value: string) => void;
  title: string;
  description: string;
  icon: React.ReactNode;
  disabled?: boolean;
}

export function CustomRadioButton({
  value,
  checked,
  onChange,
  title,
  description,
  icon,
  disabled = false,
}: CustomRadioButtonProps) {
  return (
    <button
      type="button"
      role="radio"
      aria-checked={checked}
      data-state={checked ? "checked" : "unchecked"}
      value={value}
      onClick={() => !disabled && onChange(value)}
      disabled={disabled}
      className={cn(
        "outline-none margin-0 bg-transparent w-full h-full flex flex-row items-center gap-2.5 p-2 pr-4 rounded-2xl border transition-all duration-300",
        "border-border hover:bg-muted/20",
        checked && "border-blue-500 shadow-[0_0_0_2px_rgba(41,60,96,1)]",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {/* Icon */}
      <div className="w-10 h-10 flex-shrink-0">
        {icon}
      </div>
      
      {/* Content */}
      <div className="flex flex-col items-start justify-start gap-0 flex-1">
        <div className="text-sm font-medium text-foreground">
          {title}
        </div>
        <div className="text-xs text-muted-foreground text-left">
          {description}
        </div>
      </div>
      
      {/* Custom radio indicator */}
      <span
        data-state={checked ? "checked" : "unchecked"}
        className={cn(
          "w-[18px] h-[18px] rounded-full flex-shrink-0 border border-border bg-sidebar flex items-center justify-center ml-auto",
          checked && "border-blue-500"
        )}
      >
        {checked && (
          <div className="w-2 h-2 rounded-full bg-blue-500" />
        )}
      </span>
    </button>
  );
}

// SVG Icons for each sharing level
export const MetadataOnlyIcon = () => (
  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_metadata)">
      <rect width="40" height="40" rx="12" fill="#17191C"/>
      <rect x="8.4" y="8.4" width="119.2" height="93.1999" rx="9.6" fill="#1B1D21"/>
      <rect x="8.4" y="8.4" width="119.2" height="93.1999" rx="9.6" stroke="#27282B" strokeWidth="0.8"/>
      <path d="M14.25 22C14.25 17.7198 17.7198 14.25 22 14.25C26.2802 14.25 29.75 17.7198 29.75 22C29.75 26.2803 26.2802 29.7501 22 29.7501C17.7198 29.7501 14.25 26.2803 14.25 22Z" fill="#17191C"/>
      <path d="M14.25 22C14.25 17.7198 17.7198 14.25 22 14.25C26.2802 14.25 29.75 17.7198 29.75 22C29.75 26.2803 26.2802 29.7501 22 29.7501C17.7198 29.7501 14.25 26.2803 14.25 22Z" stroke="#313337" strokeWidth="0.5"/>
      <rect x="19" y="20.8333" width="6" height="4.33333" rx="1.33333" fill="#5C5E63" stroke="#5C5E63" strokeWidth="0.666667" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M20.6667 20.5V19.8333C20.6667 19.097 21.2637 18.5 22.0001 18.5C22.7365 18.5 23.3334 19.097 23.3334 19.8333V20.5" stroke="#5C5E63" strokeWidth="0.666667" strokeLinecap="round" strokeLinejoin="round"/>
      <rect x="36" y="15" width="48" height="6" rx="3" fill="#17191C"/>
      <rect opacity="0.6" x="36" y="23" width="86" height="6" rx="3" fill="#17191C"/>
    </g>
    <rect x="0.5" y="0.5" width="39" height="39" rx="11.5" stroke="rgba(255, 255, 255, 0.06)" opacity="1"/>
    <defs>
      <clipPath id="clip0_metadata">
        <rect width="40" height="40" rx="12" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

export const SubjectLineIcon = () => (
  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_subject)">
      <rect width="40" height="40" rx="12" fill="#17191C"/>
      <rect x="8.4" y="8.4" width="119.2" height="93.1999" rx="9.6" fill="#1B1D21"/>
      <rect x="8.4" y="8.4" width="119.2" height="93.1999" rx="9.6" stroke="#27282B" strokeWidth="0.8"/>
      <path d="M14.25 22C14.25 17.7198 17.7198 14.25 22 14.25C26.2802 14.25 29.75 17.7198 29.75 22C29.75 26.2803 26.2802 29.7501 22 29.7501C17.7198 29.7501 14.25 26.2803 14.25 22Z" fill="#17191C"/>
      <path d="M14.25 22C14.25 17.7198 17.7198 14.25 22 14.25C26.2802 14.25 29.75 17.7198 29.75 22C29.75 26.2803 26.2802 29.7501 22 29.7501C17.7198 29.7501 14.25 26.2803 14.25 22Z" stroke="#313337" strokeWidth="0.5"/>
      <rect x="19" y="20.8333" width="6" height="4.33333" rx="1.33333" fill="#5C5E63" stroke="#5C5E63" strokeWidth="0.666667" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M20.6667 20.5V19.8333C20.6667 19.097 21.2637 18.5 22.0001 18.5C22.7365 18.5 23.3334 19.097 23.3334 19.8333V20.5" stroke="#5C5E63" strokeWidth="0.666667" strokeLinecap="round" strokeLinejoin="round"/>
      <rect x="36" y="15" width="48" height="6" rx="3" fill="#293C60"/>
      <rect opacity="0.6" x="36" y="23" width="86" height="6" rx="3" fill="#17191C"/>
    </g>
    <rect x="0.5" y="0.5" width="39" height="39" rx="11.5" stroke="rgba(255, 255, 255, 0.06)" opacity="1"/>
    <defs>
      <clipPath id="clip0_subject">
        <rect width="40" height="40" rx="12" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

export const FullAccessIcon = () => (
  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_full)">
      <rect width="40" height="40" rx="12" fill="#293C60"/>
      <rect x="7.6" y="7.6" width="120.8" height="94.7999" rx="10.4" fill="#1B1D21"/>
      <rect x="7.6" y="7.6" width="120.8" height="94.7999" rx="10.4" stroke="#293C60" strokeWidth="0.8"/>
      <rect opacity="0.05" x="11" y="11" width="114" height="22" rx="6.85714" fill="#266DF0"/>
      <path d="M14.3 22C14.3 17.7475 17.7474 14.3 22 14.3C26.2526 14.3 29.7 17.7475 29.7 22C29.7 26.2526 26.2526 29.7001 22 29.7001C17.7474 29.7001 14.3 26.2526 14.3 22Z" fill="#293C60"/>
      <path d="M14.3 22C14.3 17.7475 17.7474 14.3 22 14.3C26.2526 14.3 29.7 17.7475 29.7 22C29.7 26.2526 26.2526 29.7001 22 29.7001C17.7474 29.7001 14.3 26.2526 14.3 22Z" stroke="#293C60" strokeWidth="0.6"/>
      <path d="M19.9117 26H24.0883C24.325 26 24.4434 26 24.538 25.9816C24.9893 25.8938 25.3226 25.5095 25.3457 25.0502C25.3506 24.954 25.3338 24.8368 25.3004 24.6025L25.3003 24.6024C25.2485 24.2396 25.2226 24.0581 25.1748 23.9047C24.9491 23.1808 24.3333 22.6467 23.5847 22.5256C23.4261 22.5 23.2428 22.5 22.8762 22.5H21.1238C20.7572 22.5 20.5739 22.5 20.4153 22.5256C19.6667 22.6467 19.0509 23.1808 18.8252 23.9047C18.7774 24.0581 18.7515 24.2396 18.6996 24.6025C18.6662 24.8368 18.6494 24.954 18.6543 25.0502C18.6774 25.5095 19.0107 25.8938 19.462 25.9816C19.5566 26 19.675 26 19.9117 26Z" fill="#266DF0"/>
      <circle cx="22" cy="20.25" r="1.75" fill="#86888D"/>
      <rect x="36" y="15" width="48" height="6" rx="3" fill="#293C60"/>
      <rect x="36" y="23" width="86" height="6" rx="3" fill="#293C60"/>
      <path d="M14.3 44C14.3 39.7475 17.7474 36.3 22 36.3C26.2526 36.3 29.7 39.7475 29.7 44C29.7 48.2526 26.2526 51.7001 22 51.7001C17.7474 51.7001 14.3 48.2526 14.3 44Z" fill="#293C60"/>
      <path d="M14.3 44C14.3 39.7475 17.7474 36.3 22 36.3C26.2526 36.3 29.7 39.7475 29.7 44C29.7 48.2526 26.2526 51.7001 22 51.7001C17.7474 51.7001 14.3 48.2526 14.3 44Z" stroke="#293C60" strokeWidth="0.6"/>
      <rect x="36" y="37" width="48" height="6" rx="3" fill="#293C60"/>
    </g>
    <rect x="0.5" y="0.5" width="39" height="39" rx="11.5" stroke="#314872" opacity="0.6"/>
    <defs>
      <clipPath id="clip0_full">
        <rect width="40" height="40" rx="12" fill="white"/>
      </clipPath>
    </defs>
  </svg>
); 