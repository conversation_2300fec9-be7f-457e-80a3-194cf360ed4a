"use client";

import React, { useState, useEffect } from "react"; 
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import { Label } from "@ui/components/label";
import { cn } from "@ui/lib";
import { 
  ArrowLeftIcon, 
  ExternalLinkIcon,
  InfoIcon,
  PlusIcon,
  XIcon
} from "lucide-react";
import { Logo } from "@shared/components/Logo";
import { AddToBlocklistModal } from "./components/AddToBlocklistModal";
import { CustomRadioButton, MetadataOnlyIcon, SubjectLineIcon, FullAccessIcon } from "./components/CustomRadioButton";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import { useForwardingEmailConfig } from "@app/organizations/lib/use-forwarding-email-config";
import { toast } from "sonner";
import { IconCalendar, IconFolders, IconFolderSymlink, IconListLetters, IconMail, IconPlus, IconSettings, IconSignature, IconSquare, IconSquareRounded, IconUser, IconUserCancel, IconWorld } from "@tabler/icons-react";
import { DataTable } from "@ui/components/settings-table";
import type { ColumnDef } from "@tanstack/react-table";
import { ColumnHeader } from "@ui/components/column-header";

type SharingLevel = "metadata" | "subject" | "full";

interface BlocklistEntry {
  _id: string;
  entry: string;
  type: "email" | "domain";
  date?: string;
}

interface ForwardingEmailConfig {
  id: string;
  organizationId: string;
  address: string;
  isActive: boolean;
  defaultSharingLevel: SharingLevel;
  individualSharing?: Array<{ userId: string; level: SharingLevel }>;
  blockedEmails: string[];
  blockedDomains: string[];
  autoCreateContacts: boolean;
  autoCreateCompanies: boolean;
  createdAt: string;
  updatedAt: string;
}

const createBlocklistColumns = (onRemove: (entry: string) => void): ColumnDef<BlocklistEntry>[] => [
  {
    accessorKey: "entry",
    header: () => <ColumnHeader title="Domain / Email" icon={IconUser} />,
    cell: ({ row }) => {
      const type = row.original.type;
      return (
        <div className="flex items-center gap-1">
          {type === "domain" ? <IconWorld className="size-4 text-muted-foreground" /> : <IconMail className="size-4 text-muted-foreground" />}
          <span className="font-mono text-xs">{row.original.entry}</span>
        </div>
      )
    },
  },
  {
    accessorKey: "type",
    header: () => <ColumnHeader title="Type" icon={IconListLetters} />,
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      return (
        <Badge className="capitalize text-xs" variant="views">
          {type}
        </Badge>
      );
    },
  },
  {
    accessorKey: "date",
    header: () => <ColumnHeader title="Date" icon={IconCalendar} />,
    cell: ({ row }) => {
      const dateValue = row.getValue("date") as string;
      return (
        <span className="text-xs text-muted-foreground">{dateValue ? new Date(dateValue).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) : 'Unknown'}</span>
      );
    },
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => (
      <div className="flex justify-end">
      <Button
        variant="relio"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          onRemove(row.getValue("entry"));
        }}
        className="h-auto p-0.5 px-1"
      >
        Unblock
      </Button>
      </div>
    ),
  },
];

interface ConnectedAccount {
  id: string;
  provider: string;
  accountId: string;
  email: string;
  name: string;
  picture: string;
  services: string[];
  status: string;
  accessTokenExpiresAt: Date | null;
  scope: string | null;
  createdAt: Date;
}

interface EmailAccountSettingsClientProps {
  organizationSlug: string;
  accountId: string;
  accountType?: "forwarding" | "connected";
}

export function EmailAccountSettingsClient({ 
  organizationSlug,
  accountId,
  accountType 
}: EmailAccountSettingsClientProps) {
  const router = useRouter();
  const { activeOrganization } = useActiveOrganization();
  const [accountData, setAccountData] = useState<ForwardingEmailConfig | ConnectedAccount | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAccountData = async () => {
      if (!activeOrganization?.id && accountType !== "connected") return;
      
      try {
        // Determine the API endpoint based on account type
        let endpoint: string;
        
        if (accountType === "connected") {
          endpoint = `/api/connected-accounts/${accountId}`;
        } else {
          // Default to forwarding for backward compatibility
          endpoint = `/api/forwarding-email-config/${activeOrganization?.id}`;
        }
        
        const response = await fetch(endpoint, {
          credentials: "include",
        });
        
        if (response.ok) {
          const data = await response.json();
          setAccountData(data);
        } else {
          toast.error(`Failed to load ${accountType || "forwarding"} account configuration`);
        }
      } catch (error) {
        console.error("Error fetching account data:", error);
        toast.error(`Failed to load ${accountType || "forwarding"} account configuration`);
      } finally {
        setLoading(false);
      }
    };

    fetchAccountData();
  }, [activeOrganization?.id, accountId, accountType]);

  const handleBack = () => {
    router.push(`/app/${organizationSlug}/settings/account/email-calendar`);
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-32"></div>
          <div className="h-12 bg-muted rounded"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!accountData) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">Account configuration not found.</p>
        </div>
      </div>
    );
  }

  // Render different components based on account type
  if (accountType === "connected") {
    return (
      <ConnectedAccountSettings 
        account={accountData as ConnectedAccount}
        organizationSlug={organizationSlug}
        onBack={handleBack}
      />
    );
  }
  
  return (
    <ForwardingAccountSettings 
      config={accountData as ForwardingEmailConfig}
      organizationSlug={organizationSlug}
      onBack={handleBack}
    />
  );
}

// Component for Connected Account Settings
function ConnectedAccountSettings({
  account,
  organizationSlug,
  onBack
}: {
  account: ConnectedAccount;
  organizationSlug: string;
  onBack: () => void;
}) {
  const [reconnecting, setReconnecting] = useState(false);
  const [signatures, setSignatures] = useState<any[]>([]);
  const [newSignatureName, setNewSignatureName] = useState("New signature");
  const [selectedSignature, setSelectedSignature] = useState<string | null>(null);
  const [signatureContent, setSignatureContent] = useState("");
  const [sharingLevel, setSharingLevel] = useState<SharingLevel>("subject");
  const [showAddBlocklistModal, setShowAddBlocklistModal] = useState(false);
  const [blocklist, setBlocklist] = useState<string[]>([]);

  const handleSharingLevelChange = async (value: string) => {
    setSharingLevel(value as SharingLevel);
    // TODO: Add API call to save sharing level for connected accounts
    toast.success("Sharing level updated");
  };

  const handleAddToBlocklist = async (entries: string[]) => {
    try {
      // Add date to each entry
      const entriesWithDate = entries.map(entry => `${entry}|${new Date().toISOString()}`);
      
      // TODO: Add API call to save blocklist for connected accounts
      setBlocklist(prev => [...prev, ...entriesWithDate]);
      toast.success(`Added ${entries.length} ${entries.length === 1 ? 'entry' : 'entries'} to blocklist`);
      setShowAddBlocklistModal(false);
      return true;
    } catch (error) {
      console.error("Error adding to blocklist:", error);
      toast.error("Failed to add to blocklist");
      return false;
    }
  };

  const handleRemoveFromBlocklist = async (entry: string) => {
    try {
      // TODO: Add API call to remove from blocklist for connected accounts
      // Filter out items that start with the entry (to handle both old format and new format with dates)
      setBlocklist(prev => prev.filter(item => {
        const [itemEntry] = item.includes('|') ? item.split('|') : [item];
        return itemEntry !== entry;
      }));
      toast.success("Removed from blocklist");
    } catch (error) {
      console.error("Error removing from blocklist:", error);
      toast.error("Failed to remove from blocklist");
    }
  };

  const handleReconnect = async () => {
    setReconnecting(true);
    
    try {
      const response = await fetch(`/api/connected-accounts/${account.id}/reconnect`, {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        toast.success("Email account reconnected successfully");
      } else {
        toast.error("Failed to reconnect email account");
      }
    } catch (error) {
      console.error("Error reconnecting account:", error);
      toast.error("Failed to reconnect email account");
    } finally {
      setReconnecting(false);
    }
  };

  const handleCreateSignature = () => {
    const newSignature = {
      id: Date.now().toString(),
      name: newSignatureName,
      content: "",
      isDefault: signatures.length === 0
    };
    setSignatures(prev => [...prev, newSignature]);
    setSelectedSignature(newSignature.id);
    setSignatureContent("");
  };

  // Transform blocklist data for table
  const blocklistData: BlocklistEntry[] = React.useMemo(() => {
    return blocklist.map((item, index) => {
      const [entry, dateStr] = item.includes('|') ? item.split('|') : [item, null];
      const date = dateStr ? new Date(dateStr).toLocaleDateString() : 'Unknown';
      return {
        _id: `${index}`,
        entry,
        type: entry.includes('@') ? 'email' : 'domain' as "email" | "domain",
        date
      };
    });
  }, [blocklist]);

  // Create columns with remove handler
  const blocklistColumns = React.useMemo(() => 
    createBlocklistColumns(handleRemoveFromBlocklist), 
    [handleRemoveFromBlocklist]
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <Button
          variant="ghost"
          onClick={onBack}
          className="p-1 h-auto text-muted-foreground hover:text-foreground"
        >
          <ArrowLeftIcon className="size-4 mr-2" />
          Back
        </Button>

        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 bg-sidebar border">
            {/* Provider icon */}
            {account.provider === 'google' && (
              <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
              </svg>
            )}
            {account.provider === 'microsoft' && (
              <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
                <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z" fill="#00BCF2"/>
              </svg>
            )}
            {!['google', 'microsoft'].includes(account.provider) && <Logo withLabel={false} />}
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold">{account.email}</h1>
            <div className="text-muted-foreground">
              <span>Update your account permissions and settings. </span>
              <a 
                href="/help/connected-accounts" 
                className="text-foreground hover:underline inline-flex items-center gap-1"
              >
                Learn more
                <ExternalLinkIcon className="size-3" />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-fit grid-cols-3">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <IconSettings className="size-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="blocklist" className="flex items-center gap-2">
            <IconUserCancel className="size-4" />
            Blocklist
          </TabsTrigger>
          <TabsTrigger disabled value="signatures" className="flex items-center gap-2 cursor-not-allowed">
            <IconSignature className="size-4" />
            Signatures
          </TabsTrigger>
        </TabsList>

        {/* General Tab */}
        <TabsContent value="general" className="space-y-8">
          {/* Default sharing */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Default sharing</h2>
            
            <div role="radiogroup" className="flex flex-col items-start w-full gap-2">
              <CustomRadioButton
                value="metadata"
                checked={sharingLevel === "metadata"}
                onChange={handleSharingLevelChange}
                title="Metadata only"
                description="The email participants and timestamp will be visible to anyone in your workspace"
                icon={<MetadataOnlyIcon />}
              />
              
              <CustomRadioButton
                value="subject"
                checked={sharingLevel === "subject"}
                onChange={handleSharingLevelChange}
                title="Subject line and metadata"
                description="We'll share the subject, participants and timestamp with anyone in your workspace"
                icon={<SubjectLineIcon />}
              />
              
              <CustomRadioButton
                value="full"
                checked={sharingLevel === "full"}
                onChange={handleSharingLevelChange}
                title="Full access"
                description="Everything is shared with your workspace (including the body, subject line, attachments)"
                icon={<FullAccessIcon />}
              />
            </div>
          </div>

          {/* Individual sharing */}
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold">Individual sharing</h2>
              <p className="text-muted-foreground">
                Share full access to your emails with specific individuals
              </p>
            </div>

            <div className="bg-sidebar border border-border rounded-xl p-5 flex flex-col gap-3 justify-center items-center">
              <div className="inline-flex flex-row items-center h-5 rounded-lg shadow-[rgb(49,72,114)_0px_0px_0px_1px_inset] bg-slate-700 px-1.5 gap-1">
                <span className="font-medium text-xs leading-4 text-white">
                  Growth
                </span>
              </div>
              <div className="flex flex-col items-center justify-start gap-1">
                <div className="font-medium text-sm leading-5 text-[rgb(238,239,241)]">
                  Upgrade to set individual access
                </div>
                <div className="font-medium text-sm leading-5 text-[rgb(159,161,167)]">
                  Share the email content you want with who you want
                </div>
              </div>
              <Button disabled variant="action">
                {/* Upgrade now */}
                Coming soon
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Blocklist Tab */}
        <TabsContent value="blocklist" className="space-y-6">
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold">Blocklist</h2>
              <p className="text-muted-foreground">
                Emails from blocklisted domains and email addresses will not appear in Relio
              </p>
            </div>

            {blocklist.length === 0 ? (
              <div className="text-center py-16 space-y-6 flex flex-col items-center justify-center">
                <div className="flex justify-center">
                  <div className="size-16 rounded-full bg-muted flex items-center justify-center">
                    <XIcon className="size-8 text-muted-foreground" />
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">No emails or domains yet</h3>
                  <p className="text-muted-foreground">
                    Add emails or domains to the blocklist
                  </p>
                </div>
                <Button variant="action" className="gap-2" onClick={() => setShowAddBlocklistModal(true)}>
                  <PlusIcon className="size-4" />
                  Add to blocklist
                </Button> 
              </div>
            ) : (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <div className="space-y-1">
                    <h3 className="text-lg font-semibold">Blocked entries</h3>
                    <p className="text-sm text-muted-foreground">
                      {blocklist.length} blocked {blocklist.length === 1 ? 'entry' : 'entries'}
                    </p>
                  </div>
                  <Button variant="action" className="gap-2" onClick={() => setShowAddBlocklistModal(true)}>
                    <PlusIcon className="size-4" />
                    Add to blocklist
                  </Button>
                </div>

                <DataTable 
                  data={blocklistData} 
                  columns={blocklistColumns} 
                  usePagination={false} 
                  useTopBar={false} 
                  searchableColumns={["entry", "type"]}
                />
              </div>
            )}
          </div>
        </TabsContent>

        {/* Signatures Tab */}
        <TabsContent value="signatures" className="space-y-6">
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold">Signatures</h2>
              <p className="text-muted-foreground">
                Create multiple signatures for your mailbox, and set up your default signature.
              </p>
            </div>

            {signatures.length === 0 ? (
              <div className="text-center py-16 space-y-6 flex flex-col items-center justify-center">
                <div className="flex justify-center">
                  <div className="size-16 rounded-full bg-muted flex items-center justify-center">
                    <svg className="size-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.546-3.879-8.25-8.625-8.25a9.06 9.06 0 00-1.5.124m0 15.125a3.375 3.375 0 118.625-4.875c0 .621-.504 1.125-1.125 1.125H6.75a1.125 1.125 0 01-1.125-1.125v-3.375m0 3.375c0 .621.504 1.125 1.125 1.125h7.5a1.125 1.125 0 001.125-1.125v-3.375" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">No email signatures</h3>
                  <p className="text-muted-foreground">
                    You have not created any signatures.
                  </p>
                </div>
                <Button onClick={handleCreateSignature} variant="action">
                  Create first signature
                </Button>
              </div>
            ) : (
              <div className="flex gap-6">
                {/* Signatures list */}
                <div className="w-80 space-y-2">
                  {signatures.map((signature) => (
                    <div
                      key={signature.id}
                      className={cn(
                        "p-3 rounded-lg border cursor-pointer transition-colors",
                        selectedSignature === signature.id
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                          : "border-border hover:bg-muted/50"
                      )}
                      onClick={() => setSelectedSignature(signature.id)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{signature.name}</span>
                        {signature.isDefault && (
                          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs">
                            Default
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  <Button variant="relio" onClick={handleCreateSignature} className="w-full justify-start gap-2">
                    <IconPlus className="size-4" />
                    New signature
                  </Button>
                </div>

                {/* Signature editor */}
                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label>Signature name</Label>
                    <input
                      type="text"
                      value={selectedSignature ? signatures.find(s => s.id === selectedSignature)?.name || "" : newSignatureName}
                      onChange={(e) => {
                        if (selectedSignature) {
                          setSignatures(prev => prev.map(s => 
                            s.id === selectedSignature ? { ...s, name: e.target.value } : s
                          ));
                        } else {
                          setNewSignatureName(e.target.value);
                        }
                      }}
                      className="w-full p-2 rounded-lg border border-border bg-sidebar"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Signature content</Label>
                    <textarea
                      value={signatureContent}
                      onChange={(e) => setSignatureContent(e.target.value)}
                      placeholder="Start typing your signature..."
                      className="w-full h-64 p-3 rounded-lg border border-border bg-sidebar resize-none"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Add to Blocklist Modal */}
      <AddToBlocklistModal
        open={showAddBlocklistModal}
        onClose={() => setShowAddBlocklistModal(false)}
        onAdd={handleAddToBlocklist}
      />
    </div>
  );
}

// Component for Forwarding Account Settings (existing functionality)
function ForwardingAccountSettings({
  config,
  organizationSlug,
  onBack
}: {
  config: ForwardingEmailConfig;
  organizationSlug: string;
  onBack: () => void;
}) {
  const { activeOrganization } = useActiveOrganization();
  const [showAddBlocklistModal, setShowAddBlocklistModal] = useState(false);
  
  const {
    loading,
    connecting,
    isActive,
    sharingLevel,
    blocklist,
    updateSharingLevel,
    addToBlocklist,
    removeFromBlocklist,
    connect,
  } = useForwardingEmailConfig(activeOrganization?.id, config);

  const handleAddToBlocklist = async (entries: string[]) => {
    const success = await addToBlocklist(entries);
    if (success) {
      setShowAddBlocklistModal(false);
    }
  };

  const handleSharingLevelChange = async (value: string) => {
    await updateSharingLevel(value as SharingLevel);
  };

  // Transform blocklist data for table
  const blocklistData: BlocklistEntry[] = React.useMemo(() => {
    return blocklist.map((item, index) => {
      // Check if the item has a date (new format with pipe separator)
      const [entry, dateStr] = item.includes('|') ? item.split('|') : [item, null];
      const date = dateStr ? new Date(dateStr).toLocaleDateString() : 'Unknown';
      return {
        _id: `${index}`,
        entry,
        type: entry.includes('@') ? 'email' : 'domain' as "email" | "domain",
        date
      };
    });
  }, [blocklist]);

  // Create columns with remove handler
  const blocklistColumns = React.useMemo(() => 
    createBlocklistColumns(removeFromBlocklist), 
    [removeFromBlocklist]
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6 relative">
      {/* Inactive Overlay */}
      {!isActive && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-sidebar/80 backdrop-blur-sm rounded-4xl">
          <div className="text-center space-y-6 max-w-md flex flex-col items-center">
            <div className="flex justify-center">
              <div className="size-16 rounded-full bg-muted flex items-center justify-center">
                <svg className="size-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                </svg>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Forwarding email is inactive</h3>
              <p className="text-muted-foreground">
                Connect your forwarding email to start receiving and processing emails in your CRM.
              </p>
            </div>
            <Button 
              onClick={connect} 
              disabled={connecting}
              className="bg-blue-500 hover:bg-blue-600"
            >
              {connecting ? "Connecting..." : "Connect forwarding email"}
            </Button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className={cn("space-y-4", !isActive && "blur-sm pointer-events-none")}>
        <Button
          variant="ghost"
          onClick={onBack}
          className="p-1 h-auto text-muted-foreground hover:text-foreground"
        >
          <ArrowLeftIcon className="size-4 mr-2" />
          Back
        </Button>

        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 bg-sidebar border">
            <Logo withLabel={false} />
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold">{config.address}</h1>
            <div className="text-muted-foreground">
              <span>Update your forwarding email permissions and settings. </span>
              <a 
                href="/help/email-forwarding" 
                className="text-foreground hover:underline inline-flex items-center gap-1"
              >
                Learn more
                <ExternalLinkIcon className="size-3" />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="general" className={cn("space-y-6", !isActive && "blur-sm pointer-events-none")}>
        <TabsList className="grid w-fit grid-cols-2">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <IconSettings className="size-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="blocklist" className="flex items-center gap-2">
            <IconUserCancel className="size-4" />
            Blocklist
          </TabsTrigger>
        </TabsList>

        {/* General Tab */}
        <TabsContent value="general" className="space-y-8">
          {/* Default sharing */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Default sharing</h2>
            
            <div role="radiogroup" className="flex flex-col items-start w-full gap-2">
              <CustomRadioButton
                value="metadata"
                checked={sharingLevel === "metadata"}
                onChange={handleSharingLevelChange}
                title="Metadata only"
                description="The email participants and timestamp will be visible to anyone in your workspace"
                icon={<MetadataOnlyIcon />}
                disabled={!isActive}
              />
              
              <CustomRadioButton
                value="subject"
                checked={sharingLevel === "subject"}
                onChange={handleSharingLevelChange}
                title="Subject line and metadata"
                description="We'll share the subject, participants and timestamp with anyone in your workspace"
                icon={<SubjectLineIcon />}
                disabled={!isActive}
              />
              
              <CustomRadioButton
                value="full"
                checked={sharingLevel === "full"}
                onChange={handleSharingLevelChange}
                title="Full access"
                description="Everything is shared with your workspace (including the body, subject line, attachments)"
                icon={<FullAccessIcon />}
                disabled={!isActive}
              />
            </div>
          </div>

          {/* Individual sharing */}
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold">Individual sharing</h2>
              <p className="text-muted-foreground">
                Share full access to your emails with specific individuals
              </p>
            </div>

            <div className="bg-sidebar border border-border rounded-xl p-5 flex flex-col gap-3 justify-center items-center">
              <div className="inline-flex flex-row items-center h-5 rounded-lg shadow-[rgb(49,72,114)_0px_0px_0px_1px_inset] bg-slate-700 px-1.5 gap-1">
                <span className="font-medium text-xs leading-4 text-white">
                  Growth
                </span>
              </div>
              <div className="flex flex-col items-center justify-start gap-1">
                <div className="font-medium text-sm leading-5 text-[rgb(238,239,241)]">
                  Upgrade to set individual access
                </div>
                <div className="font-medium text-sm leading-5 text-[rgb(159,161,167)]">
                  Share the email content you want with who you want
                </div>
              </div>
              <Button disabled variant="action">
                {/* Upgrade now */}
                Coming soon
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Blocklist Tab */}
        <TabsContent value="blocklist" className="space-y-6">
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold">Blocklist</h2>
              <p className="text-muted-foreground">
                Emails from blocklisted domains and email addresses will not appear in Relio
              </p>
            </div>

            {blocklist.length === 0 ? (
              <div className="text-center py-16 space-y-6 flex flex-col items-center">
                <div className="flex justify-center">
                  <div className="size-16 rounded-full bg-muted flex items-center justify-center">
                    <XIcon className="size-8 text-muted-foreground" />
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">No emails or domains yet</h3>
                  <p className="text-muted-foreground">
                    Add emails or domains to the blocklist
                  </p>
                </div>
                <Button variant="action" className="gap-2" onClick={() => setShowAddBlocklistModal(true)}>
                  <PlusIcon className="size-4" />
                  Add to blocklist
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <div className="space-y-1">
                    <h3 className="text-lg font-semibold">Blocked entries</h3>
                    <p className="text-sm text-muted-foreground">
                      {blocklist.length} blocked {blocklist.length === 1 ? 'entry' : 'entries'}
                    </p>
                  </div>
                  <Button variant="action" className="gap-2" onClick={() => setShowAddBlocklistModal(true)}>
                    <PlusIcon className="size-4" />
                    Add to blocklist
                  </Button>
                </div>

                <DataTable 
                  data={blocklistData} 
                  columns={blocklistColumns} 
                  usePagination={false} 
                  useTopBar={false} 
                  searchableColumns={["entry", "type"]}
                />
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Add to Blocklist Modal */}
      <div className={cn(!isActive && "blur-sm pointer-events-none")}>
        <AddToBlocklistModal
          open={showAddBlocklistModal}
          onClose={() => setShowAddBlocklistModal(false)}
          onAdd={handleAddToBlocklist}
        />
      </div>
    </div>
  );
}

// Keep the old export for backward compatibility
export const ForwardingEmailSettingsClient = EmailAccountSettingsClient; 