"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { 
  MoreHorizontalIcon, 
  TrashIcon, 
  PaletteIcon,
  TagIcon,
  UsersIcon,
} from "lucide-react";
import { cn } from "@ui/lib";
import { useDeleteTag, useUpdateTag } from "@app/shared/hooks/useTags";
import { toast } from "sonner";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import type { TaggableObjectType } from "@repo/database/src/types/object";

interface Tag {
  id: string;
  name: string;
  color: string | null;
  objectType: TaggableObjectType;
  creator: {
    id: string;
    name: string;
    image: string | null;
  };
  permissions: Array<{
    id: string;
    role: "viewer" | "editor" | "admin";
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }>;
  _count: {
    objectTags: number;
  };
  createdAt: string;
}

const objectTypeLabels: Record<TaggableObjectType, string> = {
  contact: "Contacts",
  company: "Companies", 
  property: "Properties",
  custom_object: "Custom Objects",
};

const objectTypeColors: Record<TaggableObjectType, string> = {
  contact: "bg-blue-100 text-blue-800",
  company: "bg-green-100 text-green-800",
  property: "bg-purple-100 text-purple-800",
  custom_object: "bg-orange-100 text-orange-800",
};

function TagActionsDropdown({ tag }: { tag: Tag }) {
  const [editColorOpen, setEditColorOpen] = useState(false);
  const [color, setColor] = useState(tag.color || "#6b7280");
  
  const deleteTag = useDeleteTag();
  const updateTag = useUpdateTag();

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!confirm(`Are you sure you want to delete the tag "${tag.name}"? This will remove it from all ${tag._count.objectTags} records.`)) {
      return;
    }

    try {
      await deleteTag.mutateAsync(tag.id);
      toast.success("Tag deleted successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete tag");
    }
  };

  const handleUpdateColor = async () => {
    try {
      await updateTag.mutateAsync({ id: tag.id, color });
      toast.success("Tag color updated successfully");
      setEditColorOpen(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update tag");
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="data-[state=open]:bg-muted text-muted-foreground flex size-6"
            size="icon"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontalIcon className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem 
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setEditColorOpen(true);
            }}
            className="flex items-center gap-x-2"
          >
            <PaletteIcon className="h-4 w-4" />
            Edit Color
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={handleDelete}
            className="text-red-600 focus:text-red-600 flex items-center gap-x-2"
            disabled={deleteTag.isPending}
          >
            <TrashIcon className="h-4 w-4" />
            Delete Tag
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={editColorOpen} onOpenChange={setEditColorOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Tag Color</DialogTitle>
            <DialogDescription>
              Change the color for "{tag.name}"
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="color">Color</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="color"
                  type="color"
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  className="w-16 h-10"
                />
                <Badge
                  style={{
                    backgroundColor: `${color}20`,
                    borderColor: color,
                    color: color,
                  }}
                  className="border"
                >
                  {tag.name}
                </Badge>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setEditColorOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateColor} disabled={updateTag.isPending}>
              {updateTag.isPending ? "Updating..." : "Update Color"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export const createColumns = () => [
  {
    accessorKey: "name",
    header: () => (
      <div className="flex items-center gap-x-2">
        <TagIcon className="h-4 w-4" />
        Tag
      </div>
    ),
    cell: ({ row }: { row: { original: Tag } }) => {
      const tag = row.original;
      return (
        <Badge
          style={{
            backgroundColor: `${tag.color || '#6b7280'}20`,
            borderColor: tag.color || '#6b7280',
            color: tag.color || '#6b7280',
          }}
          className="border"
        >
          {tag.name}
        </Badge>
      );
    },
    enableHiding: false,
  },
  {
    accessorKey: "objectType",
    header: "Object Type",
    cell: ({ row }: { row: { original: Tag } }) => {
      const tag = row.original;
      return (
        <Badge 
          variant="views"
          className={cn("text-xs", objectTypeColors[tag.objectType])}
        >
          {objectTypeLabels[tag.objectType]}
        </Badge>
      );
    },
  },
  {
    accessorKey: "usage",
    header: "Usage",
    cell: ({ row }: { row: { original: Tag } }) => {
      const tag = row.original;
      return (
        <span className="text-sm">
          {tag._count.objectTags} record{tag._count.objectTags !== 1 ? 's' : ''}
        </span>
      );
    },
  },
  {
    accessorKey: "creator",
    header: "Creator",
    cell: ({ row }: { row: { original: Tag } }) => {
      const tag = row.original;
      return (
        <div className="flex items-center space-x-2">
          <Avatar className="h-6 w-6">
            <AvatarImage src={tag.creator.image || undefined} />
            <AvatarFallback>{tag.creator.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <span className="text-sm">{tag.creator.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "permissions",
    header: () => (
      <div className="flex items-center gap-x-2">
        <UsersIcon className="h-4 w-4" />
        Permissions
      </div>
    ),
    cell: ({ row }: { row: { original: Tag } }) => {
      const tag = row.original;
      return (
        <span className="text-sm text-muted-foreground">
          {tag.permissions.length} user{tag.permissions.length !== 1 ? 's' : ''}
        </span>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }: { row: { original: Tag } }) => {
      return <TagActionsDropdown tag={row.original} />;
    },
  },
];

export const filterableColumns = [
  {
    id: "objectType",
    label: "Object Type",
    options: [
      { label: "Contacts", value: "contact" },
      { label: "Companies", value: "company" },
      { label: "Properties", value: "property" },
      { label: "Custom Objects", value: "custom_object" },
    ]
  }
]; 