import { getSession, getUserAccounts } from "@app/auth/lib/server";
import { ActiveSessionsBlock } from "@app/settings/components/ActiveSessionsBlock";
import { ChangePasswordForm } from "@app/settings/components/ChangePassword";
import { PasskeysBlock } from "@app/settings/components/PasskeysBlock";
import { SetPasswordForm } from "@app/settings/components/SetPassword";
import { SettingsList } from "@app/shared/components/SettingsList";
import { config } from "@repo/config";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { SecuritySettingsClient } from "./client";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("settings.account.security.title"),
	};
}

export default async function AccountSettingsPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	const userAccounts = await getUserAccounts();

	const userHasPassword = userAccounts?.some(
		(account) => account.provider === "credential",
	);

	return (
		<>
			<SecuritySettingsClient />
			<SettingsList>
				{config.auth.enablePasswordLogin &&
					(userHasPassword ? (
						<ChangePasswordForm />
					) : (
						<SetPasswordForm />
					))}
				{config.auth.enablePasskeys && <PasskeysBlock />}
				<ActiveSessionsBlock />
			</SettingsList>
		</>
	);
}
