"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Label } from "@ui/components/label";
import { ArrowLeftIcon, TrashIcon, UsersIcon } from "lucide-react";
import { useAllTags, useOrganizationMembers, useAddTagPermission, useRemoveTagPermission } from "@app/shared/hooks/useTags";
import { cn } from "@ui/lib";
import { toast } from "sonner";
import type { TaggableObjectType } from "@repo/database/src/types/object";

interface Tag {
  id: string;
  name: string;
  color: string | null;
  objectType: TaggableObjectType;
  creator: {
    id: string;
    name: string;
    image: string | null;
  };
  permissions: Array<{
    id: string;
    role: "viewer" | "editor" | "admin";
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }>;
  _count: {
    objectTags: number;
  };
  createdAt: string;
}

interface User {
  id: string;
  name: string;
  image: string | null;
  email: string;
}

const objectTypeLabels: Record<TaggableObjectType, string> = {
  contact: "Contacts",
  company: "Companies", 
  property: "Properties",
  custom_object: "Custom Objects",
};

const objectTypeColors: Record<TaggableObjectType, string> = {
  contact: "bg-blue-100 text-blue-800",
  company: "bg-green-100 text-green-800",
  property: "bg-purple-100 text-purple-800",
  custom_object: "bg-orange-100 text-orange-800",
};

const roleLabels = {
  viewer: "Viewer",
  editor: "Editor", 
  admin: "Admin",
};

const roleColors = {
  viewer: "bg-gray-100 text-gray-800",
  editor: "bg-yellow-100 text-yellow-800",
  admin: "bg-red-100 text-red-800",
};

const TagSettingsPageContent = () => {
  const params = useParams();
  const router = useRouter();
  const { data: tags } = useAllTags();
  const { data: members } = useOrganizationMembers();
  const addPermission = useAddTagPermission();
  const removePermission = useRemoveTagPermission();
  
  const tagId = params.id as string;
  const tag = tags?.find((t: Tag) => t.id === tagId);

  const [selectedUserId, setSelectedUserId] = useState("");
  const [selectedRole, setSelectedRole] = useState<"viewer" | "editor" | "admin">("viewer");

  const availableMembers = members?.filter(
    (member: User) => !tag?.permissions.some((p: any) => p.user.id === member.id)
  ) || [];

  const handleAddPermission = async () => {
    if (!selectedUserId || !tag) return;
    
    try {
      await addPermission.mutateAsync({
        tagId: tag.id,
        userId: selectedUserId,
        role: selectedRole,
      });
      toast.success("Permission added successfully");
      setSelectedUserId("");
      setSelectedRole("viewer");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to add permission");
    }
  };

  const handleRemovePermission = async (userId: string) => {
    if (!tag) return;
    
    try {
      await removePermission.mutateAsync({
        tagId: tag.id,
        userId,
      });
      toast.success("Permission removed successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to remove permission");
    }
  };

  if (!tag) {
    return (
      <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
        <div className="text-center">
          <p className="text-muted-foreground">Tag not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
      <Button variant="ghost" className="mb-4" onClick={() => router.back()}>
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back
      </Button>
      
      <div className="flex flex-col gap-y-8">
        <div className="flex flex-row justify-between items-center">
          <div className="flex flex-row items-center gap-x-4">
            <Badge
              style={{
                backgroundColor: `${tag.color || '#6b7280'}20`,
                borderColor: tag.color || '#6b7280',
                color: tag.color || '#6b7280',
              }}
              className="border text-base px-3 py-1"
            >
              {tag.name}
            </Badge>
            <div className="flex flex-col gap-y-1">
                             <Badge 
                 variant="views"
                 className={cn("text-xs w-fit", objectTypeColors[tag.objectType as keyof typeof objectTypeColors])}
               >
                 {objectTypeLabels[tag.objectType as keyof typeof objectTypeLabels]}
               </Badge>
              <span className="text-sm text-muted-foreground">
                {tag._count.objectTags} record{tag._count.objectTags !== 1 ? 's' : ''}
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={tag.creator.image || undefined} />
              <AvatarFallback>{tag.creator.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-sm font-medium">{tag.creator.name}</span>
              <span className="text-xs text-muted-foreground">Creator</span>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold mb-4">Tag Permissions</h2>
            <p className="text-sm text-muted-foreground mb-6">
              Control who can view, edit, or manage this tag.
            </p>
          </div>

          {/* Add Permission */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Add Permission</h4>
            <div className="flex items-end space-x-2">
              <div className="flex-1">
                <Label>User</Label>
                <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a user" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableMembers.map((member: User) => (
                      <SelectItem key={member.id} value={member.id}>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={member.image || undefined} />
                            <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span>{member.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Role</Label>
                <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as any)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="viewer">Viewer</SelectItem>
                    <SelectItem value="editor">Editor</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={handleAddPermission}
                disabled={!selectedUserId || addPermission.isPending}
              >
                Add
              </Button>
            </div>
          </div>

          {/* Current Permissions */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Current Permissions</h4>
            <div className="space-y-2">
              {tag.permissions.length === 0 ? (
                <div className="text-center py-8 border rounded-lg border-dashed">
                  <UsersIcon className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No specific permissions set</p>
                  <p className="text-xs text-muted-foreground">
                    Tag inherits default organization permissions
                  </p>
                </div>
              ) : (
                tag.permissions.map((permission: any) => (
                  <div key={permission.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={permission.user.image || undefined} />
                        <AvatarFallback>{permission.user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{permission.user.name}</p>
                        <Badge 
                          variant="views" 
                          className={cn("text-xs", roleColors[permission.role as keyof typeof roleColors])}
                        >
                          {roleLabels[permission.role as keyof typeof roleLabels]}
                        </Badge>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemovePermission(permission.user.id)}
                      disabled={removePermission.isPending}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TagSettingsPageContent; 