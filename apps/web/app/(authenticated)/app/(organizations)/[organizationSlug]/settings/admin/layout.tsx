import { getSession } from "@app/auth/lib/server";
import { SettingsMenu } from "@app/settings/components/SettingsMenu";
import { SettingsPageHeader } from "@app/shared/components/SettingsPageHeader";
import { SidebarContentLayout } from "@app/shared/components/SidebarContentLayout";
import { HeaderProvider } from "@app/shared/contexts/HeaderContext";
import { config } from "@repo/config";
import { Logo } from "@shared/components/Logo";
import { Building2Icon, UsersIcon } from "lucide-react";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import type { PropsWithChildren } from "react";

export default async function AdminLayout({ children }: PropsWithChildren) {
	const t = await getTranslations();
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	if (session.user?.role !== "admin") {
		redirect("/app");
	}

	return (
		<HeaderProvider
			defaultTitle={t("admin.title")}
			defaultSubtitle={t("admin.description")}
		>
			<SettingsPageHeader />
			<SidebarContentLayout
			// sidebar={
			// 	<SettingsMenu
			// 		menuItems={[
			// 			{
			// 				avatar: (
			// 					<Logo
			// 						className="size-8"
			// 						withLabel={false}
			// 					/>
			// 				),
			// 				title: t("admin.title"),
			// 				items: [
			// 					{
			// 						title: t("admin.menu.users"),
			// 						href: "/app/admin/users",
			// 						icon: (
			// 							<UsersIcon className="size-4 opacity-50" />
			// 						),
			// 					},
			// 					...(config.organizations.enable
			// 						? [
			// 								{
			// 									title: t(
			// 										"admin.menu.organizations",
			// 									),
			// 									href: "/app/admin/organizations",
			// 									icon: (
			// 										<Building2Icon className="size-4 opacity-50" />
			// 									),
			// 								},
			// 							]
			// 						: []),
			// 				],
			// 			},
			// 		]}
			// 	/>
			// }
			>
				{children}
			</SidebarContentLayout>
		</HeaderProvider>
	);
}
