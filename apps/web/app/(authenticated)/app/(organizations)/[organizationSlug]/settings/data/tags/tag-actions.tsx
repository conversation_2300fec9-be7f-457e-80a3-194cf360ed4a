"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { 
  MoreHorizontalIcon, 
  TrashIcon, 
  PaletteIcon,
} from "lucide-react";
import { useDeleteTag, useUpdateTag } from "@app/shared/hooks/useTags";
import { toast } from "sonner";
import type { TaggableObjectType } from "@repo/database/src/types/object";

interface Tag {
  id: string;
  name: string;
  color: string | null;
  objectType: TaggableObjectType;
  creator: {
    id: string;
    name: string;
    image: string | null;
  };
  permissions: Array<{
    id: string;
    role: "viewer" | "editor" | "admin";
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }>;
  _count: {
    objectTags: number;
  };
  createdAt: string;
}

interface TagActionsDropdownProps {
  tag: Tag;
}

export function TagActionsDropdown({ tag }: TagActionsDropdownProps) {
  const [editColorOpen, setEditColorOpen] = useState(false);
  const [color, setColor] = useState(tag.color || "#6b7280");
  
  const deleteTag = useDeleteTag();
  const updateTag = useUpdateTag();

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!confirm(`Are you sure you want to delete the tag "${tag.name}"? This will remove it from all ${tag._count.objectTags} records.`)) {
      return;
    }

    try {
      await deleteTag.mutateAsync(tag.id);
      toast.success("Tag deleted successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete tag");
    }
  };

  const handleUpdateColor = async () => {
    try {
      await updateTag.mutateAsync({ id: tag.id, color });
      toast.success("Tag color updated successfully");
      setEditColorOpen(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update tag");
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="data-[state=open]:bg-muted text-muted-foreground flex size-6"
            size="icon"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontalIcon className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem 
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setEditColorOpen(true);
            }}
            className="flex items-center gap-x-2"
          >
            <PaletteIcon className="h-4 w-4" />
            Edit Color
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={handleDelete}
            className="text-red-600 focus:text-red-600 flex items-center gap-x-2"
            disabled={deleteTag.isPending}
          >
            <TrashIcon className="h-4 w-4" />
            Delete Tag
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={editColorOpen} onOpenChange={setEditColorOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Tag Color</DialogTitle>
            <DialogDescription>
              Change the color for "{tag.name}"
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="color">Color</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="color"
                  type="color"
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  className="w-16 h-10"
                />
                <Badge
                  style={{
                    backgroundColor: `${color}20`,
                    borderColor: color,
                    color: color,
                  }}
                  className="border"
                >
                  {tag.name}
                </Badge>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setEditColorOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateColor} disabled={updateTag.isPending}>
              {updateTag.isPending ? "Updating..." : "Update Color"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 