import { PurchaseCreditButton } from "@app/ai/components/PurchaseCreditButton";
import { getActiveOrganization } from "@app/auth/lib/server";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	ArrowLeft,
	Check,
	Clock,
	CreditCard,
	Shield,
	Star,
	Zap,
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { CREDIT_PACKAGES, CreditPackage } from "@app/shared/lib/constants";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: "Purchase AI Credits",
		header: {
			title: "Purchase AI Credits",
			subtitle: "Buy additional credits for your AI features",
		},
	};
}

export default async function AIPurchasePage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return redirect("/app");
	}

	return (
		<>
			<div className="mb-6">
				<div className="flex items-center gap-4 mb-4">
					<Button variant="ghost" asChild size="sm">
						<Link href={`/app/${organizationSlug}/settings/ai`}>
							<ArrowLeft className="h-4 w-4 mr-2" />
							Back to AI Settings
						</Link>
					</Button>
				</div>
				<h1 className="text-2xl font-semibold">Purchase AI Credits</h1>
				<p className="text-muted-foreground">
					Choose a credit package that fits your needs. Credits never
					expire and can be used across all AI features.
				</p>
			</div>

			<div className="grid gap-6">
				{/* Trust Indicators */}
				<Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
					<CardContent className="p-6">
						<div className="flex items-center justify-center gap-8 text-sm">
							<div className="flex items-center gap-2">
								<Shield className="h-4 w-4 text-blue-600" />
								<span>Secure Payment</span>
							</div>
							<div className="flex items-center gap-2">
								<Clock className="h-4 w-4 text-blue-600" />
								<span>Instant Delivery</span>
							</div>
							<div className="flex items-center gap-2">
								<Zap className="h-4 w-4 text-blue-600" />
								<span>Never Expire</span>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Credit Packages */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					{CREDIT_PACKAGES.map((pkg: CreditPackage) => (
						<Card
							key={pkg.id}
							className={`relative ${pkg.popular ? "border-blue-500 shadow-lg scale-105" : ""}`}
						>
							{pkg.popular && (
								<div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
									<Badge className="bg-blue-500 text-white px-3 py-1">
										<Star className="h-3 w-3 mr-1" />
										Most Popular
									</Badge>
								</div>
							)}

							<CardHeader className="text-center pb-4">
								<CardTitle className="text-xl">
									{pkg.name}
								</CardTitle>
								<CardDescription>
									{pkg.description}
								</CardDescription>

								<div className="pt-4">
									<div className="text-3xl font-bold">
										${pkg.price}
									</div>
									<div className="text-sm text-muted-foreground">
										{pkg.credits.toLocaleString()} credits
									</div>
									{/* {pkg.savings && (
										<Badge className="mt-2 bg-green-100 text-green-800">
											{pkg.savings}
										</Badge>
									)} */}
								</div>
							</CardHeader>

							<CardContent className="space-y-4">
								<ul className="space-y-2">
									{pkg.features.map((feature, index) => (
										<li
											key={index}
											className="flex items-center gap-2 text-sm"
										>
											<Check className="h-4 w-4 text-green-500 flex-shrink-0" />
											<span>{feature}</span>
										</li>
									))}
								</ul>

								<PurchaseCreditButton
									packageId={pkg.id}
									priceId={pkg.priceId}
									organizationId={organization.id}
									organizationSlug={organizationSlug}
									credits={pkg.credits}
									price={pkg.price}
									className={`w-full ${pkg.popular ? "bg-blue-500 hover:bg-blue-600" : ""}`}
								>
									<CreditCard className="h-4 w-4 mr-2" />
									Purchase {pkg.name}
								</PurchaseCreditButton>
							</CardContent>
						</Card>
					))}
				</div>

				{/* FAQ Section */}
				<Card>
					<CardHeader>
						<CardTitle>Frequently Asked Questions</CardTitle>
					</CardHeader>
					<CardContent className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h4 className="font-medium mb-2">
									Do credits expire?
								</h4>
								<p className="text-sm text-muted-foreground">
									No, purchased credits never expire. Use them
									at your own pace across all AI features.
								</p>
							</div>

							<div>
								<h4 className="font-medium mb-2">
									Can I share credits with my team?
								</h4>
								<p className="text-sm text-muted-foreground">
									Yes, credits are shared across your entire
									organization. All team members can use them.
								</p>
							</div>

							<div>
								<h4 className="font-medium mb-2">
									What payment methods do you accept?
								</h4>
								<p className="text-sm text-muted-foreground">
									We accept all major credit cards, PayPal,
									and bank transfers through our secure Stripe
									integration.
								</p>
							</div>

							<div>
								<h4 className="font-medium mb-2">
									Can I get a refund?
								</h4>
								<p className="text-sm text-muted-foreground">
									We offer refunds within 30 days of purchase
									for unused credits. Contact support for
									assistance.
								</p>
							</div>

							<div>
								<h4 className="font-medium mb-2">
									How are credits consumed?
								</h4>
								<p className="text-sm text-muted-foreground">
									Different AI operations use different
									amounts of credits based on complexity and
									model usage.
								</p>
							</div>

							<div>
								<h4 className="font-medium mb-2">
									Do you offer volume discounts?
								</h4>
								<p className="text-sm text-muted-foreground">
									Yes, larger packages offer better value.
									Contact sales for custom enterprise
									packages.
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Support */}
				<Card>
					<CardHeader>
						<CardTitle>Need Help?</CardTitle>
						<CardDescription>
							Our team is here to help you choose the right credit
							package
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="flex gap-4">
							<Button variant="outline" asChild>
								<Link href="/help">View Documentation</Link>
							</Button>
							<Button variant="outline" asChild>
								<Link href="/contact">Contact Sales</Link>
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</>
	);
}
