import { getActiveOrganization } from "@app/auth/lib/server";
import { ChangeOrganizationNameForm } from "@app/organizations/components/ChangeOrganizationNameForm";
import { DeleteOrganizationForm } from "@app/organizations/components/DeleteOrganizationForm";
import { OrganizationLogoForm } from "@app/organizations/components/OrganizationLogoForm";
import { SettingsList } from "@app/shared/components/SettingsList";
import { OrganizationSettingsClient } from "./client";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("organizations.settings.title"),
	};
}

export default async function OrganizationSettingsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	return (
		<>
			<OrganizationSettingsClient />
			<SettingsList>
				<OrganizationLogoForm />
				<ChangeOrganizationNameForm />
				<DeleteOrganizationForm />
			</SettingsList>
		</>
	);
}
