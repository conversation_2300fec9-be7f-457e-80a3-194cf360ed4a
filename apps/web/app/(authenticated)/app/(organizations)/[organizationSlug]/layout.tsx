import { getActiveOrganization } from "@app/auth/lib/server";
import { activeOrganizationQueryKey } from "@app/organizations/lib/api";
import { purchasesQueryKey } from "@app/payments/lib/api";
import { getPurchases } from "@app/payments/lib/server";
import { AppWrapper } from "@app/shared/components/AppWrapper";
import { config } from "@repo/config";
import { getQueryClient } from "@shared/lib/server";
import { notFound } from "next/navigation";
import type { PropsWithChildren } from "react";

export default async function OrganizationLayout({
	children,
	params,
}: PropsWithChildren<{
	params: Promise<{
		organizationSlug: string;
	}>;
}>) {
	const { organizationSlug } = await params;

	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	const queryClient = getQueryClient();

	await queryClient.prefetchQuery({
		queryKey: activeOrganizationQueryKey(organizationSlug),
		queryFn: () => organization,
	});

	if (config.users.enableBilling) {
		await queryClient.prefetchQuery({
			queryKey: purchasesQueryKey(organization.id),
			queryFn: () => getPurchases(organization.id),
		});
	}

	return <AppWrapper>{children}</AppWrapper>;
}
