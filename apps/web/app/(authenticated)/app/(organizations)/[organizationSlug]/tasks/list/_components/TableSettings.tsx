"use client";

import type { TaskStatus } from "@app/shared/lib/constants";
import { STORAGE_KEYS, TASK_STATUS } from "@app/shared/lib/constants";
import { setLocalStorage } from "@app/shared/lib/local-storage";
import { DataTableGroup } from "@app/tasks/components/DataTableGroup";
import { DataTableSort } from "@app/tasks/components/DataTableSort";
import { updateTaskPreferences } from "@app/tasks/lib/preferences";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconAntennaBars5,
	IconAt,
	IconCalendar,
	IconCalendarTime,
	IconClock,
	IconId,
	IconLayoutColumns,
	IconLayoutList,
	IconLetterTSmall,
	IconProgress,
	IconQuestionMark,
	IconTableOptions,
} from "@tabler/icons-react";
import type { Table } from "@tanstack/react-table";
import { But<PERSON> } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Switch } from "@ui/components/switch";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import React, { useEffect, useState } from "react";

interface TableSettingsProps {
	showCompletedTasks: boolean;
	onShowCompletedTasksChange: (show: boolean) => void;
	statusVisibility: Record<TaskStatus, boolean>;
	onStatusVisibilityChange: (status: TaskStatus, visible: boolean) => void;
	table: Table<any>;
	view: string;
	onViewChange: (view: string) => void;
	groupBy: string;
	onGroupByChange: (groupBy: string) => void;
	user: any;
	users: Record<string, any>;
}

const iconMapping = {
	Title: IconLetterTSmall,
	Status: IconProgress,
	Priority: IconAntennaBars5,
	"Date Due": IconCalendarTime,
	Related: IconId,
	"Assigned to": IconAt,
	Created: IconClock,
} as const;

export function TableSettings({
	showCompletedTasks,
	onShowCompletedTasksChange,
	statusVisibility,
	onStatusVisibilityChange,
	table,
	view,
	onViewChange,
	groupBy,
	onGroupByChange,
	user,
	users,
}: TableSettingsProps) {
	const [isOpen, setIsOpen] = useState(false);

	useEffect(() => {
		updateTaskPreferences({ view });
	}, [view]);

	useEffect(() => {
		setLocalStorage(STORAGE_KEYS.TASKS_VIEW, view);
	}, [view]);

	return (
		<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
			<DropdownMenuTrigger asChild>
				<Button variant="relio" size="sm">
					<IconTableOptions className="size-3 transition-all text-muted-foreground group-hover:text-primary" />
					<span className="text-sm">View Settings</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-[280px] p-4">
				<div className="space-y-4">
					{/* View Section */}
					<div>
						<div className="flex items-center justify-between mb-1">
							<span className="font-medium text-xs text-muted-foreground">
								View
							</span>
							<Tooltip>
								<TooltipTrigger asChild>
									<IconQuestionMark className="h-4 w-4 text-muted-foreground border rounded-full p-0.5" />
								</TooltipTrigger>
								<TooltipContent>
									<span>
										Viewing options are applied just for you
										and are synced between organizations.
									</span>
								</TooltipContent>
							</Tooltip>
						</div>
						<div className="grid grid-cols-3 gap-1 border border-input rounded-lg p-1">
							<Button
								variant={
									view === "list" ? "secondary" : "ghost"
								}
								className="w-full h-full py-2 px-4 flex flex-col items-center gap-1 relative"
								onClick={() => onViewChange("list")}
							>
								<IconLayoutList className="size-4" />
								<span className="text-xs">List</span>
							</Button>
							<Button
								variant={
									view === "kanban" ? "secondary" : "ghost"
								}
								className="w-full h-full py-2 px-4 flex flex-col items-center gap-1"
								onClick={() => onViewChange("kanban")}
							>
								<IconLayoutColumns className="h-5 w-5" />
								<span className="text-xs">Board</span>
							</Button>
							<Button
								variant={
									view === "calendar" ? "secondary" : "ghost"
								}
								className="w-full h-full py-2 px-4 flex flex-col items-center gap-1 relative !cursor-not-allowed"
								onClick={() => onViewChange("calendar")}
								disabled
							>
								<IconCalendar className="h-5 w-5" />
								<span className="text-xs">Calendar</span>
								<span className="absolute top-0 right-0 bg-amber-500 text-white text-[10px] px-1 rounded">
									Soon
								</span>
							</Button>
						</div>
					</div>

					{/* Completed Tasks Section */}
					<div className="flex items-center justify-between">
						<span className="text-xs">Show completed</span>
						<Switch
							checked={showCompletedTasks}
							onCheckedChange={onShowCompletedTasksChange}
						/>
					</div>

					{/* Group Section */}
					<div>
						<div className="flex items-center justify-between mb-1">
							<span className="font-medium text-xs text-muted-foreground">
								Grouped by
							</span>
						</div>
						<div className="space-y-2">
							<div className="flex flex-col gap-1">
								<DataTableGroup
									groupBy={groupBy}
									onGroupByChange={onGroupByChange}
									user={user}
								/>
							</div>
						</div>
					</div>

					{/* Sort Section */}
					<div>
						<div className="flex items-center justify-between mb-1">
							<span className="font-medium text-xs text-muted-foreground">
								Sorted by
							</span>
						</div>
						<div className="space-y-2">
							<div className="flex flex-col gap-1">
								<DataTableSort table={table} />
							</div>
						</div>
					</div>

					{/* Visibility Section */}
					<div>
						<div>
							<span className="font-medium text-xs text-muted-foreground">
								Visibility
							</span>
						</div>

						{/* Columns Section */}
						<div className="flex flex-col gap-1">
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="outline"
										className="h-8 gap-2 rounded-lg flex items-center justify-start w-full text-xs font-mono"
									>
										Table Columns{" "}
										<span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
											{
												table
													.getAllColumns()
													.filter((column) =>
														column.getIsVisible(),
													).length
											}
										</span>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-70 space-y-0.5">
									{table
										.getAllColumns()
										.filter(
											(column) =>
												typeof column.accessorFn !==
													"undefined" &&
												column.getCanHide(),
										)
										.map((column) => {
											let label: string;
											// Use accessorKey if present, else fallback to column.id
											const accessorKey = (
												column.columnDef as any
											).accessorKey;
											if (
												typeof column.columnDef
													.header === "function" &&
												accessorKey
											) {
												label = accessorKey as string;
											} else if (
												typeof column.columnDef
													.header === "string"
											) {
												label = column.columnDef.header;
											} else {
												label = column.id;
											}
											// Map known accessorKeys to their display label for icon mapping
											const labelForIcon =
												label === "dueDate"
													? "Date Due"
													: label === "assignee"
														? "Assigned to"
														: label === "createdAt"
															? "Created"
															: label
																	.charAt(0)
																	.toUpperCase() +
																label.slice(1);
											const Icon =
												iconMapping[
													labelForIcon as keyof typeof iconMapping
												];
											return (
												<DropdownMenuCheckboxItem
													key={column.id}
													className="capitalize h-8 flex items-center gap-2"
													checked={column.getIsVisible()}
													onCheckedChange={(value) =>
														column.toggleVisibility(
															!!value,
														)
													}
												>
													{Icon && (
														<Icon className="h-4 w-4 text-muted-foreground" />
													)}
													<span>{labelForIcon}</span>
												</DropdownMenuCheckboxItem>
											);
										})}
								</DropdownMenuContent>
							</DropdownMenu>

							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="outline"
										className="h-8 gap-2 rounded-lg flex items-center justify-start w-full text-xs font-mono"
									>
										Status Visibility
										<span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
											{
												Object.keys(
													statusVisibility,
												).filter(
													(status) =>
														statusVisibility[
															status as TaskStatus
														] !== false,
												).length
											}
										</span>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-70">
									{TASK_STATUS.map((status) => {
										const Icon = status.icon;
										return (
											<DropdownMenuCheckboxItem
												key={status.value}
												className="capitalize"
												checked={
													statusVisibility[
														status.value
													] !== false
												}
												onCheckedChange={(value) =>
													onStatusVisibilityChange(
														status.value,
														value,
													)
												}
											>
												<div className="flex items-center gap-2">
													<Icon
														className={`h-4 w-4 ${status.color}`}
													/>
													{status.label}
												</div>
											</DropdownMenuCheckboxItem>
										);
									})}
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					</div>

					{/* Filter Section */}
					{/* <div>
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-xs text-muted-foreground">Filter by</span>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-8 gap-2 rounded-lg flex items-center justify-start w-full">
                  Assignee <span className="ml-auto text-xs text-muted-foreground bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
                    {Object.keys(users).length}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-70">
                {Object.values(users).map((user) => {
                  const typedUser = user as any;
                  return (
                    <DropdownMenuCheckboxItem
                      key={typedUser.id}
                      className="capitalize"
                      checked={(table.getColumn('assignee')?.getFilterValue() as string[] | undefined)?.includes(typedUser.id)}
                      onCheckedChange={(checked) => {
                        const filterValues = table.getColumn('assignee')?.getFilterValue() as string[] || [];
                        if (checked) {
                          table.getColumn('assignee')?.setFilterValue([...filterValues, typedUser.id]);
                        } else {
                          table.getColumn('assignee')?.setFilterValue(
                            filterValues.filter((id) => id !== typedUser.id)
                          );
                        }
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <UserAvatar name={typedUser.name} avatarUrl={typedUser.avatarUrl} className="size-4" />
                        {typedUser.name}
                      </div>
                    </DropdownMenuCheckboxItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div> */}
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
