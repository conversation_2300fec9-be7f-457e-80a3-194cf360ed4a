"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { fetchFavorites, useToggleFavorite } from "@app/favorites/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import AlertDialog from "@app/shared/components/AlertDialog";
import { PrioritySelector } from "@app/shared/components/PrioritySelector";
import { StatusSelector } from "@app/shared/components/StatusSelector";
import {
	TASK_STATUS,
	type TaskPriority,
	type TaskStatus,
} from "@app/shared/lib/constants";
import { TaskContextMenu } from "@app/tasks/components/TaskContextMenu";
import { useDeleteTask, useUpdateTask } from "@app/tasks/lib/api";
import { DragOverlay, useDndContext } from "@dnd-kit/core";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { ObjectStatusHistory, Task, User } from "@repo/database/src/zod";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	IconClock,
	IconEdit,
	IconStar,
	IconStarFilled,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { AnimatedCheckbox, Checkbox } from "@ui/components/checkbox";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { differenceInDays, format } from "date-fns";
import React, { useCallback, useMemo, useState } from "react";
import DropIndicator from "./drop-indicator";

interface KanbanCardProps {
	task: Task & {
		statusHistory?: ObjectStatusHistory[];
		assignee?: User;
		favorite?: boolean;
	};
	isSelected?: boolean;
	selectedTasks?: Set<string>;
	onSelect?: (taskId: string) => void;
	onEdit?: (taskId: string) => void;
	onDelete?: (taskId: string) => void;
	icon?: React.ReactNode;
	onStatusChange?: (taskId: string, newStatus: Task["status"]) => void;
	onPriorityChange?: (taskId: string, newPriority: Task["priority"]) => void;
	columnPrefs?: {
		trackTimeInStatus: boolean;
		targetTimeInStatus: number | null;
	};
	cursor?: "grab" | "default";
	checkbox?: boolean;
}

export function KanbanCard({
	task,
	isSelected = false,
	selectedTasks = new Set(),
	onSelect,
	onEdit,
	icon,
	onStatusChange,
	onPriorityChange,
	onDelete,
	columnPrefs,
	cursor = "grab",
	checkbox = true,
}: KanbanCardProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const updateTaskMutation = useUpdateTask(activeOrganization?.id);
	const deleteTaskMutation = useDeleteTask(activeOrganization?.id);
	const [isHovered, setIsHovered] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
		isOver,
	} = useSortable({
		id: task.id,
		data: {
			type: "task",
			task: task,
			status: task.status,
			position: task.position,
			selectedTasksCount: isSelected ? selectedTasks.size : 1,
		},
		transition: {
			duration: 150,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const style = useMemo(() => {
		const transformString = transform
			? CSS.Transform.toString(transform)
			: "";
		const styleObj: React.CSSProperties = {
			transform: isDragging
				? `rotate(2deg) ${transformString}`
				: transformString,
			transition,
			opacity: isDragging ? 0.5 : task.status === "done" ? 0.5 : 1,
			cursor: isDragging ? "grabbing" : `${cursor} !important`,
			touchAction: "none",
			position: "relative",
			zIndex: isDragging ? 100 : 1,
			display: "block",
		};

		return styleObj;
	}, [transform, transition, isDragging, task.status]);

	const taskId = (task as any).id;

	const handleCheckboxChange = useCallback(
		(checked: boolean | string) => {
			onSelect?.(taskId);
		},
		[onSelect, taskId],
	);

	const toggleFavorite = useToggleFavorite(activeOrganization?.id);
	const handleFavorite = useCallback(
		(e: React.MouseEvent, _taskId: string) => {
			e.stopPropagation();
			if (!activeOrganization?.id || !taskId) return;
			toggleFavorite.mutate({
				objectId: taskId,
				objectType: "task",
				organizationId: activeOrganization.id,
			});
		},
		[toggleFavorite, activeOrganization?.id, taskId],
	);

	const handleStatusChange = useCallback(
		(_taskId: string, newStatus: TaskStatus) => {
			updateTaskMutation.mutate({ id: taskId, status: newStatus });
			onStatusChange?.(taskId, newStatus);
		},
		[updateTaskMutation, onStatusChange, taskId],
	);

	const handlePriorityChange = useCallback(
		(_taskId: string, newPriority: TaskPriority) => {
			updateTaskMutation.mutate({ id: taskId, priority: newPriority });
			onPriorityChange?.(taskId, newPriority);
		},
		[updateTaskMutation, onPriorityChange, taskId],
	);

	const handleDelete = useCallback((e: React.MouseEvent, _taskId: string) => {
		e.stopPropagation();
		setShowDeleteDialog(true);
	}, []);

	const confirmDelete = useCallback(() => {
		deleteTaskMutation.mutate(taskId, {
			onSuccess: () => {
				setShowDeleteDialog(false);
				onDelete?.(taskId);
			},
		});
	}, [deleteTaskMutation, onDelete, taskId]);

	// Fetch favorites for the current organization
	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", activeOrganization?.id],
		queryFn: () =>
			activeOrganization?.id
				? fetchFavorites(activeOrganization.id)
				: Promise.resolve([]),
		enabled: !!activeOrganization?.id,
	});

	// Determine if this task is favorited
	const isFavorite = useMemo(() => {
		if (!task?.id) return false;
		return favorites.some((f: any) => f.objectId === task.id);
	}, [favorites, task?.id]);

	const handleEditClick = useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();
			onEdit?.(taskId);
		},
		[onEdit, taskId],
	);

	const handleCardClick = useCallback(
		(e: React.MouseEvent) => {
			// Prevent card click from triggering when clicking checkbox, edit button, or priority selector
			if (
				(e.target as HTMLElement).closest(".checkbox-container") ||
				(e.target as HTMLElement).closest(".edit-button-container") ||
				(e.target as HTMLElement).closest(
					".priority-selector-container",
				) ||
				(e.target as HTMLElement).closest(".status-selector-container")
			) {
				return;
			}
			handleCheckboxChange(true);
		},
		[handleCheckboxChange],
	);

	const statusChangeInfo = useMemo(() => {
		const lastStatusChange = task?.statusHistory?.find(
			(h: ObjectStatusHistory) => h.toStatus === task.status,
		);
		if (!lastStatusChange) return null;

		const days = differenceInDays(Date.now(), lastStatusChange.createdAt);
		const daysText = `${days}d`;
		const columnName =
			TASK_STATUS.find((s) => s.value === task.status)?.label ||
			task.status;
		const fullDate = format(
			lastStatusChange.createdAt,
			"h:mm a MMMM d, yyyy",
		);

		return {
			days,
			daysText,
			lastStatusChange,
			tooltipText: (
				<div className="flex flex-col">
					<span>Moved to {columnName}</span>
					<span className="text-xs opacity-80">{fullDate}</span>
					{columnPrefs?.trackTimeInStatus &&
						columnPrefs?.targetTimeInStatus &&
						days > columnPrefs.targetTimeInStatus && (
							<span className="text-xs text-red-500 font-medium">
								Exceeded target by{" "}
								{days - columnPrefs.targetTimeInStatus} days
							</span>
						)}
				</div>
			),
		};
	}, [task.status, task.statusHistory, columnPrefs]);

	const isOverdue = useMemo(() => {
		if (
			!columnPrefs?.trackTimeInStatus ||
			!columnPrefs?.targetTimeInStatus ||
			!statusChangeInfo?.lastStatusChange
		) {
			return false;
		}
		const daysInStatus = differenceInDays(
			Date.now(),
			statusChangeInfo.lastStatusChange.createdAt,
		);
		return daysInStatus > columnPrefs.targetTimeInStatus;
	}, [columnPrefs, statusChangeInfo]);

	const SelectionComponent = useMemo(() => {
		if (!user) return null;

		return (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={isSelected}
					onCheckedChange={handleCheckboxChange}
					className="translate-y-[2px] cursor-pointer"
				/>
			</div>
		);
	}, [user, isHovered, isSelected, handleCheckboxChange]);

	return (
		<>
			{isOver && (
				<DropIndicator
					beforeId={task.id}
					column={task.status}
					isOver={isOver}
				/>
			)}
			<AlertDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				title="Delete Task"
				description="Are you sure you want to delete this task? This action cannot be undone."
				confirmLabel="Delete"
				cancelLabel="Cancel"
				confirmClassName="bg-red-500 hover:bg-red-600 text-white"
				onConfirm={confirmDelete}
				loading={deleteTaskMutation.isPending}
			/>
			<TaskContextMenu
				task={task}
				isFavorite={isFavorite ?? false}
				onEdit={handleEditClick}
				onFavorite={handleFavorite}
				onStatusChange={handleStatusChange}
				onPriorityChange={handlePriorityChange}
				onDelete={handleDelete}
				organizationId={activeOrganization?.id}
			>
				<div
					ref={setNodeRef}
					style={style}
					{...attributes}
					{...listeners}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
					onClick={handleCardClick}
					className={`relative rounded-lg border bg-zinc-100 dark:bg-accent/50 dark:hover:!bg-accent/50 hover:bg-zinc-100 p-2 ${
						isDragging
							? "border-blue-500"
							: isSelected
								? "border-blue-500"
								: isOverdue
									? "border-red-500 bg-red-500/50 dark:bg-red-500/10"
									: "border-accent"
					}`}
				>
					<div className={"flex flex-col"}>
						<div
							className={
								"flex flex-row items-start justify-between"
							}
						>
							<div className={"flex flex-row items-center gap-2"}>
								<div className={"flex flex-col"}>
									{task.dueDate && (() => {
										// Helper function to safely parse dates
										const parseDate = (dateValue: any): Date | null => {
											if (!dateValue) return null;
											
											// Handle MongoDB date format: { "$date": "2025-07-03T00:49:20.138Z" }
											if (typeof dateValue === 'object' && dateValue.$date) {
												const parsed = new Date(dateValue.$date);
												return !isNaN(parsed.getTime()) ? parsed : null;
											}
											
											// Handle regular date parsing
											const parsed = new Date(dateValue);
											return !isNaN(parsed.getTime()) ? parsed : null;
										};
										
										const dueDate = parseDate(task.dueDate);
										return dueDate ? (
											<div className={"text-sm text-zinc-500"}>
												{format(dueDate, "MMM dd")}
											</div>
										) : null;
									})()}
									<p className="text-sm text-zinc-800 dark:text-zinc-100 gap-2 flex flex-row items-center">
										{task.title}
									</p>
									<p className="text-xs text-zinc-500 dark:text-zinc-400 truncate max-w-[280px]">
										{task.description}
									</p>
								</div>
							</div>
							{checkbox && (
								<div className="checkbox-container">
									{SelectionComponent}
								</div>
							)}
						</div>
						<div
							className={"flex flex-row items-center gap-1 mt-1"}
						>
							<div className="status-selector-container">
								<StatusSelector
									status={task.status as TaskStatus}
									taskId={taskId}
									onStatusChange={(taskId, newStatus) =>
										onStatusChange?.(taskId, newStatus)
									}
								/>
							</div>

							<div className="priority-selector-container">
								<PrioritySelector
									priority={task.priority as TaskPriority}
									taskId={taskId}
									onPriorityChange={(taskId, newPriority) =>
										onPriorityChange?.(taskId, newPriority)
									}
								/>
							</div>

							<div className="ml-auto flex items-center gap-1 edit-button-container">
								<div className="flex items-center gap-1">
									{isHovered && (
										<Button
											onClick={(e) => {
												e.stopPropagation();
												onEdit?.(taskId);
											}}
											className="h-6 w-6 text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 transition-colors"
											variant="ghost"
											size="icon"
											tooltip="Edit task"
										>
											<IconEdit className="w-4 h-4" />
										</Button>
									)}

									<Button
										onClick={(e) =>
											handleFavorite(e, taskId)
										}
										className="h-6 w-6 text-zinc-500 hover:text-yellow-400 transition-colors"
										variant="ghost"
										size="icon"
										tooltip={isFavorite ? "Remove from favorites" : "Add to favorites"}
									>
										{isFavorite ? (
											<IconStarFilled className="w-4 h-4 text-yellow-400" />
										) : (
											isHovered && (
												<IconStar className="w-4 h-4" />
											)
										)}
									</Button>

									<div className="flex items-center justify-center">
										<UserAvatar
											name={task.assignee?.name ?? ""}
											avatarUrl={
												task.assignee?.image ?? ""
											}
											className="size-6"
										/>
									</div>
								</div>

								{statusChangeInfo && (
									<Tooltip>
										<TooltipTrigger asChild>
											<div className="flex items-center gap-1 text-xs text-zinc-500 cursor-default">
												<IconClock className="w-3 h-3" />
												<span>
													{statusChangeInfo.daysText}
												</span>
											</div>
										</TooltipTrigger>
										<TooltipContent side="bottom">
											{statusChangeInfo.tooltipText}
										</TooltipContent>
									</Tooltip>
								)}
							</div>
						</div>
					</div>
				</div>
			</TaskContextMenu>
		</>
	);
}

export function KanbanCardDragOverlay() {
	const { active } = useDndContext();

	if (!active) return null;

	const task = active.data.current?.task;
	const selectedTasksCount = active.data.current?.selectedTasksCount || 1;

	return (
		<DragOverlay>
			{task && (
				<div
					className="relative rounded-lg border-blue-500 bg-zinc-100 dark:bg-accent/50 p-2 shadow-lg"
					style={{
						borderWidth: `${Math.min(selectedTasksCount * 2, 8)}px`,
						transform: `translateY(${(selectedTasksCount - 1) * -4}px)`,
					}}
				>
					<KanbanCard task={task} />
				</div>
			)}
		</DragOverlay>
	);
}
