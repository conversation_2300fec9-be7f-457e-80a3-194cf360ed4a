import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import type { TaskPriority, TaskStatus } from "@app/shared/lib/constants";
import { CreateTaskModal } from "@app/tasks/components/CreateTaskModal";
import { useDroppable } from "@dnd-kit/core";
import type { Task } from "@repo/database/src/zod";
import { cn } from "@ui/lib";
import { format, isSameMonth, isToday } from "date-fns";
import { useState } from "react";
import { useCalendar } from "./calendar-context";
import { CalendarTask } from "./calendar-task";
import { useSidePanel } from "./side-panel";

interface CalendarDayProps {
	date: Date;
	tasks: Task[];
	isLastColumn?: boolean;
	isLastRow?: boolean;
}

interface TaskToEdit {
	id: any;
	title: string;
	description?: string | undefined;
	status: TaskStatus;
	priority: TaskPriority;
	dueDate?: number | undefined;
	assignee?: { id: any; name: string } | null | undefined;
	related?:
		| { id: string; name?: string; recordType?: string }
		| null
		| undefined;
}

export function CalendarDay({
	date,
	tasks,
	isLastColumn,
	isLastRow,
}: CalendarDayProps) {
	const { currentDate } = useCalendar();
	const { setOpen, setTitle, setContent } = useSidePanel();
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [selectedTask, setSelectedTask] = useState<TaskToEdit | null>(null);
	const isCurrentDay = isToday(date);
	const isCurrentMonth = isSameMonth(date, currentDate);
	const dateString = `date-${date.toISOString()}`;
	const { activeOrganization } = useActiveOrganization();

	const { setNodeRef, isOver, active } = useDroppable({
		id: dateString,
	});

	const handleTaskEdit = (task: Task) => {
		setSelectedTask({
			id: task.id,
			title: task.title,
			description: task.description ?? undefined,
			status: task.status,
			priority: task.priority,
			dueDate: task.dueDate
				? new Date(task.dueDate).getTime()
				: undefined,
			assignee: (task as any).assignee
				? { id: (task as any).assignee.id, name: (task as any).assignee.name }
				: null,
			related:
				task.relatedObjectId && task.relatedObjectType
					? { 
						id: task.relatedObjectId,
						name: (task as any).relatedObject?.name,
						recordType: task.relatedObjectType,
					}
					: undefined,
		});
		setIsModalOpen(true);
	};

	const handleModalOpenChange = (open: boolean) => {
		setIsModalOpen(open);
		if (!open) {
			setSelectedTask(null);
		}
	};

	const showTasksInSidePanel = () => {
		if (tasks.length === 0) return;

		setTitle(format(date, "MMMM d, yyyy"));
		setContent(
			<div className="space-y-2">
				{tasks.map((task) => (
					<CalendarTask
						organizationId={activeOrganization?.id ?? ""}
						key={task.id}
						task={task}
						isDraggable={true}
						onEdit={() => handleTaskEdit(task)}
					/>
				))}
			</div>,
		);
		setOpen(true);
	};

	const lastTask = tasks[tasks.length - 1];
	const remainingTasks = tasks.length > 1 ? tasks.length - 1 : 0;

	return (
		<>
			<div
				ref={setNodeRef}
				className={cn(
					"min-h-[120px] relative border-r border-b transition-colors overflow-hidden group",
					isLastColumn && "border-r-0",
					isLastRow && "border-b-0",
					isOver && active && "bg-accent/30 border-accent",
				)}
			>
				{/* Droppable area that covers entire cell */}
				<div className="absolute inset-0" />

				{/* Content container */}
				<div
					className="relative h-full p-2 cursor-pointer"
					onClick={showTasksInSidePanel}
				>
					<div className="flex flex-col gap-1">
						<div
							className={cn(
								"flex items-center justify-center h-6 w-6 rounded-full text-sm mx-auto",
								isCurrentDay &&
									"bg-primary text-primary-foreground font-medium",
								!isCurrentMonth && "text-muted-foreground/50",
							)}
						>
							{format(date, "d")}
						</div>
						<div className="space-y-1">
							{lastTask && (
								<>
									<div className="relative">
										<CalendarTask
											organizationId={
												activeOrganization?.id ?? ""
											}
											key={lastTask.id}
											task={lastTask}
											className={cn(
												!isCurrentMonth
													? "opacity-50"
													: "",
											)}
											onEdit={() =>
												handleTaskEdit(lastTask)
											}
										/>
									</div>
									{remainingTasks > 0 && (
										<div className="text-xs text-muted-foreground text-center mt-1 py-1 rounded-md bg-accent/50">
											+ {remainingTasks} more
										</div>
									)}
								</>
							)}
						</div>
					</div>
				</div>
			</div>
			<CreateTaskModal
				open={isModalOpen}
				onOpenChange={handleModalOpenChange}
				taskToEdit={selectedTask as any}
			/>
		</>
	);
}
