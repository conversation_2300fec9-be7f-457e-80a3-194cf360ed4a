"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { toggleFavorite, useToggleFavorite } from "@app/favorites/lib/api";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import AlertDialog from "@app/shared/components/AlertDialog";
import EmptyContainer from "@app/shared/components/EmptyContainer";
import type { TaskPriority, TaskStatus } from "@app/shared/lib/constants";
import { CreateTaskModal } from "@app/tasks/components/CreateTaskModal";
import { TaskContextMenu } from "@app/tasks/components/TaskContextMenu";
import {
	updateTask as updateTaskApi,
	useDeleteTask,
	useUpdateTask,
} from "@app/tasks/lib/api";
import {
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	MouseSensor,
	TouchSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { IconSquareRoundedCheck } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import {
	type ColumnDef,
	flexRender,
	type HeaderGroup,
	type Row,
	type Table,
} from "@tanstack/react-table";
import { Skeleton } from "@ui/components/skeleton";
import {
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
	Table as UITable,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import {
	endOfToday,
	endOfWeek,
	isWithinInterval,
	startOfToday,
	startOfWeek,
} from "date-fns";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";

interface TableProps {
	columns: ColumnDef<any, unknown>[];
	table: Table<any>;
	isLoading: boolean;
	groupBy: string;
	showCompletedTasks: boolean;
	onStatusVisibilityChange: (status: any, visible: boolean) => void;
	onTasksUpdated?: (() => void) | undefined;
}

export default function TaskTable({
	columns,
	table,
	isLoading,
	groupBy,
	showCompletedTasks,
	onStatusVisibilityChange,
	onTasksUpdated,
}: TableProps) {
	return (
		<DataTable
			columns={columns}
			table={table}
			isLoading={isLoading}
			groupBy={groupBy}
			showCompletedTasks={showCompletedTasks}
			onTasksUpdated={onTasksUpdated}
		/>
	);
}

interface DataTableProps {
	columns: ColumnDef<any, unknown>[];
	table: Table<any>;
	isLoading: boolean;
	groupBy: string;
	showCompletedTasks: boolean;
	onTasksUpdated?: (() => void) | undefined;
}

export function DataTable({
	columns,
	table,
	isLoading,
	groupBy,
	showCompletedTasks,
	onTasksUpdated,
}: DataTableProps) {
	const [isModalOpen, setIsModalOpen] = React.useState(false);
	const [selectedTask, setSelectedTask] = React.useState<any | null>(null);
	const [activeId, setActiveId] = React.useState<string | null>(null);
	const [activeTask, setActiveTask] = React.useState<Row<any> | null>(null);
	const { user } = useSession();

	// Setup DnD sensors
	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
	);

	const sortTasks = React.useCallback((rows: Row<any>[]) => {
		return [...rows].sort((a, b) => {
			// First sort by completion status
			if (a.original.status === "done" && b.original.status !== "done")
				return 1;
			if (a.original.status !== "done" && b.original.status === "done")
				return -1;

			// Sort by position if available
			const aPos = a.original.position;
			const bPos = b.original.position;
			if (aPos != null && bPos != null && aPos !== bPos) {
				return aPos - bPos;
			}
			// Fallback to createdAt if position is missing
			const aCreated = new Date(
				a.original.createdAt || a.original._creationTime || 0,
			).getTime();
			const bCreated = new Date(
				b.original.createdAt || b.original._creationTime || 0,
			).getTime();
			return aCreated - bCreated;
		});
	}, []);

	const getTaskGroup = React.useCallback(
		(task: any) => {
			// If the task is done, always put it in the Completed group
			if (task.status === "done") {
				return "Completed";
			}

			switch (groupBy) {
				case "status": {
					if (task.status === "in_progress") {
						return "In progress";
					}
					return (
						task.status.charAt(0).toUpperCase() +
						task.status.slice(1)
					);
				}
				case "dueDate": {
					const dueDate = task.dueDate
						? new Date(task.dueDate)
						: null;
					if (!dueDate) return "No Due Date";

					const today = {
						start: startOfToday(),
						end: endOfToday(),
					};

					const thisWeek = {
						start: startOfWeek(new Date()),
						end: endOfWeek(new Date()),
					};

					if (isWithinInterval(dueDate, today)) {
						return "Today";
					}
					if (isWithinInterval(dueDate, thisWeek)) {
						return "This Week";
					}
					return "Upcoming";
				}
				case "assignee": {
					const assigneeUser = task.assignee;
					return assigneeUser?.name || "Unassigned";
				}
				case "createdAt": {
					const createdAt = new Date(task._creationTime);
					const today = {
						start: startOfToday(),
						end: endOfToday(),
					};

					const thisWeek = {
						start: startOfWeek(new Date()),
						end: endOfWeek(new Date()),
					};

					if (isWithinInterval(createdAt, today)) {
						return "Created Today";
					}
					if (isWithinInterval(createdAt, thisWeek)) {
						return "Created This Week";
					}
					return "Created Earlier";
				}
				// TODO: Add this back in once we have a way to get organization users
				// case "createdBy": {
				// 	const creator = worganizationUsers.find(user => user.id === task.createdBy);
				// 	return creator?.name || "Unknown";
				// }
				default:
					return "Ungrouped";
			}
		},
		[
			groupBy,
			// TODO: Add this back in once we have a way to get organization users
			// organizationUsers
		],
	);

	const groupTasks = React.useCallback(
		(rows: Row<any>[], showCompleted: boolean) => {
			const groups: { [key: string]: Row<any>[] } = {};

			rows.forEach((row) => {
				const group = getTaskGroup(row.original);
				if (!groups[group]) {
					groups[group] = [];
				}
				if (row.original.status === "done" && !showCompleted) {
					return;
				}
				groups[group].push(row);
			});

			return groups;
		},
		[getTaskGroup],
	);

	const handleModalOpenChange = (open: boolean) => {
		setIsModalOpen(open);
		if (!open) {
			setSelectedTask(null);
		}
	};

	const TableSkeleton = ({ columns }: { columns: ColumnDef<any, unknown>[] }) => (
		<>
			{[...Array(5)].map((_, index) => (
				<TableRow key={index}>
					{columns.map((column, cellIndex) => (
						<TableCell key={cellIndex}>
							<Skeleton className="h-6 w-full" />
						</TableCell>
					))}
				</TableRow>
			))}
		</>
	);

	const handleRowClick = (row: Row<any>) => {
		setSelectedTask(row.original);
		setIsModalOpen(true);
	};

	const [isDragging, setIsDragging] = React.useState(false);
	const [activeGroup, setActiveGroup] = React.useState<string | null>(null);

	const handleDragStart = (event: DragStartEvent) => {
		setIsDragging(true);
		setActiveId(event.active.id as string);
		const activeRow = table.getRow(event.active.id as string);
		if (activeRow) {
			setActiveTask(activeRow);
		}
	};

	function mapGroupNameToStatus(groupName: string): TaskStatus {
		switch (groupName) {
			case "Todo":
				return "todo";
			case "In progress":
				return "in_progress";
			case "Done":
				return "done";
			case "Backlog":
				return "backlog";
			case "Review":
				return "review";
			case "Completed":
				return "done";
			default:
				return "todo";
		}
	}

	const handleDragEnd = async (event: DragEndEvent) => {
		setIsDragging(false);
		setActiveId(null);
		setActiveTask(null);
		setActiveGroup(null);
		const { active, over } = event;

		if (!over || active.id === over.id) {
			return;
		}

		const activeRow = table.getRow(active.id as string);
		const overRow = table.getRow(over.id as string);

		if (!activeRow || !overRow) {
			return;
		}

		// Get the group (status) of the active and over rows
		const activeGroupName = getTaskGroup(activeRow.original);
		const overGroupName = getTaskGroup(overRow.original);

		// If moving to a different group, update the status
		let newStatus = activeRow.original.status;
		let isStatusChanged = false;
		if (groupBy === "status" && activeGroupName !== overGroupName) {
			newStatus = mapGroupNameToStatus(overGroupName);
			isStatusChanged = true;
		}

		// Get all rows in the target group (after move)
		const allRows = sortTasks(table.getSortedRowModel().rows);
		const grouped = groupTasks(allRows, showCompletedTasks);
		const targetGroupRows = grouped[overGroupName] || [];

		// Remove the active row from its old group and insert into new group at newIndex
		let newGroupRows = [...targetGroupRows];
		// Remove if already present (shouldn't be, but for safety)
		newGroupRows = newGroupRows.filter((r) => r.id !== active.id);
		// Find the new index in the target group
		const newIndex = targetGroupRows.findIndex((r) => r.id === over.id);
		newGroupRows.splice(newIndex, 0, activeRow);

		// Prepare updates
		const updates = newGroupRows.map((row, idx) => ({
			id: row.original.id,
			position: idx,
			// If this is the dragged row and status changed, update status
			...(row.id === active.id && isStatusChanged
				? { status: newStatus }
				: {}),
		}));

		try {
			await Promise.all(updates.map((update) => updateTaskApi(update)));
			if (onTasksUpdated) onTasksUpdated();
		} catch (error) {
			console.error("Error updating task order:", error);
			// TODO: Add toast notification for error
		}
	};

	const sortedRows = sortTasks(table.getSortedRowModel().rows);
	const taskGroups = groupTasks(sortedRows, showCompletedTasks);
	
	const defaultGroupOrder = [
		"Today",
		"This Week",
		"Upcoming",
		"No Due Date",
		"Completed",
	];
	const statusGroupOrder = ["Todo", "In progress", "Done", "Blocked"];

	const groupOrder =
		groupBy === "status" ? statusGroupOrder : defaultGroupOrder;

	const sortedGroups = Object.entries(taskGroups).sort(([a], [b]) => {
		const aIndex = groupOrder.indexOf(a);
		const bIndex = groupOrder.indexOf(b);
		if (aIndex === -1) return 1;
		if (bIndex === -1) return -1;
		return aIndex - bIndex;
	});

	return (
		<div className="space-y-4">
			{user && (
				<CreateTaskModal
					open={isModalOpen}
					onOpenChange={handleModalOpenChange}
					taskToEdit={
						selectedTask
							? {
									...selectedTask,
									assignee: selectedTask.assignee
										? {
												id: selectedTask.assignee.id,
												name:
													selectedTask.assignee
														.name ||
													selectedTask.assignee.email,
											}
										: undefined,
									related: selectedTask.related?.[0]
										? {
												id: selectedTask.related[0].id,
												name: `${selectedTask.related[0].firstName || ""} ${selectedTask.related[0].lastName || ""}`.trim(),
												recordType: "contact",
											}
										: undefined,
								}
							: undefined
					}
				/>
			)}
			<div className="overflow-auto">
				<DndContext
					sensors={sensors}
					onDragEnd={handleDragEnd}
					onDragStart={handleDragStart}
				>
					<UITable>
						<TableHeader>
							{table
								.getHeaderGroups()
								.map((headerGroup: HeaderGroup<any>) => (
									<TableRow
										key={headerGroup.id}
										className="border-b hover:bg-transparent"
									>
										{headerGroup.headers.map((header) => (
											<TableHead
												key={header.id}
												colSpan={header.colSpan}
												className="px-4 text-xs font-medium text-zinc-500 !h-8"
											>
												<div className="flex items-center h-full w-full !-mt-4">
													{header.isPlaceholder
														? null
														: flexRender(
																header.column
																	.columnDef
																	.header,
																header.getContext(),
															)}
												</div>
											</TableHead>
										))}
									</TableRow>
								))}
						</TableHeader>
						<TableBody>
							{isLoading ? (
								<TableSkeleton columns={columns} />
							) : (
								<>
									{sortedGroups.map(
										([groupName, rows]) =>
											rows.length > 0 && (
												<React.Fragment key={groupName}>
													<TableRow
														className={cn(
															"group",
															// isDragging && activeGroup === groupName && "bg-blue-50/50 dark:bg-blue-900/30 border-t border-blue-500"
														)}
														onMouseEnter={() =>
															isDragging &&
															setActiveGroup(
																groupName,
															)
														}
														onMouseLeave={() =>
															isDragging &&
															setActiveGroup(null)
														}
													>
														<TableCell
															colSpan={
																columns.length
															}
															className={cn(
																"bg-zinc-100 dark:bg-sidebar px-4 py-2",
															)}
														>
															<div className="flex items-center gap-x-2">
																<h2 className="text-xs">
																	{groupName}
																</h2>
																<span className="rounded-sm bg-zinc-100 dark:bg-sidebar px-1 py-0 text-xs font-medium border border-input font-mono">
																	{
																		rows.length
																	}
																</span>
															</div>
														</TableCell>
													</TableRow>
													<SortableContext
														items={rows.map(
															(row) => row.id,
														)}
														strategy={
															verticalListSortingStrategy
														}
													>
														{rows.map(
															(
																row: Row<any>,
																index,
															) => (
																<DraggableTableRow
																	key={row.id}
																	row={row}
																	onClick={() =>
																		handleRowClick(
																			row,
																		)
																	}
																	columns={
																		columns
																	}
																	table={
																		table
																	}
																	groupName={
																		groupName
																	}
																	isGroupActive={
																		isDragging &&
																		activeGroup ===
																			groupName
																	}
																	isLastInGroup={
																		index ===
																		rows.length -
																			1
																	}
																	onMouseEnter={() =>
																		isDragging &&
																		setActiveGroup(
																			groupName,
																		)
																	}
																	onMouseLeave={() =>
																		isDragging &&
																		setActiveGroup(
																			null,
																		)
																	}
																/>
															),
														)}
													</SortableContext>
												</React.Fragment>
											),
									)}
									{!table.getFilteredRowModel().rows
										?.length ||
										(Object.values(taskGroups).flat()
											.length === 0 && (
											<TableRow>
												<TableCell
													colSpan={columns.length}
													className="h-24 text-center rounded-b-lg"
												>
													<EmptyContainer
														title="No tasks found"
														subtitle="You have no tasks for this organization."
														button="Create task"
														icon={
															IconSquareRoundedCheck
														}
													/>
												</TableCell>
											</TableRow>
										))}
								</>
							)}
						</TableBody>
					</UITable>
					<DragOverlay>
						{activeTask && (
							<div className="bg-white dark:bg-sidebar shadow-lg rounded-md border border-input w-fit">
								<TableRow
									className={cn(
										"cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
										activeTask.original.status === "done"
											? "opacity-40"
											: "bg-white dark:bg-transparent",
										"ring-1 ring-blue-500",
									)}
								>
									{activeTask
										.getVisibleCells()
										.map((cell) => (
											<TableCell
												key={cell.id}
												className={cn(
													"px-4 py-2",
													"bg-blue-50 dark:bg-blue-900/20",
												)}
											>
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext(),
												)}
											</TableCell>
										))}
								</TableRow>
							</div>
						)}
					</DragOverlay>
				</DndContext>
			</div>
		</div>
	);
}

interface DraggableTableRowProps {
	row: Row<any>;
	onClick: () => void;
	columns: ColumnDef<any, unknown>[];
	table: Table<any>;
	groupName: string;
	isGroupActive: boolean;
	isLastInGroup: boolean;
	onMouseEnter?: () => void;
	onMouseLeave?: () => void;
}

const DraggableTableRow = ({
	row,
	onClick,
	columns,
	table,
	groupName,
	isGroupActive,
	isLastInGroup,
	onMouseEnter,
	onMouseLeave,
}: DraggableTableRowProps) => {
	const {
		attributes,
		listeners,
		transform,
		transition,
		setNodeRef,
		isDragging,
		over,
	} = useSortable({
		id: row.id,
	});
	// Create api call to update tasks
	// Create api call to delete tasks
	// Create api call to get favorites
	// Create api call to toggle favorites

	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id;
	const { data: favoritesData = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetch(`/api/favorites?organizationId=${organizationId}`).then(
						(res) => res.json(),
					)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});
	const toggleFavorite = useToggleFavorite(organizationId);
	const isFavorite = React.useMemo(() => {
		return favoritesData.some(
			(f: { objectId: string }) => f?.objectId === row.original.id,
		);
	}, [favoritesData, row.original.id]);

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	const overRow = over?.id ? table.getRow(over.id as string) : null;
	const isMovingBetweenStatuses =
		isDragging &&
		overRow &&
		row.original.status !== overRow.original.status;

	const updateTaskMutation = useUpdateTask(organizationId);
	const deleteTaskMutation = useDeleteTask(organizationId);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);

	const taskId = row.original.id;

	const handleStatusChange = (_: string, newStatus: TaskStatus) => {
		updateTaskMutation.mutate({ id: taskId, status: newStatus });
	};

	const handlePriorityChange = (_: string, newPriority: TaskPriority) => {
		updateTaskMutation.mutate({ id: taskId, priority: newPriority });
	};

	const handleDelete = (e: React.MouseEvent, _: string) => {
		e.stopPropagation();
		setShowDeleteDialog(true);
	};

	const confirmDelete = () => {
		deleteTaskMutation.mutate(taskId, {
			onSuccess: () => {
				setShowDeleteDialog(false);
			},
		});
	};

	const handleFavorite = (e: React.MouseEvent, _: string) => {
		e.stopPropagation();
		if (!organizationId) return;
		toggleFavorite.mutate({
			objectId: row.original.id,
			objectType: "task",
			organizationId,
		});
	};

	const searchParams = useSearchParams();
	const highlightId = searchParams.get("highlight");
	const [isHighlighted, setIsHighlighted] = useState(false);

	React.useEffect(() => {
		if (highlightId && row.original.id === highlightId) {
			setIsHighlighted(true);
			const timeout = setTimeout(() => setIsHighlighted(false), 5000);
			return () => clearTimeout(timeout);
		}
	}, [highlightId, row.original.id]);

	return (
		<>
			<AlertDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				title="Delete Task"
				description="Are you sure you want to delete this task? This action cannot be undone."
				confirmLabel="Delete"
				cancelLabel="Cancel"
				confirmClassName="bg-red-500 hover:bg-red-600 text-white"
				onConfirm={confirmDelete}
				loading={deleteTaskMutation.isPending}
			/>
			<TaskContextMenu
				task={row.original}
				isFavorite={isFavorite}
				organizationId={organizationId}
				onEdit={(e) => onClick()}
				onFavorite={handleFavorite}
				onStatusChange={handleStatusChange}
				onPriorityChange={handlePriorityChange}
				onDelete={handleDelete}
				asChild
			>
				<TableRow
					ref={setNodeRef}
					style={style}
					data-state={row.getIsSelected() && "selected"}
					className={cn(
						"cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800/50 border-b data-[state=selected]:dark:bg-zinc-800/50",
						row.original.status === "done"
							? "opacity-40"
							: "bg-white dark:bg-transparent",
						isDragging && "opacity-50",
						isHighlighted &&
							"!bg-yellow-500/25 !border-b !border-yellow-500 animate-pulse",
					)}
					onMouseEnter={onMouseEnter}
					onMouseLeave={onMouseLeave}
					{...attributes}
					{...listeners}
				>
					{row.getVisibleCells().map((cell, cellIndex) => (
						<TableCell
							key={cell.id}
							className={cn(
								"px-4 py-2",
								isMovingBetweenStatuses &&
									"bg-blue-50 dark:bg-blue-900/20",
							)}
							onClick={onClick}
						>
							{flexRender(
								cell.column.columnDef.cell,
								cell.getContext(),
							)}
						</TableCell>
					))}
				</TableRow>
			</TaskContextMenu>
		</>
	);
};
