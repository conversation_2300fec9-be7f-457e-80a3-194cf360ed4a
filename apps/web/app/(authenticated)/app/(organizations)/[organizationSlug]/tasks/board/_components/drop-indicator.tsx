import type { Task } from "@repo/database";

interface DropIndicatorProps {
	beforeId: string;
	column: Task["status"];
	isOver: boolean;
}

export default function DropIndicator({
	beforeId,
	column,
	isOver,
}: DropIndicatorProps) {
	if (!isOver) return null;

	return (
		<div
			className="h-0.5 w-full bg-blue-500 rounded-full my-1 transition-all duration-200"
			style={{
				transform: isOver ? "scaleY(1)" : "scaleY(0)",
			}}
		/>
	);
}
