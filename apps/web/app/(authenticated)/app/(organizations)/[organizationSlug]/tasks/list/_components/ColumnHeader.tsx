import { ColumnHeader } from "@app/shared/components/ColumnHeader";
import {
	IconAntennaBars5,
	IconAt,
	IconCalendarTime,
	IconId,
	IconLetterTSmall,
	IconProgress,
} from "@tabler/icons-react";
import type { Column } from "@tanstack/react-table";
import { cn } from "@ui/lib";

const iconMapping = {
	Title: IconLetterTSmall,
	"Created By": IconProgress,
	Record: IconAntennaBars5,
	"Date Due": IconCalendarTime,
	Related: IconId,
	"Assigned to": IconAt,
} as const;

interface DataTableColumnHeaderProps<TData, TValue>
	extends React.HTMLAttributes<HTMLDivElement> {
	column: Column<TData, TValue>;
	title: string;
}

export function DataTableColumnHeader<TData, TValue>({
	column,
	title,
	className,
}: DataTableColumnHeaderProps<TData, TValue>) {
	if (!column.getCanSort()) {
		return <div className={cn(className)}>{title}</div>;
	}

	const Icon = iconMapping[title as keyof typeof iconMapping];

	return (
		<div className={cn("flex items-center space-x-2", className)}>
			<ColumnHeader title={title} icon={Icon} />

			{/* 
                TODO: YOu can use this to create a  a dropdown menu when you click on the column header 
                to sort the column by ascending or descending order or hide the column
            */}
			{/* <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        size="sm"
                        className="-ml-3 h-8 data-[state=open]:bg-accent"
                    >
                        <span>{title}</span>
                        {column.getIsSorted() === "desc" ? (
                            <IconChevronDown className="ml-2 h-4 w-4 text-blue-500" />
                        ) : column.getIsSorted() === "asc" ? (
                            <IconChevronUp className="ml-2 h-4 w-4 text-blue-500" />
                        ) : null}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                    <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
                        <ArrowUp className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        Asc
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
                        <ArrowDown className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        Desc
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>
                        <EyeOff className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        Hide
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu> */}
		</div>
	);
}
