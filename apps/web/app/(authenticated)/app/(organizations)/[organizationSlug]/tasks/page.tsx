"use client";

import { useSession } from "@app/auth/hooks/use-session";
import { useActiveOrganization } from "@app/organizations/hooks/use-active-organization";
import {
	STORAGE_KEYS,
	TASK_STATUS,
	type TaskStatus,
} from "@app/shared/lib/constants";
import {
	getLocalStorage,
	setLocalStorage,
} from "@app/shared/lib/local-storage";
import BottomBar from "@app/tasks/components/BottomBar";
import { CreateTaskButton } from "@app/tasks/components/CreateTaskButton";
import { TableToolbar } from "@app/tasks/components/TableToolbar";
import { fetchTasks } from "@app/tasks/lib/api";
import {
	getTaskPreferences,
	updateTaskPreferences,
} from "@app/tasks/lib/preferences";
import type { Task } from "@repo/database/src/zod";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
	type ColumnDef,
	type ColumnFiltersState,
	getCoreRowModel,
	getFacetedRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	type SortingState,
	useReactTable,
	type VisibilityState,
} from "@tanstack/react-table";
import type { Filter } from "@ui/components/filters";
import React, { useEffect, useMemo, useState } from "react";
import TasksBoard from "./board";
import CalendarPage from "./calendar";
import TaskList from "./list";
import { columns } from "./list/_components/Columns";
import { TableSettings } from "./list/_components/TableSettings";

const Tasks = () => {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	const { data: tasks = [], isLoading: isTasksLoading } = useQuery<Task[]>({
		queryKey: ["tasks", activeOrganization?.id],
		queryFn: () =>
			activeOrganization?.id
				? fetchTasks(activeOrganization.id)
				: Promise.resolve([]),
		enabled: !!activeOrganization?.id,
		staleTime: 2 * 60 * 1000, // 2 minutes - data is considered fresh
		gcTime: 10 * 60 * 1000, // 10 minutes - cache garbage collection time
		refetchOnWindowFocus: false, // Don't refetch on window focus to reduce API calls
		refetchOnMount: false, // Don't refetch on component mount if data is fresh
	});

	const users = useMemo(() => [], []);

	const usersRecord = useMemo(() => {
		const record: Record<string, any> = {};
		for (const user of users) {
			record[user] = user;
		}
		return record;
	}, [users]);

	const tableData = useMemo(
		() =>
			(tasks as Task[])
				.filter(
					(task: Task) =>
						typeof task === "object" &&
						task !== null &&
						!Array.isArray(task),
				)
				.map((task: Task) => ({ ...task })),
		[tasks, users],
	);
	const [isMounted, setIsMounted] = useState(false);

	// Load initial preferences
	const preferences = getTaskPreferences();
	const [sorting, setSorting] = useState<SortingState>(
		preferences.sortBy
			? [
					{
						id: preferences.sortBy,
						desc: preferences.sortOrder === "desc",
					},
				]
			: [],
	);
	const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
		preferences.columnVisibility || {},
	);
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
		Object.entries(preferences.filters || {})
			.map(([id, value]) => ({
				id,
				value: value || [],
			}))
			.filter((filter) => filter.value.length > 0),
	);
	const [rowSelection, setRowSelection] = useState({});
	const [selectedTasks, setSelectedTasks] = useState<any[]>([]);
	const [value, setValue] = useState<string>(preferences.view);
	const [groupBy, setGroupBy] = useState(preferences.groupBy || "status");
	const [showCompletedTasks, setShowCompletedTasks] = useState(
		preferences.showCompleted ?? true,
	);
	const [statusVisibility, setStatusVisibility] = useState<
		Record<TaskStatus, boolean>
	>(() => {
		const savedVisibility = preferences.statusColumnVisibility;
		return TASK_STATUS.reduce(
			(acc, status) => {
				acc[status.value] = savedVisibility
					? savedVisibility[status.value] !== false
					: true;
				return acc;
			},
			{} as Record<TaskStatus, boolean>,
		);
	});
	const [filters, setFilters] = useState<Filter[]>([]);

	// Update preferences when view changes
	useEffect(() => {
		updateTaskPreferences({ view: value });
	}, [value]);

	// Update preferences when sorting changes
	useEffect(() => {
		if (sorting.length > 0 && sorting[0]) {
			updateTaskPreferences({
				sortBy: sorting[0]?.id ?? "",
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
			});
		}
	}, [sorting]);

	// Update preferences when filters change
	useEffect(() => {
		const filters = columnFilters.reduce(
			(acc, filter) => {
				acc[filter.id] = filter.value;
				return acc;
			},
			{} as Record<string, any>,
		);

		updateTaskPreferences({ filters });
	}, [columnFilters]);

	// Update preferences when groupBy changes
	useEffect(() => {
		updateTaskPreferences({ groupBy });
	}, [groupBy]);

	// Update preferences when showCompletedTasks changes
	useEffect(() => {
		updateTaskPreferences({ showCompleted: showCompletedTasks });
	}, [showCompletedTasks]);

	// Update preferences when column visibility changes
	useEffect(() => {
		updateTaskPreferences({ columnVisibility });
	}, [columnVisibility]);

	// Update preferences when status visibility changes
	useEffect(() => {
		updateTaskPreferences({ statusColumnVisibility: statusVisibility });
	}, [statusVisibility]);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	useEffect(() => {
		setLocalStorage(STORAGE_KEYS.TASKS_VIEW ?? "", value ?? "");
	}, [value]);

	useEffect(() => {
		const storedValue = getLocalStorage(STORAGE_KEYS.TASKS_VIEW ?? "");
		if (typeof storedValue === "string" && storedValue) {
			setValue(storedValue);
		}
	}, []);

	const table = useReactTable({
		data: tableData as unknown as any[],
		columns: columns(filters) as ColumnDef<any>[],
		state: {
			sorting,
			columnVisibility,
			rowSelection,
			columnFilters,
		},
		enableRowSelection: true,
		enableSorting: true,
		enableMultiSort: false,
		onRowSelectionChange: setRowSelection,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
	});

	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		if (tableData !== undefined && isMounted) {
			setIsLoading(false);
		}
	}, [tableData, isMounted]);

	useEffect(() => {
		if (table && isMounted) {
			const selectedRows = table.getFilteredSelectedRowModel().rows;
			const selectedTasksData = selectedRows.map(
				(row) => row.original as any,
			);
			setSelectedTasks(selectedTasksData);
		}
	}, [rowSelection, table, isMounted]);

	const handleStatusVisibilityChange = (
		status: TaskStatus,
		visible: boolean,
	) => {
		setStatusVisibility((prev) => ({ ...prev, [status]: visible }));
	};

	if (!isMounted) {
		return null;
	}

	return (
		<>
			<div
				className={
					"border-b border-muted p-2 w-full flex justify-between items-center"
				}
			>
				<TableToolbar
					table={table}
					groupBy={groupBy}
					onGroupByChange={setGroupBy}
					filters={filters}
					setFilters={setFilters}
				/>
				<div
					className={
						"flex justify-end space-x-2 flex-row items-center"
					}
				>
					<TableSettings
						showCompletedTasks={showCompletedTasks}
						onShowCompletedTasksChange={setShowCompletedTasks}
						statusVisibility={statusVisibility}
						onStatusVisibilityChange={handleStatusVisibilityChange}
						table={table}
						view={value}
						onViewChange={setValue}
						groupBy={groupBy}
						onGroupByChange={setGroupBy}
						user={user}
						users={usersRecord}
					/>
					<CreateTaskButton icon={false} />
				</div>
			</div>

			<div className="pt-4">
				{value === "list" && (
					<TaskList
						columns={columns(filters)}
						table={table}
						isLoading={isLoading}
						groupBy={groupBy}
						showCompletedTasks={showCompletedTasks}
						onStatusVisibilityChange={handleStatusVisibilityChange}
						onTasksUpdated={() =>
							queryClient.invalidateQueries({
								queryKey: ["tasks", activeOrganization?.id],
							})
						}
					/>
				)}

				{value === "kanban" && (
					<TasksBoard
						tasks={tasks}
						table={table}
						statusVisibility={statusVisibility}
						onStatusVisibilityChange={handleStatusVisibilityChange}
					/>
				)}

				{value === "calendar" && (
					<CalendarPage
						tasks={tasks}
						onTasksUpdated={() =>
							queryClient.invalidateQueries({
								queryKey: ["tasks", activeOrganization?.id],
							})
						}
						onStatusVisibilityChange={handleStatusVisibilityChange}
					/>
				)}
			</div>

			{/* TODO: Add bottom bar and figure out why batch actions are messing with auth */}
			{/* <BottomBar
						selectedTasks={selectedTasks}
						onDeselectAll={() => {
							setRowSelection({});
							setSelectedTasks([]);
						}}
					/> */}
		</>
	);
};

export default Tasks;
