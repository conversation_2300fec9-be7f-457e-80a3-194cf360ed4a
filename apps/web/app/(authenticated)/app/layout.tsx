import { SessionProvider } from "@app/auth/components/SessionProvider";
import { sessionQ<PERSON>y<PERSON><PERSON> } from "@app/auth/lib/api";
import { getOrganizationList, getSession } from "@app/auth/lib/server";
import { ContactsProvider } from "@app/contacts/lib/contacts-provider";
import { NotesProvider } from "@app/notes/lib/notes-provider";
import { ActiveOrganizationProvider } from "@app/organizations/components/ActiveOrganizationProvider";
import { organizationListQueryKey } from "@app/organizations/lib/api";
import { purchasesQueryKey } from "@app/payments/lib/api";
import { getPurchases } from "@app/payments/lib/server";
import { PropertiesProvider } from "@app/properties/lib/properties-provider";
import { ConfirmationAlertProvider } from "@app/shared/components/ConfirmationAlertProvider";
import { HelpProvider } from "@app/shared/lib/help-context";
import { TasksProvider } from "@app/tasks/lib/tasks-provider";
import { EmailComposeProvider } from "@shared/components/EmailComposeProvider";
import { GlobalEmailCompose } from "@shared/components/GlobalEmailCompose";
import { config } from "@repo/config";
import { EdgeStoreProvider } from "@repo/storage";
import { getQueryClient } from "@shared/lib/server";
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import type { PropsWithChildren } from "react";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function Layout({ children }: PropsWithChildren) {
	const session = await getSession();

	const queryClient = getQueryClient();

	await queryClient.prefetchQuery({
		queryKey: sessionQueryKey,
		queryFn: () => session,
	});

	if (config.organizations.enable) {
		await queryClient.prefetchQuery({
			queryKey: organizationListQueryKey,
			queryFn: getOrganizationList,
		});
	}

	if (config.users.enableBilling) {
		await queryClient.prefetchQuery({
			queryKey: purchasesQueryKey(),
			queryFn: () => getPurchases(),
		});
	}

	return (
		<HydrationBoundary state={dehydrate(queryClient)}>
			<EdgeStoreProvider>
				<SessionProvider>
					<ActiveOrganizationProvider>
						<PropertiesProvider>
							<ContactsProvider>
								<NotesProvider>
									<TasksProvider>
										<ConfirmationAlertProvider>
											<HelpProvider>
												<EmailComposeProvider>
													{children}
													<GlobalEmailCompose />
												</EmailComposeProvider>
											</HelpProvider>
										</ConfirmationAlertProvider>
									</TasksProvider>
								</NotesProvider>
							</ContactsProvider>
						</PropertiesProvider>
					</ActiveOrganizationProvider>
				</SessionProvider>
			</EdgeStoreProvider>
		</HydrationBoundary>
	);
}
