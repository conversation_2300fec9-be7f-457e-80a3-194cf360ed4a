import { createMDXSource } from "@fumadocs/content-collections";
import { config } from "@repo/config";
import { allHelpMetas, allHelps } from "content-collections";
import { loader } from "fumadocs-core/source";
import { Home } from "lucide-react";
import { createElement } from "react";

export const helpSource = loader({
	baseUrl: "/help",
	i18n: {
		defaultLanguage: config.i18n.defaultLocale,
		languages: Object.keys(config.i18n.locales),
	},
	source: createMDXSource(allHelps, allHelpMetas),
	icon(icon) {
		if (!icon) {
			return;
		}

		const icons = {
			Home,
		};

		if (icon in icons) {
			return createElement(icons[icon as keyof typeof icons]);
		}
	},
});
