import { db } from "@repo/database/server";
import { type NextRequest, NextResponse } from "next/server";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		const { id } = await params;

		const note = await db.note.findUnique({
			where: { id },
			include: {
				user: {
					select: { id: true, name: true, email: true, image: true },
				},
			},
		});

		if (!note) {
			return NextResponse.json(
				{ error: "Note not found" },
				{ status: 404 },
			);
		}

		// Only return published notes that are not archived
		if (!note.isPublished || note.isArchived) {
			return NextResponse.json(
				{ error: "Note is not public" },
				{ status: 403 },
			);
		}

		const noteWithCreatedBy = {
			...note,
			createdBy: note.user
				? {
						id: note.user.id,
						name: note.user.name,
						email: note.user.email,
						image: note.user.image,
					}
				: undefined,
		};

		return NextResponse.json(noteWithCreatedBy);
	} catch (error) {
		console.error("Error fetching note:", error);
		return NextResponse.json(
			{ error: "Failed to fetch note" },
			{ status: 500 },
		);
	}
}
