import { NextResponse } from "next/server";

const LOOPS_API_KEY = process.env.LOOPS_TRANSACTIONAL_API_KEY as string;
const LOOPS_API_ENDPOINT = "https://app.loops.so/api/v1/transactional";

export async function POST(request: Request) {
	try {
		const { email } = await request.json();

		if (!email) {
			return NextResponse.json(
				{ success: false, message: "Email is required" },
				{ status: 400 },
			);
		}

		const payload = {
			transactionalId: "cm1y0mwwl00l3wo70447dqm44",
			email: email,
		};

		try {
			const response = await fetch(LOOPS_API_ENDPOINT, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${LOOPS_API_KEY}`,
				},
				body: JSON.stringify(payload),
			});

			const data = await response.json();

			if (response.ok) {
				return NextResponse.json({ success: true });
			}
			console.error("Loops API error:", JSON.stringify(data, null, 2));
			return NextResponse.json(
				{
					success: false,
					message: "Failed to send email",
					details: data,
				},
				{ status: response.status },
			);
		} catch (loopsError) {
			console.error("Loops API threw an error:", loopsError);
			return NextResponse.json(
				{
					success: false,
					message: "Error calling Loops API",
					details: loopsError as string,
				},
				{ status: 500 },
			);
		}
	} catch (error) {
		console.error("Unexpected error:", error);
		return NextResponse.json(
			{
				success: false,
				message: "An unexpected error occurred",
				details: error as string,
			},
			{ status: 500 },
		);
	}
}
