import { LoopsClient } from "loops";
import { type NextRequest, NextResponse } from "next/server";

const LOOPS_ID = "cm17j923m01gcm9zq4rxnb56o";
const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);

export async function POST(request: NextRequest) {
	try {
		const res = await request.json();
		const email = res["email"];

		if (!email || typeof email !== "string") {
			return NextResponse.json(
				{ success: false, message: "Email is required" },
				{ status: 400 },
			);
		}

		// Add contact to Loops
		const resp = await loops.createContact(LOOPS_ID, {
			email,
			source: "Early Access",
			subscribed: true,
			timestamp: new Date().toISOString(),
		});

		if (!resp.success) {
			return NextResponse.json(
				{ success: false, message: "Failed to subscribe" },
				{ status: 400 },
			);
		}

		return NextResponse.json({
			success: true,
			message: "Successfully subscribed to early access",
		});
	} catch (error) {
		console.error("Error subscribing to early access:", error);
		return NextResponse.json(
			{ success: false, message: "Failed to subscribe" },
			{ status: 500 },
		);
	}
}
