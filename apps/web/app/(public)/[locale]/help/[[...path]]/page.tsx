import { config } from "@repo/config";
import { Rate } from "@ui/components/rate";
import { DocsBody, DocsPage } from "fumadocs-ui/page";
import { notFound } from "next/navigation";
import { getTranslations, setRequestLocale } from "next-intl/server";
import { HelpPageWrapper } from "../../../../../modules/homepage/shared/components/HelpPageWrapper";
import { helpSource } from "../../../../help-source";

export default async function DocumentationPage(props: {
	params: Promise<{ path?: string[]; locale: string }>;
}) {
	const params = await props.params;
	setRequestLocale(params.locale);
	const page = helpSource.getPage(params.path, params.locale);
	const isHomePage =
		!params.path ||
		params.path.length === 0 ||
		(params.path.length === 1 && params.path[0] === "");

	if (!page) {
		notFound();
	}

	return (
		<DocsPage
			toc={page.data.toc}
			full={page.data.full}
			breadcrumb={{
				enabled: true,
				includePage: true,
				includeSeparator: true,
			}}
			tableOfContent={{
				enabled: !isHomePage,
			}}
		>
			<DocsBody>
				<h1 className="text-foreground">{page.data.title}</h1>
				{page.data.description && (
					<p className="-mt-6 text-foreground/50 text-lg lg:text-xl">
						{page.data.description}
					</p>
				)}
				<div className="prose dark:prose-invert max-w-full prose-a:text-foreground prose-p:text-foreground/80">
					<HelpPageWrapper code={page.data.body} />
				</div>
				{!isHomePage && (
					<Rate
						onRateAction={async (url, feedback) => {
							"use server";
							// await posthog.capture('on_rate_docs', feedback);
						}}
					/>
				)}
			</DocsBody>
		</DocsPage>
	);
}

export async function generateStaticParams() {
	return helpSource.getPages().flatMap((page) => ({
		path: page.slugs,
		locale: page.locale ?? config.i18n.defaultLocale,
	}));
}

export async function generateMetadata(props: {
	params: Promise<{ path?: string[]; locale: string }>;
}) {
	const t = await getTranslations();
	const params = await props.params;
	const page = helpSource.getPage(params.path, params.locale);

	if (!page) {
		notFound();
	}

	return {
		title: `${page.data.title} | ${t("help.title")}`,
		description: page.data.description,
	};
}
