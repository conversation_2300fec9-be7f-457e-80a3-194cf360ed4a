import { getLocale, getTranslations } from "next-intl/server";
import React from "react";
import { FAQ } from "../../../../modules/homepage/home/<USER>/FAQ";
import Pricing from "../../../../modules/homepage/home/<USER>/Pricing";
import Pricing2 from "../../../../modules/homepage/home/<USER>/Pricing2";

export async function generateMetadata() {
	const t = await getTranslations();
	return {
		title: t("blog.title"),
	};
}

export default async function BlogListPage() {
	const locale = await getLocale();
	const t = await getTranslations();

	return (
		<div className="container max-w-6xl pt-32 pb-16">
			<div className="mb-12 pt-8 text-center">
				<h1 className="mb-2 font-bold text-5xl">
					{t("pricing.title")}
				</h1>
				<p className="text-lg opacity-50">{t("pricing.description")}</p>
			</div>

			<>
				<div className="mb-12 pt-8 text-center">
					<Pricing />
				</div>
				<Pricing2 />
				<FAQ />
			</>
		</div>
	);
}
