"use client";

import { cn } from "@ui/lib";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";
import { ChangelogEntry } from "../../../../modules/homepage/changelog/components/ChangelogEntry";
import {
	type ChangelogItem,
	changelog,
} from "../../../../modules/homepage/changelog/data";

// Group entries by year and month
const groupByYearAndMonth = (entries: ChangelogItem[]) => {
	return entries.reduce<Record<string, Record<string, ChangelogItem[]>>>(
		(acc, entry) => {
			const date = new Date(entry.date);
			const year = date.getFullYear().toString();
			const month = date.toLocaleString("default", { month: "long" });

			if (!acc[year]) {
				acc[year] = {};
			}

			if (!acc[year][month]) {
				acc[year][month] = [];
			}

			acc[year][month].push(entry);
			return acc;
		},
		{},
	);
};

// Sort years in descending order
const getSortedYears = (
	groupedData: ReturnType<typeof groupByYearAndMonth>,
) => {
	return Object.keys(groupedData).sort(
		(a, b) => Number.parseInt(b) - Number.parseInt(a),
	);
};

// Sort months in descending order within a year
const getSortedMonths = (months: string[]) => {
	const monthOrder = [
		"January",
		"February",
		"March",
		"April",
		"May",
		"June",
		"July",
		"August",
		"September",
		"October",
		"November",
		"December",
	];

	return [...months].sort(
		(a, b) => monthOrder.indexOf(b) - monthOrder.indexOf(a),
	);
};

interface CollapsibleSectionProps {
	title: string;
	children: React.ReactNode;
	defaultOpen?: boolean;
	className?: string;
	level?: "year" | "month";
}

const CollapsibleSection = ({
	title,
	children,
	defaultOpen = true,
	className = "",
	level = "month",
}: CollapsibleSectionProps) => {
	const [isOpen, setIsOpen] = useState(defaultOpen);

	return (
		<div className={className}>
			<button
				onClick={() => setIsOpen(!isOpen)}
				className={cn(
					"flex items-center gap-2 py-2 font-medium w-full text-left",
					level === "year" ? "text-2xl font-bold" : "text-xl",
				)}
				aria-expanded={isOpen}
			>
				{isOpen ? (
					<ChevronDown className="w-5 h-5" />
				) : (
					<ChevronRight className="w-5 h-5" />
				)}
				{title}
			</button>
			<div
				className={cn(
					"overflow-hidden transition-all duration-200",
					isOpen ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0",
				)}
			>
				<div
					className={cn(
						"py-2",
						level === "year" ? "pl-4 border-l-2 border-muted" : "",
					)}
				>
					{children}
				</div>
			</div>
		</div>
	);
};

export default function ChangelogPage() {
	const t = {
		"changelog.title": "Changelog",
		"changelog.description": "See what's new in our latest updates",
	};

	const groupedData = groupByYearAndMonth(changelog);
	const sortedYears = getSortedYears(groupedData);
	const hasSingleYear = sortedYears.length === 1;
	const hasSingleMonth =
		hasSingleYear &&
		Object.keys(groupedData[sortedYears[0] ?? ""] ?? {}).length === 1;

	const renderEntries = (year: string, month: string) => (
		<div className="space-y-8 pl-6">
			{groupedData[year]?.[month]
				?.sort(
					(a, b) =>
						new Date(b.date).getTime() - new Date(a.date).getTime(),
				)
				.map((entry, i) => (
					<ChangelogEntry key={i} item={entry} />
				))}
		</div>
	);

	// If there's only one year and one month, render directly without collapsible sections
	if (hasSingleYear && hasSingleMonth) {
		const year = sortedYears[0];
		const month = getSortedMonths(
			Object.keys(groupedData[year ?? ""] ?? {}),
		)[0];

		return (
			<div className="container mx-auto flex flex-col gap-10 px-4 pt-32 sm:px-16 xl:px-20">
				<div className="mb-8 pt-8 text-center">
					<h1 className="mb-2 font-bold text-5xl">
						{t["changelog.title"]}
					</h1>
					<p className="text-lg opacity-50">
						{t["changelog.description"]}
					</p>
				</div>

				<div className="max-w-7xl mx-auto w-full">
					<h2 className="text-2xl font-bold mb-4">
						{year} - {month}
					</h2>
					{renderEntries(year ?? "", month ?? "")}
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto flex flex-col gap-10 px-4 pt-32 sm:px-16 xl:px-20">
			<div className="mb-8 pt-8 text-center">
				<h1 className="mb-2 font-bold text-5xl">
					{t["changelog.title"]}
				</h1>
				<p className="text-lg opacity-50">
					{t["changelog.description"]}
				</p>
			</div>

			<div className="max-w-7xl mx-auto w-full">
				{sortedYears.map((year) => {
					const months = Object.keys(groupedData[year ?? ""] ?? {});
					const sortedMonths = getSortedMonths(months);

					return (
						<CollapsibleSection
							key={year}
							title={year}
							level="year"
							className="mb-6"
						>
							{sortedMonths.map((month) => (
								<CollapsibleSection
									key={`${year}-${month}`}
									title={month}
									level="month"
									className="mb-4"
								>
									{renderEntries(year, month)}
								</CollapsibleSection>
							))}
						</CollapsibleSection>
					);
				})}
			</div>
		</div>
	);
}
