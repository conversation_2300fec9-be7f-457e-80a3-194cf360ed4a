import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import type { ActivityTypeType } from "@repo/database/src/zod";

export interface SystemActivityParams {
	recordId: string;
	recordType: "contact" | "company" | "property";
	organizationId: string;
	userId: string;
	message: string;
	type?: ActivityTypeType;
}

export interface FieldChange {
	field: string;
	oldValue: any;
	newValue: any;
	displayName?: string;
}

/**
 * Create a system activity for record tracking
 */
export async function createSystemActivity(params: SystemActivityParams) {
	try {
		const activity = await db.activity.create({
			data: {
				recordId: params.recordId,
				recordType: params.recordType,
				organizationId: params.organizationId,
				userId: params.userId,
				type: params.type || "system",
				message: params.message,
				system: true,
			},
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						image: true,
					},
				},
			},
		});

		logger.info(`System activity created: ${activity.id} for ${params.recordType} ${params.recordId}`);
		return activity;
	} catch (error) {
		logger.error("Failed to create system activity:", error);
		// Don't throw - we don't want activity creation to fail the main operation
		return null;
	}
}

/**
 * Create a system activity for record creation
 */
export async function createRecordCreatedActivity(
	recordId: string,
	recordType: "contact" | "company" | "property",
	organizationId: string,
	userId: string,
	recordName?: string
) {
	const entityName = recordType.charAt(0).toUpperCase() + recordType.slice(1);
	const message = recordName 
		? `${recordName} was created`
		: `${entityName} was created`;

	return createSystemActivity({
		recordId,
		recordType,
		organizationId,
		userId,
		message,
		type: "system"
	});
}

/**
 * Create a system activity for record deletion
 */
export async function createRecordDeletedActivity(
	recordId: string,
	recordType: "contact" | "company" | "property",
	organizationId: string,
	userId: string,
	recordName?: string
) {
	const entityName = recordType.charAt(0).toUpperCase() + recordType.slice(1);
	const message = recordName 
		? `${recordName} was deleted`
		: `${entityName} was deleted`;

	return createSystemActivity({
		recordId,
		recordType,
		organizationId,
		userId,
		message,
		type: "system"
	});
}

/**
 * Format field value for display
 */
function formatFieldValue(value: any, fieldName: string): string {
	if (value === null || value === undefined || value === '') {
		return 'empty';
	}

	// Handle arrays (like emails, phones, addresses)
	if (Array.isArray(value)) {
		if (value.length === 0) return 'empty';
		if (fieldName.includes('email') || fieldName.includes('phone')) {
			return value.map(item => 
				typeof item === 'object' ? (item.address || item.value || item.number || String(item)) : String(item)
			).join(', ');
		}
		if (fieldName.includes('address')) {
			return value.map(addr => 
				typeof addr === 'object' ? 
					[addr.street, addr.city, addr.state].filter(Boolean).join(', ') || String(addr) 
					: String(addr)
			).join('; ');
		}
		return value.map(String).join(', ');
	}

	// Handle objects
	if (typeof value === 'object') {
		if (value.name) return value.name;
		if (value.title) return value.title;
		if (value.label) return value.label;
		return JSON.stringify(value);
	}

	// Handle booleans
	if (typeof value === 'boolean') {
		return value ? 'Yes' : 'No';
	}

	// Handle dates
	if (value instanceof Date) {
		return value.toLocaleDateString();
	}

	return String(value);
}

/**
 * Get human-readable field name
 */
function getFieldDisplayName(fieldName: string): string {
	const fieldMapping: Record<string, string> = {
		firstName: 'First Name',
		lastName: 'Last Name',
		fullName: 'Full Name',
		email: 'Email',
		phone: 'Phone',
		title: 'Title',
		company: 'Company',
		website: 'Website',
		address: 'Address',
		status: 'Status',
		stage: 'Stage',
		source: 'Source',
		industry: 'Industry',
		size: 'Company Size',
		description: 'Description',
		propertyType: 'Property Type',
		propertySubType: 'Property Sub Type',
		listingId: 'Listing ID',
		price: 'Price',
		estimatedValue: 'Estimated Value',
		squareFootage: 'Square Footage',
		bedrooms: 'Bedrooms',
		bathrooms: 'Bathrooms',
		yearBuilt: 'Year Built',
		// Add more as needed
	};

	return fieldMapping[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
}

/**
 * Create system activity for field changes
 */
export async function createFieldChangeActivity(
	recordId: string,
	recordType: "contact" | "company" | "property",
	organizationId: string,
	userId: string,
	changes: FieldChange[]
) {
	if (changes.length === 0) return null;

	// Filter out changes where old and new values are the same
	const actualChanges = changes.filter(change => {
		const oldStr = formatFieldValue(change.oldValue, change.field);
		const newStr = formatFieldValue(change.newValue, change.field);
		return oldStr !== newStr;
	});

	if (actualChanges.length === 0) return null;

	let message: string;

	if (actualChanges.length === 1) {
		const change = actualChanges[0];
		const fieldName = change.displayName || getFieldDisplayName(change.field);
		const oldValue = formatFieldValue(change.oldValue, change.field);
		const newValue = formatFieldValue(change.newValue, change.field);
		
		if (oldValue === 'empty') {
			message = `${fieldName} was set to "${newValue}"`;
		} else if (newValue === 'empty') {
			message = `${fieldName} was cleared (was "${oldValue}")`;
		} else {
			message = `${fieldName} changed from "${oldValue}" to "${newValue}"`;
		}
	} else {
		// Multiple changes
		const fieldNames = actualChanges.map(change => 
			change.displayName || getFieldDisplayName(change.field)
		);
		
		if (fieldNames.length === 2) {
			message = `${fieldNames[0]} and ${fieldNames[1]} were updated`;
		} else if (fieldNames.length <= 4) {
			const lastField = fieldNames.pop();
			message = `${fieldNames.join(', ')}, and ${lastField} were updated`;
		} else {
			message = `${fieldNames.length} fields were updated: ${fieldNames.slice(0, 3).join(', ')}, and ${fieldNames.length - 3} more`;
		}
	}

	return createSystemActivity({
		recordId,
		recordType,
		organizationId,
		userId,
		message,
		type: "system"
	});
}

/**
 * Compare two objects and detect field changes
 */
export function detectFieldChanges(
	oldRecord: any,
	newRecord: any,
	fieldsToTrack?: string[]
): FieldChange[] {
	const changes: FieldChange[] = [];
	
	// Default fields to track if none specified
	const defaultFields = [
		'firstName', 'lastName', 'email', 'phone', 'title', 'company', 'website', 
		'address', 'status', 'stage', 'source', 'industry', 'size', 'description',
		'name', 'propertyType', 'propertySubType', 'listingId', 'price', 
		'estimatedValue', 'squareFootage', 'bedrooms', 'bathrooms', 'yearBuilt'
	];
	
	const fieldsToCheck = fieldsToTrack || defaultFields;

	for (const field of fieldsToCheck) {
		if (field in newRecord) {
			const oldValue = oldRecord[field];
			const newValue = newRecord[field];

			// Deep comparison for objects/arrays
			const oldStr = JSON.stringify(oldValue);
			const newStr = JSON.stringify(newValue);

			if (oldStr !== newStr) {
				changes.push({
					field,
					oldValue,
					newValue
				});
			}
		}
	}

	return changes;
}

/**
 * Create system activity for email address changes specifically
 */
export async function createEmailChangeActivity(
	recordId: string,
	recordType: "contact" | "company" | "property",
	organizationId: string,
	userId: string,
	oldEmails: any[],
	newEmails: any[],
	action: 'added' | 'removed' | 'changed'
) {
	const oldEmailStrs = (oldEmails || []).map(e => typeof e === 'object' ? e.address || e.value : e);
	const newEmailStrs = (newEmails || []).map(e => typeof e === 'object' ? e.address || e.value : e);

	let message: string;

	if (action === 'added') {
		const addedEmails = newEmailStrs.filter(email => !oldEmailStrs.includes(email));
		if (addedEmails.length > 0) {
			message = `Email address${addedEmails.length > 1 ? 'es' : ''} ${addedEmails.join(', ')} ${addedEmails.length > 1 ? 'were' : 'was'} added`;
		} else {
			return null;
		}
	} else if (action === 'removed') {
		const removedEmails = oldEmailStrs.filter(email => !newEmailStrs.includes(email));
		if (removedEmails.length > 0) {
			message = `Email address${removedEmails.length > 1 ? 'es' : ''} ${removedEmails.join(', ')} ${removedEmails.length > 1 ? 'were' : 'was'} removed`;
		} else {
			return null;
		}
	} else {
		// changed
		message = `Email addresses updated`;
	}

	return createSystemActivity({
		recordId,
		recordType,
		organizationId,
		userId,
		message,
		type: "system"
	});
}

 