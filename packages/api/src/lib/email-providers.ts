import { logger } from "@repo/logs";
import { db } from "@repo/database/server";

interface SendEmailParams {
  to: string;
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  htmlBody?: string;
}

interface ConnectedAccount {
  id: string;
  providerId: string;
  accessToken?: string | null;
  refreshToken?: string | null;
  accessTokenExpiresAt?: Date | null;
  idToken?: string | null;
}

/**
 * Refresh expired Google OAuth token
 */
async function refreshGoogleToken(account: ConnectedAccount): Promise<ConnectedAccount> {
  if (!account.refreshToken) {
    throw new Error("No refresh token available - please reconnect your account");
  }

  const response = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      client_id: process.env.GOOGLE_CLIENT_ID as string,
      client_secret: process.env.GOOGLE_CLIENT_SECRET as string,
      refresh_token: account.refreshToken,
      grant_type: 'refresh_token',
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    logger.error("Failed to refresh Google token", { error, status: response.status });
    throw new Error("Failed to refresh access token - please reconnect your account");
  }

  const tokenData = await response.json();
  
  // Update the account in database with new tokens
  const expiresAt = new Date(Date.now() + (tokenData.expires_in * 1000));
  
  await db.account.update({
    where: { id: account.id },
    data: {
      accessToken: tokenData.access_token,
      accessTokenExpiresAt: expiresAt,
      // Only update refresh token if a new one is provided
      ...(tokenData.refresh_token && { refreshToken: tokenData.refresh_token }),
    },
  });

  logger.info("Successfully refreshed Google access token", { accountId: account.id });

  return {
    ...account,
    accessToken: tokenData.access_token,
    accessTokenExpiresAt: expiresAt,
    refreshToken: tokenData.refresh_token || account.refreshToken,
  };
}

/**
 * Refresh expired Microsoft OAuth token
 */
async function refreshMicrosoftToken(account: ConnectedAccount): Promise<ConnectedAccount> {
  if (!account.refreshToken) {
    throw new Error("No refresh token available - please reconnect your account");
  }

  const response = await fetch('https://login.microsoftonline.com/common/oauth2/v2.0/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      client_id: process.env.MICROSOFT_CLIENT_ID as string,
      client_secret: process.env.MICROSOFT_CLIENT_SECRET as string,
      refresh_token: account.refreshToken,
      grant_type: 'refresh_token',
      scope: 'User.Read Mail.Send Mail.Read',
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    logger.error("Failed to refresh Microsoft token", { error, status: response.status });
    throw new Error("Failed to refresh access token - please reconnect your account");
  }

  const tokenData = await response.json();
  
  // Update the account in database with new tokens
  const expiresAt = new Date(Date.now() + (tokenData.expires_in * 1000));
  
  await db.account.update({
    where: { id: account.id },
    data: {
      accessToken: tokenData.access_token,
      accessTokenExpiresAt: expiresAt,
      refreshToken: tokenData.refresh_token,
    },
  });

  logger.info("Successfully refreshed Microsoft access token", { accountId: account.id });

  return {
    ...account,
    accessToken: tokenData.access_token,
    accessTokenExpiresAt: expiresAt,
    refreshToken: tokenData.refresh_token,
  };
}

/**
 * Send email via Gmail API
 */
async function sendViaGmail(
  account: ConnectedAccount, 
  emailParams: SendEmailParams
): Promise<{ messageId: string }> {
  let workingAccount = account;

  // Check if token is expired and refresh if needed
  if (account.accessTokenExpiresAt && new Date() > account.accessTokenExpiresAt) {
    logger.info("Gmail access token expired, attempting to refresh", { accountId: account.id });
    workingAccount = await refreshGoogleToken(account);
  }

  if (!workingAccount.accessToken) {
    throw new Error("No access token available for Gmail account");
  }

  // Get sender email from ID token
  let fromEmail = '';
  if (workingAccount.idToken) {
    try {
      const payload = JSON.parse(Buffer.from(workingAccount.idToken.split('.')[1], 'base64').toString());
      fromEmail = payload.email || '';
    } catch (error) {
      logger.error("Failed to parse email from Gmail ID token", { error });
      throw new Error("Unable to determine sender email address");
    }
  }

  if (!fromEmail) {
    throw new Error("Unable to determine Gmail sender email address");
  }

  // Build email message in RFC 2822 format
  const emailLines = [
    `From: ${fromEmail}`,
    `To: ${emailParams.to}`,
  ];

  if (emailParams.cc && emailParams.cc.length > 0) {
    emailLines.push(`Cc: ${emailParams.cc.join(', ')}`);
  }

  if (emailParams.bcc && emailParams.bcc.length > 0) {
    emailLines.push(`Bcc: ${emailParams.bcc.join(', ')}`);
  }

  emailLines.push(
    `Subject: ${emailParams.subject}`,
    `Content-Type: text/html; charset=utf-8`,
    ``,
    emailParams.htmlBody || emailParams.body
  );

  const rawEmail = emailLines.join('\r\n');
  const encodedEmail = Buffer.from(rawEmail).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

  // Send via Gmail API
  const response = await fetch('https://gmail.googleapis.com/gmail/v1/users/me/messages/send', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${workingAccount.accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      raw: encodedEmail,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    logger.error("Gmail API error", { error, status: response.status });
    throw new Error(`Failed to send email via Gmail: ${response.status}`);
  }

  const result = await response.json();
  return { messageId: result.id };
}

/**
 * Send email via Microsoft Graph API
 */
async function sendViaMicrosoft(
  account: ConnectedAccount, 
  emailParams: SendEmailParams
): Promise<{ messageId: string }> {
  let workingAccount = account;

  // Check if token is expired and refresh if needed
  if (account.accessTokenExpiresAt && new Date() > account.accessTokenExpiresAt) {
    logger.info("Microsoft access token expired, attempting to refresh", { accountId: account.id });
    workingAccount = await refreshMicrosoftToken(account);
  }

  if (!workingAccount.accessToken) {
    throw new Error("No access token available for Microsoft account");
  }

  // Build email message for Microsoft Graph
  const message = {
    subject: emailParams.subject,
    body: {
      contentType: "HTML",
      content: emailParams.htmlBody || emailParams.body.replace(/\n/g, '<br>')
    },
    toRecipients: [
      {
        emailAddress: {
          address: emailParams.to
        }
      }
    ],
    ccRecipients: emailParams.cc?.map(email => ({
      emailAddress: { address: email }
    })) || [],
    bccRecipients: emailParams.bcc?.map(email => ({
      emailAddress: { address: email }
    })) || [],
  };

  // Send via Microsoft Graph API
  const response = await fetch('https://graph.microsoft.com/v1.0/me/sendMail', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${workingAccount.accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      message,
      saveToSentItems: true,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    logger.error("Microsoft Graph API error", { error, status: response.status });
    throw new Error(`Failed to send email via Microsoft Graph: ${response.status}`);
  }

  // Microsoft Graph doesn't return a message ID for sendMail, so we generate one
  const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  return { messageId };
}

/**
 * Send email using connected account (Gmail or Microsoft)
 */
export async function sendEmailViaConnectedAccount(
  account: ConnectedAccount,
  emailParams: SendEmailParams
): Promise<{ messageId: string }> {
  logger.info("Sending email via connected account", {
    provider: account.providerId,
    accountId: account.id,
    to: emailParams.to,
    subject: emailParams.subject,
  });

  try {
    switch (account.providerId) {
      case 'google':
        return await sendViaGmail(account, emailParams);
      
      case 'microsoft':
        return await sendViaMicrosoft(account, emailParams);
      
      default:
        throw new Error(`Unsupported email provider: ${account.providerId}`);
    }
  } catch (error) {
    logger.error("Failed to send email via connected account", {
      error,
      provider: account.providerId,
      accountId: account.id,
    });
    throw error;
  }
} 