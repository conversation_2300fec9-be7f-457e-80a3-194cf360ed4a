import { db } from "@repo/database/server";
import { logger } from "@repo/logs";

export interface EmailParticipant {
  email: string;
  name?: string;
  domain?: string;
}

export interface ProcessedEmail {
  id: string;
  messageId: string;
  linkedContacts: string[];
  linkedCompanies: string[];
}

/**
 * Extract email addresses from email content
 */
export function extractEmailParticipants(body: string, fromEmail: string): string[] {
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  const foundEmails = body.match(emailRegex) || [];
  
  // Add the sender email and remove duplicates
  const allEmails = Array.from(new Set([fromEmail, ...foundEmails]));
  
  // Filter out common system emails
  const systemEmails = ['noreply', 'no-reply', 'donotreply', 'mailer-daemon'];
  return allEmails.filter(email => 
    !systemEmails.some(sys => email.toLowerCase().includes(sys))
  );
}

/**
 * Find existing contact by email address
 */
export async function findContactByEmail(
  organizationId: string, 
  email: string
): Promise<string | null> {
  const contacts = await db.contact.findMany({
    where: {
      organizationId,
      isDeleted: false,
    },
    select: {
      id: true,
      email: true
    }
  });
  
  // Find contact with matching email
  const existingContact = contacts.find(contact => {
    if (!contact.email) return false;
    const emails = contact.email as any[];
    return emails.some((emailObj: any) => 
      emailObj.address?.toLowerCase() === email.toLowerCase()
    );
  });
  
  return existingContact?.id || null;
}

/**
 * Create a new contact from an email address
 */
export async function createContactFromEmail(
  organizationId: string,
  email: string,
  createdBy: string
): Promise<string> {
  const [name, domain] = email.split('@');
  const firstName = name.split('.')[0] || name.split('_')[0] || name;
  const lastName = name.split('.')[1] || name.split('_')[1] || '';
  
  const newContact = await db.contact.create({
    data: {
      organizationId,
      firstName: firstName.charAt(0).toUpperCase() + firstName.slice(1),
      lastName: lastName ? lastName.charAt(0).toUpperCase() + lastName.slice(1) : undefined,
      email: [
        {
          address: email,
          label: 'Work',
          isPrimary: true,
          isBad: false
        }
      ],
      source: 'Email Forwarding',
      createdBy,
      updatedBy: createdBy
    }
  });
  
  return newContact.id;
}

/**
 * Process email participants and create/link contacts
 */
export async function processEmailForContacts(
  organizationId: string,
  forwardedByUserId: string,
  participants: string[]
): Promise<{ contacts: string[]; companies: string[] }> {
  const linkedContacts: string[] = [];
  const linkedCompanies: string[] = [];
  
  for (const email of participants) {
    try {
      // Check if contact already exists
      const existingContactId = await findContactByEmail(organizationId, email);
      
      if (existingContactId) {
        linkedContacts.push(existingContactId);
        logger.info(`Linked existing contact: ${existingContactId} for email: ${email}`);
      } else {
        // Create new contact
        const newContactId = await createContactFromEmail(
          organizationId,
          email,
          forwardedByUserId
        );
        
        linkedContacts.push(newContactId);
        logger.info(`Created new contact: ${newContactId} for email: ${email}`);
      }
    } catch (error) {
      logger.error(`Failed to process participant ${email}:`, error);
    }
  }
  
  return { contacts: linkedContacts, companies: linkedCompanies };
}

/**
 * Store a forwarded email in the database
 */
export async function storeForwardedEmail({
  organizationId,
  messageId,
  from,
  to,
  subject,
  body,
  attachments,
  participants,
  linkedRecords,
  forwardedBy,
  headers,
  sharingLevel = 'full'
}: {
  organizationId: string;
  messageId: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  attachments?: any[];
  participants: string[];
  linkedRecords: { contacts: string[]; companies: string[] };
  forwardedBy: string;
  headers?: any;
  sharingLevel?: string;
}): Promise<ProcessedEmail> {
  const forwardedEmail = await db.forwardedEmail.create({
    data: {
      organizationId,
      messageId,
      from,
      to,
      subject,
      body,
      attachments: attachments?.length ? attachments : null,
      participants,
      linkedRecords,
      forwardedBy,
      processedAt: new Date(),
      sharingLevel,
      headers: headers || {}
    }
  });
  
  return {
    id: forwardedEmail.id,
    messageId: forwardedEmail.messageId,
    linkedContacts: linkedRecords.contacts,
    linkedCompanies: linkedRecords.companies
  };
}

/**
 * Check if an email has already been processed
 */
export async function isEmailAlreadyProcessed(messageId: string): Promise<boolean> {
  const existingEmail = await db.forwardedEmail.findUnique({
    where: { messageId }
  });
  
  return !!existingEmail;
}

/**
 * Get organization by email forwarding address
 */
export async function getOrganizationByForwardingAddress(
  emailAddress: string
): Promise<{ organization: any; slug: string } | null> {
  // Extract organization slug from email address
  // Expected format: {organizationSlug}@inbox.reliocrm.com
  const emailParts = emailAddress.split('@');
  if (emailParts.length !== 2 || !emailParts[1].includes('reliocrm.com')) {
    return null;
  }

  const orgSlug = emailParts[0].split('+')[0]; // Support plus addressing
  
  const organization = await db.organization.findUnique({
    where: { slug: orgSlug },
    include: { 
      members: { 
        include: { user: true } 
      } 
    }
  });
  
  if (!organization) {
    return null;
  }
  
  return { organization, slug: orgSlug };
}

/**
 * Verify that the sender is authorized to forward emails to this organization
 */
export function verifyEmailSender(
  organization: any,
  fromEmail: string
): { isAuthorized: boolean; member?: any } {
  const senderMember = organization.members.find((m: any) => 
    m.user.email.toLowerCase() === fromEmail.toLowerCase()
  );
  
  return {
    isAuthorized: !!senderMember,
    member: senderMember
  };
} 