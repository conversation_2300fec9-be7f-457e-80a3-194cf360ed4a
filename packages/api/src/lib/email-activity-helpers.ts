import { db } from "@repo/database/server";

export interface CreateEmailActivityParams {
  organizationId: string;
  userId: string;
  sourceType: 'connected_account' | 'forwarded' | 'manual';
  sourceId?: string;
  messageId: string;
  threadId?: string;
  inReplyTo?: string;
  references?: string[];
  from: string;
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  bodyPlain?: string;
  attachments?: any[];
  headers?: any;
  direction: 'inbound' | 'outbound';
  timestamp: Date;
  isRead?: boolean;
  isImportant?: boolean;
  linkedContacts?: string[];
  linkedCompanies?: string[];
  linkedTasks?: string[];
  processingNotes?: string;
}

/**
 * Create an email activity record
 */
export async function createEmailActivity(params: CreateEmailActivityParams) {
  try {
    const emailActivity = await db.emailActivity.create({
      data: {
        organizationId: params.organizationId,
        userId: params.userId,
        sourceType: params.sourceType,
        sourceId: params.sourceId || null,
        messageId: params.messageId,
        threadId: params.threadId || null,
        inReplyTo: params.inReplyTo || null,
        references: params.references || [],
        from: params.from,
        to: params.to,
        cc: params.cc || [],
        bcc: params.bcc || [],
        subject: params.subject,
        body: params.body,
        bodyPlain: params.bodyPlain || null,
        attachments: params.attachments || null,
        headers: params.headers || null,
        direction: params.direction,
        timestamp: params.timestamp,
        isRead: params.isRead || false,
        isImportant: params.isImportant || false,
        processedAt: new Date(),
        processingNotes: params.processingNotes || null,
      },
    });

    if (params.linkedContacts?.length || params.linkedCompanies?.length || params.linkedTasks?.length) {
      await linkEmailActivityToRecords(emailActivity.id, {
        contacts: params.linkedContacts,
        companies: params.linkedCompanies,
        tasks: params.linkedTasks,
      });
    }

    return emailActivity;
  } catch (error) {
    console.error('Error creating email activity:', error);
    throw error;
  }
}

/**
 * Create email activity from a forwarded email
 */
export async function createEmailActivityFromForwardedEmail(
  forwardedEmail: any,
  organizationId: string,
  userId: string
) {
  // Extract thread ID from headers or references
  const threadId = extractThreadId(forwardedEmail.headers);
  
  // Extract references from headers
  const references = extractReferences(forwardedEmail.headers);
  
  return createEmailActivity({
    organizationId,
    userId,
    sourceType: 'forwarded',
    sourceId: forwardedEmail.id,
    messageId: forwardedEmail.messageId,
    threadId: threadId || undefined,
    inReplyTo: forwardedEmail.headers?.['in-reply-to'] || undefined,
    references,
    from: forwardedEmail.from,
    to: [forwardedEmail.to],
    subject: forwardedEmail.subject,
    body: forwardedEmail.body,
    attachments: forwardedEmail.attachments,
    headers: forwardedEmail.headers,
    direction: 'inbound', // Forwarded emails are typically inbound
    timestamp: new Date(forwardedEmail.createdAt),
    linkedContacts: forwardedEmail.linkedRecords?.contacts || [],
    linkedCompanies: forwardedEmail.linkedRecords?.companies || [],
    processingNotes: 'Created from forwarded email',
  });
}

/**
 * Create email activity from a connected account email
 */
export async function createEmailActivityFromConnectedAccount(
  emailData: any,
  organizationId: string,
  userId: string,
  accountId: string
) {
  return createEmailActivity({
    organizationId,
    userId,
    sourceType: 'connected_account',
    sourceId: accountId,
    messageId: emailData.id || emailData.messageId,
    threadId: emailData.threadId,
    inReplyTo: emailData.inReplyTo,
    references: emailData.references || [],
    from: emailData.from,
    to: emailData.to || [],
    cc: emailData.cc || [],
    bcc: emailData.bcc || [],
    subject: emailData.subject,
    body: emailData.body || emailData.bodyPreview,
    bodyPlain: emailData.bodyText,
    attachments: emailData.attachments,
    headers: emailData.headers,
    direction: emailData.direction || 'inbound',
    timestamp: new Date(emailData.receivedDateTime || emailData.sentDateTime),
    isRead: emailData.isRead,
    isImportant: emailData.importance === 'high',
    processingNotes: 'Created from connected account sync',
  });
}

/**
 * Link email activity to CRM records (contacts, companies, tasks)
 */
export async function linkEmailActivityToRecords(
  emailActivityId: string,
  linkedRecords: {
    contacts?: string[];
    companies?: string[];
    tasks?: string[];
  }
) {
  // Get the current email activity to access its headers
  const emailActivity = await db.emailActivity.findUnique({
    where: { id: emailActivityId },
    select: { headers: true }
  });

  // Create or update the headers object to include linked records
  const headers: Record<string, any> = emailActivity?.headers ? 
    (typeof emailActivity.headers === 'object' ? { ...emailActivity.headers as object } : {}) : 
    {};
  
  // Store linked records in the headers JSON field
  headers._linkedRecords = {
    ...(headers._linkedRecords as Record<string, any> || {}),
    contacts: linkedRecords.contacts || [],
    companies: linkedRecords.companies || [],
    tasks: linkedRecords.tasks || [],
  };

  // Update the email activity with the modified headers
  return db.emailActivity.update({
    where: { id: emailActivityId },
    data: {
      headers,
      processingNotes: `Linked to ${linkedRecords.contacts?.length || 0} contacts, ${linkedRecords.companies?.length || 0} companies, ${linkedRecords.tasks?.length || 0} tasks`
    },
  });
}

/**
 * Get email activities for a specific contact
 */
export async function getEmailActivitiesForContact(
  contactId: string,
  organizationId: string,
  options: { limit?: number; page?: number } = {}
) {
  const limit = options.limit || 20;
  const skip = ((options.page || 1) - 1) * limit;

  // First, get all email activities for the organization
  const allActivities = await db.emailActivity.findMany({
    where: {
      organizationId,
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
    },
    orderBy: {
      timestamp: 'desc',
    },
  });

  // Filter activities that have the contact ID in their _linkedRecords.contacts array
  const filteredActivities = allActivities.filter(activity => {
    if (!activity.headers || typeof activity.headers !== 'object') return false;
    const headers = activity.headers as Record<string, any>;
    const linkedRecords = headers._linkedRecords as { contacts?: string[] } | undefined;
    return linkedRecords?.contacts?.includes(contactId);
  });

  // Apply pagination manually
  const paginatedActivities = filteredActivities.slice(skip, skip + limit);

  return paginatedActivities;
}

/**
 * Get email activities for a specific company
 */
export async function getEmailActivitiesForCompany(
  companyId: string,
  organizationId: string,
  options: { limit?: number; page?: number } = {}
) {
  const limit = options.limit || 20;
  const skip = ((options.page || 1) - 1) * limit;

  // First, get all email activities for the organization
  const allActivities = await db.emailActivity.findMany({
    where: {
      organizationId,
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
    },
    orderBy: {
      timestamp: 'desc',
    },
  });

  // Filter activities that have the company ID in their _linkedRecords.companies array
  const filteredActivities = allActivities.filter(activity => {
    if (!activity.headers || typeof activity.headers !== 'object') return false;
    const headers = activity.headers as Record<string, any>;
    const linkedRecords = headers._linkedRecords as { companies?: string[] } | undefined;
    return linkedRecords?.companies?.includes(companyId);
  });

  // Apply pagination manually
  const paginatedActivities = filteredActivities.slice(skip, skip + limit);

  return paginatedActivities;
}

/**
 * Get email thread by thread ID
 */
export async function getEmailThread(
  threadId: string,
  organizationId: string
) {
  return db.emailActivity.findMany({
    where: {
      threadId,
      organizationId,
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
    },
    orderBy: {
      timestamp: 'asc',
    },
  });
}

/**
 * Helper function to extract thread ID from email headers
 */
function extractThreadId(headers: any): string | null {
  if (!headers) return null;
  
  // Try different header fields that might contain thread information
  const threadHeaders = [
    'thread-id',
    'x-thread-id',
    'message-id',
    'references',
  ];
  
  for (const header of threadHeaders) {
    const value = headers[header];
    if (value) {
      // For references, take the first one as thread ID
      if (header === 'references' && Array.isArray(value)) {
        return value[0] || null;
      }
      return typeof value === 'string' ? value : null;
    }
  }
  
  return null;
}

/**
 * Helper function to extract references from email headers
 */
function extractReferences(headers: any): string[] {
  if (!headers) return [];
  
  const references = headers.references || headers.References;
  if (!references) return [];
  
  if (typeof references === 'string') {
    // Split by whitespace and clean up
    return references.split(/\s+/).filter(ref => ref.trim().length > 0);
  }
  
  if (Array.isArray(references)) {
    return references.filter(ref => typeof ref === 'string');
  }
  
  return [];
}

/**
 * Automatically link email to contacts/companies based on email addresses
 */
export async function autoLinkEmailToRecords(
  emailActivity: any,
  organizationId: string
) {
  const allEmails = [
    emailActivity.from,
    ...emailActivity.to,
    ...emailActivity.cc,
    ...emailActivity.bcc,
  ].filter(Boolean);

  // Find matching contacts by searching in their email array
  // For now, we'll use a simpler approach that works with the current schema
  const contacts = await db.contact.findMany({
    where: {
      organizationId,
      isDeleted: false,
      // We'll need to implement email matching differently since path queries may not work
    },
    select: {
      id: true,
      companyId: true,
      email: true,
    },
  });

  const matchingContacts = contacts.filter(contact => {
    if (!contact.email || !Array.isArray(contact.email)) return false;
    return (contact.email as any[]).some((emailObj: any) => 
      allEmails.includes(emailObj.address || emailObj.value)
    );
  });

  const linkedContacts = matchingContacts.map(c => c.id);
  const linkedCompanies = Array.from(new Set(
    matchingContacts.map(c => c.companyId).filter((id): id is string => !!id)
  ));
  
  if (linkedContacts.length > 0 || linkedCompanies.length > 0) {
    await linkEmailActivityToRecords(emailActivity.id, {
      contacts: linkedContacts,
      companies: linkedCompanies,
    });
  }

  return {
    linkedContacts,
    linkedCompanies,
  };
} 