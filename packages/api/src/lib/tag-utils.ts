/**
 * Tag normalization utilities to ensure consistent, URL-friendly tag names
 */

/**
 * Normalizes a tag name to be URL-friendly and lowercase
 * - Converts to lowercase
 * - Replaces spaces and special characters with hyphens
 * - Removes consecutive hyphens
 * - Trims hyphens from start and end
 * - Limits length to 50 characters
 */
export function normalizeTagName(name: string): string {
  return name
    .toLowerCase()
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[^a-z0-9]+/g, '-')
    // Remove consecutive hyphens
    .replace(/-+/g, '-')
    // Remove leading and trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length
    .substring(0, 50);
}

/**
 * Validates a tag name
 */
export function validateTagName(name: string): { isValid: boolean; error?: string } {
  if (!name || !name.trim()) {
    return { isValid: false, error: "Tag name is required" };
  }

  const normalized = normalizeTagName(name);
  
  if (!normalized) {
    return { isValid: false, error: "Tag name must contain at least one letter or number" };
  }

  if (normalized.length < 2) {
    return { isValid: false, error: "Tag name must be at least 2 characters long" };
  }

  return { isValid: true };
} 