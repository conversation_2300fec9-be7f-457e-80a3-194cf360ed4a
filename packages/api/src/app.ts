import { auth } from "@repo/auth";
import { getBaseUrl } from "@repo/utils";
import { apiReference } from "@scalar/hono-api-reference";
import { Hono } from "hono";
import { openAPISpecs } from "hono-openapi";
import {} from "openapi-merge";
import { mergeOpenApiSchemas } from "./lib/openapi-schema";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";
import { activitiesRouter } from "./routes/activities/router";
import { adminRouter } from "./routes/admin";
import { aiRouter } from "./routes/ai/";
import { authRouter } from "./routes/auth";
import { columnPreferencesRouter } from "./routes/column-preferences/router";
import { contactRouter } from "./routes/contact";
import { contactsRouter } from "./routes/contacts";
import { creditsRouter } from "./routes/credits";
import { customFieldDefinitionsRouter } from "./routes/custom-field-definitions";
import { emailActivitiesRouter } from "./routes/email-activities";
import { favoriteFoldersRouter } from "./routes/favorite-folders";
import { favoritesRouter } from "./routes/favorites";
import { forwardingEmailConfigRouter } from "./routes/forwarding-email-config/router";
import { healthRouter } from "./routes/health";
import { newsletterRouter } from "./routes/newsletter";
import { notesRouter } from "./routes/notes";
import { notesPublicRouter } from "./routes/notes/public";
import { notificationsRouter } from "./routes/notifications";
import { objectViewsRouter } from "./routes/object-views";
import { objectsInfiniteRouter, objectsRouter } from "./routes/objects";
import { organizationsRouter } from "./routes/organizations";
import { paymentsRouter } from "./routes/payments";
import { pinsRouter } from "./routes/pins/router";
import { reaRouter } from "./routes/properties/rea";
import { searchRouter } from "./routes/search";
import { startRouter } from "./routes/start/router";
import { tasksRouter } from "./routes/tasks";
import { unitMixesRouter } from "./routes/unit-mixes/router";
import { saleHistoryRouter } from "./routes/sale-history/router";
import { uploadsRouter } from "./routes/uploads";
import { webhooksRouter } from "./routes/webhooks";
import { recordNavigationRouter } from "./routes/objects/record-navigation";
import { tagsRouter } from "./routes/tags/router";
import { sendEmailRouter } from "./routes/send-email";

export const app = new Hono().basePath("/api");

app.use(loggerMiddleware);
app.use(corsMiddleware);

const appRouter = app
	.route("/", authRouter)
	.route("/", webhooksRouter)
	.route("/", aiRouter)
	.route("/", activitiesRouter)
	.route("/", creditsRouter)
	.route("/", uploadsRouter)
	.route("/", paymentsRouter)
	.route("/", contactRouter)
	.route("/", contactsRouter)
	.route("/", newsletterRouter)
	.route("/", organizationsRouter)
	.route("/", adminRouter)
	.route("/", healthRouter)
	.route("/", tasksRouter)
	.route("/", unitMixesRouter)
	.route("/", saleHistoryRouter)
	.route("/", notificationsRouter)
	.route("/", favoritesRouter)
	.route("/", favoriteFoldersRouter)
	.route("/", notesRouter)
	.route("/", notesPublicRouter)
	.route("/", searchRouter)
	.route("/", objectViewsRouter)
	.route("/", pinsRouter)
	.route("/", customFieldDefinitionsRouter)
	.route("/", columnPreferencesRouter)
	.route("/", forwardingEmailConfigRouter)
	.route("/email-activities", emailActivitiesRouter)
	.route("/rea", reaRouter)
	.route("/start", startRouter)
	.route("/", objectsInfiniteRouter)
	.route("/", objectsRouter)
	.route("/navigation", recordNavigationRouter)
	.route("/tags", tagsRouter)
	.route("/", sendEmailRouter);

app.get(
	"/app-openapi",
	openAPISpecs(app, {
		documentation: {
			info: {
				title: "Relio API",
				version: "1.0.0",
			},
			servers: [
				{
					url: getBaseUrl(),
					description: "API server",
				},
			],
		},
	}),
);

app.get("/openapi", async (c) => {
	const authSchema = await auth.api.generateOpenAPISchema();
	const appSchema = await (
		app.request("/api/app-openapi") as Promise<Response>
	).then((res) => res.json());

	const mergedSchema = mergeOpenApiSchemas({
		appSchema,
		authSchema: authSchema as any,
	});

	return c.json(mergedSchema);
});

app.get(
	"/help",
	apiReference({
		theme: "saturn",
		spec: {
			url: "/api/openapi",
		},
	}),
);

export type AppRouter = typeof appRouter;
