import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../middleware/auth";
import { createEmailActivity } from "../lib/email-activity-helpers";
import { sendEmailViaConnectedAccount } from "../lib/email-providers";

const sendEmailSchema = z.object({
  organizationId: z.string(),
  fromAccountId: z.string(), // Connected account ID instead of email address
  to: z.string().email(),
  cc: z.array(z.string().email()).optional(),
  bcc: z.array(z.string().email()).optional(),
  subject: z.string().min(1),
  body: z.string().min(1),
});

export const sendEmailRouter = new Hono()
  .basePath("/send-email")
  .post(
    "/",
    authMiddleware,
    validator("json", sendEmailSchema),
    describeRoute({
      summary: "Send an email from the CRM",
      tags: ["Email"],
      description: "Send an outbound email using the organization's configured email address",
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { organizationId, fromAccountId, to, cc, bcc, subject, body } = c.req.valid("json");

        // Verify user has access to this organization
        const membership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId,
          },
        });

        if (!membership) {
          return c.json({ error: "Access denied" }, 403);
        }

        // Get and verify the connected account with OAuth tokens
        const connectedAccount = await db.account.findFirst({
          where: {
            id: fromAccountId,
            userId: user.id,
          },
          select: {
            id: true,
            providerId: true,
            accessToken: true,
            refreshToken: true,
            accessTokenExpiresAt: true,
            idToken: true,
          },
        });

        if (!connectedAccount) {
          return c.json({ 
            error: "Connected account not found or access denied",
            code: "NO_CONNECTED_ACCOUNT"
          }, 400);
        }

        // Parse email from the connected account's ID token for logging/activity creation
        let fromEmail = '';
        if (connectedAccount.idToken) {
          try {
            const payload = JSON.parse(Buffer.from(connectedAccount.idToken.split('.')[1], 'base64').toString());
            fromEmail = payload.email || '';
          } catch (error) {
            logger.error("Failed to parse email from ID token", { error });
          }
        }

        if (!fromEmail) {
          return c.json({ 
            error: "Unable to determine email address from connected account",
            code: "INVALID_ACCOUNT_EMAIL"
          }, 400);
        }

        // Prepare email content
        const htmlBody = body.replace(/\n/g, '<br>');

        try {
          // Send the email via connected account (Gmail/Outlook API)
          const emailResult = await sendEmailViaConnectedAccount(connectedAccount, {
            to,
            cc: cc || [],
            bcc: bcc || [],
            subject,
            body,
            htmlBody,
          });

          // Use the message ID returned from the email service
          const messageId = emailResult.messageId;

          // Create email activity record
          const emailActivity = await createEmailActivity({
            organizationId,
            userId: user.id,
            sourceType: 'connected_account',
            sourceId: fromAccountId,
            messageId,
            from: fromEmail,
            to: [to],
            cc: cc || [],
            bcc: bcc || [],
            subject,
            body: htmlBody,
            bodyPlain: body,
            direction: 'outbound',
            timestamp: new Date(),
            isRead: true, // Outbound emails are considered "read"
            processingNotes: 'Sent via CRM compose modal from connected account',
          });

          logger.info("Email sent successfully", {
            organizationId,
            userId: user.id,
            fromEmail,
            to,
            subject,
            messageId,
            connectedAccountId: fromAccountId,
          });

          return c.json({ 
            success: true,
            messageId,
            emailActivityId: emailActivity.id,
            message: "Email sent successfully" 
          });

        } catch (emailError) {
          logger.error("Failed to send email", {
            error: emailError,
            organizationId,
            userId: user.id,
            fromEmail,
            to,
            subject,
          });

          // Check if it's a token-related error that requires reconnection
          const errorMessage = emailError instanceof Error ? emailError.message : "Unknown error";
          const isTokenError = errorMessage.includes("access token") || errorMessage.includes("refresh token") || errorMessage.includes("reconnect");

          return c.json({ 
            error: isTokenError ? errorMessage : "Failed to send email. Please try again later.",
            code: isTokenError ? "TOKEN_EXPIRED" : "SEND_FAILED",
            requiresReconnection: isTokenError
          }, isTokenError ? 401 : 500);
        }

      } catch (error) {
        logger.error("Send email endpoint error:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  ); 