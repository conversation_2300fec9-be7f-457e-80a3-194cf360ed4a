import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import { z } from "zod";

export const unitMixesRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Validation schemas
const createUnitMixSchema = z.object({
	organizationId: z.string().min(1, "Organization ID is required"),
	propertyId: z.string().min(1, "Property ID is required"),
	name: z.string().min(1, "Name is required"),
	units: z.number().optional(),
	minSquareFootage: z.number().optional(),
	maxSquareFootage: z.number().optional(),
	minPrice: z.number().optional(),
	maxPrice: z.number().optional(),
	minRent: z.number().optional(),
	maxRent: z.number().optional(),
});

const updateUnitMixSchema = z.object({
	name: z.string().min(1, "Name is required").optional(),
	units: z.number().optional(),
	minSquareFootage: z.number().optional(),
	maxSquareFootage: z.number().optional(),
	minPrice: z.number().optional(),
	maxPrice: z.number().optional(),
	minRent: z.number().optional(),
	maxRent: z.number().optional(),
});

// Get all unit mixes for an organization
unitMixesRouter.get("/unit-mixes", authMiddleware, async (c) => {
	const organizationId = c.req.query("organizationId");
	
	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		const user = c.get("user");
		
		// Verify organization membership
		await verifyOrganizationMembership(organizationId, user.id);

		const unitMixes = await db.propertyUnitMix.findMany({
			where: { 
				organizationId,
			},
			include: {
				property: {
					select: {
						id: true,
						name: true,
						propertyType: true,
						market: true,
						subMarket: true,
						status: true,
					},
				},
			},
			orderBy: { createdAt: "desc" },
			cacheStrategy: {
				ttl: 300, // 5 minutes
				tags: [`unit_mixes_org_${organizationId}`, `org_${organizationId}`],
			},
		});

		// Transform the data to include property information
		const transformedUnitMixes = unitMixes.map((unitMix) => ({
			id: unitMix.id,
			propertyId: unitMix.propertyId,
			organizationId: unitMix.organizationId,
			name: unitMix.name,
			units: unitMix.units,
			minSquareFootage: unitMix.minSquareFootage,
			maxSquareFootage: unitMix.maxSquareFootage,
			minPrice: unitMix.minPrice,
			maxPrice: unitMix.maxPrice,
			minRent: unitMix.minRent,
			maxRent: unitMix.maxRent,
			createdAt: unitMix.createdAt,
			updatedAt: unitMix.updatedAt,
		}));

		return c.json(transformedUnitMixes, 200);
	} catch (error) {
		logger.error("[API] Failed to fetch unit mixes:", error);
		return c.json(
			{
				error: "Failed to fetch unit mixes",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Create a new unit mix
unitMixesRouter.post("/unit-mixes", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		
		// Validate the request body
		const validatedData = createUnitMixSchema.parse(body);
		
		// Verify organization membership
		await verifyOrganizationMembership(validatedData.organizationId, user.id);
		
		// Verify the property exists and belongs to the organization
		const property = await db.property.findFirst({
			where: {
				id: validatedData.propertyId,
				organizationId: validatedData.organizationId,
			},
		});
		
		if (!property) {
			return c.json({ error: "Property not found" }, 404);
		}
		
		// Create the unit mix
		const unitMix = await db.propertyUnitMix.create({
			data: {
				propertyId: validatedData.propertyId,
				organizationId: validatedData.organizationId,
				name: validatedData.name,
				units: validatedData.units,
				minSquareFootage: validatedData.minSquareFootage,
				maxSquareFootage: validatedData.maxSquareFootage,
				minPrice: validatedData.minPrice,
				maxPrice: validatedData.maxPrice,
				minRent: validatedData.minRent,
				maxRent: validatedData.maxRent,
			},
			include: {
				property: {
					select: {
						id: true,
						name: true,
						propertyType: true,
						market: true,
						subMarket: true,
						status: true,
					},
				},
			},
		});
		
		return c.json({
			id: unitMix.id,
			propertyId: unitMix.propertyId,
			organizationId: unitMix.organizationId,
			name: unitMix.name,
			units: unitMix.units,
			minSquareFootage: unitMix.minSquareFootage,
			maxSquareFootage: unitMix.maxSquareFootage,
			minPrice: unitMix.minPrice,
			maxPrice: unitMix.maxPrice,
			minRent: unitMix.minRent,
			maxRent: unitMix.maxRent,
			createdAt: unitMix.createdAt,
			updatedAt: unitMix.updatedAt,
			property: unitMix.property,
		}, 201);
	} catch (error) {
		logger.error("[API] Failed to create unit mix:", error);
		return c.json(
			{
				error: "Failed to create unit mix",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Update a unit mix
unitMixesRouter.put("/unit-mixes/:id", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const unitMixId = c.req.param("id");
		const body = await c.req.json();
		
		// Validate the request body
		const validatedData = updateUnitMixSchema.parse(body);
		
		// Find the unit mix and verify ownership
		const existingUnitMix = await db.propertyUnitMix.findFirst({
			where: { id: unitMixId },
			include: {
				property: {
					select: {
						organizationId: true,
					},
				},
			},
		});
		
		if (!existingUnitMix) {
			return c.json({ error: "Unit mix not found" }, 404);
		}
		
		// Verify organization membership
		await verifyOrganizationMembership(existingUnitMix.property.organizationId, user.id);
		
		// Update the unit mix
		const updatedUnitMix = await db.propertyUnitMix.update({
			where: { id: unitMixId },
			data: validatedData,
			include: {
				property: {
					select: {
						id: true,
						name: true,
						propertyType: true,
						market: true,
						subMarket: true,
						status: true,
					},
				},
			},
		});
		
		return c.json({
			id: updatedUnitMix.id,
			propertyId: updatedUnitMix.propertyId,
			organizationId: updatedUnitMix.organizationId,
			name: updatedUnitMix.name,
			units: updatedUnitMix.units,
			minSquareFootage: updatedUnitMix.minSquareFootage,
			maxSquareFootage: updatedUnitMix.maxSquareFootage,
			minPrice: updatedUnitMix.minPrice,
			maxPrice: updatedUnitMix.maxPrice,
			minRent: updatedUnitMix.minRent,
			maxRent: updatedUnitMix.maxRent,
			createdAt: updatedUnitMix.createdAt,
			updatedAt: updatedUnitMix.updatedAt,
			property: updatedUnitMix.property,
		}, 200);
	} catch (error) {
		logger.error("[API] Failed to update unit mix:", error);
		return c.json(
			{
				error: "Failed to update unit mix",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Delete a unit mix
unitMixesRouter.delete("/unit-mixes/:id", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const unitMixId = c.req.param("id");
		
		// Find the unit mix and verify ownership
		const existingUnitMix = await db.propertyUnitMix.findFirst({
			where: { id: unitMixId },
			include: {
				property: {
					select: {
						organizationId: true,
					},
				},
			},
		});
		
		if (!existingUnitMix) {
			return c.json({ error: "Unit mix not found" }, 404);
		}
		
		// Verify organization membership
		await verifyOrganizationMembership(existingUnitMix.property.organizationId, user.id);
		
		// Delete the unit mix
		await db.propertyUnitMix.delete({
			where: { id: unitMixId },
		});
		
		return c.json({ message: "Unit mix deleted successfully" }, 200);
	} catch (error) {
		logger.error("[API] Failed to delete unit mix:", error);
		return c.json(
			{
				error: "Failed to delete unit mix",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

export type UnitMixesRouter = typeof unitMixesRouter;
