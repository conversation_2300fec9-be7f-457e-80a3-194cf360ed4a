import { z } from "zod";

// Filter schema - same as your property example but generic
export const filterSchema = z.object({
	field: z.string(), // Generic field name
	logic: z.union([
		z.literal("Equals"),
		z.literal("Contains"),
		z.literal("Is empty"),
		z.literal("Is not empty"),
		z.literal("Greater than"),
		z.literal("Less than"),
		z.literal("Greater than or equal to"),
		z.literal("Less than or equal to"),
		z.literal("Starts with"),
		z.literal("Ends with"),
	]),
	text: z.optional(z.string()),
	number: z.optional(z.number()),
});

// Column definition schema
export const columnDefSchema = z.object({
	field: z.string(),
	headerName: z.string(),
	width: z.number(),
	type: z.string().optional(),
	visible: z.boolean().optional(),
});

// Card row field schema
export const cardRowFieldSchema = z.object({
	field: z.string(),
	headerName: z.string(),
	type: z.optional(z.string()),
});

// Kanban status schema
export const kanbanStatusSchema = z.object({
	label: z.string(),
	value: z.string(),
	color: z.string(),
	trackTime: z.optional(z.boolean()),
	showConfetti: z.optional(z.boolean()),
	targetTime: z.optional(z.number()),
	targetTimeUnit: z.optional(z.string()),
	icon: z.optional(z.any()), // Icon component, stored as any
	order: z.optional(z.number()),
});

// Kanban config schema
export const kanbanConfigSchema = z.object({
	customStatuses: z.optional(z.array(kanbanStatusSchema)),
	hiddenColumns: z.optional(z.array(z.string())),
});

// Object view schemas
export const objectViewCreateSchema = z.object({
	name: z.string(),
	objectType: z.enum([
		"contact",
		"company",
		"property",
		"custom_object",
	]),
	organizationId: z.string(),
	viewType: z.enum(["table", "kanban", "map"]).default("table"),
	statusAttribute: z.optional(z.string()),
	columnDefs: z.array(columnDefSchema),
	cardRowFields: z.optional(z.array(cardRowFieldSchema)),
	showAttributeLabels: z.optional(z.boolean()),
	kanbanConfig: z.optional(kanbanConfigSchema),
	filters: z.optional(z.array(filterSchema)),
	filterCondition: z.optional(z.enum(["and", "or"])),
	sortBy: z.optional(z.string()),
	sortDirection: z.optional(z.enum(["asc", "desc"])),
	isDefault: z.optional(z.boolean()),
	isPublic: z.optional(z.boolean()),
});

export const objectViewUpdateSchema = z.object({
	name: z.optional(z.string()),
	viewType: z.optional(z.enum(["table", "kanban", "map"])),
	statusAttribute: z.optional(z.string()),
	columnDefs: z.optional(z.array(columnDefSchema)),
	cardRowFields: z.optional(z.array(cardRowFieldSchema)),
	showAttributeLabels: z.optional(z.boolean()),
	kanbanConfig: z.optional(kanbanConfigSchema),
	mapConfig: z.optional(z.object({
		displayType: z.enum(["table", "grid"]),
		rowDensity: z.enum(["compact", "normal", "comfortable"]),
		showExportOptions: z.boolean(),
		allowColumnReorder: z.boolean(),
		showSearchBar: z.boolean(),
	})),
	filters: z.optional(z.array(filterSchema)),
	filterCondition: z.optional(z.enum(["and", "or"])),
	sortBy: z.optional(z.string()),
	sortDirection: z.optional(z.enum(["asc", "desc"])),
	isDefault: z.optional(z.boolean()),
	isPublic: z.optional(z.boolean()),
});

// Type exports
export type FilterSchema = z.infer<typeof filterSchema>;
export type ColumnDefSchema = z.infer<typeof columnDefSchema>;
export type CardRowFieldSchema = z.infer<typeof cardRowFieldSchema>;
export type KanbanStatusSchema = z.infer<typeof kanbanStatusSchema>;
export type KanbanConfigSchema = z.infer<typeof kanbanConfigSchema>;
export type ObjectViewCreate = z.infer<typeof objectViewCreateSchema>;
export type ObjectViewUpdate = z.infer<typeof objectViewUpdateSchema>;

// Default column definitions for different object types
export const defaultColumnDefs = {
	contact: [
		{ field: "name", headerName: "Name", width: 200, type: "text", visible: true }
	],
	company: [
		{ field: "name", headerName: "Company Name", width: 200, type: "text", visible: true }
	],
	property: [
		{ field: "name", headerName: "Property Name", width: 200, type: "text", visible: true }
	],
	custom_object: [
		{ field: "name", headerName: "Name", width: 200, type: "text", visible: true }
	],
};
