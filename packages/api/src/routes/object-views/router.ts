import { db } from "@repo/database/server";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import {
	defaultColumnDefs,
	type FilterSchema,
	filterSchema,
	type ObjectViewCreate,
	type ObjectViewUpdate,
	objectViewCreateSchema,
	objectViewUpdateSchema,
} from "./types";

export const objectViewsRouter = new Hono<{
	Variables: { user: any };
}>().basePath("/object-views");

// Get a specific view
objectViewsRouter.get("/views/:viewId", authMiddleware, async (c) => {
	try {
		const viewId = c.req.param("viewId");

		const view = await db.objectView.findUnique({
			where: { id: viewId },
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
				organization: {
					select: { id: true, name: true },
				},
			},
		});

		if (!view) {
			return c.json({ error: "View not found" }, 404);
		}

		return c.json(view);
	} catch (error) {
		console.error("Error fetching view:", error);
		return c.json({ error: "Failed to fetch view" }, 500);
	}
});

// Get all views for an organization and object type
objectViewsRouter.get("/views", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const organizationId =
			c.req.query("organizationId") ||
			c.get("session").activeOrganizationId;
		const objectType = c.req.query("objectType");

		if (!organizationId || !objectType) {
			return c.json(
				{ error: "Organization ID and object type are required" },
				400,
			);
		}

		// Verify user has access to the organization
		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: organizationId,
			},
		});

		if (!userMembership) {
			return c.json({ error: "Access denied to this organization" }, 403);
		}

		const views = await db.objectView.findMany({
			where: {
				organizationId,
				objectType,
				OR: [
					{ isPublic: true }, // Public views visible to all workspace members
					{ createdBy: user.id }, // Private views visible only to creator
				],
			},
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
			},
			orderBy: [
				{ isDefault: "desc" }, // Default views first
				{ createdAt: "asc" },
			],
		});

		return c.json(views);
	} catch (error) {
		console.error("Error fetching views:", error);
		return c.json({ error: "Failed to fetch views" }, 500);
	}
});

// Create a new view
objectViewsRouter.post("/views", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();

		const validatedData = objectViewCreateSchema.parse({
			...body,
			createdBy: user.id,
		});

		const view = await db.objectView.create({
			data: {
				name: validatedData.name,
				objectType: validatedData.objectType,
				organizationId: validatedData.organizationId,
				viewType: validatedData.viewType || "table",
				statusAttribute: validatedData.statusAttribute,
				columnDefs: validatedData.columnDefs,
				cardRowFields: validatedData.cardRowFields,
				filters: validatedData.filters || [],
				filterCondition: validatedData.filterCondition || "and",
				sortBy: validatedData.sortBy,
				sortDirection: validatedData.sortDirection || "asc",
				createdBy: user.id,
				isDefault: validatedData.isDefault || false,
				isPublic: validatedData.isPublic || false,
			},
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
			},
		});

		return c.json(view, 201);
	} catch (error) {
		console.error("Error creating view:", error);
		return c.json({ error: "Failed to create view" }, 500);
	}
});

// Duplicate a view
objectViewsRouter.post(
	"/views/:viewId/duplicate",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");
			const user = c.get("user");
			const body = await c.req.json();
			const { currentFilters } = body || {};

			// Fetch the original view
			const originalView = await db.objectView.findUnique({
				where: { id: viewId },
				include: {
					creator: {
						select: { id: true, name: true, email: true },
					},
				},
			});

			if (!originalView) {
				return c.json({ error: "View not found" }, 404);
			}

			// Verify user has access to the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: originalView.organizationId,
				},
			});

			if (!userMembership) {
				return c.json(
					{ error: "Access denied to this organization" },
					403,
				);
			}

			// Find existing views with similar names to determine the next number
			const baseViewName = originalView.name;
			const existingViews = await db.objectView.findMany({
				where: {
					organizationId: originalView.organizationId,
					objectType: originalView.objectType,
					name: {
						startsWith: baseViewName,
					},
				},
				select: {
					name: true,
				},
			});

			// Find the next available number
			let copyNumber = 2;
			let duplicateName = `${baseViewName} (${copyNumber})`;

			while (existingViews.some((view) => view.name === duplicateName)) {
				copyNumber++;
				duplicateName = `${baseViewName} (${copyNumber})`;
			}

			// Convert current table filters to view filters format
			let filtersToSave = originalView.filters;
			if (currentFilters && Array.isArray(currentFilters)) {
				filtersToSave = currentFilters
					.filter(
						(filter) =>
							filter.value !== undefined && filter.value !== null,
					)
					.map((filter) => {
						// Convert table filter format to database filter format
						if (Array.isArray(filter.value)) {
							// Handle array filters (checkbox, slider, etc.)
							if (
								filter.value.length === 2 &&
								typeof filter.value[0] === "number"
							) {
								// Slider/range filter
								return {
									field: filter.id,
									logic: "between",
									number: filter.value,
								};
							}
							// Checkbox/multi-select filter
							return {
								field: filter.id,
								logic: "in",
								text: filter.value.join(","),
							};
						}
						if (typeof filter.value === "string") {
							// Text filter
							return {
								field: filter.id,
								logic: "contains",
								text: filter.value,
							};
						}
						if (typeof filter.value === "number") {
							// Number filter
							return {
								field: filter.id,
								logic: "equals",
								number: filter.value,
							};
						}
						return null;
					})
					.filter(Boolean);
			}

			// Create the duplicate view
			const duplicatedView = await db.objectView.create({
				data: {
					name: duplicateName,
					objectType: originalView.objectType,
					organizationId: originalView.organizationId,
					viewType: originalView.viewType,
					statusAttribute: originalView.statusAttribute,
					columnDefs: originalView.columnDefs,
					filters: filtersToSave,
					filterCondition: originalView.filterCondition,
					sortBy: originalView.sortBy,
					sortDirection: originalView.sortDirection,
					createdBy: user.id,
					isDefault: false,
					isPublic: originalView.isPublic,
				},
				include: {
					creator: {
						select: { id: true, name: true, email: true },
					},
				},
			});

			return c.json(duplicatedView, 201);
		} catch (error) {
			console.error("Error duplicating view:", error);
			return c.json({ error: "Failed to duplicate view" }, 500);
		}
	},
);

// Update a view (general update endpoint)
objectViewsRouter.patch("/views/:viewId", authMiddleware, async (c) => {
	try {
		const viewId = c.req.param("viewId");
		const user = c.get("user");
		const updateData = await c.req.json();

		const view = await db.objectView.findUnique({
			where: { id: viewId },
		});

		if (!view) {
			return c.json({ error: "View not found" }, 404);
		}

		// Only creator can update the view (or you could add organization admin check)
		if (view.createdBy !== user.id) {
			return c.json({ error: "Unauthorized to update this view" }, 403);
		}

		// Validate update data using update schema
		const validatedData = objectViewUpdateSchema.parse(updateData);

		const updatedView = await db.objectView.update({
			where: { id: viewId },
			data: {
				...validatedData,
				updatedAt: new Date(),
			},
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
			},
		});

		return c.json(updatedView);
	} catch (error) {
		console.error("Error updating view:", error);
		return c.json({ error: "Failed to update view" }, 500);
	}
});

// Update column width
objectViewsRouter.patch(
	"/views/:viewId/column-width",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");
			const { field, width } = await c.req.json();

			const view = await db.objectView.findUnique({
				where: { id: viewId },
			});

			if (!view) {
				return c.json({ error: "View not found" }, 404);
			}

			const columnDefs = view.columnDefs as any[];
			const updatedColumnDefs = columnDefs.map((col) =>
				col.field === field ? { ...col, width } : col,
			);

			const updatedView = await db.objectView.update({
				where: { id: viewId },
				data: { columnDefs: updatedColumnDefs },
			});

			return c.json(updatedView);
		} catch (error) {
			console.error("Error updating column width:", error);
			return c.json({ error: "Failed to update column width" }, 500);
		}
	},
);

// Update column order
objectViewsRouter.patch(
	"/views/:viewId/column-order",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");
			const { newOrder } = await c.req.json();

			const view = await db.objectView.findUnique({
				where: { id: viewId },
			});

			if (!view) {
				return c.json({ error: "View not found" }, 404);
			}

			const columnDefs = view.columnDefs as any[];
			const columnMap = new Map(
				columnDefs.map((col) => [col.field, col]),
			);

			const reorderedColumns = newOrder
				.map((field: string) => columnMap.get(field))
				.filter((col: any) => col !== undefined);

			const updatedView = await db.objectView.update({
				where: { id: viewId },
				data: { columnDefs: reorderedColumns },
			});

			return c.json(updatedView);
		} catch (error) {
			console.error("Error updating column order:", error);
			return c.json({ error: "Failed to update column order" }, 500);
		}
	},
);

// Toggle column visibility
objectViewsRouter.patch(
	"/views/:viewId/toggle-column",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");
			const { field, headerName } = await c.req.json();

			const view = await db.objectView.findUnique({
				where: { id: viewId },
			});

			if (!view) {
				return c.json({ error: "View not found" }, 404);
			}

			const columnDefs = view.columnDefs as any[];
			const existingColumnIndex = columnDefs.findIndex(
				(col) => col.field === field,
			);

			let updatedColumnDefs;
			if (existingColumnIndex >= 0) {
				// Remove column
				updatedColumnDefs = columnDefs.filter(
					(col) => col.field !== field,
				);
			} else {
				// Add column
				updatedColumnDefs = [
					...columnDefs,
					{ field, headerName, width: 150 },
				];
			}

			const updatedView = await db.objectView.update({
				where: { id: viewId },
				data: { columnDefs: updatedColumnDefs },
			});

			return c.json(updatedView);
		} catch (error) {
			console.error("Error toggling column:", error);
			return c.json({ error: "Failed to toggle column" }, 500);
		}
	},
);

// Add filter
objectViewsRouter.post("/views/:viewId/filters", authMiddleware, async (c) => {
	try {
		const viewId = c.req.param("viewId");
		const filterData = await c.req.json();

		const validatedFilter = filterSchema.parse(filterData);

		const view = await db.objectView.findUnique({
			where: { id: viewId },
		});

		if (!view) {
			return c.json({ error: "View not found" }, 404);
		}

		const currentFilters = (view.filters as FilterSchema[]) || [];
		const existingFilterIndex = currentFilters.findIndex(
			(f) => f.field === validatedFilter.field,
		);

		if (existingFilterIndex >= 0) {
			return c.json(
				{ error: "Filter for this field already exists" },
				400,
			);
		}

		const updatedFilters = [...currentFilters, validatedFilter];

		const updatedView = await db.objectView.update({
			where: { id: viewId },
			data: { filters: updatedFilters },
		});

		return c.json(updatedView);
	} catch (error) {
		console.error("Error adding filter:", error);
		return c.json({ error: "Failed to add filter" }, 500);
	}
});

// Remove filter
objectViewsRouter.delete(
	"/views/:viewId/filters/:field",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");
			const field = c.req.param("field");

			const view = await db.objectView.findUnique({
				where: { id: viewId },
			});

			if (!view) {
				return c.json({ error: "View not found" }, 404);
			}

			const currentFilters = (view.filters as FilterSchema[]) || [];
			const updatedFilters = currentFilters.filter(
				(f) => f.field !== field,
			);

			const updatedView = await db.objectView.update({
				where: { id: viewId },
				data: { filters: updatedFilters },
			});

			return c.json(updatedView);
		} catch (error) {
			console.error("Error removing filter:", error);
			return c.json({ error: "Failed to remove filter" }, 500);
		}
	},
);

// Update filter
objectViewsRouter.patch(
	"/views/:viewId/filters/:field",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");
			const field = c.req.param("field");
			const updateData = await c.req.json();

			const view = await db.objectView.findUnique({
				where: { id: viewId },
			});

			if (!view) {
				return c.json({ error: "View not found" }, 404);
			}

			const currentFilters = (view.filters as FilterSchema[]) || [];
			const updatedFilters = currentFilters.map((f) =>
				f.field === field ? { ...f, ...updateData } : f,
			);

			const updatedView = await db.objectView.update({
				where: { id: viewId },
				data: { filters: updatedFilters },
			});

			return c.json(updatedView);
		} catch (error) {
			console.error("Error updating filter:", error);
			return c.json({ error: "Failed to update filter" }, 500);
		}
	},
);

// Toggle filter condition (AND/OR)
objectViewsRouter.patch(
	"/views/:viewId/filter-condition",
	authMiddleware,
	async (c) => {
		try {
			const viewId = c.req.param("viewId");

			const view = await db.objectView.findUnique({
				where: { id: viewId },
			});

			if (!view) {
				return c.json({ error: "View not found" }, 404);
			}

			const newCondition = view.filterCondition === "or" ? "and" : "or";

			const updatedView = await db.objectView.update({
				where: { id: viewId },
				data: { filterCondition: newCondition },
			});

			return c.json(updatedView);
		} catch (error) {
			console.error("Error toggling filter condition:", error);
			return c.json({ error: "Failed to toggle filter condition" }, 500);
		}
	},
);

// Delete view
objectViewsRouter.delete("/views/:viewId", authMiddleware, async (c) => {
	try {
		const viewId = c.req.param("viewId");
		const user = c.get("user");

		const view = await db.objectView.findUnique({
			where: { id: viewId },
		});

		if (!view) {
			return c.json({ error: "View not found" }, 404);
		}

		// Only creator can delete the view (or you could add organization admin check)
		if (view.createdBy !== user.id) {
			return c.json({ error: "Unauthorized to delete this view" }, 403);
		}

		await db.objectView.delete({
			where: { id: viewId },
		});

		return c.json({ message: "View deleted successfully" });
	} catch (error) {
		console.error("Error deleting view:", error);
		return c.json({ error: "Failed to delete view" }, 500);
	}
});

// Get user's default view for an object type
objectViewsRouter.get(
	"/user-preferences/default",
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const organizationId =
				c.req.query("organizationId") ||
				c.get("session").activeOrganizationId;
			const objectType = c.req.query("objectType");

			if (!organizationId || !objectType) {
				return c.json(
					{ error: "Organization ID and object type are required" },
					400,
				);
			}

			// Verify user has access to the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: organizationId,
				},
			});

			if (!userMembership) {
				return c.json(
					{ error: "Access denied to this organization" },
					403,
				);
			}

			const preference = await db.userViewPreference.findUnique({
				where: {
					userId_organizationId_objectType: {
						userId: user.id,
						organizationId,
						objectType,
					},
				},
				include: {
					view: {
						include: {
							creator: {
								select: { id: true, name: true, email: true },
							},
						},
					},
				},
			});

			if (!preference) {
				return c.json({ defaultView: null });
			}

			return c.json({ defaultView: preference.view });
		} catch (error) {
			console.error("Error fetching user default view:", error);
			return c.json({ error: "Failed to fetch user default view" }, 500);
		}
	},
);

// Set user's default view for an object type
objectViewsRouter.post(
	"/user-preferences/default",
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const { organizationId, objectType, viewId } = await c.req.json();
			const finalOrganizationId =
				organizationId || c.get("session").activeOrganizationId;

			if (!finalOrganizationId || !objectType || !viewId) {
				return c.json(
					{
						error: "Organization ID, object type, and view ID are required",
					},
					400,
				);
			}

			// Verify user has access to the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: finalOrganizationId,
				},
			});

			if (!userMembership) {
				return c.json(
					{ error: "Access denied to this organization" },
					403,
				);
			}

			// Check if the view exists and belongs to the organization
			const view = await db.objectView.findFirst({
				where: {
					id: viewId,
					organizationId: finalOrganizationId,
					objectType,
				},
			});

			if (!view) {
				return c.json(
					{ error: "View not found or access denied" },
					404,
				);
			}

			// Upsert the user view preference
			const preference = await db.userViewPreference.upsert({
				where: {
					userId_organizationId_objectType: {
						userId: user.id,
						organizationId: finalOrganizationId,
						objectType,
					},
				},
				update: {
					viewId,
					updatedAt: new Date(),
				},
				create: {
					userId: user.id,
					organizationId: finalOrganizationId,
					objectType,
					viewId,
				},
				include: {
					view: {
						include: {
							creator: {
								select: { id: true, name: true, email: true },
							},
						},
					},
				},
			});

			return c.json({ preference });
		} catch (error) {
			console.error("Error setting user default view:", error);
			return c.json({ error: "Failed to set user default view" }, 500);
		}
	},
);

// Remove user's default view preference for an object type
objectViewsRouter.delete(
	"/user-preferences/default",
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const organizationId =
				c.req.query("organizationId") ||
				c.get("session").activeOrganizationId;
			const objectType = c.req.query("objectType");

			if (!organizationId || !objectType) {
				return c.json(
					{ error: "Organization ID and object type are required" },
					400,
				);
			}

			// Verify user has access to the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: organizationId,
				},
			});

			if (!userMembership) {
				return c.json(
					{ error: "Access denied to this organization" },
					403,
				);
			}

			await db.userViewPreference.deleteMany({
				where: {
					userId: user.id,
					organizationId,
					objectType,
				},
			});

			return c.json({ message: "User default view preference removed" });
		} catch (error) {
			console.error("Error removing user default view:", error);
			return c.json({ error: "Failed to remove user default view" }, 500);
		}
	},
);

// Create default view for object type
objectViewsRouter.post("/views/create-default", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const { organizationId, objectType, name } = await c.req.json();

		if (!defaultColumnDefs[objectType as keyof typeof defaultColumnDefs]) {
			return c.json({ error: "Invalid object type" }, 400);
		}

		const view = await db.objectView.create({
			data: {
				name: name || `Default ${objectType} View`,
				objectType,
				organizationId,
				columnDefs:
					defaultColumnDefs[
						objectType as keyof typeof defaultColumnDefs
					],
				filters: [],
				filterCondition: "and",
				createdBy: user.id,
				isDefault: true,
				isPublic: true,
			},
			include: {
				creator: {
					select: { id: true, name: true, email: true },
				},
			},
		});

		return c.json(view, 201);
	} catch (error) {
		console.error("Error creating default view:", error);
		return c.json({ error: "Failed to create default view" }, 500);
	}
});

objectViewsRouter.post("/migrate-primary-columns", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		
		const getPrimaryColumnDef = (objectType: string) => {
			const headerNames = {
				contact: "Name",
				company: "Company Name", 
				property: "Property Name",
				custom_object: "Name"
			};
			
			return {
				field: "name",
				headerName: headerNames[objectType as keyof typeof headerNames] || "Name",
				width: 200,
				type: "text",
				visible: true
			};
		};

		const allViews = await db.objectView.findMany({
			select: {
				id: true,
				name: true,
				objectType: true,
				columnDefs: true,
			}
		});

		const viewsToUpdate = allViews.filter(view => {
			const columnDefs = (view.columnDefs as any[]) || [];
			const hasPrimaryColumn = columnDefs.some((col: any) => col?.field === "name");
			return !hasPrimaryColumn || columnDefs.length === 0;
		});

		let updatedCount = 0;
		
		for (const view of viewsToUpdate) {
			try {
				const columnDefs = (view.columnDefs as any[]) || [];
				const hasPrimaryColumn = columnDefs.some((col: any) => col.field === "name");
				
				if (!hasPrimaryColumn) {
					const primaryColumn = getPrimaryColumnDef(view.objectType);
					const updatedColumnDefs = [primaryColumn, ...columnDefs];
					
					await db.objectView.update({
						where: { id: view.id },
						data: { columnDefs: updatedColumnDefs }
					});
					
					updatedCount++;
				}
			} catch (error) {
				console.error(`[Migration] Error updating view ${view.id}:`, error);
			}
		}
		
		return c.json({ 
			message: "Migration completed successfully",
			viewsFound: viewsToUpdate.length,
			viewsUpdated: updatedCount
		});
	} catch (error) {
		console.error("[Migration] Error during primary column migration:", error);
		return c.json({ error: "Migration failed" }, 500);
	}
});

export type ObjectViewsRouter = typeof objectViewsRouter;
