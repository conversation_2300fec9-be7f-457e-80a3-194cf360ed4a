import { db } from "@repo/database/server";
import { <PERSON>o } from "hono";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

// Validation schemas
const customFieldDefinitionCreateSchema = z.object({
	name: z.string().min(1),
	label: z.string().min(1),
	type: z.string(),
	objectType: z.string(),
	organizationId: z.string(),
	options: z
		.object({
			choices: z
				.array(
					z.object({
						label: z.string(),
						value: z.string(),
						color: z.string().optional(),
						trackTime: z.boolean().optional(),
						showConfetti: z.boolean().optional(),
						targetTime: z.number().optional(),
						targetTimeUnit: z.string().optional(),
					}),
				)
				.optional(),
		})
		.optional(),
	isRequired: z.boolean().optional(),
	position: z.number().optional(),
});

const customFieldDefinitionUpdateSchema = z.object({
	name: z.string().min(1).optional(),
	label: z.string().min(1).optional(),
	type: z.string().optional(),
	options: z
		.object({
			choices: z
				.array(
					z.object({
						label: z.string(),
						value: z.string(),
						color: z.string().optional(),
						trackTime: z.boolean().optional(),
						showConfetti: z.boolean().optional(),
						targetTime: z.number().optional(),
						targetTimeUnit: z.string().optional(),
					}),
				)
				.optional(),
		})
		.optional(),
	isRequired: z.boolean().optional(),
	position: z.number().optional(),
});

export const customFieldDefinitionsRouter = new Hono<{
	Variables: { user: any };
}>().basePath("/custom-field-definitions");

// Get custom field definitions for an object type
customFieldDefinitionsRouter.get("/:objectType", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const objectType = c.req.param("objectType");
		const organizationId =
			c.req.query("organizationId") ||
			c.get("session")?.activeOrganizationId;

		if (!organizationId) {
			return c.json({ error: "Organization ID is required" }, 400);
		}

		// Verify user has access to the organization
		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: organizationId,
			},
		});

		if (!userMembership) {
			return c.json({ error: "Access denied to this organization" }, 403);
		}

		const definitions = await db.customFieldDefinition.findMany({
			where: {
				organizationId,
				objectType,
				isActive: true,
			},
			orderBy: {
				position: "asc",
			},
		});

		return c.json(definitions);
	} catch (error) {
		console.error("Error fetching custom field definitions:", error);
		return c.json(
			{ error: "Failed to fetch custom field definitions" },
			500,
		);
	}
});

// Get a specific custom field definition
customFieldDefinitionsRouter.get(
	"/definition/:id",
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const id = c.req.param("id");

			const definition = await db.customFieldDefinition.findUnique({
				where: { id },
			});

			if (!definition) {
				return c.json(
					{ error: "Custom field definition not found" },
					404,
				);
			}

			// Verify user has access to the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: definition.organizationId,
				},
			});

			if (!userMembership) {
				return c.json(
					{ error: "Access denied to this organization" },
					403,
				);
			}

			return c.json(definition);
		} catch (error) {
			console.error("Error fetching custom field definition:", error);
			return c.json(
				{ error: "Failed to fetch custom field definition" },
				500,
			);
		}
	},
);

// Create a new custom field definition
customFieldDefinitionsRouter.post("/", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();

		const validatedData = customFieldDefinitionCreateSchema.parse(body);

		// Verify user has access to the organization
		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: validatedData.organizationId,
			},
		});

		if (!userMembership) {
			return c.json({ error: "Access denied to this organization" }, 403);
		}

		// Check if field definition already exists
		const existingDefinition = await db.customFieldDefinition.findFirst({
			where: {
				organizationId: validatedData.organizationId,
				objectType: validatedData.objectType,
				name: validatedData.name,
				isActive: true,
			},
		});

		if (existingDefinition) {
			return c.json(
				{
					error: "Custom field definition with this name already exists",
				},
				400,
			);
		}

		// Create new definition
		const definition = await db.customFieldDefinition.create({
			data: {
				name: validatedData.name,
				label: validatedData.label,
				type: validatedData.type,
				objectType: validatedData.objectType,
				organizationId: validatedData.organizationId,
				options: validatedData.options as any,
				isRequired: validatedData.isRequired ?? false,
				position: validatedData.position ?? 0,
				isSystem: false,
				isActive: true,
			},
		});

		return c.json(definition, 201);
	} catch (error) {
		console.error("Error creating custom field definition:", error);
		return c.json(
			{ error: "Failed to create custom field definition" },
			500,
		);
	}
});

// Update a custom field definition
customFieldDefinitionsRouter.patch("/:id", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const id = c.req.param("id");
		const updateData = await c.req.json();

		const validatedData =
			customFieldDefinitionUpdateSchema.parse(updateData);

		const definition = await db.customFieldDefinition.findUnique({
			where: { id },
		});

		if (!definition) {
			return c.json({ error: "Custom field definition not found" }, 404);
		}

		// Check if it's a system field
		if (definition.isSystem) {
			return c.json({ error: "Cannot update system fields" }, 400);
		}

		// Verify user has access to the organization
		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: definition.organizationId,
			},
		});

		if (!userMembership) {
			return c.json({ error: "Access denied to this organization" }, 403);
		}

		// If updating name, check for conflicts
		if (validatedData.name && validatedData.name !== definition.name) {
			const existingDefinition = await db.customFieldDefinition.findFirst(
				{
					where: {
						organizationId: definition.organizationId,
						objectType: definition.objectType,
						name: validatedData.name,
						isActive: true,
						id: { not: id },
					},
				},
			);

			if (existingDefinition) {
				return c.json(
					{
						error: "Custom field definition with this name already exists",
					},
					400,
				);
			}
		}

		const updatedDefinition = await db.customFieldDefinition.update({
			where: { id },
			data: {
				...validatedData,
				updatedAt: new Date(),
			},
		});

		return c.json(updatedDefinition);
	} catch (error) {
		console.error("Error updating custom field definition:", error);
		return c.json(
			{ error: "Failed to update custom field definition" },
			500,
		);
	}
});

// Create or update a custom field definition (upsert)
customFieldDefinitionsRouter.post("/upsert", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();

		const validatedData = customFieldDefinitionCreateSchema.parse(body);

		// Verify user has access to the organization
		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: validatedData.organizationId,
			},
		});

		if (!userMembership) {
			return c.json({ error: "Access denied to this organization" }, 403);
		}

		// Check if field definition already exists
		const existingDefinition = await db.customFieldDefinition.findFirst({
			where: {
				organizationId: validatedData.organizationId,
				objectType: validatedData.objectType,
				name: validatedData.name,
				isActive: true,
			},
		});

		let result;

		if (existingDefinition) {
			// Update existing definition
			result = await db.customFieldDefinition.update({
				where: { id: existingDefinition.id },
				data: {
					label: validatedData.label,
					type: validatedData.type,
					options: validatedData.options as any,
					isRequired: validatedData.isRequired ?? false,
					position: validatedData.position ?? 0,
					updatedAt: new Date(),
				},
			});
		} else {
			// Create new definition
			result = await db.customFieldDefinition.create({
				data: {
					name: validatedData.name,
					label: validatedData.label,
					type: validatedData.type,
					objectType: validatedData.objectType,
					organizationId: validatedData.organizationId,
					options: validatedData.options as any,
					isRequired: validatedData.isRequired ?? false,
					position: validatedData.position ?? 0,
					isSystem: false,
					isActive: true,
				},
			});
		}

		return c.json(result, existingDefinition ? 200 : 201);
	} catch (error) {
		console.error("Error upserting custom field definition:", error);
		return c.json(
			{ error: "Failed to upsert custom field definition" },
			500,
		);
	}
});

// Delete a custom field definition
customFieldDefinitionsRouter.delete("/:id", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const id = c.req.param("id");

		const definition = await db.customFieldDefinition.findUnique({
			where: { id },
		});

		if (!definition) {
			return c.json({ error: "Custom field definition not found" }, 404);
		}

		// Check if it's a system field
		if (definition.isSystem) {
			return c.json({ error: "Cannot delete system fields" }, 400);
		}

		// Verify user has access to the organization
		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: definition.organizationId,
			},
		});

		if (!userMembership) {
			return c.json({ error: "Access denied to this organization" }, 403);
		}

		// Soft delete by setting isActive to false
		await db.customFieldDefinition.update({
			where: { id },
			data: {
				isActive: false,
				updatedAt: new Date(),
			},
		});

		return c.json({
			message: "Custom field definition deleted successfully",
		});
	} catch (error) {
		console.error("Error deleting custom field definition:", error);
		return c.json(
			{ error: "Failed to delete custom field definition" },
			500,
		);
	}
});

export type CustomFieldDefinitionsRouter = typeof customFieldDefinitionsRouter;
