import { anthropic } from "@ai-sdk/anthropic";
import { createOpenA<PERSON>, openai } from "@ai-sdk/openai";
import { createCRMTools, getSystemPrompt } from "@repo/ai";
import {
	addPurchasedCredits,
	calculateModelCost,
	checkUserCredits,
	deductUserCredits,
	getUserCreditsStatus,
	trackAiUsage,
} from "@repo/ai/lib/credits";
import { db } from "@repo/database/server";
import { createDataStream, smoothStream, streamText } from "ai";
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { validator } from "hono/validator";
import { describeRoute } from "hono-openapi";
import { resolver } from "hono-openapi/zod";
import { ObjectId } from "mongodb";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

// Simple organization verification
async function verifyOrganizationMembership(
	organizationId: string,
	userId: string,
) {
	const membership = await db.member.findFirst({
		where: {
			organizationId,
			userId,
		},
	});

	if (!membership) {
		throw new HTTPException(403, {
			message: "Access denied: User not member of organization",
		});
	}

	return membership;
}

// Model interface
export interface Model {
	id: string;
	name: string;
	model: string;
	provider: "azure" | "openrouter";
	cost: number;
	capabilities?: string[];
	isPremium?: boolean;
	icon?: string;
}

// Available models configuration with fallback priority
const AVAILABLE_MODELS: Model[] = [
	{
		id: "claude-3-5-sonnet-20241022",
		name: "Claude 3.5 Sonnet",
		model: "anthropic/claude-3.5-sonnet-20241022",
		provider: "openrouter",
		cost: 2,
		capabilities: ["tools"],
		isPremium: false,
		icon: "anthropic",
	},
	{
		id: "gemini-2-5-flash-thinking",
		name: "Gemini 2.5 Flash Thinking",
		model: "google/gemini-2.5-flash-thinking",
		provider: "openrouter",
		cost: 1,
		capabilities: ["tools"],
		isPremium: false,
		icon: "google",
	},
	{
		id: "gpt-4o-mini",
		name: "GPT-4o Mini",
		model: "gpt-4o-mini",
		provider: "azure",
		cost: 3,
		capabilities: ["tools"],
		isPremium: false,
		icon: "openai",
	},
	{
		id: "gpt-4o",
		name: "GPT-4o",
		model: "gpt-4o",
		provider: "azure",
		cost: 5,
		capabilities: ["tools", "vision"],
		isPremium: true,
		icon: "openai",
	},
];

// Helper function to get database model ID from static model ID
async function getDbModelId(staticModelId: string): Promise<string | null> {
	try {
		// Look up the model in the database by matching the static ID or model string
		const dbModel = await db.model.findFirst({
			where: {
				OR: [
					{ name: staticModelId },
					{ model: staticModelId },
					// Try to match with AVAILABLE_MODELS mapping
					...AVAILABLE_MODELS.map((m) => ({
						AND: [
							{ name: m.name },
							{ model: m.model },
							{ provider: m.provider },
						],
					})).filter((m) =>
						AVAILABLE_MODELS.find(
							(am) =>
								am.id === staticModelId &&
								am.name === m.AND[0].name,
						),
					),
				],
			},
		});

		if (dbModel) {
			return dbModel.id;
		}

		// Fallback: try to find by static model ID in available models, then match by name
		const staticModel = AVAILABLE_MODELS.find(
			(m) => m.id === staticModelId,
		);
		if (staticModel) {
			const fallbackDbModel = await db.model.findFirst({
				where: {
					name: staticModel.name,
					provider: staticModel.provider,
				},
			});
			return fallbackDbModel?.id || null;
		}

		console.warn(
			`[AI Router] Could not find database model for static ID: ${staticModelId}`,
		);
		return null;
	} catch (error) {
		console.error("[AI Router] Error looking up database model ID:", error);
		return null;
	}
}

function getSelectedModel(modelId: string) {
	const modelConfig = AVAILABLE_MODELS.find((m) => m.id === modelId);
	if (!modelConfig) {
		// Default to first available model (Claude)
		return getModelProvider(AVAILABLE_MODELS[0]);
	}

	return getModelProvider(modelConfig);
}

function getModelProvider(modelConfig: Model) {
	let model;
	switch (modelConfig.provider) {
		case "azure": {
			// Create OpenAI provider with YOUR personal API key (not organization)
			const personalOpenAI = createOpenAI({
				apiKey: process.env.OPENAI_API_KEY,
				// Explicitly remove organization to use personal account
				organization: undefined,
			});
			model = personalOpenAI(modelConfig.model as any);
			break;
		}
		case "openrouter": {
			// Use OpenAI provider with OpenRouter base URL for Claude/Gemini
			const openRouter = createOpenAI({
				baseURL: "https://openrouter.ai/api/v1",
				apiKey: process.env.OPENROUTER_API_KEY,
			});
			model = openRouter(modelConfig.model as any);
			break;
		}
		default: {
			// Default to OpenAI with personal API key
			const defaultOpenAI = createOpenAI({
				apiKey: process.env.OPENAI_API_KEY,
				organization: undefined,
			});
			model = defaultOpenAI(modelConfig.model as any);
		}
	}

	return { model, cost: modelConfig.cost, config: modelConfig };
}

// Smart fallback when rate limited
function getModelWithFallback(
	requestedModelId: string,
	attempt = 0,
): { model: any; cost: number; config: Model } {
	// If this is a retry, skip rate-limited providers
	if (attempt > 0) {
		// Skip Azure/OpenAI models on retry, use OpenRouter models
		const fallbackModels = AVAILABLE_MODELS.filter(
			(m) => m.provider === "openrouter",
		);
		const fallbackModel = fallbackModels[attempt - 1] || fallbackModels[0];
		if (fallbackModel) {
			return getModelProvider(fallbackModel);
		}
	}

	// Normal model selection
	return getSelectedModel(requestedModelId);
}

// Retry function with automatic model fallback
async function executeStreamTextWithRetry(
	params: {
		model: any;
		messages: any[];
		tools: any;
		systemPrompt: string;
		maxTokens: number;
		maxSteps: number;
		onStepFinish: (params: any) => Promise<void>;
		onFinish: (params: any) => Promise<void>;
		onError: (params: any) => Promise<void>;
	},
	requestedModelId: string,
	maxAttempts = 3,
): Promise<{ result: any; actualModel: string }> {
	let lastError: Error | null = null;

	for (let attempt = 0; attempt < maxAttempts; attempt++) {
		try {
			// Get model for this attempt (with fallback if needed)
			const {
				model: attemptModel,
				cost,
				config,
			} = getModelWithFallback(requestedModelId, attempt);

			// Create stream with current model
			const result = streamText({
				model: attemptModel,
				experimental_transform: smoothStream({
					chunking: "word",
				}),
				temperature: 0.7,
				messages: [
					{ role: "system", content: params.systemPrompt },
					...params.messages,
				],
				tools: params.tools,
				maxTokens: params.maxTokens,
				toolChoice: "auto",
				maxSteps: params.maxSteps,
				onStepFinish: params.onStepFinish,
				onFinish: params.onFinish,
				onError: params.onError,
			});

			return { result, actualModel: config.name }; // Success!
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			lastError =
				error instanceof Error ? error : new Error(errorMessage);

			console.error(
				`[AI Data Chat] Attempt ${attempt + 1} failed:`,
				errorMessage,
			);

			// Check if this is a rate limit error that we should retry
			const isRateLimit =
				errorMessage.includes("rate") ||
				errorMessage.includes("Rate limit") ||
				errorMessage.includes("429") ||
				errorMessage.includes("quota");

			if (!isRateLimit || attempt === maxAttempts - 1) {
				// Not a rate limit or final attempt - throw the error
				throw lastError;
			}

			// Wait briefly before retry
			await new Promise((resolve) => setTimeout(resolve, 1000));
		}
	}

	throw lastError || new Error("All retry attempts failed");
}

export const aiRouter = new Hono()
	.basePath("/ai")
	.use(authMiddleware)

	// Get available models
	.get(
		"/models",
		describeRoute({
			tags: ["AI"],
			summary: "Get available AI models",
			description:
				"Get list of available AI models with their capabilities and costs",
			responses: {
				200: {
					description: "Available AI models",
					content: {
						"application/json": {
							schema: resolver(
								z.array(
									z.object({
										id: z.string(),
										name: z.string(),
										cost: z.number(),
										capabilities: z
											.array(z.string())
											.optional(),
										isPremium: z.boolean().optional(),
										icon: z.string().optional(),
									}),
								),
							),
						},
					},
				},
			},
		}),
		async (c) => {
			return c.json(AVAILABLE_MODELS);
		},
	)

	// Get user credits status
	.get(
		"/credits",
		describeRoute({
			tags: ["AI"],
			summary: "Get user credits status",
			description:
				"Get current user's credit usage and limits for the active organization",
			responses: {
				200: {
					description: "User credits status",
					content: {
						"application/json": {
							schema: resolver(
								z.object({
									total: z.number(),
									used: z.number(),
									remaining: z.number(),
									resetDate: z.string(),
									hoursUntilReset: z.number(),
									needsReset: z.boolean(),
								}),
							),
						},
					},
				},
			},
		}),
		async (c) => {
			const user = c.get("user");
			const organizationId = c.get("session").activeOrganizationId;

			if (!organizationId) {
				throw new HTTPException(400, {
					message: "Organization context required",
				});
			}

			const creditsStatus = await getUserCreditsStatus(
				db,
				user.id,
				organizationId,
			);

			return c.json({
				total: creditsStatus.total,
				used: creditsStatus.used,
				remaining: creditsStatus.remaining,
				resetDate:
					creditsStatus.resetDate?.toISOString() ||
					new Date().toISOString(),
				hoursUntilReset: creditsStatus.hoursUntilReset,
				needsReset: creditsStatus.needsReset,
			});
		},
	)

	// General AI Chat with Streaming
	.post(
		"/chat",
		describeRoute({
			tags: ["AI"],
			summary: "General AI Chat with Streaming",
			description:
				"General AI chat with streaming responses and optional tools",
			responses: {
				200: {
					description: "Streaming AI response",
				},
			},
		}),
		validator("json", (value, c) => {
			return z
				.object({
					messages: z.array(
						z.object({
							role: z.enum(["user", "assistant"]),
							content: z.string(),
						}),
					),
					organizationId: z.string(),
					chatId: z.string().optional(),
					selectedModel: z.string().optional(),
					tool: z.string().optional(),
				})
				.parse(value);
		}),
		async (c) => {
			try {
				const {
					messages,
					organizationId,
					chatId,
					selectedModel,
					tool,
				} = c.req.valid("json");
				const user = c.get("user");

				if (!messages) {
					throw new HTTPException(400, {
						message: "Messages are required",
					});
				}

				if (!organizationId) {
					throw new HTTPException(400, {
						message:
							"Organization context required for AI operations",
					});
				}

				// Verify user has access to organization
				await verifyOrganizationMembership(organizationId, user.id);

				// Check user credits BEFORE processing
				const modelUsed = selectedModel || "gpt-4o-mini";
				const modelCost =
					AVAILABLE_MODELS.find((m) => m.id === modelUsed)?.cost || 0;

				const hasCredits = await checkUserCredits(
					db,
					user.id,
					organizationId,
					modelCost,
				);
				if (!hasCredits) {
					const creditsStatus = await getUserCreditsStatus(
						db,
						user.id,
						organizationId,
					);
					throw new HTTPException(429, {
						message: `Insufficient credits. Used: ${creditsStatus.used}/${creditsStatus.total}. Resets in ${creditsStatus.hoursUntilReset} hours.`,
					});
				}

				// Validate chat access if chatId provided
				let validChatId = chatId;
				if (chatId) {
					const chat = await db.chat.findUnique({
						where: { id: chatId },
					});
					if (
						!chat ||
						chat.userId !== user.id ||
						chat.organizationId !== organizationId
					) {
						validChatId = undefined;
					}
				}

				// Get the model using new AI SDK providers
				const { model: actualModel } = getSelectedModel(modelUsed);

				// Generate final chat ID upfront
				const finalChatId = validChatId || new ObjectId().toHexString();

				// Get organization name for personalized prompt
				const organization = await db.organization.findUnique({
					where: { id: organizationId },
					select: { name: true },
				});

				// Create system prompt for general chat
				const systemPrompt = getSystemPrompt({
					organizationId,
					organizationName: organization?.name,
					userRole: user.role || "user",
					userName: user.name || user.email,
					currentDate: new Date().toLocaleDateString(),
				});

				// Define general tools (simplified for now)
				const tools = undefined;

				// Create streaming response using direct streamText result
				const result = streamText({
					model: actualModel,
					messages: [
						{ role: "system", content: systemPrompt },
						...messages,
					],
					tools,
					maxTokens: 1000,
					onFinish: async ({ text, toolCalls, usage }) => {
						try {
							const actualCost =
								modelCost +
								(toolCalls?.length ? toolCalls.length * 10 : 0);

							// Deduct credits
							await deductUserCredits(
								db,
								user.id,
								organizationId,
								actualCost,
							).catch(console.error);

							// Track AI usage
							await trackAiUsage(db, {
								userId: user.id,
								organizationId,
								chatId: validChatId,
								feature: "general_chat",
								activity: tool || "AI Chat",
								creditsUsed: actualCost,
								promptTokens: usage?.promptTokens,
								completionTokens: usage?.completionTokens,
								toolCalls: toolCalls?.length || 0,
								model: modelUsed,
								success: true,
								metadata: {
									requestedModel: selectedModel,
									actualModel: modelUsed,
									messageLength: text?.length || 0,
								},
							}).catch(console.error);

							const timestamp = new Date();

							// Generate chat title from user message
							const userMessage =
								messages[messages.length - 1]?.content || "";
							const words = userMessage.split(" ").slice(0, 3);
							const generatedTitle =
								words
									.map(
										(word: string) =>
											word.charAt(0).toUpperCase() +
											word.slice(1).toLowerCase(),
									)
									.join(" ") || "AI Chat";

							// Create assistant message with tool calls included
							const assistantMessage = {
								role: "assistant",
								content: text || "",
								...(toolCalls?.length > 0 && {
									toolCalls: toolCalls.map((call: any) => ({
										name: call.toolName,
										parameters: call.args,
										result: call.result,
									})),
								}),
							};

							// Save chat and message data
							if (validChatId) {
								// Look up the actual database model ID
								const dbModelId = await getDbModelId(modelUsed);

								// Update existing chat
								await db.message.create({
									data: {
										id: new ObjectId().toHexString(),
										prompt: userMessage,
										content: text || "",
										uiMessages: JSON.stringify([
											...messages,
											assistantMessage,
										]),
										responseStreamId:
											new ObjectId().toHexString(),
										tool: tool ? (tool as any) : undefined,
										userId: user.id,
										chatId: validChatId,
										modelId:
											dbModelId ||
											new ObjectId().toHexString(), // Use actual DB model ID or fallback
										promptTokens: usage?.promptTokens || 0,
										completionTokens:
											usage?.completionTokens || 0,
										totalTokens:
											(usage?.promptTokens || 0) +
											(usage?.completionTokens || 0),
										creditsSpent: actualCost,
										createdAt: timestamp,
										updatedAt: timestamp,
									},
								});

								await db.chat.update({
									where: { id: validChatId },
									data: {
										lastMessageTimestamp: timestamp,
										status: "ready",
										updatedAt: timestamp,
									},
								});
							} else {
								// Create new chat
								await db.chat.create({
									data: {
										id: finalChatId,
										title: generatedTitle,
										userId: user.id,
										organizationId: organizationId,
										isPublic: false,
										status: "ready",
										lastMessageTimestamp: timestamp,
										createdAt: timestamp,
										updatedAt: timestamp,
									},
								});

								// Look up the actual database model ID for new chat too
								const dbModelIdForNewChat =
									await getDbModelId(modelUsed);

								await db.message.create({
									data: {
										id: new ObjectId().toHexString(),
										prompt: userMessage,
										content: text || "",
										uiMessages: JSON.stringify([
											...messages,
											assistantMessage,
										]),
										responseStreamId:
											new ObjectId().toHexString(),
										tool: tool ? (tool as any) : undefined,
										userId: user.id,
										chatId: finalChatId,
										modelId:
											dbModelIdForNewChat ||
											new ObjectId().toHexString(), // Use actual DB model ID or fallback
										promptTokens: usage?.promptTokens || 0,
										completionTokens:
											usage?.completionTokens || 0,
										totalTokens:
											(usage?.promptTokens || 0) +
											(usage?.completionTokens || 0),
										creditsSpent: actualCost,
										createdAt: timestamp,
										updatedAt: timestamp,
									},
								});
							}

						} catch (error) {
							console.error(
								"[AI Chat] Error in onFinish:",
								error,
							);
						}
					},
				});

				// Return the direct streaming response for ai/react compatibility
				return result.toDataStreamResponse({
					headers: {
						"X-Chat-ID": finalChatId,
						"X-Model-Used": modelUsed,
						"Access-Control-Expose-Headers":
							"X-Chat-ID,X-Model-Used",
					},
				});
			} catch (error) {
				console.error("[AI Chat] Error:", error);
				throw new HTTPException(500, {
					message: "Failed to process AI chat request",
				});
			}
		},
	)

	// AI Data Chat with CRM Tools
	.post(
		"/data-chat",
		describeRoute({
			tags: ["AI"],
			summary: "AI Data Chat with Streaming",
			description:
				"Chat with AI that can query and analyze your organization's data using function calling with streaming responses",
			responses: {
				200: {
					description:
						"Streaming AI response with function call results",
				},
			},
		}),
		validator("json", (value, c) => {
			return z
				.object({
					messages: z.array(
						z.object({
							role: z.enum(["user", "assistant"]),
							content: z.string(),
						}),
					),
					organizationId: z.string(),
					chatId: z.string().optional(),
					tool: z.string().optional(),
					selectedModel: z.string().optional(),
				})
				.parse(value);
		}),
		async (c) => {
			try {
				const {
					messages,
					organizationId,
					chatId,
					tool,
					selectedModel,
				} = c.req.valid("json");
				const user = c.get("user");

				if (!messages || !organizationId) {
					throw new HTTPException(400, {
						message: "Messages and organizationId are required",
					});
				}

				// Verify user has access to organization
				await verifyOrganizationMembership(organizationId, user.id);

				// Check user credits BEFORE processing
				const estimatedCost = calculateModelCost("gpt-4", 1000, 500, 0);
				const hasCredits = await checkUserCredits(
					db,
					user.id,
					organizationId,
					estimatedCost,
				);
				if (!hasCredits) {
					const creditsStatus = await getUserCreditsStatus(
						db,
						user.id,
						organizationId,
					);
					throw new HTTPException(429, {
						message: `Insufficient credits. Used: ${creditsStatus.used}/${creditsStatus.total}. Resets in ${creditsStatus.hoursUntilReset} hours.`,
					});
				}

				// Get the actual model to use with smart fallback
				const modelUsed = selectedModel || "claude-3-5-sonnet-20241022"; // Default to Claude (OpenRouter)
				const {
					model: actualModel,
					cost: modelCost,
					config: modelConfig,
				} = getModelWithFallback(modelUsed);

				// Generate final chat ID upfront
				const finalChatId = chatId || new ObjectId().toHexString();

				// Get organization name for personalized prompt
				const organization = await db.organization.findUnique({
					where: { id: organizationId },
					select: { name: true },
				});

				// Create CRM tools for this organization
				const tools = createCRMTools({
					organizationId,
					userId: user.id,
					userRole: user.role || "user",
					db,
				});

				// Create system prompt with proper context
				const systemPrompt = getSystemPrompt({
					organizationId,
					organizationName: organization?.name,
					userRole: user.role || "user",
					userName: user.name || user.email,
					currentDate: new Date().toLocaleDateString(),
				});

				let usedModel = modelUsed; // Track which model was actually used
				const stream = createDataStream({
					execute: async (writer) => {
						try {
							// Use retry wrapper to automatically fallback on rate limits
							const { result, actualModel: actualUsedModel } =
								await executeStreamTextWithRetry(
									{
										model: actualModel, // This will be overridden in retry function
										messages,
										tools,
										systemPrompt,
										maxTokens: 2000,
										maxSteps: 3,
										onStepFinish: async ({
											stepType,
											text,
											toolCalls,
											toolResults,
											finishReason,
											usage,
											warnings,
										}) => {},
										onError: async ({ error }) => {
											console.error(
												"[AI Data Chat] streamText onError:",
												error,
											);
											const actualError =
												error instanceof Error
													? error
													: new Error(String(error));
											console.error(
												"[AI Data Chat] Error type:",
												actualError.constructor.name,
											);
											console.error(
												"[AI Data Chat] Error message:",
												actualError.message,
											);
										},
										onFinish: async ({
											text,
											toolCalls,
											usage,
											finishReason,
											warnings,
										}) => {
											try {
												const actualCost =
													modelCost +
													(toolCalls?.length
														? toolCalls.length * 10
														: 0);
												await deductUserCredits(
													db,
													user.id,
													organizationId,
													actualCost,
												).catch(console.error);

												// Track AI usage
												await trackAiUsage(db, {
													userId: user.id,
													organizationId,
													chatId: finalChatId,
													feature: "data_chat",
													activity:
														tool || "Data Chat",
													creditsUsed: actualCost,
													promptTokens:
														usage?.promptTokens,
													completionTokens:
														usage?.completionTokens,
													toolCalls:
														toolCalls?.length || 0,
													model: modelUsed,
													success: true,
													metadata: {
														requestedModel:
															selectedModel,
														actualModel:
															actualUsedModel,
														messageLength:
															text?.length || 0,
														toolCallsCount:
															toolCalls?.length ||
															0,
													},
												}).catch(console.error);

												// Generate chat title from user message
												const userMessage =
													messages[
														messages.length - 1
													]?.content || "";
												const generatedTitle =
													userMessage
														.split(" ")
														.slice(0, 5)
														.join(" ") ||
													"AI Data Chat";

												const timestamp = new Date();

												// Create or update chat
												await db.chat.upsert({
													where: { id: finalChatId },
													update: {
														lastMessageTimestamp:
															timestamp,
														status: "ready",
														updatedAt: timestamp,
													},
													create: {
														id: finalChatId,
														title: generatedTitle,
														userId: user.id,
														organizationId:
															organizationId,
														isPublic: false,
														status: "ready",
														lastMessageTimestamp:
															timestamp,
														createdAt: timestamp,
														updatedAt: timestamp,
													},
												});

												// Create assistant message with tool calls included
												const assistantMessage = {
													role: "assistant",
													content: text || "",
													...(toolCalls?.length >
														0 && {
														toolCalls:
															toolCalls.map(
																(
																	call: any,
																) => ({
																	name: call.toolName,
																	parameters:
																		call.args,
																	result: call.result,
																}),
															),
													}),
												};

												// Look up the actual database model ID
												const dbModelIdForDataChat =
													await getDbModelId(
														modelUsed,
													);

												// Create message record
												await db.message.create({
													data: {
														id: new ObjectId().toHexString(),
														prompt: userMessage,
														content: text || "",
														uiMessages:
															JSON.stringify([
																...messages,
																assistantMessage,
															]),
														responseStreamId:
															new ObjectId().toHexString(),
														tool: tool
															? (tool as any)
															: undefined,
														userId: user.id,
														chatId: finalChatId,
														modelId:
															dbModelIdForDataChat ||
															new ObjectId().toHexString(), // Use actual DB model ID or fallback
														promptTokens:
															usage?.promptTokens ||
															0,
														completionTokens:
															usage?.completionTokens ||
															0,
														totalTokens:
															(usage?.promptTokens ||
																0) +
															(usage?.completionTokens ||
																0),
														creditsSpent:
															actualCost,
														createdAt: timestamp,
														updatedAt: timestamp,
													},
												});
											} catch (error) {
												console.error(
													"[AI Data Chat] Error in onFinish:",
													error,
												);
											}
										},
									},
									modelUsed,
									3,
								); // Try up to 3 different models

							usedModel = actualUsedModel; // Update the model that was actually used
							

							// EXACT Zeron Chat pattern: consumeStream() THEN mergeIntoDataStream()
							try {
								result.consumeStream();
							} catch (consumeError) {
								console.error(
									"[AI Data Chat] Error in consumeStream():",
									consumeError,
								);
								throw consumeError;
							}

							try {
								result.mergeIntoDataStream(writer, {
									sendReasoning: true,
								});
							} catch (mergeError) {
								console.error(
									"[AI Data Chat] Error in mergeIntoDataStream():",
									mergeError,
								);
								throw mergeError;
							}
						} catch (streamError) {
							const error =
								streamError instanceof Error
									? streamError
									: new Error(String(streamError));
							console.error(
								"[AI Data Chat] Error during streamText execution:",
								error,
							);
							console.error(
								"[AI Data Chat] Error type:",
								error.constructor.name,
							);
							console.error(
								"[AI Data Chat] Error message:",
								error.message,
							);
							console.error(
								"[AI Data Chat] Error stack:",
								error.stack,
							);

							// Don't write to stream - let the error propagate
							throw error;
						}
					},
				});

				return new Response(stream, {
					headers: {
						"Content-Type": "text/plain; charset=utf-8",
						"X-Chat-ID": finalChatId,
						"X-Model-Used": usedModel || modelUsed,
						"X-Model-Requested": modelUsed,
						"Access-Control-Expose-Headers":
							"X-Chat-ID,X-Model-Used,X-Model-Requested",
					},
				});
			} catch (error) {
				console.error("[AI Data Chat] Error:", error);

				// Re-throw HTTPException with original status code
				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					message: "Failed to process AI data chat request",
				});
			}
		},
	)

	// Create new chat
	.post(
		"/chats",
		describeRoute({
			tags: ["AI"],
			summary: "Create new chat",
			description: "Create a new chat conversation",
			responses: {
				201: {
					description: "Chat created successfully",
				},
			},
		}),
		validator("json", (value, c) => {
			return z
				.object({
					title: z.string().optional(),
					organizationId: z.string(),
				})
				.parse(value);
		}),
		async (c) => {
			const user = c.get("user");
			const { title, organizationId } = c.req.valid("json");

			// Verify user has access to organization
			await verifyOrganizationMembership(organizationId, user.id);

			const chatId = new ObjectId().toHexString();
			const timestamp = new Date();

			const chat = await db.chat.create({
				data: {
					id: chatId,
					title: title || "New Chat",
					userId: user.id,
					organizationId: organizationId,
					isPublic: false,
					status: "ready",
					lastMessageTimestamp: timestamp,
					createdAt: timestamp,
					updatedAt: timestamp,
				},
				select: {
					id: true,
					title: true,
					createdAt: true,
					updatedAt: true,
					lastMessageTimestamp: true,
					organizationId: true,
					isPublic: true,
					status: true,
				},
			});

			return c.json(chat, 201);
		},
	)

	// Get chats for user/organization
	.get(
		"/chats",
		describeRoute({
			tags: ["AI"],
			summary: "Get chats",
			description: "Get all chats for current user or organization",
			responses: {
				200: {
					description: "User chats",
				},
			},
		}),
		validator("query", (value, c) => {
			return z
				.object({
					organizationId: z.string().optional(),
				})
				.parse(value);
		}),
		async (c) => {
			const user = c.get("user");
			const query = c.req.valid("query");

			const chats = await db.chat.findMany({
				where: query?.organizationId
					? {
							organizationId: query.organizationId,
						}
					: {
							userId: user.id,
							organizationId: null,
						},
				orderBy: { lastMessageTimestamp: "desc" },
				take: 50,
				select: {
					id: true,
					title: true,
					createdAt: true,
					updatedAt: true,
					lastMessageTimestamp: true,
					organizationId: true,
					isPublic: true,
					status: true,
				},
			});

			return c.json(chats);
		},
	)

	// Get specific chat
	.get(
		"/chats/:id",
		describeRoute({
			tags: ["AI"],
			summary: "Get chat by ID",
			description: "Get a specific chat with messages",
			responses: {
				200: {
					description: "Chat details",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.param();
			const user = c.get("user");

			const chat = await db.chat.findUnique({
				where: { id },
				include: {
					messages: {
						orderBy: { createdAt: "desc" },
						take: 1,
						select: {
							uiMessages: true,
							content: true,
							createdAt: true,
						},
					},
				},
			});

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			// Verify access
			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					user.id,
				);
			} else if (chat.userId !== user.id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			// Parse messages with tool calls preserved
			let messages: any[] = [];
			const chatWithMessages = chat as any; // Type assertion for included messages
			if (chatWithMessages.messages?.length > 0) {
				try {
					const uiMessages = chatWithMessages.messages[0].uiMessages
						? JSON.parse(chatWithMessages.messages[0].uiMessages)
						: [];

					// Add creation date to messages and preserve all existing data
					messages = uiMessages.map((msg: any) => ({
						...msg, // This preserves toolCalls, model info, and other data from stored uiMessages
						createdAt: chatWithMessages.messages[0].createdAt,
					}));
				} catch (error) {
					console.error("Failed to parse UI messages:", error);
					messages = [];
				}
			}

			return c.json({
				...chat,
				messages,
			});
		},
	)

	// Update chat
	.put(
		"/chats/:id",
		describeRoute({
			tags: ["AI"],
			summary: "Update chat",
			description: "Update chat title and other properties",
			responses: {
				200: {
					description: "Chat updated successfully",
				},
			},
		}),
		validator("json", (value, c) => {
			return z
				.object({
					title: z.string().optional(),
				})
				.parse(value);
		}),
		async (c) => {
			const { id } = c.req.param();
			const user = c.get("user");
			const { title } = c.req.valid("json");

			const chat = await db.chat.findUnique({ where: { id } });

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			// Verify access
			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					user.id,
				);
			} else if (chat.userId !== user.id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			const updatedChat = await db.chat.update({
				where: { id },
				data: {
					...(title && { title }),
					updatedAt: new Date(),
				},
				select: {
					id: true,
					title: true,
					createdAt: true,
					updatedAt: true,
					lastMessageTimestamp: true,
					organizationId: true,
					isPublic: true,
					status: true,
				},
			});

			return c.json(updatedChat);
		},
	)

	// Delete chat
	.delete(
		"/chats/:id",
		describeRoute({
			tags: ["AI"],
			summary: "Delete chat",
			description: "Delete a chat and all its messages",
			responses: {
				204: {
					description: "Chat deleted",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.param();
			const user = c.get("user");

			const chat = await db.chat.findUnique({ where: { id } });

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			// Verify access
			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					user.id,
				);
			} else if (chat.userId !== user.id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			// Delete messages first, then chat
			await db.message.deleteMany({ where: { chatId: id } });
			await db.chat.delete({ where: { id } });

			return c.body(null, 204);
		},
	)

	// Purchase credits
	.post(
		"/purchase-credits",
		describeRoute({
			tags: ["AI"],
			summary: "Purchase AI credits",
			description:
				"Create a Stripe checkout session for purchasing AI credits",
			responses: {
				200: {
					description: "Checkout session created",
				},
			},
		}),
		validator("json", (value, c) => {
			return z
				.object({
					packageId: z.string(),
					priceId: z.string(),
					organizationId: z.string(),
					credits: z.number(),
					price: z.number(),
					successUrl: z.string(),
					cancelUrl: z.string(),
				})
				.parse(value);
		}),
		async (c) => {
			const user = c.get("user");
			const {
				packageId,
				priceId,
				organizationId,
				credits,
				price,
				successUrl,
				cancelUrl,
			} = c.req.valid("json");

			// Verify user has access to organization
			await verifyOrganizationMembership(organizationId, user.id);

			try {
				// Import the createCheckoutLink function from payments package
				const { createCheckoutLink } = await import("@repo/payments");

				// Create Stripe checkout session
				const checkoutUrl = await createCheckoutLink({
					type: "one-time",
					productId: priceId,
					redirectUrl: successUrl,
					organizationId,
					userId: user.id,
					seats: 1, // Always 1 for credit purchases
				});

				if (!checkoutUrl) {
					throw new HTTPException(500, {
						message: "Failed to create checkout session",
					});
				}

				return c.json({ checkoutUrl });
			} catch (error) {
				console.error("Error creating checkout session:", error);
				throw new HTTPException(500, {
					message: "Failed to create checkout session",
				});
			}
		},
	)

	// Get organization analytics
	.get(
		"/analytics/:organizationId",
		describeRoute({
			tags: ["AI"],
			summary: "Get organization analytics",
			description:
				"Get detailed analytics and usage statistics for an organization",
			responses: {
				200: {
					description: "Organization analytics",
				},
			},
		}),
		async (c) => {
			const { organizationId } = c.req.param();
			const user = c.get("user");

			// Verify user has access to organization
			await verifyOrganizationMembership(organizationId, user.id);

			// Get user credits status
			const creditsStatus = await getUserCreditsStatus(
				db,
				user.id,
				organizationId,
			);

			// Calculate date range for current period
			const now = new Date();
			const periodStart = new Date(creditsStatus.resetDate || now);

			// Get usage data from the last 30 days
			const usageData = await db.aiUsage.findMany({
				where: {
					organizationId,
					createdAt: {
						gte: periodStart,
					},
					success: true,
				},
				orderBy: {
					createdAt: "desc",
				},
			});

			// Calculate breakdown by feature
			const featureBreakdown = {
				data_chat: { credits: 0, sessions: 0 },
				task_creation: { credits: 0, sessions: 0 },
				analytics: { credits: 0, sessions: 0 },
				general_chat: { credits: 0, sessions: 0 },
			};

			for (const usage of usageData) {
				const feature = usage.feature as keyof typeof featureBreakdown;
				if (featureBreakdown[feature]) {
					featureBreakdown[feature].credits += usage.creditsUsed;
					featureBreakdown[feature].sessions += 1;
				}
			}

			const totalCreditsUsed = usageData.reduce(
				(sum, usage) => sum + usage.creditsUsed,
				0,
			);

			// Calculate daily usage for the last 7 days
			const last7Days = Array.from({ length: 7 }, (_, i) => {
				const date = new Date();
				date.setDate(date.getDate() - i);
				return date;
			}).reverse();

			const dailyUsage = last7Days.map((date) => {
				const dayStart = new Date(
					date.getFullYear(),
					date.getMonth(),
					date.getDate(),
				);
				const dayEnd = new Date(
					dayStart.getTime() + 24 * 60 * 60 * 1000,
				);

				const dayUsage = usageData.filter(
					(usage) =>
						usage.createdAt >= dayStart && usage.createdAt < dayEnd,
				);

				return {
					date: date.toISOString().split("T")[0],
					credits: dayUsage.reduce(
						(sum, usage) => sum + usage.creditsUsed,
						0,
					),
					sessions: dayUsage.length,
				};
			});

			return c.json({
				creditsStatus,
				usage: {
					totalCreditsUsed,
					featureBreakdown,
					dailyUsage,
					period: {
						start: periodStart.toISOString(),
						end: now.toISOString(),
					},
				},
			});
		},
	)

	// Get detailed usage analytics with user-level permissions
	.get(
		"/usage/:organizationId",
		describeRoute({
			tags: ["AI"],
			summary: "Get detailed usage analytics",
			description:
				"Get detailed usage analytics and user breakdown. Owners/admins see all users, members see only their own usage.",
			responses: {
				200: {
					description: "Detailed usage analytics",
				},
			},
		}),
		async (c) => {
			const { organizationId } = c.req.param();
			const user = c.get("user");

			// Verify user has access to organization and get their role
			const membership = await db.member.findUnique({
				where: {
					userId_organizationId: {
						userId: user.id,
						organizationId,
					},
				},
				include: {
					organization: true,
				},
			});

			if (!membership) {
				throw new HTTPException(404, {
					message: "User is not a member of this organization",
				});
			}

			const isAdmin = ["owner", "admin"].includes(membership.role);

			// Get user credits status for the requesting user
			const creditsStatus = await getUserCreditsStatus(
				db,
				user.id,
				organizationId,
			);

			// Calculate date range - use last 30 days for usage history
			const now = new Date();
			const last30Days = new Date(
				now.getTime() - 30 * 24 * 60 * 60 * 1000,
			);

			// Determine which users' data to include
			const userFilter = isAdmin
				? { organizationId } // Admin sees all users in organization
				: { organizationId, userId: user.id }; // Regular member sees only their own

			// Get usage data
			const usageData = await db.aiUsage.findMany({
				where: {
					...userFilter,
					createdAt: {
						gte: last30Days,
					},
					success: true,
				},
				orderBy: {
					createdAt: "desc",
				},
			});

			// Get user data for admin breakdown
			let usersData: {
				[userId: string]: { id: string; name: string; email: string };
			} = {};
			if (isAdmin && usageData.length > 0) {
				const userIds = Array.from(
					new Set(usageData.map((usage) => usage.userId)),
				);
				const users = await db.user.findMany({
					where: {
						id: { in: userIds },
					},
					select: {
						id: true,
						name: true,
						email: true,
					},
				});
				usersData = users.reduce(
					(acc, user) => {
						acc[user.id] = user;
						return acc;
					},
					{} as {
						[userId: string]: {
							id: string;
							name: string;
							email: string;
						};
					},
				);
			}

			// Calculate feature breakdown
			const featureBreakdown: {
				[key: string]: {
					credits: number;
					sessions: number;
					percentage: number;
				};
			} = {
				data_chat: { credits: 0, sessions: 0, percentage: 0 },
				task_creation: { credits: 0, sessions: 0, percentage: 0 },
				analytics: { credits: 0, sessions: 0, percentage: 0 },
				general_chat: { credits: 0, sessions: 0, percentage: 0 },
			};

			const totalCreditsUsed = usageData.reduce((sum, usage) => {
				const feature = usage.feature as keyof typeof featureBreakdown;
				if (featureBreakdown[feature]) {
					featureBreakdown[feature].credits += usage.creditsUsed;
					featureBreakdown[feature].sessions += 1;
				}
				return sum + usage.creditsUsed;
			}, 0);

			// Calculate percentages
			Object.keys(featureBreakdown).forEach((key) => {
				const feature = featureBreakdown[key];
				feature.percentage =
					totalCreditsUsed > 0
						? Math.round((feature.credits / totalCreditsUsed) * 100)
						: 0;
			});

			// Calculate daily usage for the last 7 days
			const last7Days = Array.from({ length: 7 }, (_, i) => {
				const date = new Date();
				date.setDate(date.getDate() - i);
				return date;
			}).reverse();

			const dailyUsage = last7Days.map((date) => {
				const dayStart = new Date(
					date.getFullYear(),
					date.getMonth(),
					date.getDate(),
				);
				const dayEnd = new Date(
					dayStart.getTime() + 24 * 60 * 60 * 1000,
				);

				const dayUsage = usageData.filter(
					(usage) =>
						usage.createdAt >= dayStart && usage.createdAt < dayEnd,
				);

				return {
					day: date.toLocaleDateString("en-US", { weekday: "short" }),
					credits: dayUsage.reduce(
						(sum, usage) => sum + usage.creditsUsed,
						0,
					),
				};
			});

			// Calculate top activities
			const activityMap = new Map<
				string,
				{ credits: number; count: number }
			>();
			usageData.forEach((usage) => {
				const activity = usage.activity || usage.feature;
				if (!activityMap.has(activity)) {
					activityMap.set(activity, { credits: 0, count: 0 });
				}
				const current = activityMap.get(activity)!;
				current.credits += usage.creditsUsed;
				current.count += 1;
			});

			const topActivities = Array.from(activityMap.entries())
				.map(([activity, data]) => ({
					activity,
					credits: data.credits,
					count: data.count,
				}))
				.sort((a, b) => b.credits - a.credits)
				.slice(0, 5);

			// User breakdown (only for admins)
			let userBreakdown: any[] = [];
			if (isAdmin) {
				const userMap = new Map<
					string,
					{
						user: any;
						credits: number;
						sessions: number;
						lastActivity: Date;
					}
				>();

				usageData.forEach((usage) => {
					if (!userMap.has(usage.userId)) {
						userMap.set(usage.userId, {
							user: usersData[usage.userId] || {
								id: usage.userId,
								name: "Unknown User",
								email: "",
							},
							credits: 0,
							sessions: 0,
							lastActivity: usage.createdAt,
						});
					}
					const userData = userMap.get(usage.userId)!;
					userData.credits += usage.creditsUsed;
					userData.sessions += 1;
					if (usage.createdAt > userData.lastActivity) {
						userData.lastActivity = usage.createdAt;
					}
				});

				userBreakdown = Array.from(userMap.values())
					.sort((a, b) => b.credits - a.credits)
					.map((data) => ({
						user: {
							id: data.user.id,
							name: data.user.name,
							email: data.user.email,
						},
						credits: data.credits,
						sessions: data.sessions,
						lastActivity: data.lastActivity.toISOString(),
						percentage:
							totalCreditsUsed > 0
								? Math.round(
										(data.credits / totalCreditsUsed) * 100,
									)
								: 0,
					}));
			}

			// Return data in format expected by UsageAnalyticsClient
			return c.json({
				currentPeriod: {
					total: creditsStatus.total,
					used: creditsStatus.used,
					remaining: creditsStatus.remaining,
					daysInPeriod: 1, // Daily reset
					daysRemaining: creditsStatus.hoursUntilReset > 0 ? 1 : 0,
				},
				breakdown: {
					dataChat: {
						credits: featureBreakdown.data_chat.credits,
						percentage: featureBreakdown.data_chat.percentage,
						sessions: featureBreakdown.data_chat.sessions,
					},
					taskCreation: {
						credits: featureBreakdown.task_creation.credits,
						percentage: featureBreakdown.task_creation.percentage,
						sessions: featureBreakdown.task_creation.sessions,
					},
					analytics: {
						credits: featureBreakdown.analytics.credits,
						percentage: featureBreakdown.analytics.percentage,
						sessions: featureBreakdown.analytics.sessions,
					},
				},
				dailyUsage,
				topActivities,
				// Additional data for admins
				...(isAdmin && {
					userBreakdown,
					organizationStats: {
						totalUsers: userBreakdown.length,
						activeUsers: userBreakdown.filter((u) => u.credits > 0)
							.length,
						totalCreditsUsed,
						averageCreditsPerUser:
							userBreakdown.length > 0
								? Math.round(
										totalCreditsUsed / userBreakdown.length,
									)
								: 0,
					},
				}),
				permissions: {
					isAdmin,
					canViewAllUsers: isAdmin,
				},
			});
		},
	);
