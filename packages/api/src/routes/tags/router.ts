import { <PERSON>o } from "hono";
import { z } from "zod";
import { zValida<PERSON> } from "@hono/zod-validator";
import { prisma } from "@repo/database/server";
import { TAGGABLE_OBJECT_TYPES } from "@repo/database/src/types/object";
import { authMiddleware } from "../../middleware/auth";
import type { Session } from "@repo/auth";
import { normalizeTagName, validateTagName } from "../../lib/tag-utils";

type Variables = {
  user: Session["user"];
  auth: {
    userId: string;
    organizationId: string;
  };
};

const tagsRouter = new Hono<{ Variables: Variables }>();

// Apply auth middleware to all routes
tagsRouter.use("*", authMiddleware);

// Create a new tag
const createTagSchema = z.object({
  name: z.string(),
  color: z.string(),
  objectType: z.enum(TAGGABLE_OBJECT_TYPES),
});

tagsRouter.post("/", zValidator("json", createTagSchema), async (c) => {
  const { name, color, objectType } = c.req.valid("json");
  const { organizationId, userId } = c.get("auth");

  // Validate and normalize the tag name
  const validation = validateTagName(name);
  if (!validation.isValid) {
    return c.json({ error: validation.error }, 400);
  }

  const normalizedName = normalizeTagName(name);

  try {
    // Check if a tag with this normalized name already exists for this object type
    const existingTag = await prisma.tag.findFirst({
      where: {
        name: normalizedName,
        objectType,
        organizationId,
      },
    });

    if (existingTag) {
      return c.json(
        { error: "A tag with this name already exists for this object type" },
        400
      );
    }

    const tag = await prisma.tag.create({
      data: {
        name: normalizedName,
        color,
        objectType,
        organizationId,
        createdBy: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return c.json(tag);
  } catch (error: any) {
    if (error.code === "P2002") {
      return c.json(
        { error: "A tag with this name already exists for this object type" },
        400
      );
    }
    console.error("Failed to create tag:", error);
    return c.json({ error: "Failed to create tag" }, 500);
  }
});

// Get tags for an object
tagsRouter.get("/object/:objectId/:objectType", async (c) => {
  const objectId = c.req.param("objectId");
  const objectType = c.req.param("objectType");
  const { organizationId } = c.get("auth");

  try {
    if (!TAGGABLE_OBJECT_TYPES.includes(objectType as any)) {
      return c.json({ error: "Invalid object type" }, 400);
    }

    const objectTags = await prisma.objectTag.findMany({
      where: {
        objectId,
        objectType,
        tag: {
          organizationId,
        },
      },
      include: {
        tag: {
          select: {
            id: true,
            name: true,
            color: true,
          },
        },
        adder: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return c.json(objectTags);
  } catch (error) {
    console.error("Failed to fetch object tags:", error);
    return c.json({ error: "Failed to fetch object tags" }, 500);
  }
});

// Get all tags for an organization and object type
tagsRouter.get("/:objectType", async (c) => {
  const objectType = c.req.param("objectType");
  const { organizationId } = c.get("auth");

  try {
    if (!TAGGABLE_OBJECT_TYPES.includes(objectType as any)) {
      return c.json({ error: "Invalid object type" }, 400);
    }

    const tags = await prisma.tag.findMany({
      where: {
        organizationId,
        objectType,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return c.json(tags);
  } catch (error) {
    console.error("Failed to fetch tags:", error);
    return c.json({ error: "Failed to fetch tags" }, 500);
  }
});

// Delete a tag
tagsRouter.delete("/:id", async (c) => {
  const id = c.req.param("id");
  const { organizationId } = c.get("auth");

  try {
    const tag = await prisma.tag.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    await prisma.tag.delete({
      where: {
        id,
      },
    });

    return c.json({ success: true });
  } catch (error) {
    console.error("Failed to delete tag:", error);
    return c.json({ error: "Failed to delete tag" }, 500);
  }
});

// Get all tags across all object types for organization (for management interface)
tagsRouter.get("/", async (c) => {
  const { organizationId } = c.get("auth");

  try {
    const tags = await prisma.tag.findMany({
      where: {
        organizationId,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        permissions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            objectTags: true,
          },
        },
      },
      orderBy: [
        { objectType: 'asc' },
        { name: 'asc' },
      ],
    });

    return c.json(tags);
  } catch (error) {
    console.error("Failed to fetch tags:", error);
    return c.json({ error: "Failed to fetch tags" }, 500);
  }
});

// Get tag permissions for a specific tag
tagsRouter.get("/:id/permissions", async (c) => {
  const tagId = c.req.param("id");
  const { organizationId } = c.get("auth");

  try {
    // Verify tag belongs to organization
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        organizationId,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    const permissions = await prisma.tagPermission.findMany({
      where: {
        tagId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return c.json(permissions);
  } catch (error) {
    console.error("Failed to fetch tag permissions:", error);
    return c.json({ error: "Failed to fetch tag permissions" }, 500);
  }
});

// Add permission to a tag
const addPermissionSchema = z.object({
  userId: z.string(),
  role: z.enum(["viewer", "editor", "admin"]),
});

tagsRouter.post("/:id/permissions", zValidator("json", addPermissionSchema), async (c) => {
  const tagId = c.req.param("id");
  const { userId, role } = c.req.valid("json");
  const { organizationId } = c.get("auth");

  try {
    // Verify tag belongs to organization
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        organizationId,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    // Verify user belongs to organization
    const membership = await prisma.member.findFirst({
      where: {
        userId,
        organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "User not found in organization" }, 404);
    }

    // Create or update permission
    const permission = await prisma.tagPermission.upsert({
      where: {
        tagId_userId: {
          tagId,
          userId,
        },
      },
      create: {
        tagId,
        userId,
        role,
      },
      update: {
        role,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return c.json(permission);
  } catch (error) {
    console.error("Failed to add tag permission:", error);
    return c.json({ error: "Failed to add tag permission" }, 500);
  }
});

// Remove permission from a tag
tagsRouter.delete("/:id/permissions/:userId", async (c) => {
  const tagId = c.req.param("id");
  const userId = c.req.param("userId");
  const { organizationId } = c.get("auth");

  try {
    // Verify tag belongs to organization
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        organizationId,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    await prisma.tagPermission.deleteMany({
      where: {
        tagId,
        userId,
      },
    });

    return c.json({ success: true });
  } catch (error) {
    console.error("Failed to remove tag permission:", error);
    return c.json({ error: "Failed to remove tag permission" }, 500);
  }
});

// Get organization members for permission assignment
tagsRouter.get("/members", async (c) => {
  const { organizationId } = c.get("auth");

  try {
    const members = await prisma.member.findMany({
      where: {
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
          },
        },
      },
    });

    return c.json(members.map(member => member.user));
  } catch (error) {
    console.error("Failed to fetch organization members:", error);
    return c.json({ error: "Failed to fetch organization members" }, 500);
  }
});

// Add tag to object
const addTagSchema = z.object({
  objectId: z.string(),
  objectType: z.enum(TAGGABLE_OBJECT_TYPES),
});

tagsRouter.post("/:id/add", zValidator("json", addTagSchema), async (c) => {
  const tagId = c.req.param("id");
  const { objectId, objectType } = c.req.valid("json");
  const { organizationId, userId } = c.get("auth");

  try {
    // Check if tag exists and belongs to organization
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        organizationId,
        objectType,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    try {
      const objectTag = await prisma.objectTag.create({
        data: {
          tagId,
          objectId,
          objectType,
          addedBy: userId,
        },
        include: {
          tag: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          adder: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });

      // Invalidate caches that depend on tag facets
      // Note: This would be better with a proper cache invalidation system
      // For now, we rely on frontend cache invalidation

      return c.json(objectTag);
    } catch (error: any) {
      if (error.code === "P2002") {
        return c.json({ error: "Object already has this tag" }, 400);
      }
      throw error;
    }
  } catch (error) {
    console.error("Failed to add tag to object:", error);
    return c.json({ error: "Failed to add tag to object" }, 500);
  }
});

// Remove tag from object
tagsRouter.delete("/:id/remove/:objectId", async (c) => {
  const tagId = c.req.param("id");
  const objectId = c.req.param("objectId");
  const { organizationId } = c.get("auth");

  try {
    // Check if tag exists and belongs to organization
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        organizationId,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    await prisma.objectTag.deleteMany({
      where: {
        tagId,
        objectId,
      },
    });

    return c.json({ success: true });
  } catch (error) {
    console.error("Failed to remove tag from object:", error);
    return c.json({ error: "Failed to remove tag from object" }, 500);
  }
});

// Update a tag
const updateTagSchema = z.object({
  color: z.string(),
});

tagsRouter.patch("/:id", zValidator("json", updateTagSchema), async (c) => {
  const id = c.req.param("id");
  const { color } = c.req.valid("json");
  const { organizationId } = c.get("auth");

  try {
    // Check if tag exists and belongs to organization
    const tag = await prisma.tag.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!tag) {
      return c.json({ error: "Tag not found" }, 404);
    }

    const updatedTag = await prisma.tag.update({
      where: { id },
      data: { color },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return c.json(updatedTag);
  } catch (error) {
    console.error("Failed to update tag:", error);
    return c.json({ error: "Failed to update tag" }, 500);
  }
});

export { tagsRouter }; 