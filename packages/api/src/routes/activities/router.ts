import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { sendEmail } from "@repo/mail";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";

export const activitiesRouter = new Hono<{ Variables: { user: Session["user"] } }>();

// Apply auth middleware
activitiesRouter.use("*", authMiddleware);

// Activity schema definitions
const ActivityCreateSchema = z.object({
	recordId: z.string().optional(),
	recordType: z.enum(["contact", "company", "property"]).optional(),
	type: z.enum(["note", "call", "email", "meeting", "task", "system"]).default("note"),
	message: z.string().min(1),
	phone: z.string().optional(),
	result: z.string().optional(),
	system: z.boolean().default(false),
	mentionedUsers: z.array(z.string()).default([]),
	organizationId: z.string(),
});

const ActivityUpdateSchema = z.object({
	message: z.string().min(1).optional(),
	phone: z.string().optional(),
	result: z.string().optional(),
	resolved: z.boolean().optional(),
	mentionedUsers: z.array(z.string()).optional(),
	organizationId: z.string(),
});

const ActivityReplyCreateSchema = z.object({
	message: z.string().min(1),
	organizationId: z.string(),
	mentionedUsers: z.array(z.string()).default([]),
});

const ActivityQuerySchema = z.object({
	organizationId: z.string(),
	recordId: z.string().optional(),
	limit: z.coerce.number().min(1).max(100).default(50),
	offset: z.coerce.number().min(0).default(0),
});

// Optimized include configuration for activity queries
const ACTIVITY_INCLUDE = {
	user: {
		select: {
			id: true,
			name: true,
			email: true,
			image: true,
		},
	},
	resolver: {
		select: {
			id: true,
			name: true,
			email: true,
			image: true,
		},
	},
	replies: {
		include: {
			user: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
		},
		orderBy: {
			createdAt: "asc" as const,
		},
	},
};

// Create activity
activitiesRouter.post(
	"/activities",
	validator("json", ActivityCreateSchema),
	describeRoute({
		tags: ["Activities"],
		summary: "Create a new activity",
		description: "Creates a new activity for a record",
		responses: {
			201: {
				description: "Activity created successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ activity: z.any() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const data = c.req.valid("json");

			// Verify organization membership
			await verifyOrganizationMembership(data.organizationId, user.id);

			const activity = await db.activity.create({
				data: {
					...data,
					userId: user.id,
				},
				include: ACTIVITY_INCLUDE,
			});

			logger.info(`Activity created: ${activity.id} by user ${user.id}`);
			return c.json({ activity }, 201);
		} catch (error) {
			logger.error("Failed to create activity:", error);
			return c.json({ error: "Failed to create activity" }, 500);
		}
	},
);

// Get activities by record
activitiesRouter.get(
	"/activities/record/:recordId",
	validator("query", z.object({ organizationId: z.string() })),
	describeRoute({
		tags: ["Activities"],
		summary: "Get activities by record ID",
		description: "Retrieves all activities for a specific record",
		responses: {
			200: {
				description: "Activities retrieved successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ activities: z.array(z.any()) })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const recordId = c.req.param("recordId");
			const { organizationId } = c.req.valid("query");

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			const activities = await db.activity.findMany({
				where: {
					organizationId,
					recordId,
				},
				include: ACTIVITY_INCLUDE,
				orderBy: {
					createdAt: "desc",
				},
			});

			return c.json({ activities });
		} catch (error) {
			logger.error("Failed to fetch activities:", error);
			return c.json({ error: "Failed to fetch activities" }, 500);
		}
	},
);

// Get all activities for organization
activitiesRouter.get(
	"/activities",
	validator("query", ActivityQuerySchema),
	describeRoute({
		tags: ["Activities"],
		summary: "Get activities with pagination",
		description: "Retrieves activities for an organization with pagination",
		responses: {
			200: {
				description: "Activities retrieved successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ 
							activities: z.array(z.any()),
							pagination: z.object({
								total: z.number(),
								limit: z.number(),
								offset: z.number(),
								hasMore: z.boolean(),
							})
						})),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const query = c.req.valid("query");

			// Verify organization membership
			await verifyOrganizationMembership(query.organizationId, user.id);

			const whereConditions: any = {
				organizationId: query.organizationId,
			};

			if (query.recordId) {
				whereConditions.recordId = query.recordId;
			}

			const [activities, total] = await Promise.all([
				db.activity.findMany({
					where: whereConditions,
					include: ACTIVITY_INCLUDE,
					orderBy: {
						createdAt: "desc",
					},
					take: query.limit,
					skip: query.offset,
				}),
				db.activity.count({ where: whereConditions }),
			]);

			return c.json({
				activities,
				pagination: {
					total,
					limit: query.limit,
					offset: query.offset,
					hasMore: query.offset + query.limit < total,
				},
			});
		} catch (error) {
			logger.error("Failed to fetch activities:", error);
			return c.json({ error: "Failed to fetch activities" }, 500);
		}
	},
);

// Update activity
activitiesRouter.patch(
	"/activities/:id",
	validator("json", ActivityUpdateSchema),
	describeRoute({
		tags: ["Activities"],
		summary: "Update activity",
		description: "Updates an existing activity",
		responses: {
			200: {
				description: "Activity updated successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ activity: z.any() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const id = c.req.param("id");
			const data = c.req.valid("json");

			// Verify organization membership
			await verifyOrganizationMembership(data.organizationId, user.id);

			// Check if activity exists and user has permission
			const existingActivity = await db.activity.findFirst({
				where: {
					id,
					organizationId: data.organizationId,
				},
			});

			if (!existingActivity) {
				return c.json({ error: "Activity not found" }, 404);
			}

			// Check if user is the owner
			if (existingActivity.userId !== user.id) {
				return c.json({ error: "Not authorized to edit this activity" }, 403);
			}

			const updateData: any = { ...data };
			delete updateData.organizationId;
			
			// If message is being updated, mark as edited
			if (data.message && data.message !== existingActivity.message) {
				updateData.edited = true;
				updateData.editedAt = new Date();
			}

			// If resolved status is being changed, update resolver
			if (data.resolved !== undefined && data.resolved !== existingActivity.resolved) {
				updateData.resolvedBy = data.resolved ? user.id : null;
			}

			const activity = await db.activity.update({
				where: { id },
				data: updateData,
				include: ACTIVITY_INCLUDE,
			});

			logger.info(`Activity updated: ${id} by user ${user.id}`);
			return c.json({ activity });
		} catch (error) {
			logger.error("Failed to update activity:", error);
			return c.json({ error: "Failed to update activity" }, 500);
		}
	},
);

// Delete activity
activitiesRouter.delete(
	"/activities/:id",
	validator("query", z.object({ organizationId: z.string() })),
	describeRoute({
		tags: ["Activities"],
		summary: "Delete activity",
		description: "Deletes an activity and its replies",
		responses: {
			200: {
				description: "Activity deleted successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ success: z.boolean() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const id = c.req.param("id");
			const { organizationId } = c.req.valid("query");

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			// Check if activity exists and user has permission
			const existingActivity = await db.activity.findFirst({
				where: {
					id,
					organizationId,
				},
			});

			if (!existingActivity) {
				return c.json({ error: "Activity not found" }, 404);
			}

			// Check if user is the owner
			if (existingActivity.userId !== user.id) {
				return c.json({ error: "Not authorized to delete this activity" }, 403);
			}

			// Delete activity (cascades to replies)
			await db.activity.delete({
				where: { id },
			});

			logger.info(`Activity deleted: ${id} by user ${user.id}`);
			return c.json({ success: true });
		} catch (error) {
			logger.error("Failed to delete activity:", error);
			return c.json({ error: "Failed to delete activity" }, 500);
		}
	},
);

// Create activity reply
activitiesRouter.post(
	"/activities/:activityId/replies",
	validator("json", ActivityReplyCreateSchema),
	describeRoute({
		tags: ["Activities"],
		summary: "Create activity reply",
		description: "Creates a reply to an activity",
		responses: {
			201: {
				description: "Reply created successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ reply: z.any() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const activityId = c.req.param("activityId");
			const data = c.req.valid("json");

			// Verify organization membership
			await verifyOrganizationMembership(data.organizationId, user.id);

			// Check if activity exists and user has access
			const activity = await db.activity.findFirst({
				where: {
					id: activityId,
					organizationId: data.organizationId,
				},
			});

			if (!activity) {
				return c.json({ error: "Activity not found" }, 404);
			}

			// Get the activity with its creator to send notification
			const activityWithUser = await db.activity.findUnique({
				where: { id: activityId },
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							image: true,
						},
					},
				},
			});

			if (!activityWithUser || !activityWithUser.user) {
				return c.json({ error: "Activity or user not found" }, 404);
			}

			// Create the reply
			const reply = await db.activityReply.create({
				data: {
					message: data.message,
					activityId,
					userId: user.id,
					mentionedUsers: data.mentionedUsers,
				},
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							image: true,
						},
					},
				},
			});

			// Only send notification if the reply is from someone other than the original poster
			if (activityWithUser.userId !== user.id && activityWithUser.user.email) {
				try {
					// Get organization details for the email
					const organization = await db.organization.findUnique({
						where: { id: data.organizationId },
						select: { name: true, slug: true },
					});

					if (!organization) {
						logger.error("Organization not found for reply notification");
					} else {
						// Construct activity URL
						let activityUrl = `/app/${organization.slug}`;
						
						if (activity.recordType && activity.recordId) {
							// Use singular form of record type (remove trailing 's' if present)
							const recordType = activity.recordType.endsWith('s') ? 
								activity.recordType.slice(0, -1) : activity.recordType;
							activityUrl += `/${recordType}/${activity.recordId}`;
						}
						
						// Add highlight parameter for the reply
						activityUrl += `?highlight=${reply.id}`;

						// Send email notification
						const emailResult = await sendEmail({
							to: activityWithUser.user.email,
							templateId: 'replyNotification',
							context: {
								repliedByName: user.name || 'A team member',
								repliedByImage: user.image || undefined,
								message: data.message,
								activityUrl,
								organizationName: organization.name || 'Relio',
							},
						});

						if (!emailResult) {
							logger.error("Failed to send reply notification email");
						}
					}
				} catch (emailError) {
					logger.error("Error sending reply notification email:", emailError);
					// Don't fail the request if email sending fails
				}
			}

			logger.info(`Activity reply created: ${reply.id} by user ${user.id}`);
			return c.json({ reply }, 201);
		} catch (error) {
			logger.error("Failed to create activity reply:", error);
			return c.json({ error: "Failed to create activity reply" }, 500);
		}
	},
);

// Delete activity reply
activitiesRouter.delete(
	"/activities/replies/:replyId",
	validator("query", z.object({ organizationId: z.string() })),
	describeRoute({
		tags: ["Activities"],
		summary: "Delete activity reply",
		description: "Deletes an activity reply",
		responses: {
			200: {
				description: "Reply deleted successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ success: z.boolean() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const replyId = c.req.param("replyId");
			const { organizationId } = c.req.valid("query");

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			// Check if reply exists and user has permission
			const reply = await db.activityReply.findFirst({
				where: {
					id: replyId,
				},
				include: {
					activity: true,
				},
			});

			if (!reply || reply.activity.organizationId !== organizationId) {
				return c.json({ error: "Reply not found" }, 404);
			}

			// Check if user is the owner
			if (reply.userId !== user.id) {
				return c.json({ error: "Not authorized to delete this reply" }, 403);
			}

			await db.activityReply.delete({
				where: { id: replyId },
			});

			logger.info(`Activity reply deleted: ${replyId} by user ${user.id}`);
			return c.json({ success: true });
		} catch (error) {
			logger.error("Failed to delete activity reply:", error);
			return c.json({ error: "Failed to delete activity reply" }, 500);
		}
	},
);

// Schema for reaction validation
const ActivityReactionSchema = z.object({
	emoji: z.string(),
});

// Add reaction to activity
activitiesRouter.post(
	"/activities/:activityId/reactions",
	validator("json", ActivityReactionSchema),
	describeRoute({
		description: "Add or remove a reaction from an activity",
		tags: ["activities"],
	}),
	async (c) => {
		try {
			const { activityId } = c.req.param();
			const { emoji } = await c.req.json();
			const organizationId = c.req.query("organizationId");
			const user = c.get("user");

			if (!organizationId) {
				return c.json(
					{ error: "Organization ID is required for adding reactions" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			// Check if activity exists and belongs to organization
			const activity = await db.activity.findFirst({
				where: {
					id: activityId,
					organizationId,
				},
			});

			if (!activity) {
				return c.json({ error: "Activity not found" }, 404);
			}

			// Create or delete reaction
			const existingReaction = await db.activityReaction.findFirst({
				where: {
					activityId,
					userId: user.id,
					emoji,
				},
			});

			if (existingReaction) {
				// Remove reaction if it exists
				await db.activityReaction.delete({
					where: {
						id: existingReaction.id,
					},
				});
				logger.info(`Reaction removed from activity ${activityId}: ${emoji}`);
				return c.json({ action: "removed" });
			} else {
				// Add new reaction
				await db.activityReaction.create({
					data: {
						activityId,
						userId: user.id,
						emoji,
					},
				});
				logger.info(`Reaction added to activity ${activityId}: ${emoji}`);
				return c.json({ action: "added" });
			}
		} catch (error) {
			logger.error("Failed to handle activity reaction:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	}
);

// Get reactions for activity
activitiesRouter.get(
	"/activities/:activityId/reactions",
	describeRoute({
		description: "Get all reactions for an activity",
		tags: ["activities"],
	}),
	async (c) => {
		try {
			const { activityId } = c.req.param();
			const organizationId = c.req.query("organizationId");
			const user = c.get("user");

			if (!organizationId) {
				return c.json(
					{ error: "Organization ID is required for getting reactions" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			type ReactionWithUser = {
				id: string;
				emoji: string;
				activityId: string;
				userId: string;
				createdAt: Date;
				user: {
					id: string;
					name: string;
					image: string | null;
				};
			};

			// Get all reactions for the activity with user details
			const reactions = await db.activityReaction.findMany({
				where: {
					activityId,
					activity: {
						organizationId,
					},
				},
				include: {
					user: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
				orderBy: {
					createdAt: 'asc',
				},
			}) as ReactionWithUser[];

			// Group reactions by emoji
			const groupedReactions = reactions.reduce((acc: Record<string, { emoji: string; count: number; users: Array<{ id: string; name: string; image?: string | null }> }>, reaction) => {
				if (!acc[reaction.emoji]) {
					acc[reaction.emoji] = {
						emoji: reaction.emoji,
						count: 0,
						users: [],
					};
				}
				acc[reaction.emoji].count++;
				acc[reaction.emoji].users.push({
					id: reaction.user.id,
					name: reaction.user.name,
					image: reaction.user.image,
				});
				return acc;
			}, {});

			return c.json(Object.values(groupedReactions));
		} catch (error) {
			logger.error("Failed to get activity reactions:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	}
);

// Mail send endpoint
const MailSendSchema = z.object({
	to: z.string().email(),
	templateId: z.string().optional(),
	subject: z.string().optional(),
	text: z.string().optional(),
	html: z.string().optional(),
	context: z.record(z.unknown()).optional(),
	locale: z.string().optional(),
	from: z.string().optional(),
});

activitiesRouter.post(
	"/activities/mail/send",
	validator("json", MailSendSchema),
	describeRoute({
		tags: ["Mail"],
		summary: "Send an email",
		description: "Sends an email using a template or raw content",
		responses: {
			200: {
				description: "Email sent successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ success: z.boolean() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const data = c.req.valid("json");
			
			logger.info(`Mail send request from user ${user.id}`, { to: data.to, templateId: data.templateId });
			
			// Validate that either templateId or subject is provided
			if (!data.templateId && !data.subject) {
				return c.json({ error: "Either templateId or subject is required" }, 400);
			}
			
			// If using a template, context is required
			if (data.templateId && !data.context) {
				return c.json({ error: "Context is required when using a template" }, 400);
			}
			
			// Send the email using the mail package
			const result = await sendEmail(data as any);
			
			if (!result) {
				return c.json({ error: "Failed to send email" }, 500);
			}
			
			logger.info(`Email sent successfully to ${data.to}`);
			return c.json({ success: true });
		} catch (error) {
			logger.error("Failed to send email:", error);
			return c.json({ error: "Failed to send email" }, 500);
		}
	},
);