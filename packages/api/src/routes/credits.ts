import { resetAllUserCredits } from "@repo/ai/lib/credits";
import { db } from "@repo/database/server";
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { describeRoute } from "hono-openapi";
import { resolver } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../middleware/auth";

export const creditsRouter = new Hono()
	.basePath("/credits")
	.use(authMiddleware)
	.post(
		"/reset-all",
		describeRoute({
			tags: ["Credits"],
			summary: "Reset all user credits (Admin only)",
			description:
				"Reset credits for all users across all organizations - typically called by cron job",
			responses: {
				200: {
					description: "Credits reset successfully",
					content: {
						"application/json": {
							schema: resolver(
								z.object({
									message: z.string(),
									recordsReset: z.number(),
								}),
							),
						},
					},
				},
			},
		}),
		async (c) => {
			const user = c.get("user");

			// Only allow admins to reset all credits
			if (user.role !== "admin") {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			try {
				// Use the new resetAllUserCredits function that works with UserOrganizationCredits
				const { resetCount } = await resetAllUserCredits(db);

				return c.json({
					message: `Successfully reset credits for ${resetCount} user-organization pairs`,
					recordsReset: resetCount,
				});
			} catch (error) {
				console.error("Error resetting credits:", error);
				throw new HTTPException(500, {
					message: "Failed to reset credits",
				});
			}
		},
	)
	.post(
		"/reset/:userId/:organizationId",
		describeRoute({
			tags: ["Credits"],
			summary: "Reset specific user credits for an organization",
			description:
				"Reset credits for a specific user in a specific organization",
			responses: {
				200: {
					description: "User credits reset successfully",
					content: {
						"application/json": {
							schema: resolver(
								z.object({
									message: z.string(),
									newTotal: z.number(),
								}),
							),
						},
					},
				},
			},
		}),
		async (c) => {
			const { userId, organizationId } = c.req.param();
			const currentUser = c.get("user");

			// Only allow admins or users resetting their own credits
			if (currentUser.role !== "admin" && currentUser.id !== userId) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			try {
				// Import the resetUserCredits function
				const { resetUserCredits } = await import(
					"@repo/ai/lib/credits"
				);

				// Reset credits for this specific user-organization pair
				const defaultCredits = 10; // Default daily credits for free users
				await resetUserCredits(
					db,
					userId,
					organizationId,
					defaultCredits,
				);

				return c.json({
					message: "Credits reset successfully",
					newTotal: defaultCredits,
				});
			} catch (error) {
				console.error("Error resetting user credits:", error);
				throw new HTTPException(500, {
					message: "Failed to reset user credits",
				});
			}
		},
	);
