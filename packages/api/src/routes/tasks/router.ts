import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { TaskSchema } from "@repo/database/src/zod";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";

const CreateTaskInputSchema = TaskSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

export const tasksRouter = new Hono<{ Variables: { user: Session["user"] } }>();

tasksRouter.post("/tasks", authMiddleware, async (c) => {
	const body = await c.req.json();
	const parse = CreateTaskInputSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const data = parse.data;

	const allowedStatus = ["backlog", "todo", "in_progress", "review", "done"];
	const allowedPriority = ["no_priority", "urgent", "high", "medium", "low"];

	if (
		!data.status ||
		!allowedStatus.includes(data.status) ||
		!data.priority ||
		!allowedPriority.includes(data.priority)
	) {
		return c.json({ error: "Invalid status or priority value" }, 400);
	}

	try {
		if (!data.assigneeId && data.createdById) {
			data.assigneeId = data.createdById;
		}
		const task = await db.task.create({
			data: {
				title: data.title,
				description: data.description,
				createdById: data.createdById,
				dueDate: typeof data.dueDate === "number" ? data.dueDate : null,
				status: data.status,
				assigneeId: data.assigneeId,
				relatedObjectId: data.relatedObjectId || null,
				relatedObjectType: data.relatedObjectType || null,
				organizationId: data.organizationId,
				priority: data.priority,
				position: data.position,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		// Invalidate cache after creating task - Prisma Accelerate will handle this automatically
		// when the tags are specified in the cacheStrategy of subsequent queries
		
		// Also invalidate related object cache if applicable
		if (data.relatedObjectId && data.relatedObjectType) {
			// Comment: This line is removed as per the instructions
		}

		// Send notification if assigned to another user
		if (task.assigneeId && task.assigneeId !== task.createdById) {
			const assigner = c.get("user");
			if (!assigner) {
				console.warn("[API] Assigner (user) is not set in context");
			}
			await db.notification.create({
				data: {
					userId: task.assigneeId,
					type: "task_assigned",
					title: "You have been assigned a task",
					body: `${assigner?.name ?? "Someone"} assigned a task to you: ${task.title}`,
					data: {
						taskId: task.id,
						title: task.title,
						assigner: assigner?.name ?? null,
						assignerId: assigner?.id ?? null,
						assignerImage: assigner?.image ?? null,
						status: task.status,
						priority: task.priority,
					},
				},
			});
		}
		return c.json(task, 201);
	} catch (error) {
		console.error("[API] Failed to create task:", error);
		return c.json(
			{
				error: "Failed to create task",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Helper function to fetch related object details
async function fetchRelatedObjectDetails(relatedObjectId: string, relatedObjectType: string) {
	try {
		switch (relatedObjectType) {
			case 'contact':
				const contact = await db.contact.findUnique({
					where: { id: relatedObjectId },
					select: {
						id: true,
						firstName: true,
						lastName: true,
						email: true,
						title: true,
						image: true,
					},
				});
				if (contact) {
					// Handle email JSON array safely
					let emailAddress: string | undefined;
					if (contact.email && Array.isArray(contact.email) && contact.email.length > 0) {
						const firstEmail = contact.email[0] as any;
						emailAddress = firstEmail?.address || firstEmail?.email;
					}
					
					return {
						id: contact.id,
						name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact',
						type: 'contact',
						subtitle: emailAddress || contact.title || undefined,
					};
				}
				break;
			case 'company':
				const company = await db.company.findUnique({
					where: { id: relatedObjectId },
					select: {
						id: true,
						name: true,
						website: true,
						industry: true,
						logo: true,
					},
				});
				if (company) {
					return {
						id: company.id,
						name: company.name || 'Unnamed Company',
						type: 'company',
						subtitle: company.website || company.industry || undefined,
					};
				}
				break;
			case 'property':
				const property = await db.property.findUnique({
					where: { id: relatedObjectId },
					select: {
						id: true,
						name: true,
						propertyType: true,
						status: true,
						image: true,
					},
				});
				if (property) {
					return {
						id: property.id,
						name: property.name || 'Unnamed Property',
						type: 'property',
						subtitle: property.propertyType || property.status || undefined,
					};
				}
				break;
		}
	} catch (error) {
		console.warn(`Failed to fetch related ${relatedObjectType}:`, error);
	}
	return null;
}

// Optimized batch fetching for related objects
async function fetchRelatedObjectsBatch(tasks: any[]) {
	// Group tasks by related object type
	const contactIds = new Set<string>();
	const companyIds = new Set<string>();
	const propertyIds = new Set<string>();

	for (const task of tasks) {
		if (task.relatedObjectId && task.relatedObjectType) {
			switch (task.relatedObjectType) {
				case 'contact':
					contactIds.add(task.relatedObjectId);
					break;
				case 'company':
					companyIds.add(task.relatedObjectId);
					break;
				case 'property':
					propertyIds.add(task.relatedObjectId);
					break;
			}
		}
	}

	// Batch fetch all related objects with caching
	const [contacts, companies, properties] = await Promise.all([
		contactIds.size > 0 ? db.contact.findMany({
			where: { id: { in: Array.from(contactIds) } },
			select: {
				id: true,
				firstName: true,
				lastName: true,
				email: true,
				title: true,
				image: true,
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for reference data
				tags: Array.from(contactIds).map(id => `contact_${id}`),
			},
		}) : [],
		companyIds.size > 0 ? db.company.findMany({
			where: { id: { in: Array.from(companyIds) } },
			select: {
				id: true,
				name: true,
				website: true,
				industry: true,
				logo: true,
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for reference data
				tags: Array.from(companyIds).map(id => `company_${id}`),
			},
		}) : [],
		propertyIds.size > 0 ? db.property.findMany({
			where: { id: { in: Array.from(propertyIds) } },
			select: {
				id: true,
				name: true,
				propertyType: true,
				status: true,
				image: true,
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for reference data
				tags: Array.from(propertyIds).map(id => `property_${id}`),
			},
		}) : [],
	]);

	// Create lookup maps
	const contactMap = new Map(contacts.map(contact => [contact.id, {
		id: contact.id,
		name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact',
		type: 'contact',
		subtitle: (() => {
			if (contact.email && Array.isArray(contact.email) && contact.email.length > 0) {
				const firstEmail = contact.email[0] as any;
				return firstEmail?.address || firstEmail?.email || contact.title || undefined;
			}
			return contact.title || undefined;
		})(),
		image: contact.image,
	}]));

	const companyMap = new Map(companies.map(company => [company.id, {
		id: company.id,
		name: company.name || 'Unnamed Company',
		type: 'company',
		subtitle: company.website || company.industry || undefined,
	}]));

	const propertyMap = new Map(properties.map(property => [property.id, {
		id: property.id,
		name: property.name || 'Unnamed Property',
		type: 'property',
		subtitle: property.propertyType || property.status || undefined,
	}]));

	// Attach related objects to tasks
	return tasks.map(task => {
		if (task.relatedObjectId && task.relatedObjectType) {
			let relatedObject = null;
			switch (task.relatedObjectType) {
				case 'contact':
					relatedObject = contactMap.get(task.relatedObjectId);
					break;
				case 'company':
					relatedObject = companyMap.get(task.relatedObjectId);
					break;
				case 'property':
					relatedObject = propertyMap.get(task.relatedObjectId);
					break;
			}
			return { ...task, relatedObject };
		}
		return task;
	});
}

tasksRouter.get("/tasks", authMiddleware, async (c) => {
	const organizationId = c.req.query("organizationId");
	const relatedObjectId = c.req.query("relatedObjectId");
	const relatedObjectType = c.req.query("relatedObjectType");
	
	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		// Build where clause based on filters
		const whereClause: any = { organizationId };
		
		// Add filters for related object if provided
		if (relatedObjectId && relatedObjectType) {
			whereClause.relatedObjectId = relatedObjectId;
			whereClause.relatedObjectType = relatedObjectType;
		}

		const tasks = await db.task.findMany({
			where: whereClause,
			orderBy: { createdAt: "desc" },
			include: {
				assignee: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
			cacheStrategy: {
				ttl: 300, // 5 minutes
				tags: [`tasks_org_${organizationId}`, `org_${organizationId}`],
			},
		});

		// Convert MongoDB dates to ISO strings and handle _id fields
		const tasksWithFormattedData = tasks.map(task => {
			const taskData = task as any; // MongoDB data has additional fields
			
			// Helper function to handle MongoDB date format
			const formatDate = (dateValue: any): string | null => {
				if (!dateValue) return null;
				
				// Handle MongoDB date format: { "$date": "2025-07-03T00:49:20.138Z" }
				if (typeof dateValue === 'object' && dateValue.$date) {
					return new Date(dateValue.$date).toISOString();
				}
				
				// Handle regular Date objects
				if (dateValue instanceof Date) {
					return dateValue.toISOString();
				}
				
				// Handle string/number dates
				if (typeof dateValue === 'string' || typeof dateValue === 'number') {
					return new Date(dateValue).toISOString();
				}
				
				return null;
			};
			
			return {
				...taskData,
				id: taskData.id || taskData._id,
				dueDate: formatDate(taskData.dueDate),
				createdAt: formatDate(taskData.createdAt),
				updatedAt: formatDate(taskData.updatedAt),
				assignee: taskData.assignee ? {
					...taskData.assignee,
					id: taskData.assignee.id || taskData.assignee._id,
				} : null,
			};
		});

		// Optimize related objects fetching with batch queries and caching
		const tasksWithRelatedObjects = await fetchRelatedObjectsBatch(tasksWithFormattedData);

		return c.json(tasksWithRelatedObjects, 200);
	} catch (error) {
		console.error("[API] Failed to fetch tasks:", error);
		return c.json(
			{
				error: "Failed to fetch tasks",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

tasksRouter.patch("/tasks/:id", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const body = await c.req.json();

	const allowedStatus = ["backlog", "todo", "in_progress", "review", "done"];
	const allowedPriority = ["no_priority", "urgent", "high", "medium", "low"];

	if (body.status && !allowedStatus.includes(body.status)) {
		return c.json({ error: "Invalid status value" }, 400);
	}
	if (body.priority && !allowedPriority.includes(body.priority)) {
		return c.json({ error: "Invalid priority value" }, 400);
	}

	try {
		const updatedTask = await db.task.update({
			where: { id },
			data: {
				...body,
				dueDate: body.dueDate ? new Date(body.dueDate) : null,
				updatedAt: new Date(),
			},
		});

		// Get current user from context
		const user = c.get("user");
		if (!user) {
			console.warn("[API] Assigner (user) is not set in context");
		}
		// Send notification if assigned to another user
		if (updatedTask.assigneeId && updatedTask.assigneeId !== user?.id) {
			const assigner = user;
			await db.notification.create({
				data: {
					userId: updatedTask.assigneeId,
					type: "task_assigned",
					title: "Task assigned to you",
					body: `${assigner?.name ?? "Someone"} assigned a task to you: ${updatedTask.title}`,
					data: {
						taskId: updatedTask.id,
						title: updatedTask.title,
						assigner: assigner?.name ?? null,
						assignerId: assigner?.id ?? null,
						assignerImage: assigner?.image ?? null,
					},
				},
			});
		}
		
		return c.json(updatedTask, 200);
	} catch (error) {
		console.error("[API] Failed to update task:", error);
		return c.json(
			{
				error: "Failed to update task",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

tasksRouter.delete("/tasks/:id", authMiddleware, async (c) => {
	const id = c.req.param("id");
	try {
		const deleted = await db.task.delete({ where: { id } });
		if (!deleted) {
			return c.json({ error: "Task not found" }, 404);
		}
		return c.body(null, 204);
	} catch (error) {
		console.error("[API] Failed to delete task:", error);
		return c.json({ error: "Failed to delete task" }, 500);
	}
});

export type TasksRouter = typeof tasksRouter;
