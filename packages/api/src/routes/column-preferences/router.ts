import { db } from "@repo/database/server";
import { Hono } from "hono";
import { z } from "zod";

const OrgIdSchema = z.object({ organizationId: z.string() });
const OrgColSchema = z.object({
	organizationId: z.string(),
	column: z.string(),
});
const UpsertSchema = OrgColSchema.extend({
	trackTimeInStatus: z.boolean().optional(),
	showConfetti: z.boolean().optional(),
	hidden: z.boolean().optional(),
	targetTimeInStatus: z.number().nullable().optional(),
});

export const columnPreferencesRouter = new Hono().basePath(
	"/column-preferences",
);

// GET /column-preferences?organizationId=...
columnPreferencesRouter.get("/", async (c) => {
	const parse = OrgIdSchema.safeParse(c.req.query());
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const { organizationId } = parse.data;
	try {
		const prefs = await db.columnPreference.findMany({
			where: { organizationId },
			orderBy: { column: "asc" },
		});
		return c.json(prefs, 200);
	} catch (error) {
		return c.json(
			{
				error: "Failed to fetch column preferences",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// GET /column-preferences/single?organizationId=...&column=...
columnPreferencesRouter.get("/single", async (c) => {
	const parse = OrgColSchema.safeParse(c.req.query());
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const { organizationId, column } = parse.data;
	try {
		const pref = await db.columnPreference.findFirst({
			where: { organizationId, column },
		});
		return c.json(pref, 200);
	} catch (error) {
		return c.json(
			{
				error: "Failed to fetch column preference",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// PATCH /column-preferences (upsert)
columnPreferencesRouter.patch("/", async (c) => {
	const body = await c.req.json();
	const parse = UpsertSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const { organizationId, column, ...updates } = parse.data;
	try {
		const existing = await db.columnPreference.findFirst({
			where: { organizationId, column },
		});
		if (existing) {
			const updated = await db.columnPreference.update({
				where: { id: existing.id },
				data: updates,
			});
			return c.json(updated, 200);
		}
		const created = await db.columnPreference.create({
			data: {
				organizationId,
				column,
				trackTimeInStatus: updates.trackTimeInStatus ?? false,
				showConfetti: updates.showConfetti ?? false,
				hidden: updates.hidden ?? false,
				targetTimeInStatus: updates.targetTimeInStatus ?? null,
			},
		});
		return c.json(created, 201);
	} catch (error) {
		return c.json(
			{
				error: "Failed to upsert column preference",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});
