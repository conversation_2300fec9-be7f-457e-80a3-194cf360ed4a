import { db } from "@repo/database/server";
import { Hono } from "hono";

// Helper function to fetch related object details
async function fetchRelatedObjectDetails(objectId: string | null, objectType: string | null) {
	if (!objectId || !objectType) return null;
	
	try {
		switch (objectType) {
			case 'contact': {
				const contact = await db.contact.findUnique({
					where: { id: objectId },
					select: {
						id: true,
						firstName: true,
						lastName: true,
						email: true,
						title: true,
						image: true,
					},
				});
				if (contact) {
					return {
						id: contact.id,
						name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact',
						email: Array.isArray(contact.email) && contact.email.length > 0 
							? contact.email[0]?.address || contact.email[0]?.email || ''
							: '',
						title: contact.title,
						image: contact.image,
						type: 'contact',
					};
				}
				break;
			}
			case 'company': {
				const company = await db.company.findUnique({
					where: { id: objectId },
					select: {
						id: true,
						name: true,
						description: true,
						logo: true,
					},
				});
				if (company) {
					return {
						id: company.id,
						name: company.name || 'Unnamed Company',
						description: company.description,
						logo: company.logo,
						type: 'company',
					};
				}
				break;
			}
			case 'property': {
				const property = await db.property.findUnique({
					where: { id: objectId },
					select: {
						id: true,
						name: true,
						propertyType: true,
						status: true,
					},
				});
				if (property) {
					return {
						id: property.id,
						name: property.name || 'Unnamed Property',
						propertyType: property.propertyType,
						status: property.status,
						type: 'property',
					};
				}
				break;
			}
		}
	} catch (error) {
		console.error('Error fetching related object:', error);
	}
	
	return null;
}

// Optimized batch fetching for related objects
async function fetchRelatedObjectsBatch(notes: any[]) {
	// Group notes by related object type
	const contactIds = new Set<string>();
	const companyIds = new Set<string>();
	const propertyIds = new Set<string>();

	for (const note of notes) {
		if (note.objectId && note.objectType) {
			switch (note.objectType) {
				case 'contact':
					contactIds.add(note.objectId);
					break;
				case 'company':
					companyIds.add(note.objectId);
					break;
				case 'property':
					propertyIds.add(note.objectId);
					break;
			}
		}
	}

	// Batch fetch all related objects with caching
	const [contacts, companies, properties] = await Promise.all([
		contactIds.size > 0 ? db.contact.findMany({
			where: { id: { in: Array.from(contactIds) } },
			select: {
				id: true,
				firstName: true,
				lastName: true,
				email: true,
				title: true,
				image: true,
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for reference data
				tags: Array.from(contactIds).map(id => `contact_${id}`),
			},
		}) : [],
		companyIds.size > 0 ? db.company.findMany({
			where: { id: { in: Array.from(companyIds) } },
			select: {
				id: true,
				name: true,
				description: true,
				logo: true,
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for reference data
				tags: Array.from(companyIds).map(id => `company_${id}`),
			},
		}) : [],
		propertyIds.size > 0 ? db.property.findMany({
			where: { id: { in: Array.from(propertyIds) } },
			select: {
				id: true,
				name: true,
				propertyType: true,
				status: true,
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for reference data
				tags: Array.from(propertyIds).map(id => `property_${id}`),
			},
		}) : [],
	]);

	// Create lookup maps
	const contactMap = new Map(contacts.map(contact => [contact.id, {
		id: contact.id,
		name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact',
		email: Array.isArray(contact.email) && contact.email.length > 0 
			? contact.email[0]?.address || contact.email[0]?.email || ''
			: '',
		title: contact.title,
		image: contact.image,
		type: 'contact',
	}]));

	const companyMap = new Map(companies.map(company => [company.id, {
		id: company.id,
		name: company.name || 'Unnamed Company',
		description: company.description,
		logo: company.logo,
		type: 'company',
	}]));

	const propertyMap = new Map(properties.map(property => [property.id, {
		id: property.id,
		name: property.name || 'Unnamed Property',
		propertyType: property.propertyType,
		status: property.status,
		type: 'property',
	}]));

	// Attach related objects to notes
	return notes.map(note => {
		if (note.objectId && note.objectType) {
			let objectRecord = null;
			switch (note.objectType) {
				case 'contact':
					objectRecord = contactMap.get(note.objectId);
					break;
				case 'company':
					objectRecord = companyMap.get(note.objectId);
					break;
				case 'property':
					objectRecord = propertyMap.get(note.objectId);
					break;
			}
			return { 
				...note,
				objectRecord,
				createdBy: note.user ? {
					id: note.user.id,
					name: note.user.name,
					email: note.user.email,
					image: note.user.image,
				} : undefined,
			};
		}
		return { 
			...note,
			createdBy: note.user ? {
				id: note.user.id,
				name: note.user.name,
				email: note.user.email,
				image: note.user.image,
			} : undefined,
		};
	});
}

export const notesPublicRouter = new Hono();

notesPublicRouter.get("/note/:id", async (c) => {
	const id = c.req.param("id");

	try {
		const note = await db.note.findUnique({
			where: { id },
			include: {
				user: {
					select: { id: true, name: true, email: true, image: true },
				},
			},
			cacheStrategy: {
				ttl: 600, // 10 minutes - longer for public notes
				tags: [`note_${id}`, `public_note_${id}`],
			},
		});
		if (!note) return c.json({ error: "Note not found" }, 404);
		if (!note.isPublished || note.isArchived)
			return c.json({ error: "Note is not public" }, 403);

		// Use batch function for consistency
		const notesWithDetails = await fetchRelatedObjectsBatch([note]);
		const noteWithDetails = notesWithDetails[0];

		return c.json(noteWithDetails, 200);
	} catch (error) {
		return c.json({ error: "Failed to fetch note", details: error }, 500);
	}
});
