import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { NoteSchema } from "@repo/database/src/zod";
import { Hono } from "hono";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

// Helper function to fetch related object details
async function fetchRelatedObjectDetails(objectId: string | null, objectType: string | null) {
	if (!objectId || !objectType) return null;
	
	try {
		switch (objectType) {
			case 'contact': {
				const contact = await db.contact.findUnique({
					where: { id: objectId },
					select: {
						id: true,
						firstName: true,
						lastName: true,
						email: true,
						title: true,
						image: true,
					},
				});
				if (contact) {
					return {
						id: contact.id,
						name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact',
						email: Array.isArray(contact.email) && contact.email.length > 0 
							? contact.email[0]?.address || contact.email[0]?.email || ''
							: '',
						title: contact.title,
						image: contact.image,
						type: 'contact',
					};
				}
				break;
			}
			case 'company': {
				const company = await db.company.findUnique({
					where: { id: objectId },
					select: {
						id: true,
						name: true,
						description: true,
						logo: true,
					},
				});
				if (company) {
					return {
						id: company.id,
						name: company.name || 'Unnamed Company',
						description: company.description,
						logo: company.logo,
						type: 'company',
					};
				}
				break;
			}
			case 'property': {
				const property = await db.property.findUnique({
					where: { id: objectId },
					select: {
						id: true,
						name: true,
						propertyType: true,
						status: true,
					},
				});
				if (property) {
					return {
						id: property.id,
						name: property.name || 'Unnamed Property',
						propertyType: property.propertyType,
						status: property.status,
						type: 'property',
					};
				}
				break;
			}
		}
	} catch (error) {
		console.error('Error fetching related object:', error);
	}
	
	return null;
}

// Optimized batch fetching for related objects
async function fetchRelatedObjectsBatch(notes: any[]) {
	// Group notes by related object type
	const contactIds = new Set<string>();
	const companyIds = new Set<string>();
	const propertyIds = new Set<string>();

	for (const note of notes) {
		if (note.objectId && note.objectType) {
			switch (note.objectType) {
				case 'contact':
					contactIds.add(note.objectId);
					break;
				case 'company':
					companyIds.add(note.objectId);
					break;
				case 'property':
					propertyIds.add(note.objectId);
					break;
			}
		}
	}

	// Batch fetch all related objects with caching
	const [contacts, companies, properties] = await Promise.all([
		contactIds.size > 0 ? db.contact.findMany({
			where: { id: { in: Array.from(contactIds) } },
			select: {
				id: true,
				firstName: true,
				lastName: true,
				email: true,
				title: true,
				image: true,
			},

		}) : [],
		companyIds.size > 0 ? db.company.findMany({
			where: { id: { in: Array.from(companyIds) } },
			select: {
				id: true,
				name: true,
				description: true,
				logo: true,
			},

		}) : [],
		propertyIds.size > 0 ? db.property.findMany({
			where: { id: { in: Array.from(propertyIds) } },
			select: {
				id: true,
				name: true,
				propertyType: true,
				status: true,
			},

		}) : [],
	]);

	// Create lookup maps
	const contactMap = new Map(contacts.map(contact => [contact.id, {
		id: contact.id,
		name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact',
		email: Array.isArray(contact.email) && contact.email.length > 0 
			? contact.email[0]?.address || contact.email[0]?.email || ''
			: '',
		title: contact.title,
		image: contact.image,
		type: 'contact',
	}]));

	const companyMap = new Map(companies.map(company => [company.id, {
		id: company.id,
		name: company.name || 'Unnamed Company',
		description: company.description,
		logo: company.logo,
		type: 'company',
	}]));

	const propertyMap = new Map(properties.map(property => [property.id, {
		id: property.id,
		name: property.name || 'Unnamed Property',
		propertyType: property.propertyType,
		status: property.status,
		type: 'property',
	}]));

	// Attach related objects to notes
	return notes.map(note => {
		if (note.objectId && note.objectType) {
			let objectRecord = null;
			switch (note.objectType) {
				case 'contact':
					objectRecord = contactMap.get(note.objectId);
					break;
				case 'company':
					objectRecord = companyMap.get(note.objectId);
					break;
				case 'property':
					objectRecord = propertyMap.get(note.objectId);
					break;
			}
			return { 
				...note,
				objectRecord,
				createdBy: note.user ? {
					id: note.user.id,
					name: note.user.name,
					email: note.user.email,
					image: note.user.image,
				} : undefined,
			};
		}
		return { 
			...note,
			createdBy: note.user ? {
				id: note.user.id,
				name: note.user.name,
				email: note.user.email,
				image: note.user.image,
			} : undefined,
		};
	});
}

const CreateNoteInputSchema = z.object({
	title: z.string().min(1, "Title is required"),
	parentDocument: z.string().nullable().optional(),
	objectId: z.string().nullable().optional(),
	objectType: z.string().nullable().optional(),
	organizationId: z.string().min(1, "organizationId is required"),
	content: z.string().nullable().optional(),
});

const UpdateNoteInputSchema = z.object({
	title: z.string().optional(),
	content: z.string().nullable().optional(),
	coverImage: z.string().nullable().optional(),
	icon: z.string().nullable().optional(),
	isPublished: z.boolean().optional(),
	objectId: z.string().nullable().optional(),
	objectType: z.string().nullable().optional(),
});

export const notesRouter = new Hono<{ Variables: { user: Session["user"] } }>();

// Create note
notesRouter.post("/notes", authMiddleware, async (c) => {
	const user = c.get("user");
	const body = await c.req.json();
	const parse = CreateNoteInputSchema.safeParse(body);

	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const data = parse.data;

	try {
		const note = await db.note.create({
			data: {
				userId: user.id,
				title: data.title,
				parentDocument: data.parentDocument ?? null,
				isArchived: false,
				isPublished: false,
				objectId: data.objectId ?? null,
				objectType: data.objectType ?? null,
				orgId: data.organizationId,
				content: `{"type":"doc","content":[{"type":"paragraph"}]}`,
			},
			include: {
				user: {
					select: { id: true, name: true, email: true, image: true },
				},
			},
		});

		// Use batch function for consistency
		const notesWithDetails = await fetchRelatedObjectsBatch([note]);
		const noteWithDetails = notesWithDetails[0];

		return c.json(noteWithDetails, 201);
	} catch (error) {
		return c.json({ error: "Failed to create note", details: error }, 500);
	}
});

// Get all notes for user
notesRouter.get("/notes", authMiddleware, async (c) => {
	const user = c.get("user");
	const organizationId = c.req.query("organizationId");
	const objectId = c.req.query("objectId");
	const objectType = c.req.query("objectType");
	
	try {
		const whereCondition: any = { 
			userId: user.id, 
			isArchived: false 
		};
		
		// Add organization filter if provided
		if (organizationId) {
			whereCondition.orgId = organizationId;
		}
		
		// Add object filters if provided
		if (objectId && objectType) {
			whereCondition.objectId = objectId;
			whereCondition.objectType = objectType;
		}
		
		const notes = await db.note.findMany({
			where: whereCondition,
			orderBy: { createdAt: "desc" },
			include: {
				user: {
					select: { id: true, name: true, email: true, image: true },
				},
			},
		});

		const notesWithCreatedBy = await fetchRelatedObjectsBatch(notes);
		return c.json(notesWithCreatedBy, 200);
	} catch (error) {
		return c.json({ error: "Failed to fetch notes", details: error }, 500);
	}
});

// Get note by id
notesRouter.get("/notes/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	try {
		const note = await db.note.findUnique({
			where: { id },
			include: {
				user: {
					select: { id: true, name: true, email: true, image: true },
				},
			},

		});
		if (!note) return c.json({ error: "Note not found" }, 404);
		
		// Use batch function for consistency (even though it's just one note)
		const notesWithDetails = await fetchRelatedObjectsBatch([note]);
		const noteWithDetails = notesWithDetails[0];
		
		if (note.isPublished && !note.isArchived) return c.json(noteWithDetails, 200);
		if (note.userId !== user.id)
			return c.json({ error: "Unauthorized" }, 403);
		return c.json(noteWithDetails, 200);
	} catch (error) {
		return c.json({ error: "Failed to fetch note", details: error }, 500);
	}
});

// Update note
notesRouter.patch("/notes/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	const body = await c.req.json();
	const parse = UpdateNoteInputSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	try {
		const existing = await db.note.findUnique({ where: { id } });
		if (!existing) return c.json({ error: "Note not found" }, 404);
		if (existing.userId !== user.id)
			return c.json({ error: "Unauthorized" }, 403);
		const note = await db.note.update({
			where: { id },
			data: parse.data,
			include: {
				user: {
					select: { id: true, name: true, email: true, image: true },
				},
			},
		});
		

		const notesWithDetails = await fetchRelatedObjectsBatch([note]);
		const noteWithDetails = notesWithDetails[0];
		
		return c.json(noteWithDetails, 200);
	} catch (error) {
		return c.json({ error: "Failed to update note", details: error }, 500);
	}
});

// Archive note (and children recursively)
notesRouter.post("/notes/:id/archive", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	try {
		const note = await db.note.findUnique({ where: { id } });
		if (!note) return c.json({ error: "Note not found" }, 404);
		if (note.userId !== user.id)
			return c.json({ error: "Unauthorized" }, 403);
		// Recursively archive children
		const archiveChildren = async (parentId: string) => {
			const children = await db.note.findMany({
				where: { parentDocument: parentId },
			});
			for (const child of children) {
				await db.note.update({
					where: { id: child.id },
					data: { isArchived: true },
				});
				await archiveChildren(child.id);
			}
		};
		await db.note.update({ where: { id }, data: { isArchived: true } });
		await archiveChildren(id);
		return c.json({ success: true });
	} catch (error) {
		return c.json({ error: "Failed to archive note", details: error }, 500);
	}
});

// Restore note (and children recursively)
notesRouter.post("/notes/:id/restore", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	try {
		const note = await db.note.findUnique({ where: { id } });
		if (!note) return c.json({ error: "Note not found" }, 404);
		if (note.userId !== user.id)
			return c.json({ error: "Unauthorized" }, 403);
		// Recursively restore children
		const restoreChildren = async (parentId: string) => {
			const children = await db.note.findMany({
				where: { parentDocument: parentId },
			});
			for (const child of children) {
				await db.note.update({
					where: { id: child.id },
					data: { isArchived: false },
				});
				await restoreChildren(child.id);
			}
		};
		const updateData: any = { isArchived: false };
		if (note.parentDocument) {
			const parent = await db.note.findUnique({
				where: { id: note.parentDocument },
			});
			if (parent?.isArchived) updateData.parentDocument = null;
		}
		await db.note.update({ where: { id }, data: updateData });
		await restoreChildren(id);
		return c.json({ success: true });
	} catch (error) {
		return c.json({ error: "Failed to restore note", details: error }, 500);
	}
});

// Delete note
notesRouter.delete("/notes/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	try {
		const note = await db.note.findUnique({ where: { id } });
		if (!note) return c.json({ error: "Note not found" }, 404);
		if (note.userId !== user.id)
			return c.json({ error: "Unauthorized" }, 403);
		await db.favorite.deleteMany({
			where: { objectId: id, objectType: "note" },
		});
		await db.note.delete({ where: { id } });
		return c.json({ success: true });
	} catch (error) {
		return c.json({ error: "Failed to delete note", details: error }, 500);
	}
});

// Get trashed notes
// notesRouter.get("/notes/trash", authMiddleware, async (c) => {
//   const user = c.get("user");
//   try {
//     const notes = await db.note.findMany({
//       where: { userId: user.id, isArchived: true },
//       orderBy: { createdAt: "desc" },
//     });
//     return c.json(notes, 200);
//   } catch (error) {
//     return c.json({ error: "Failed to fetch trashed notes", details: error }, 500);
//   }
// });

// Get note count (not archived)
// notesRouter.get("/notes/count", authMiddleware, async (c) => {
//   const user = c.get("user");
//   try {
//     const count = await db.note.count({ where: { userId: user.id, isArchived: false } });
//     return c.json({ count }, 200);
//   } catch (error) {
//     return c.json({ error: "Failed to fetch note count", details: error }, 500);
//   }
// });

export type NotesRouter = typeof notesRouter;
