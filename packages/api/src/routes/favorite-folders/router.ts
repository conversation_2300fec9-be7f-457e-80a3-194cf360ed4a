import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { FavoriteFolderSchema } from "@repo/database/src/zod";
import { Hono } from "hono";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

const CreateFavoriteFolderInputSchema = FavoriteFolderSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

const UpdateFavoriteFolderInputSchema = z.object({
	name: z.string().optional(),
	isOpen: z
		.union([
			z.boolean(),
			z
				.string()
				.transform((v) =>
					v === "true" ? true : v === "false" ? false : v,
				),
		])
		.nullable()
		.optional(),
	position: z.number().nullable().optional(),
});

export const favoriteFoldersRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// List all favorite folders
favoriteFoldersRouter.get("/favorite-folders", authMiddleware, async (c) => {
	const user = c.get("user");
	const organizationId = c.req.query("organizationId");

	if (!organizationId) {
		return c.json([], 200);
	}
	try {
		const folders = await db.favoriteFolder.findMany({
			where: { userId: user.id, organizationId },
			orderBy: { position: "asc" },
		});
		return c.json(folders, 200);
	} catch (error) {
		return c.json([], 200);
	}
});

// Create a folder
favoriteFoldersRouter.post("/favorite-folders", authMiddleware, async (c) => {
	const user = c.get("user");
	const body = await c.req.json();
	const parse = CreateFavoriteFolderInputSchema.safeParse({
		...body,
		userId: user.id,
	});
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	try {
		const folder = await db.favoriteFolder.create({
			data: {
				...parse.data,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});
		return c.json(folder, 201);
	} catch (error) {
		return c.json(
			{
				error: "Failed to create folder",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// Update a folder (rename, open/close, reorder)
favoriteFoldersRouter.patch(
	"/favorite-folders/:id",
	authMiddleware,
	async (c) => {
		const id = c.req.param("id");
		const body = await c.req.json();
		const parse = UpdateFavoriteFolderInputSchema.safeParse(body);
		if (!parse.success) {
			return c.json(
				{ error: "Invalid input", details: parse.error.flatten() },
				400,
			);
		}
		try {
			// Ensure isOpen is boolean or null
			const updateData = { ...parse.data, updatedAt: new Date() };
			if (typeof updateData.isOpen === "string") {
				if (updateData.isOpen === "true") updateData.isOpen = true;
				else if (updateData.isOpen === "false")
					updateData.isOpen = false;
				else updateData.isOpen = null;
			}
			// Remove isOpen if not boolean or null
			if (
				Object.prototype.hasOwnProperty.call(updateData, "isOpen") &&
				!(
					typeof updateData.isOpen === "boolean" ||
					updateData.isOpen === null
				)
			) {
				delete updateData.isOpen;
			}
			// TypeScript: cast updateData as any to satisfy Prisma type
			const folder = await db.favoriteFolder.update({
				where: { id },
				data: updateData as any,
			});
			return c.json(folder, 200);
		} catch (error) {
			return c.json(
				{
					error: "Failed to update folder",
					details: error instanceof Error ? error.message : error,
				},
				500,
			);
		}
	},
);

// Delete a folder
favoriteFoldersRouter.delete(
	"/favorite-folders/:id",
	authMiddleware,
	async (c) => {
		const id = c.req.param("id");
		try {
			await db.favoriteFolder.delete({ where: { id } });
			return c.json({ success: true }, 200);
		} catch (error) {
			return c.json(
				{
					error: "Failed to delete folder",
					details: error instanceof Error ? error.message : error,
				},
				500,
			);
		}
	},
);
