import { db } from "@repo/database/server";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

// Schema for validation
const UpdateConfigSchema = z.object({
  defaultSharingLevel: z.enum(["metadata", "subject", "full"]).optional(),
  individualSharing: z.array(z.object({
    userId: z.string(),
    level: z.enum(["metadata", "subject", "full"])
  })).optional(),
  blockedEmails: z.array(z.string()).optional(),
  blockedDomains: z.array(z.string()).optional(),
  autoCreateContacts: z.boolean().optional(),
  autoCreateCompanies: z.boolean().optional(),
  isActive: z.boolean().optional()
});

const AddToBlocklistSchema = z.object({
  emails: z.array(z.string()).default([]),
  domains: z.array(z.string()).default([])
});

export const forwardingEmailConfigRouter = new Hono()
  .basePath("/forwarding-email-config")
  
  // Get forwarding email config for organization
  .get(
    "/:organizationId",
    authMiddleware,
    describeRoute({
      summary: "Get forwarding email configuration",
      tags: ["Forwarding Email"],
    }),
    async (c) => {
      try {
        const organizationId = c.req.param("organizationId");
        const user = c.get("user");
        
        // Check if user has access to this organization
        const membership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId
          }
        });

        if (!membership) {
          return c.json({ error: "Access denied" }, 403);
        }

        // Get or create config
        let config = await db.forwardingEmailConfig.findUnique({
          where: { organizationId }
        });

        if (!config) {
          // Create default config if it doesn't exist
          const organization = await db.organization.findUnique({
            where: { id: organizationId },
            select: { slug: true }
          });

          if (!organization) {
            return c.json({ error: "Organization not found" }, 404);
          }

          config = await db.forwardingEmailConfig.create({
            data: {
              organizationId,
              address: `${organization.slug}@inbox.reliocrm.com`,
              defaultSharingLevel: "full",
              blockedEmails: [],
              blockedDomains: [],
              autoCreateContacts: true,
              autoCreateCompanies: true,
              isActive: true
            }
          });
        }

        return c.json(config);
      } catch (error) {
        console.error("Error fetching forwarding email config:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  )

  // Update forwarding email config
  .patch(
    "/:organizationId",
    authMiddleware,
    validator("json", UpdateConfigSchema),
    describeRoute({
      summary: "Update forwarding email configuration",
      tags: ["Forwarding Email"],
    }),
    async (c) => {
      try {
        const organizationId = c.req.param("organizationId");
        const user = c.get("user");
        const updates = c.req.valid("json");
        
        // Check if user has admin access to this organization
        const membership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId,
            role: { in: ["owner", "admin"] }
          }
        });

        if (!membership) {
          return c.json({ error: "Owner or admin access required to modify forwarding email settings" }, 403);
        }

        // Get organization for address generation
        const organization = await db.organization.findUnique({
          where: { id: organizationId },
          select: { slug: true }
        });

        if (!organization) {
          return c.json({ error: "Organization not found" }, 404);
        }

        // Update config
        const config = await db.forwardingEmailConfig.upsert({
          where: { organizationId },
          update: updates,
          create: {
            organizationId,
            address: `${organization.slug}@inbox.reliocrm.com`,
            defaultSharingLevel: "full",
            blockedEmails: [],
            blockedDomains: [],
            autoCreateContacts: true,
            autoCreateCompanies: true,
            isActive: true,
            ...updates
          }
        });

        return c.json(config);
      } catch (error) {
        console.error("Error updating forwarding email config:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  )

  // Add to blocklist
  .post(
    "/:organizationId/blocklist",
    authMiddleware,
    validator("json", AddToBlocklistSchema),
    describeRoute({
      summary: "Add emails or domains to blocklist",
      tags: ["Forwarding Email"],
    }),
    async (c) => {
      try {
        const organizationId = c.req.param("organizationId");
        const user = c.get("user");
        const { emails, domains } = c.req.valid("json");
        
        // Check admin access
        const membership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId,
            role: { in: ["owner", "admin"] }
          }
        });

        if (!membership) {
          return c.json({ error: "Owner or admin access required to modify forwarding email settings" }, 403);
        }

        // Get current config
        const config = await db.forwardingEmailConfig.findUnique({
          where: { organizationId }
        });

        if (!config) {
          return c.json({ error: "Forwarding email config not found" }, 404);
        }

        // Add timestamp to new entries
        const currentDate = new Date().toISOString();
        const emailsWithDate = emails.map(email => `${email}|${currentDate}`);
        const domainsWithDate = domains.map(domain => `${domain}|${currentDate}`);

        // Add new entries to blocklist (avoiding duplicates by checking base entry without date)
        const existingEmails = config.blockedEmails.map(item => item.split('|')[0]);
        const existingDomains = config.blockedDomains.map(item => item.split('|')[0]);
        
        const newEmails = emailsWithDate.filter(item => !existingEmails.includes(item.split('|')[0]));
        const newDomains = domainsWithDate.filter(item => !existingDomains.includes(item.split('|')[0]));

        const updatedConfig = await db.forwardingEmailConfig.update({
          where: { organizationId },
          data: {
            blockedEmails: {
              set: [...config.blockedEmails, ...newEmails]
            },
            blockedDomains: {
              set: [...config.blockedDomains, ...newDomains]
            }
          }
        });

        return c.json(updatedConfig);
      } catch (error) {
        console.error("Error adding to blocklist:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  )

  // Remove from blocklist
  .delete(
    "/:organizationId/blocklist",
    authMiddleware,
    validator(
      "query", 
      z.object({
        email: z.string().optional(),
        domain: z.string().optional()
      })
    ),
    describeRoute({
      summary: "Remove email or domain from blocklist",
      tags: ["Forwarding Email"],
    }),
    async (c) => {
      try {
        const organizationId = c.req.param("organizationId");
        const user = c.get("user");
        const { email, domain } = c.req.valid("query");
        
        // Check admin access
        const membership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId,
            role: { in: ["owner", "admin"] }
          }
        });

        if (!membership) {
          return c.json({ error: "Owner or admin access required to modify forwarding email settings" }, 403);
        }

        // Get current config
        const config = await db.forwardingEmailConfig.findUnique({
          where: { organizationId }
        });

        if (!config) {
          return c.json({ error: "Forwarding email config not found" }, 404);
        }

        // Remove from blocklist (handle both old format and new format with dates)
        const updateData: any = {};
        
        if (email) {
          updateData.blockedEmails = {
            set: config.blockedEmails.filter(item => {
              const [itemEmail] = item.split('|');
              return itemEmail !== email;
            })
          };
        }
        
        if (domain) {
          updateData.blockedDomains = {
            set: config.blockedDomains.filter(item => {
              const [itemDomain] = item.split('|');
              return itemDomain !== domain;
            })
          };
        }

        const updatedConfig = await db.forwardingEmailConfig.update({
          where: { organizationId },
          data: updateData
        });

        return c.json(updatedConfig);
      } catch (error) {
        console.error("Error removing from blocklist:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  ); 