import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { FavoriteSchema } from "@repo/database/src/zod";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";

const CreateFavoriteInputSchema = FavoriteSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

const UpdateFavoriteInputSchema = FavoriteSchema.pick({
	folderId: true,
	position: true,
}).partial();

export const favoritesRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// List all favorites for a user/organization
favoritesRouter.get("/favorites", authMiddleware, async (c) => {
	const user = c.get("user");
	const organizationId = c.req.query("organizationId");
	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}
	try {
		const favorites = await db.favorite.findMany({
			where: { userId: user.id, organizationId },
			orderBy: { position: "asc" },
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						image: true,
					},
				},
			},
		});
		return c.json(favorites, 200);
	} catch (error) {
		return c.json(
			{
				error: "Failed to fetch favorites",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// Add a favorite
favoritesRouter.post("/favorites", authMiddleware, async (c) => {
	const user = c.get("user");
	const body = await c.req.json();
	const parse = CreateFavoriteInputSchema.safeParse({
		...body,
		userId: user.id,
	});
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	try {
		const favorite = await db.favorite.create({
			data: {
				...parse.data,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});
		return c.json(favorite, 201);
	} catch (error) {
		return c.json(
			{
				error: "Failed to create favorite",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// Update a favorite (move to folder, reorder)
favoritesRouter.patch("/favorites/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	const body = await c.req.json();
	const parse = UpdateFavoriteInputSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	try {
		const existingFavorite = await db.favorite.findFirst({
			where: { id, userId: user.id },
		});
		if (!existingFavorite) {
			return c.json({ error: "Favorite not found or unauthorized" }, 404);
		}

		const favorite = await db.favorite.update({
			where: { id },
			data: { ...parse.data, updatedAt: new Date() },
		});
		return c.json(favorite, 200);
	} catch (error) {
		return c.json(
			{
				error: "Failed to update favorite",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// Remove a favorite
favoritesRouter.delete("/favorites/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");
	try {
		const existingFavorite = await db.favorite.findFirst({
			where: { id, userId: user.id },
		});
		if (!existingFavorite) {
			return c.json({ error: "Favorite not found or unauthorized" }, 404);
		}

		await db.favorite.delete({ where: { id } });
		return c.json({ success: true }, 200);
	} catch (error) {
		return c.json(
			{
				error: "Failed to delete favorite",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});
