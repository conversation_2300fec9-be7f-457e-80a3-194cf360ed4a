import { db } from "@repo/database/server";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";

export const modelRouter = new Hono()
	.basePath("/models")
	.use(adminMiddleware)

	.get(
		"/",
		describeRoute({
			tags: ["Administration"],
			summary: "Get all AI models",
		}),
		async (c) => {
			try {
				const models = await db.model.findMany({
					orderBy: [
						{ isDisabled: "asc" },
						{ isPremium: "asc" },
						{ name: "asc" },
					],
				});

				return c.json(
					models.map((model) => ({
						id: model.id,
						name: model.name,
						model: model.model,
						provider: model.provider,
						icon: model.icon,
						capabilities: model.capabilities,
						description: model.description,
						isPremium: model.isPremium,
						isDisabled: model.isDisabled,
						cost: model.cost,
						createdAt: model.createdAt.toISOString(),
						updatedAt: model.updatedAt.toISOString(),
					})),
				);
			} catch (error) {
				console.error("[Admin Models] Error fetching models:", error);
				return c.json({ error: "Failed to fetch models" }, 500);
			}
		},
	);
