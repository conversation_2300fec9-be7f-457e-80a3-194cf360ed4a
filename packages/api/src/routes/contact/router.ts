import { config } from "@repo/config";
import { logger } from "@repo/logs";
import { sendEmail } from "@repo/mail";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { localeMiddleware } from "../../middleware/locale";
import { contactFormSchema } from "./types";

export const contactRouter = new Hono().basePath("/contact").post(
	"/",
	localeMiddleware,
	validator("form", contactFormSchema),
	describeRoute({
		tags: ["Contact"],
		summary: "Send a message from the contact form",
		description: "Send a message with an email and name",
		responses: {
			204: {
				description: "Message sent",
			},
			400: {
				description: "Could not send message",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
		},
	}),
	async (c) => {
		const { email, name, message, companyName, employeeCount } =
			c.req.valid("form");
		const locale = c.get("locale");

		const text = [
			`Name: ${name}`,
			`Email: ${email}`,
			companyName ? `Company: ${companyName}` : null,
			employeeCount ? `Employees: ${employeeCount}` : null,
			"",
			`Message: ${message}`,
		]
			.filter(Boolean)
			.join("\n");

		const html = [
			`<p><strong>Name:</strong> ${name}</p>`,
			`<p><strong>Email:</strong> ${email}</p>`,
			companyName
				? `<p><strong>Company:</strong> ${companyName}</p>`
				: "",
			employeeCount
				? `<p><strong>Employees:</strong> ${employeeCount}</p>`
				: "",
			`<p><strong>Message:</strong><br/>${message.replace(/\n/g, "<br/>")}</p>`,
		]
			.filter(Boolean)
			.join("\n");

		try {
			await sendEmail({
				to: config.contactForm.to,
				locale,
				subject: config.contactForm.subject,
				text,
				html,
			});

			return c.body(null, 204);
		} catch (error) {
			logger.error(error);
			return c.json({ error: "Could not send email" }, 500);
		}
	},
);
