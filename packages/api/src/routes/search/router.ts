import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { Hono } from "hono";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

const SearchQuerySchema = z.object({
	query: z.string().min(1, "Search query is required"),
	organizationId: z.string().min(1, "Organization ID is required"),
	type: z
		.enum(["all", "task", "note", "contact", "user", "property"])
		.optional()
		.default("all"),
	limit: z.coerce.number().min(1).max(50).optional().default(20),
	offset: z.coerce.number().min(0).optional().default(0),
});

export const searchRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

searchRouter.get("/search/debug", authMiddleware, async (c) => {
	const user = c.get("user");
	const organizationId = c.req.query("organizationId");

	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		const allTasks = await db.task.findMany({
			where: { organizationId },
			select: { id: true, title: true, description: true },
		});

		const allNotes = await db.note.findMany({
			where: { orgId: organizationId },
			select: { id: true, title: true, content: true, isArchived: true },
		});

		return c.json({
			organizationId,
			userId: user.id,
			tasks: allTasks.length,
			notes: allNotes.length,
			sampleTasks: allTasks.slice(0, 3),
			sampleNotes: allNotes.slice(0, 3),
		});
	} catch (error) {
		return c.json({ error: "Debug failed", details: error }, 500);
	}
});

searchRouter.get("/search", authMiddleware, async (c) => {
	const user = c.get("user");
	const params = c.req.query();

	const parse = SearchQuerySchema.safeParse(params);
	if (!parse.success) {
		return c.json(
			{
				error: "Invalid search parameters",
				details: parse.error.flatten(),
			},
			400,
		);
	}

	const { query, organizationId, type, limit, offset } = parse.data;

	try {
		// Verify user has access to the organization and get the organization details
		const membership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: organizationId,
			},
			include: {
				organization: {
					select: {
						slug: true,
					},
				},
			},
		});

		// First, let's test if we can find ANY tasks and notes for this organization
		const allTasks = await db.task.findMany({
			where: { organizationId },
			select: { id: true, title: true, description: true },
			take: 5,
		});

		const allNotes = await db.note.findMany({
			where: { orgId: organizationId },
			select: { id: true, title: true, content: true },
			take: 5,
		});

		const allProperties = await db.property.findMany({
			where: { organizationId },
			select: { 
				id: true, name: true, image: true,
				location: {
					select: {
						address: true,
						location: true
					}
				},
				createdAt: true,
				updatedAt: true,
				creator: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
			take: 5,
		});

		if (!membership) {
			return c.json({ error: "Access denied to organization" }, 403);
		}

		const organizationSlug = membership.organization.slug;

		const results: {
			tasks: any[];
			notes: any[];
			contacts: any[];
			users: any[];
			properties: any[];
			total: number;
		} = {
			tasks: [],
			notes: [],
			contacts: [],
			users: [],
			properties: [],
			total: 0,
		};

		// Search tasks if type is "all" or "tasks"
		if (type === "all" || type === "task") {
			const tasks = await db.task.findMany({
				where: {
					organizationId: organizationId,
					OR: [
						{
							title: {
								contains: query,
								mode: "insensitive",
							},
						},
						{
							description: {
								contains: query,
								mode: "insensitive",
							},
						},
					],
				},
				include: {
					assignee: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
					createdBy: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
				orderBy: {
					updatedAt: "desc",
				},
				take: type === "task" ? limit : Math.ceil(limit / 2),
				skip: type === "task" ? offset : 0,
				cacheStrategy: {
					ttl: 120, // 2 minutes cache for search results
					tags: [`search_tasks_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			results.tasks = tasks.map((task) => ({
				...task,
				type: "task",
				searchResultType: "task",
				url: `/app/${organizationSlug}/tasks`,
			}));
		}

		if (type === "all" || type === "note") {
			const notes = await db.note.findMany({
				where: {
					orgId: organizationId,
					isArchived: false,
					OR: [
						{
							title: {
								contains: query,
								mode: "insensitive",
							},
						},
						{
							content: {
								contains: query,
								mode: "insensitive",
							},
						},
					],
				},
				select: {
					id: true,
					title: true,
					content: true,
					icon: true,
					coverImage: true,
					isPublished: true,
					objectId: true,
					objectType: true,
					createdAt: true,
					updatedAt: true,
					user: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
				orderBy: {
					updatedAt: "desc",
				},
				take: type === "note" ? limit : Math.ceil(limit / 2),
				skip: type === "note" ? offset : 0,
				cacheStrategy: {
					ttl: 120, // 2 minutes cache for search results
					tags: [`search_notes_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			results.notes = notes.map((note) => ({
				...note,
				type: "note",
				searchResultType: "note",
				url: `/app/${organizationSlug}/notes/${note.id}`,
				createdBy: (note as any).user,
			}));
		}

		// Search contacts
		if (type === "all" || type === "contact") {
			// Multi-word search support
			const words = query.split(/\s+/).filter(Boolean);
			const contactWhere = {
				organizationId: organizationId,
				isDeleted: false,
				AND: words.map((word) => ({
					OR: [
						{ firstName: { contains: word, mode: "insensitive" as const } },
						{ lastName: { contains: word, mode: "insensitive" as const } },
						{ title: { contains: word, mode: "insensitive" as const } },
						{ summary: { contains: word, mode: "insensitive" as const } },
					],
				})),
			};
			const contacts = await db.contact.findMany({
				where: contactWhere,
				select: {
					id: true,
					firstName: true,
					lastName: true,
					title: true,
					status: true,
					image: true,
					summary: true,
					createdAt: true,
					updatedAt: true,
					email: true,
					phone: true,
					company: {
						select: {
							id: true,
							name: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
				orderBy: {
					updatedAt: "desc",
				},
				take: type === "contact" ? limit : Math.ceil(limit / 3),
				skip: type === "contact" ? offset : 0,
				cacheStrategy: {
					ttl: 120, // 2 minutes cache for search results
					tags: [`search_contacts_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			results.contacts = contacts.map((contact) => {
				const name =
					`${contact.firstName || ""} ${contact.lastName || ""}`.trim();
				const primaryEmail =
					Array.isArray(contact.email) && contact.email.length > 0
						? (
								contact.email.find((e: any) => e.isPrimary) ||
								contact.email[0]
							)?.address || contact.email[0]?.value
						: undefined;
				const primaryPhone =
					Array.isArray(contact.phone) && contact.phone.length > 0
						? (
								contact.phone.find((p: any) => p.isPrimary) ||
								contact.phone[0]
							)?.number || contact.phone[0]?.value
						: undefined;

				return {
					...contact,
					type: "contact",
					searchResultType: "contact",
					title: name || "Unknown Contact",
					fullName: name || "Unknown Contact",
					email: primaryEmail,
					phone: primaryPhone,
					jobTitle: contact.title,
					description: contact.summary,
					url: `/app/${organizationSlug}/contacts/${contact.id}`,
					avatarUrl: contact.image,
					createdBy: (contact as any).creator,
				};
			});
		}

		// Search users if type is "all" or "users" and user is admin
		if ((type === "all" || type === "user") && 
		    (await isGlobalAdmin(user.id) || await isUserAdminInOrganization(user.id, organizationId))) {
			const users = await db.member.findMany({
				where: {
					organizationId: organizationId,
					user: {
						OR: [
							{
								name: {
									contains: query,
									mode: "insensitive",
								},
							},
							{
								email: {
									contains: query,
									mode: "insensitive",
								},
							},
							{
								username: {
									contains: query,
									mode: "insensitive",
								},
							},
						],
					},
				},
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							username: true,
							image: true,
							createdAt: true,
							updatedAt: true,
						},
					},
				},
				orderBy: {
					createdAt: "desc",
				},
				take: type === "user" ? limit : Math.ceil(limit / 4),
				skip: type === "user" ? offset : 0,
			});

			results.users = users.map((member: any) => ({
				id: member.user.id,
				title: member.user.name,
				name: member.user.name,
				email: member.user.email,
				username: member.user.username,
				role: member.role,
				avatarUrl: member.user.image,
				memberSince: member.createdAt,
				type: "user",
				searchResultType: "user",
				url: `/app/${organizationSlug}/settings/members`,
				createdAt: member.user.createdAt,
				updatedAt: member.user.updatedAt,
			}));
		}

		// Search properties
		if (type === "all" || type === "property") {
			const propertiesByBasicFields = await db.property.findMany({
				where: {
					organizationId: organizationId,
					isDeleted: false,
					OR: [
						{
							name: {
								contains: query,
								mode: "insensitive",
							},
						},
						{
							status: {
								contains: query,
								mode: "insensitive",
							},
						},
						{
							propertyType: {
								contains: query,
								mode: "insensitive",
							},
						},
						{
							market: {
								contains: query,
								mode: "insensitive",
							},
						},
					],
				},
				include: {
					location: {
						select: {
							address: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
				orderBy: {
					updatedAt: "desc",
				},
			});

			// Second, search properties by address through PropertyLocation
			const propertyLocationMatches = await db.propertyLocation.findMany({
				where: {
					property: {
						organizationId: organizationId,
						isDeleted: false,
					},
				},
				include: {
					property: {
						include: {
							creator: {
								select: {
									id: true,
									name: true,
									image: true,
								},
							},
						},
					},
				},
			});

			// Filter property locations by address in memory (for now, until we find a better MongoDB JSON query approach)
			const matchingPropertiesByAddress = propertyLocationMatches
				.filter(loc => {
					if (!loc.address) return false;
					const address = loc.address as any;
					const searchLower = query.toLowerCase();
					return (
						(address.street || "").toLowerCase().includes(searchLower) ||
						(address.city || "").toLowerCase().includes(searchLower) ||
						(address.state || "").toLowerCase().includes(searchLower) ||
						(address.zip || "").toLowerCase().includes(searchLower) ||
						(address.county || "").toLowerCase().includes(searchLower)
					);
				})
				.map(loc => ({
					...loc.property,
					location: loc,
				}));

			const allProperties = [...propertiesByBasicFields, ...matchingPropertiesByAddress];
			const uniqueProperties = Array.from(
				new Map(allProperties.map(item => [item.id, item])).values()
			).sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

			// Apply pagination
			const paginatedProperties = uniqueProperties.slice(
				type === "property" ? offset : 0,
				type === "property" ? offset + limit : Math.ceil(limit / 3)
			);

			results.properties = paginatedProperties.map((property: any) => {
				// Extract address info for display
				const address = property.location?.address as any;
				let addressString = "";
				if (address) {
					const parts = [
						address.street,
						address.city,
						address.state,
						address.zip
					].filter(Boolean);
					addressString = parts.join(", ");
				}

				return {
					id: property.id,
					name: property.name,
					image: property.image,
					status: property.status,
					propertyType: property.propertyType,
					market: property.market,
					createdAt: property.createdAt,
					updatedAt: property.updatedAt,
					type: "property",
					searchResultType: "property",
					title: property.name,
					description: addressString || property.status,
					// Keep the proper location structure for the frontend
					location: property.location ? {
						address: property.location.address,
						location: property.location.location
					} : undefined,
					url: `/app/${organizationSlug}/properties/${property.id}`,
					avatarUrl: property.image,
					creator: property.creator,
				};
			});

		}

		const taskCount =
			type === "note" || type === "contact" || type === "user" || type === "property"
				? 0
				: await db.task.count({
						where: {
							organizationId: organizationId,
							OR: [
								{
									title: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									description: {
										contains: query,
										mode: "insensitive",
									},
								},
							],
						},
					});

		const noteCount =
			type === "task" || type === "contact" || type === "user" || type === "property"
				? 0
				: await db.note.count({
						where: {
							orgId: organizationId,
							isArchived: false,
							OR: [
								{
									title: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									content: {
										contains: query,
										mode: "insensitive",
									},
								},
							],
						},
					});

		const contactCount =
			type === "task" || type === "note" || type === "user" || type === "property"
				? 0
				: await db.contact.count({
						where: {
							organizationId: organizationId,
							isDeleted: false,
							OR: [
								{
									firstName: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									lastName: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									title: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									summary: {
										contains: query,
										mode: "insensitive",
									},
								},
							],
						},
					});

		const userCount =
			(type === "task" || type === "note" || type === "contact" || type === "property") ||
			!(await isGlobalAdmin(user.id) || await isUserAdminInOrganization(user.id, organizationId))
				? 0
				: await db.member.count({
						where: {
							organizationId: organizationId,
							user: {
								OR: [
									{
										name: {
											contains: query,
											mode: "insensitive",
										},
									},
									{
										email: {
											contains: query,
											mode: "insensitive",
										},
									},
									{
										username: {
											contains: query,
											mode: "insensitive",
										},
									},
								],
							},
						},
					});

		const propertyCount =
			type === "task" || type === "note" || type === "contact" || type === "user"
				? 0
				: await db.property.count({
						where: {
							organizationId: organizationId,
							isDeleted: false,
							OR: [
								{
									name: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									status: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									propertyType: {
										contains: query,
										mode: "insensitive",
									},
								},
								{
									market: {
										contains: query,
										mode: "insensitive",
									},
								},
							],
						},
					});

		// Calculate total count (property count now includes address matches from the enhanced search above)
		const actualPropertyCount = type === "all" || type === "property" 
			? results.properties.length 
			: propertyCount;

		results.total = taskCount + noteCount + contactCount + userCount + actualPropertyCount;

		let combinedResults = [];
		if (type === "all") {
			combinedResults = [
				...results.tasks,
				...results.notes,
				...results.contacts,
				...results.users,
				...results.properties,
			]
				.sort(
					(a, b) =>
						new Date(b.updatedAt).getTime() -
						new Date(a.updatedAt).getTime(),
				)
				.slice(offset, offset + limit);
		} else if (type === "task") {
			combinedResults = results.tasks;
		} else if (type === "note") {
			combinedResults = results.notes;
		} else if (type === "contact") {
			combinedResults = results.contacts;
		} else if (type === "user") {
			combinedResults = results.users;
		} else if (type === "property") {
			combinedResults = results.properties;
		}

		return c.json(
			{
				results: combinedResults,
				pagination: {
					total: results.total,
					limit,
					offset,
					hasMore: offset + limit < results.total,
				},
				counts: {
					tasks: taskCount,
					notes: noteCount,
					contacts: contactCount,
					users: userCount,
					properties: propertyCount,
				},
			},
			200,
		);
	} catch (error) {
		console.error("[API] Search failed:", error);
		return c.json(
			{
				error: "Search failed",
				details: error instanceof Error ? error.message : error,
			},
			500,
		);
	}
});

// Helper function to check if user is admin in organization
const isUserAdminInOrganization = async (userId: string, organizationId: string): Promise<boolean> => {
	const membership = await db.member.findFirst({
		where: {
			userId,
			organizationId,
		},
		select: {
			role: true,
		},
	});
	
	return membership?.role === "admin" || membership?.role === "owner";
};

// Helper function to check if user is global admin
const isGlobalAdmin = async (userId: string): Promise<boolean> => {
	const user = await db.user.findUnique({
		where: { id: userId },
		select: { role: true },
	});
	
	return user?.role === "admin";
};

export type SearchRouter = typeof searchRouter;
