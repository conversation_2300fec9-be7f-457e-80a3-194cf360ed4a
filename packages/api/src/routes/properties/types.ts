import { z } from "zod";


export const PropertyCreateSchema = z.object({
	// Basic Info
	name: z.string().min(1),
	organizationId: z.string().min(1),
	recordType: z.string().default("property"),
	image: z.string().optional(),
	
	// Property Classification
	propertyType: z.string().optional(),
	propertySubType: z.string().optional(),
	market: z.string().optional(),
	subMarket: z.string().optional(),
	listingId: z.string().optional(),
	status: z.string().optional(),
	
	// REA (Real Estate API) Integration
	reaId: z.string().optional(),
	
	// Simple relationships
	tags: z.array(z.string()).default([]),
	lists: z.array(z.string()).default([]),
	linkedContacts: z.array(z.string()).default([]),
	linkedCompanies: z.array(z.string()).default([]),
	tasks: z.array(z.string()).default([]),
	
	// Location & Address
	address: z.object({
		street: z.string().optional(),
		street2: z.string().optional(),
		city: z.string().optional(),
		state: z.string().optional(),
		zip: z.string().optional(),
		county: z.string().optional(),
		country: z.string().optional(),
	}).optional(),
	location: z.object({
		type: z.literal("Point"),
		coordinates: z.tuple([z.number(), z.number()]), // [longitude, latitude]
	}).optional(),
	website: z.string().optional(),
	
	// Geographic details
	neighborhood: z.any().optional(), // JSON field
	subdivision: z.string().optional(),
	lotNumber: z.string().optional(),
	parcelNumber: z.string().optional(),
	zoning: z.string().optional(),
	
	// Physical Details
	yearBuilt: z.number().optional(),
	squareFootage: z.number().optional(),
	units: z.number().optional(),
	floors: z.number().optional(),
	structures: z.number().optional(),
	bedrooms: z.number().optional(),
	bathrooms: z.number().optional(),
	roomsCount: z.number().optional(),
	buildingSquareFeet: z.number().optional(),
	garageSquareFeet: z.number().optional(),
	livingSquareFeet: z.number().optional(),
	lotSquareFeet: z.number().optional(),
	lotSize: z.number().optional(),
	lotType: z.string().optional(),
	lotAcres: z.number().optional(),
	construction: z.string().optional(),
	primaryUse: z.string().optional(),
	propertyUse: z.string().optional(),
	class: z.string().optional(),
	parking: z.string().optional(),
	parkingSpaces: z.number().optional(),
	garageType: z.string().optional(),
	heatingType: z.string().optional(),
	meterType: z.string().optional(),
	legalDescription: z.string().optional(),
	
	// Financial Information
	price: z.number().optional(),
	estimatedValue: z.number().optional(),
	pricePerSquareFoot: z.number().optional(),
	equity: z.number().optional(),
	equityPercent: z.number().optional(),
	estimatedEquity: z.number().optional(),
	saleDate: z.date().optional(),
	salePrice: z.number().optional(),
	lastSalePrice: z.number().optional(),
	lastSaleDate: z.date().optional(),
	landValue: z.number().optional(),
	buildingValue: z.number().optional(),
	cap: z.number().optional(),
	exchange: z.boolean().optional(),
	exchangeId: z.string().optional(),
	taxInfo: z.any().optional(), // JSON field for tax information
	
	// Boolean Flags
	absenteeOwner: z.boolean().optional(),
	inStateAbsenteeOwner: z.boolean().optional(),
	outOfStateAbsenteeOwner: z.boolean().optional(),
	ownerOccupied: z.boolean().optional(),
	corporateOwned: z.boolean().optional(),
	vacant: z.boolean().optional(),
	mobileHome: z.boolean().optional(),
	carport: z.boolean().optional(),
	auction: z.boolean().optional(),
	cashBuyer: z.boolean().optional(),
	investorBuyer: z.boolean().optional(),
	freeClear: z.boolean().optional(),
	highEquity: z.boolean().optional(),
	privateLender: z.boolean().optional(),
	deedInLieu: z.boolean().optional(),
	quitClaim: z.boolean().optional(),
	sheriffsDeed: z.boolean().optional(),
	warrantyDeed: z.boolean().optional(),
	inherited: z.boolean().optional(),
	spousalDeath: z.boolean().optional(),
	lien: z.boolean().optional(),
	taxLien: z.boolean().optional(),
	preForeclosure: z.boolean().optional(),
	trusteeSale: z.boolean().optional(),
	floodZone: z.boolean().optional(),
	
	// MLS Data
	mlsActive: z.boolean().optional(),
	mlsCancelled: z.boolean().optional(),
	mlsFailed: z.boolean().optional(),
	mlsHasPhotos: z.boolean().optional(),
	mlsPending: z.boolean().optional(),
	mlsSold: z.boolean().optional(),
	mlsDaysOnMarket: z.number().optional(),
	mlsListingPrice: z.number().optional(),
	mlsListingPricePerSquareFoot: z.number().optional(),
	mlsSoldPrice: z.number().optional(),
	mlsStatus: z.string().optional(),
	mlsType: z.string().optional(),
	mlsListingDate: z.string().optional(),
	
	// Legal & Environmental
	floodZoneDescription: z.string().optional(),
	floodZoneType: z.string().optional(),
	noticeType: z.string().optional(),
	lastUpdateDate: z.string().optional(),
	
	// Demographics
	fmrEfficiency: z.number().optional(),
	fmrFourBedroom: z.number().optional(),
	fmrOneBedroom: z.number().optional(),
	fmrThreeBedroom: z.number().optional(),
	fmrTwoBedroom: z.number().optional(),
	fmrYear: z.number().optional(),
	hudAreaCode: z.string().optional(),
	hudAreaName: z.string().optional(),
	medianIncome: z.number().optional(),
	suggestedRent: z.number().optional(),
	
	// Complex nested data from REA API
	reaApiData: z.object({
		propertyInfo: z.any().optional(),
		demographics: z.any().optional(),
		saleHistory: z.array(z.any()).optional(),
		currentMortgages: z.array(z.any()).optional(),
		mortgageHistory: z.array(z.any()).optional(),
		foreclosureInfo: z.array(z.any()).optional(),
		mlsHistory: z.array(z.any()).optional(),
		lotInfo: z.any().optional(),
		taxInfo: z.any().optional(),
		ownerInfo: z.any().optional(),
		neighborhood: z.any().optional(),
	}).optional(),
	
	// Legacy/Optional fields
	description: z.string().optional(),
	customFields: z.any().optional(),
});

export const PropertyUpdateSchema = PropertyCreateSchema.omit({
	organizationId: true,
}).partial();

export const PropertyQuerySchema = z.object({
	organizationId: z.string().min(1),
	limit: z.number().int().positive().max(100).optional(),
	offset: z.number().int().min(0).optional(),
	search: z.string().optional(),
	propertyType: z.string().optional(),
	status: z.string().optional(),
});

// Related entity schemas
export const PropertyUnitMixCreateSchema = z.object({
	propertyId: z.string().min(1),
	organizationId: z.string().min(1),
	name: z.string().min(1),
	units: z.number().optional(),
	minSquareFootage: z.number().optional(),
	maxSquareFootage: z.number().optional(),
	minPrice: z.number().optional(),
	maxPrice: z.number().optional(),
	minRent: z.number().optional(),
	maxRent: z.number().optional(),
});

export const PropertySaleHistoryCreateSchema = z.object({
	propertyId: z.string().min(1),
	organizationId: z.string().min(1),
	seller: z.string().optional(),
	buyer: z.string().optional(),
	saleDate: z.coerce.date().optional(),
	salePrice: z.number().optional(),
	askingPrice: z.number().optional(),
	transactionType: z.string().optional(),
	pricePerSquareFoot: z.number().optional(),
	pricePerUnit: z.number().optional(),
	transferredOwnershipPercentage: z.number().optional(),
	capRate: z.number().optional(),
	grmRate: z.number().optional(),
});

export const PropertyMortgageCreateSchema = z.object({
	propertyId: z.string().min(1),
	organizationId: z.string().min(1),
	amount: z.number().optional(),
	assumable: z.boolean().optional(),
	deedType: z.any().optional(),
	documentDate: z.string().optional(),
	documentNumber: z.any().optional(),
	granteeName: z.string().optional(),
	interestRate: z.number().optional(),
	interestRateType: z.string().optional(),
	lenderCode: z.string().optional(),
	lenderName: z.string().optional(),
	lenderType: z.string().optional(),
	loanType: z.string().optional(),
	loanTypeCode: z.string().optional(),
	maturityDate: z.string().optional(),
	mortgageId: z.number().optional(),
	open: z.boolean().optional(),
	position: z.string().optional(),
	recordingDate: z.string().optional(),
	seqNo: z.number().optional(),
	term: z.number().optional(),
	termType: z.string().optional(),
	transactionType: z.string().optional(),
});

export type PropertyWithRelations = {
	id: string;
	organizationId: string;
	name: string;
	address: {
		street?: string;
		street2?: string;
		city?: string;
		state?: string;
		zip?: string;
		county?: string;
		country?: string;
	} | null;
	propertyType: string | null;
	status: string | null;
	price: number | null;
	bedrooms: number | null;
	bathrooms: number | null;
	squareFootage: number | null;
	lotSize: number | null;
	yearBuilt: number | null;
	description: string | null;
	features: string | null;
	images: string[];
	location: {
		type: "Point";
		coordinates: [number, number];
	} | null;
	isDeleted: boolean;
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	creator?: {
		id: string;
		name: string | null;
		email: string;
		image: string | null;
	};
	// Related entities (loaded on demand)
	unitMixes?: PropertyUnitMix[];
	saleHistory?: PropertySaleHistory[];
	mortgages?: PropertyMortgage[];
	demographics?: PropertyDemographics;
	foreclosureInfo?: PropertyForeclosureInfo[];
	mlsHistory?: PropertyMlsHistory[];
};

export type PropertyUnitMix = z.infer<typeof PropertyUnitMixCreateSchema> & {
	id: string;
	createdAt: Date;
	updatedAt: Date;
};

export type PropertySaleHistory = z.infer<
	typeof PropertySaleHistoryCreateSchema
> & {
	id: string;
	createdAt: Date;
	updatedAt: Date;
};

export type PropertyMortgage = z.infer<typeof PropertyMortgageCreateSchema> & {
	id: string;
	createdAt: Date;
	updatedAt: Date;
};

export type PropertyDemographics = {
	id: string;
	propertyId: string;
	organizationId: string;
	fmrEfficiency?: number;
	fmrFourBedroom?: number;
	fmrOneBedroom?: number;
	fmrThreeBedroom?: number;
	fmrTwoBedroom?: number;
	fmrYear?: number;
	hudAreaCode?: string;
	hudAreaName?: string;
	medianIncome?: number;
	suggestedRent?: number;
	createdAt: Date;
	updatedAt: Date;
};

export type PropertyForeclosureInfo = {
	id: string;
	propertyId: string;
	organizationId: string;
	foreclosureId?: any;
	originalLoanAmount?: number;
	estimatedBankValue?: number;
	defaultAmount?: number;
	recordingDate?: string;
	openingBid?: number;
	auctionDate?: string;
	auctionTime?: string;
	auctionStreetAddress?: string;
	documentType?: string;
	trusteeSaleNumber?: any;
	typeName?: string;
	active?: boolean;
	lenderName?: string;
	lenderPhone?: string;
	noticeType?: string;
	seqNo?: number;
	trusteeAddress?: string;
	trusteeName?: string;
	trusteePhone?: string;
	judgmentDate?: string;
	judgmentAmount?: number;
	createdAt: Date;
	updatedAt: Date;
};

export type PropertyMlsHistory = {
	id: string;
	propertyId: string;
	organizationId: string;
	mlsId?: number;
	type?: string;
	price?: number;
	beds?: number;
	baths?: number;
	daysOnMarket?: number;
	agentName?: string;
	agentOffice?: string;
	agentPhone?: string;
	agentEmail?: string;
	status?: string;
	statusDate?: string;
	lastStatusDate?: string;
	createdAt: Date;
	updatedAt: Date;
};

export type PropertyCreateInput = z.infer<typeof PropertyCreateSchema>;
export type PropertyUpdateInput = z.infer<typeof PropertyUpdateSchema>;
