import type { Session } from "@repo/auth";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

export const reaRouter = new Hono<{ Variables: { user: Session["user"] } }>();

const apiKey = process.env.REAL_ESTATE_API_KEY as string;

// Schema for property search
const propertySearchSchema = z.object({
	address: z.string(),
});

// Get property info from Real Estate API
reaRouter.post("/property-info", authMiddleware, async (c: any) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		const { address } = propertySearchSchema.parse(body);

		if (!apiKey) {
			return c.json({ error: "Real Estate API key not configured" }, 500);
		}

		const searchOptions = {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"x-api-key": api<PERSON>ey,
			},
			body: JSON.stringify({
				address: address,
			}),
		};

		const response = await fetch(
			"https://api.realestateapi.com/v2/PropertyDetail",
			searchOptions,
		);

		if (!response.ok) {
			const errorText = await response.text();
			logger.error(`REA API error: ${response.status} ${errorText}`);
			return c.json(
				{ error: "Failed to fetch property info" },
				response.status as any,
			);
		}

		const data = await response.json();

		if (!data || Object.keys(data).length === 0) {
			return c.json({ error: "No property data found" }, 404);
		}

		logger.info(`Property info fetched from REA for address: ${address}`);
		return c.json({ data });
	} catch (error) {
		logger.error("Failed to fetch property info from REA:", error);
		return c.json({ error: "Failed to fetch property info" }, 500);
	}
});

// Get property boundary
reaRouter.post("/property-boundary", authMiddleware, async (c: any) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();

		// Support both address and coordinate-based requests
		const schema = z
			.object({
				address: z.string().optional(),
				id: z.number().optional(),
				lat: z.number().optional(),
				lng: z.number().optional(),
			})
			.refine((data) => data.address || (data.lat && data.lng && data.id), {
				message:
					"Either address or lat/lng coordinates with id must be provided",
			});

		const { address, id, lat, lng } = schema.parse(body);

		if (!apiKey) {
			return c.json({ error: "Real Estate API key not configured" }, 500);
		}

		const url = "https://api.realestateapi.com/v1/PropertyParcel";
		const headers = {
			"Content-Type": "application/json",
			"x-api-key": apiKey,
		};

		// Build request payload based on available data
		const input: any = {};

		if (address) {
			input.address = address;
		} else if (lat && lng) {
			input.latitude = lat;
			input.longitude = lng;
			if (id) {
				input.id = id;
			}
		}

		const response = await fetch(url, {
			method: "POST",
			headers,
			body: JSON.stringify(input),
		});

		if (!response.ok) {
			const errorText = await response.text();
			logger.error(
				`REA Boundary API error: ${response.status} ${errorText}`,
				{
					input,
					status: response.status,
				},
			);
			return c.json(
				{ error: "Failed to fetch property boundary" },
				response.status as any,
			);
		}

		const data = await response.json();

		if (!data || Object.keys(data).length === 0) {
			return c.json({ error: "No boundary data found" }, 404);
		}

		logger.info("Property boundary fetched", {
			address,
			coordinates: lat && lng ? [lng, lat] : null,
			id,
		});
		return c.json(data);
	} catch (error) {
		logger.error("Failed to fetch property boundary:", error);
		return c.json({ error: "Failed to fetch property boundary" }, 500);
	}
});

// Schema for autocomplete search
const autocompleteSearchSchema = z.object({
	query: z.string(),
});

// REA AutoComplete API endpoint
reaRouter.post("/autocomplete", authMiddleware, async (c: any) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		const { query } = autocompleteSearchSchema.parse(body);

		if (!apiKey) {
			return c.json({ error: "Real Estate API key not configured" }, 500);
		}

		// Call REA AutoComplete API
		const reaResponse = await fetch(
			"https://api.realestateapi.com/v2/AutoComplete",
			{
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"x-api-key": apiKey,
				},
				body: JSON.stringify({
					search: query, // REA API expects "search" not "query"
				}),
			},
		);

		if (!reaResponse.ok) {
			const errorText = await reaResponse.text();
			logger.error(
				`REA AutoComplete API error: ${reaResponse.status} ${errorText}`,
			);
			return c.json(
				{ error: "Failed to fetch autocomplete results" },
				reaResponse.status as any,
			);
		}

		const reaData = await reaResponse.json();

		logger.info("AutoComplete results fetched from REA", {
			search: query,
			resultsCount: reaData.data?.length || 0,
		});

		return c.json(reaData);
	} catch (error) {
		logger.error("Error in REA autocomplete endpoint", { error });
		return c.json({ error: "Internal server error" }, 500);
	}
});

export type ReaRouter = typeof reaRouter;
