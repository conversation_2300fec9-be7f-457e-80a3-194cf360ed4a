import { db } from "@repo/database/server";
import { logger } from "@repo/logs";

/**
 * Database optimization utilities for contacts
 */

// Optimized contact queries with proper projections
export const contactQueries = {
	// Get contacts with minimal projection for list views
	getContactsMinimal: async (
		organizationId: string,
		limit = 50,
		skip = 0,
	) => {
		return db.contact.findMany({
			where: {
				organizationId,
				isDeleted: false,
			},
			select: {
				id: true,
				firstName: true,
				lastName: true,
				title: true,
				status: true,
				email: true,
				phone: true,
				company: {
					select: {
						id: true,
						name: true,
					},
				},
				createdAt: true,
				updatedAt: true,
			},
			orderBy: { createdAt: "desc" },
			take: limit,
			skip,
		});
	},

	// Get full contact with all relations
	getContactFull: async (id: string, organizationId: string) => {
		return db.contact.findFirst({
			where: {
				id,
				organizationId,
				isDeleted: false,
			},
			include: {
				company: true,
				creator: {
					select: {
						id: true,
						name: true,
						email: true,
						image: true,
					},
				},
				updater: {
					select: {
						id: true,
						name: true,
						email: true,
						image: true,
					},
				},
			},
		});
	},

	// Search contacts with optimized query
	searchContacts: async (
		organizationId: string,
		searchTerm: string,
		limit = 20,
	) => {
		return db.contact.findMany({
			where: {
				organizationId,
				isDeleted: false,
				OR: [
					{
						firstName: {
							contains: searchTerm,
							mode: "insensitive",
						},
					},
					{
						lastName: {
							contains: searchTerm,
							mode: "insensitive",
						},
					},
					{
						title: {
							contains: searchTerm,
							mode: "insensitive",
						},
					},
				],
			},
			select: {
				id: true,
				firstName: true,
				lastName: true,
				title: true,
				image: true,
				company: {
					select: {
						name: true,
					},
				},
			},
			take: limit,
		});
	},

	// Get contact statistics for dashboard
	getContactStats: async (organizationId: string) => {
		const [total, withEmail, withPhone, withCompany, recentCount] =
			await Promise.all([
				// Total contacts
				db.contact.count({
					where: {
						organizationId,
						isDeleted: false,
					},
				}),
				// Contacts with email
				db.contact.count({
					where: {
						organizationId,
						isDeleted: false,
						email: {
							not: {
								equals: [],
							},
						},
					},
				}),
				// Contacts with phone
				db.contact.count({
					where: {
						organizationId,
						isDeleted: false,
						phone: {
							not: {
								equals: [],
							},
						},
					},
				}),
				// Contacts with company
				db.contact.count({
					where: {
						organizationId,
						isDeleted: false,
						companyId: {
							not: null,
						},
					},
				}),
				// Recent contacts (last 30 days)
				db.contact.count({
					where: {
						organizationId,
						isDeleted: false,
						createdAt: {
							gte: new Date(
								Date.now() - 30 * 24 * 60 * 60 * 1000,
							),
						},
					},
				}),
			]);

		return {
			total,
			withEmail,
			withPhone,
			withCompany,
			recentCount,
			completionRate:
				total > 0 ? ((withEmail + withPhone) / (total * 2)) * 100 : 0,
		};
	},
};

// Batch operations for performance
export const contactBatchOperations = {
	// Bulk update contacts with transaction
	bulkUpdate: async (
		contactIds: string[],
		updates: any,
		organizationId: string,
		userId: string,
	) => {
		try {
			const results = await db.$transaction(
				contactIds.map((id) =>
					db.contact.update({
						where: {
							id,
							organizationId,
							isDeleted: false,
						},
						data: {
							...updates,
							updatedBy: userId,
						},
						select: {
							id: true,
							firstName: true,
							lastName: true,
							updatedAt: true,
						},
					}),
				),
			);

			logger.info(`Bulk updated ${results.length} contacts`);
			return results;
		} catch (error) {
			logger.error("Bulk update failed:", error);
			throw error;
		}
	},

	// Bulk soft delete contacts
	bulkSoftDelete: async (
		contactIds: string[],
		organizationId: string,
		userId: string,
	) => {
		try {
			const result = await db.contact.updateMany({
				where: {
					id: {
						in: contactIds,
					},
					organizationId,
					isDeleted: false,
				},
				data: {
					isDeleted: true,
					deletedAt: new Date(),
					deletedBy: userId,
				},
			});

			logger.info(`Bulk deleted ${result.count} contacts`);
			return result;
		} catch (error) {
			logger.error("Bulk delete failed:", error);
			throw error;
		}
	},

	// Bulk import contacts with deduplication
	bulkImport: async (
		contacts: any[],
		organizationId: string,
		userId: string,
		deduplicateBy: "email" | "name" | "phone" | "none" = "email",
	) => {
		try {
			const results = [];
			let duplicatesSkipped = 0;

			for (const contactData of contacts) {
				// Check for duplicates based on deduplication strategy
				let existingContact = null;

				if (
					deduplicateBy === "email" &&
					contactData.email?.length > 0
				) {
					const primaryEmail =
						contactData.email.find((e: any) => e.isPrimary)
							?.address || contactData.email[0]?.address;

					if (primaryEmail) {
						// Note: For MongoDB/JSON queries, we'll need to use a different approach
						// This is a simplified version - in production you'd want to implement proper JSON querying
						existingContact = await db.contact.findFirst({
							where: {
								organizationId,
								isDeleted: false,
								// For now, we'll skip email deduplication for complex JSON queries
								// In production, implement proper JSON path queries based on your database
							},
							select: { id: true },
						});
					}
				} else if (deduplicateBy === "name") {
					existingContact = await db.contact.findFirst({
						where: {
							organizationId,
							isDeleted: false,
							firstName: contactData.firstName,
							lastName: contactData.lastName,
						},
						select: { id: true },
					});
				}

				if (existingContact) {
					duplicatesSkipped++;
					continue;
				}

				// Create new contact
				const newContact = await db.contact.create({
					data: {
						...contactData,
						organizationId,
						createdBy: userId,
						isDeleted: false,
					},
					select: {
						id: true,
						firstName: true,
						lastName: true,
					},
				});

				results.push(newContact);
			}

			logger.info(
				`Bulk imported ${results.length} contacts, skipped ${duplicatesSkipped} duplicates`,
			);
			return {
				imported: results.length,
				duplicatesSkipped,
				contacts: results,
			};
		} catch (error) {
			logger.error("Bulk import failed:", error);
			throw error;
		}
	},
};

// Query optimization utilities
export const contactQueryOptimizations = {
	// Get frequently used aggregations
	getAggregations: async (organizationId: string) => {
		const [statusDistribution, sourceDistribution] = await Promise.all([
			// Status distribution
			db.contact.groupBy({
				by: ["status"],
				where: {
					organizationId,
					isDeleted: false,
				},
				_count: {
					status: true,
				},
			}),
			// Source distribution
			db.contact.groupBy({
				by: ["source"],
				where: {
					organizationId,
					isDeleted: false,
					source: {
						not: null,
					},
				},
				_count: {
					source: true,
				},
			}),
		]);

		return {
			statusDistribution: statusDistribution.map((item: any) => ({
				status: item.status || "Unknown",
				count: item._count.status,
			})),
			sourceDistribution: sourceDistribution.map((item: any) => ({
				source: item.source || "Unknown",
				count: item._count.source,
			})),
		};
	},

	// Cache frequently accessed data
	cacheContactCounts: async (organizationId: string) => {
		// This could be implemented with Redis or similar caching solution
		const counts = await contactQueries.getContactStats(organizationId);

		// For now, just return the counts
		// In production, you'd want to cache this with TTL
		return counts;
	},
};

// Performance monitoring utilities
export const contactPerformanceMonitoring = {
	// Log slow queries
	logSlowQuery: (operation: string, duration: number, threshold = 1000) => {
		if (duration > threshold) {
			logger.warn(
				`Slow contact query detected: ${operation} took ${duration}ms`,
			);
		}
	},

	// Monitor query performance
	withPerformanceMonitoring: async <T>(
		operation: string,
		queryFn: () => Promise<T>,
	): Promise<T> => {
		const start = Date.now();
		try {
			const result = await queryFn();
			const duration = Date.now() - start;
			contactPerformanceMonitoring.logSlowQuery(operation, duration);
			return result;
		} catch (error) {
			const duration = Date.now() - start;
			logger.error(
				`Contact query failed: ${operation} after ${duration}ms`,
				error,
			);
			throw error;
		}
	},
};
