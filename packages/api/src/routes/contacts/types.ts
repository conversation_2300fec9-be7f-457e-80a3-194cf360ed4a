import { z } from "zod";

// Optimized field schemas with better validation
const AddressSchema = z.object({
	label: z.string().min(1, "Address label is required"),
	street: z.string().optional(),
	street2: z.string().optional(),
	city: z.string().optional(),
	state: z.string().optional(),
	zip: z.union([z.number(), z.string()]).optional(),
	county: z.string().optional(),
	country: z.string().optional(),
});

const PhoneSchema = z.object({
	label: z.string().min(1, "Phone label is required"),
	number: z
		.string()
		.min(10, "Phone number must be at least 10 digits")
		.regex(/^[\d\s\-+()]+$/, "Invalid phone number format"),
	isBad: z.boolean().default(false),
	isPrimary: z.boolean().default(false),
});

const EmailSchema = z.object({
	label: z.string().min(1, "Email label is required"),
	address: z.string().email("Invalid email format"),
	isBad: z.boolean().default(false),
	isPrimary: z.boolean().default(false),
});

const SocialSchema = z.object({
	linkedin: z.union([z.string().url(), z.literal(""), z.null()]).optional(),
	facebook: z.union([z.string().url(), z.literal(""), z.null()]).optional(),
	twitter: z.union([z.string().url(), z.literal(""), z.null()]).optional(),
	instagram: z.union([z.string().url(), z.literal(""), z.null()]).optional(),
});

const BuyerNeedsSchema = z
	.object({
		propertyType: z.string().optional(),
		minUnits: z.number().min(0).optional(),
		maxUnits: z.number().min(0).optional(),
		minPrice: z.number().min(0).optional(),
		maxPrice: z.number().min(0).optional(),
		minEquity: z.number().min(0).optional(),
		maxEquity: z.number().min(0).optional(),
		exchange: z.boolean().optional(),
		exchangeId: z.string().optional(),
		cap: z.number().min(0).max(100).optional(),
		yearBuiltFrom: z
			.number()
			.min(1800)
			.max(new Date().getFullYear())
			.optional(),
		yearBuiltTo: z
			.number()
			.min(1800)
			.max(new Date().getFullYear())
			.optional(),
	})
	.refine((data) => {
		if (data.minUnits && data.maxUnits) {
			return data.minUnits <= data.maxUnits;
		}
		return true;
	}, "Minimum units cannot be greater than maximum units")
	.refine((data) => {
		if (data.minPrice && data.maxPrice) {
			return data.minPrice <= data.maxPrice;
		}
		return true;
	}, "Minimum price cannot be greater than maximum price");

// Base contact schema without refinements
const ContactBaseSchema = z.object({
	// Migration fields
	mongoId: z.string().optional(),
	apolloId: z.string().optional(),

	// Personal information - at least firstName or lastName required
	firstName: z.string().min(1).optional(),
	lastName: z.string().min(1).optional(),
	image: z.string().url().nullable().optional(),
	title: z.string().max(100).optional(),
	persona: z.string().max(50).optional(),
	status: z.string().max(50).optional(),

	// Contact information
	address: z
		.array(AddressSchema)
		.max(5, "Maximum 5 addresses allowed")
		.optional(),
	phone: z
		.array(PhoneSchema)
		.max(10, "Maximum 10 phone numbers allowed")
		.optional(),
	email: z
		.array(EmailSchema)
		.max(10, "Maximum 10 email addresses allowed")
		.optional(),
	website: z.union([z.string().url(), z.literal("")]).optional(),
	social: SocialSchema.optional(),

	// Additional info
	source: z.string().max(100).optional(),
	stage: z.string().max(50).optional(),
	birthday: z.string().datetime().optional(),
	age: z.number().min(0).max(150).optional(),
	spouseName: z.string().max(100).optional(),
	summary: z.string().max(2000).optional(),

	// Company association
	companyId: z.string().optional(),

	// Buyer information
	buyerNeeds: BuyerNeedsSchema.optional(),

	// AI/ML
	aiGeneratedSummary: z.string().max(5000).optional(),

	// Required
	organizationId: z.string().min(1, "Organization ID is required"),
});

// Optimized create contact schema with validation refinements
export const ContactCreateSchema = ContactBaseSchema.refine((data) => {
	// At least one of firstName or lastName must be provided
	return data.firstName || data.lastName;
}, "Either firstName or lastName is required")
	.refine((data) => {
		// Ensure only one primary email
		if (data.email) {
			const primaryEmails = data.email.filter((e) => e.isPrimary);
			return primaryEmails.length <= 1;
		}
		return true;
	}, "Only one email can be marked as primary")
	.refine((data) => {
		// Ensure only one primary phone
		if (data.phone) {
			const primaryPhones = data.phone.filter((p) => p.isPrimary);
			return primaryPhones.length <= 1;
		}
		return true;
	}, "Only one phone can be marked as primary");

// Optimized update schema
export const ContactUpdateSchema = ContactBaseSchema.partial()
	.omit({
		organizationId: true, // Organization cannot be changed
		mongoId: true, // Migration fields cannot be updated
		apolloId: true,
	})
	.refine((data) => {
		// Ensure only one primary email if emails are provided
		if (data.email) {
			const primaryEmails = data.email.filter((e) => e.isPrimary);
			return primaryEmails.length <= 1;
		}
		return true;
	}, "Only one email can be marked as primary")
	.refine((data) => {
		// Ensure only one primary phone if phones are provided
		if (data.phone) {
			const primaryPhones = data.phone.filter((p) => p.isPrimary);
			return primaryPhones.length <= 1;
		}
		return true;
	}, "Only one phone can be marked as primary");

// Enhanced query schema with better filtering
export const ContactQuerySchema = z.object({
	organizationId: z.string().min(1, "Organization ID is required"),

	// Pagination
	page: z.coerce.number().min(1).default(1),
	limit: z.coerce.number().min(1).max(100).default(50),
	cursor: z.string().optional(),
	direction: z.enum(["next", "prev"]).default("next"),

	// Search and filters
	search: z.string().max(255).optional(),
	status: z.string().max(50).optional(),
	stage: z.string().max(50).optional(),
	source: z.string().max(100).optional(),
	companyId: z.string().optional(),

	// Date filters
	createdAfter: z.string().datetime().optional(),
	createdBefore: z.string().datetime().optional(),
	updatedAfter: z.string().datetime().optional(),
	updatedBefore: z.string().datetime().optional(),

	// Relationship filters
	hasEmail: z.coerce.boolean().optional(),
	hasPhone: z.coerce.boolean().optional(),
	hasCompany: z.coerce.boolean().optional(),

	// Flags
	tags: z.coerce.boolean().default(false),
	lists: z.coerce.boolean().default(false),
	isDeleted: z.coerce.boolean().default(false),

	// Sorting
	sortBy: z
		.enum([
			"createdAt",
			"updatedAt",
			"firstName",
			"lastName",
			"lastViewedAt",
		])
		.default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Bulk operations schemas
export const ContactBulkUpdateSchema = z.object({
	contactIds: z
		.array(z.string())
		.min(1, "At least one contact ID required")
		.max(100, "Maximum 100 contacts per bulk operation"),
	updates: ContactUpdateSchema,
	organizationId: z.string().min(1, "Organization ID is required"),
});

export const ContactBulkDeleteSchema = z.object({
	contactIds: z
		.array(z.string())
		.min(1, "At least one contact ID required")
		.max(100, "Maximum 100 contacts per bulk operation"),
	organizationId: z.string().min(1, "Organization ID is required"),
	permanent: z.boolean().default(false), // Soft delete by default
});

// Import schema with better validation
export const ContactImportSchema = z.object({
	fileContent: z.string().min(1, "File content is required"),
	organizationId: z.string().min(1, "Organization ID is required"),
	autoEnrich: z.boolean().default(false),
	mapping: z.record(z.string(), z.string()).optional(),
	deduplicateBy: z.enum(["email", "name", "phone", "none"]).default("email"),
	batchSize: z.number().min(1).max(1000).default(100),
});

// Enhanced contact interface with better typing
export interface ContactWithRelations {
	id: string;
	mongoId?: string | null;
	apolloId?: string | null;
	firstName?: string | null;
	lastName?: string | null;
	name?: string;
	image?: string | null;
	title?: string | null;
	persona?: string | null;
	status?: string | null;
	address?: AddressType[] | null;
	phone: PhoneType[];
	email: EmailType[];
	website?: string | null;
	social?: SocialType | null;
	source?: string | null;
	stage?: string | null;
	birthday?: string | null;
	age?: number | null;
	spouseName?: string | null;
	summary?: string | null;
	companyId?: string | null;
	buyerNeeds?: BuyerNeedsType | null;
	embedding?: number[];
	aiGeneratedSummary?: string | null;
	organizationId: string;
	createdBy: string;
	updatedBy?: string | null;
	lastViewedAt?: Date | null;
	lastViewedBy?: string | null;
	isDeleted: boolean;
	deletedAt?: Date | null;
	deletedBy?: string | null;
	createdAt: Date;
	updatedAt: Date;

	// Relations
	company?: {
		id: string;
		name: string;
		website?: string | null;
		logo?: string | null;
	} | null;
	creator: {
		id: string;
		name: string;
		email: string;
		image?: string | null;
	};
	updater?: {
		id: string;
		name: string;
		email: string;
		image?: string | null;
	} | null;
	tags?: Array<{
		id: string;
		name: string;
		color: string;
		addedBy?: { id: string; name: string };
		addedAt?: Date;
	}>;
	lists?: Array<{
		id: string;
		name: string;
		description?: string | null;
		addedBy?: { id: string; name: string };
		addedAt?: Date;
	}>;
	tasks?: Array<{
		id: string;
		title: string;
		status: string;
		assignee?: { id: string; name: string; image?: string | null } | null;
	}>;
	relatedContacts?: Array<{
		id: string;
		firstName?: string | null;
		lastName?: string | null;
		image?: string | null;
	}>;
}

// Type definitions
export type AddressType = z.infer<typeof AddressSchema>;
export type PhoneType = z.infer<typeof PhoneSchema>;
export type EmailType = z.infer<typeof EmailSchema>;
export type SocialType = z.infer<typeof SocialSchema>;
export type BuyerNeedsType = z.infer<typeof BuyerNeedsSchema>;

// Input type exports
export type ContactCreateInput = z.infer<typeof ContactCreateSchema>;
export type ContactUpdateInput = z.infer<typeof ContactUpdateSchema>;
export type ContactQueryInput = z.infer<typeof ContactQuerySchema>;
export type ContactImportInput = z.infer<typeof ContactImportSchema>;
export type ContactBulkUpdateInput = z.infer<typeof ContactBulkUpdateSchema>;
export type ContactBulkDeleteInput = z.infer<typeof ContactBulkDeleteSchema>;

// Legacy interfaces for backward compatibility
export interface ContactCreate {
	firstName: string;
	lastName: string;
	title?: string | null;
	summary?: string | null;
	aiGeneratedSummary?: string | null;
	organizationId: string;
	createdById: string;
	email?: any[];
	phone?: any[];
	address?: any[];
	social?: Record<string, any>;
	companyId?: string | null;
}

export interface ContactImport {
	firstName?: string;
	lastName?: string;
	title?: string;
	summary?: string;
	email?: any;
	phone?: any;
	address?: any;
	social?: Record<string, any>;
	company?: {
		name?: string;
		website?: string;
	};
}
