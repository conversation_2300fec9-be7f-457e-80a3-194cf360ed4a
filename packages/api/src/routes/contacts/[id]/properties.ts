import { z } from "zod";
import { db } from "@repo/database/server";
import type { Session } from "@repo/auth";
import { Hono } from "hono";
import { authMiddleware } from "../../../middleware/auth";
import { logger } from "@repo/logs";

// Schema for property linking validation
const linkPropertySchema = z.object({
  propertyId: z.string(),
  organizationId: z.string(),
  relation: z.string().optional(),
});

const unlinkPropertySchema = z.object({
  propertyId: z.string(),
  organizationId: z.string(),
});

const updatePropertyRelationSchema = z.object({
  propertyId: z.string(),
  organizationId: z.string(),
  relation: z.string(),
});

export const app = new Hono<{
  Variables: { user: Session["user"] };
}>();

// GET /api/contacts/[id]/properties - Get properties linked to a contact
app.get("/", authMiddleware, async (c) => {
  try {
    const contactId = c.req.param("id");
    const organizationId = c.req.query("organizationId");
    const user = c.get("user");

    if (!organizationId) {
      return c.json({ error: "Organization ID is required" }, 400);
    }

    // Verify the contact exists
    const contact = await db.contact.findFirst({
      where: {
        id: contactId,
        organizationId: organizationId as string,
        isDeleted: false,
      },
    });

    if (!contact) {
      return c.json({ error: "Contact not found" }, 404);
    }

    // Get all properties linked to this contact through the junction table
    const linkedProperties = await db.linkedProperty.findMany({
      where: {
        contactId: contactId,
        organizationId: organizationId as string,
      },
      include: {
        property: {
          include: {
            organization: true,
            location: true,
            financials: true,
            creator: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            updater: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform the results to include relation information
    const propertiesWithRelations = linkedProperties.map((link: any) => ({
      ...link.property,
      relation: link.relation,
      linkedAt: link.createdAt,
      linkedBy: link.createdBy,
    }));

    logger.info(`Found ${propertiesWithRelations.length} properties for contact ${contactId}`);
    return c.json({ properties: propertiesWithRelations });
  } catch (error) {
    logger.error("Error fetching contact properties:", error);
    return c.json({ error: "Failed to fetch contact properties" }, 500);
  }
});

// POST /api/contacts/[id]/properties - Link a property to a contact
app.post("/", authMiddleware, async (c) => {
  try {
    const contactId = c.req.param("id");
    const body = await c.req.json();
    const { propertyId, organizationId, relation } = linkPropertySchema.parse(body);
    const user = c.get("user");

    // Verify that both contact and property exist and belong to the organization
    const [contact, property] = await Promise.all([
      db.contact.findUnique({
        where: {
          id: contactId,
          organizationId: organizationId,
          isDeleted: false,
        },
      }),
      db.property.findUnique({
        where: {
          id: propertyId,
          organizationId: organizationId,
          isDeleted: false,
        },
      }),
    ]);

    if (!contact) {
      return c.json({ error: "Contact not found" }, 404);
    }

    if (!property) {
      return c.json({ error: "Property not found" }, 404);
    }

    // Check if property is already linked to this contact
    const existingLink = await db.linkedProperty.findFirst({
      where: {
        contactId: contactId,
        propertyId: propertyId,
        organizationId: organizationId,
      },
    });

    if (existingLink) {
      return c.json({ error: "Property is already linked to this contact" }, 400);
    }

    // Create the link in the junction table with default relation "contact"
    const linkedProperty = await db.linkedProperty.create({
      data: {
        contactId: contactId!,
        propertyId: propertyId,
        organizationId: organizationId,
        relation: relation || "contact", // Use provided relation or default to "contact"
        createdBy: user.id,
      },
      include: {
        property: {
          include: {
            creator: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            updater: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    logger.info(`Linked property ${propertyId} to contact ${contactId} by user ${user.id}`);
    return c.json({ 
      message: "Property linked successfully",
      linkedProperty: {
        id: linkedProperty.id,
        relation: linkedProperty.relation,
        createdAt: linkedProperty.createdAt,
        property: linkedProperty.property
      }
    });
  } catch (error) {
    logger.error("Error linking property to contact:", error);
    return c.json({ error: "Failed to link property to contact" }, 500);
  }
});

// DELETE /api/contacts/[id]/properties - Unlink a property from a contact
app.delete("/", authMiddleware, async (c) => {
  try {
    const contactId = c.req.param("id");
    const propertyId = c.req.query("propertyId");
    const organizationId = c.req.query("organizationId");
    const user = c.get("user");

    if (!propertyId || !organizationId) {
      return c.json({ error: "propertyId and organizationId are required" }, 400);
    }

    // Find the existing link to delete
    const existingLink = await db.linkedProperty.findFirst({
      where: {
        contactId: contactId,
        propertyId: propertyId as string,
        organizationId: organizationId as string,
      },
      include: {
        property: {
          include: {
            creator: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            updater: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!existingLink) {
      return c.json({ error: "Property is not linked to this contact" }, 400);
    }

    // Delete the link from the junction table
    await db.linkedProperty.delete({
      where: {
        id: existingLink.id,
      },
    });

    logger.info(`Unlinked property ${propertyId} from contact ${contactId} by user ${user.id}`);
    return c.json({ 
      message: "Property unlinked successfully",
      property: existingLink.property 
    });
  } catch (error) {
    logger.error("Error unlinking property from contact:", error);
    return c.json({ error: "Failed to unlink property from contact" }, 500);
  }
});

// PATCH /api/contacts/[id]/properties - Update property relation
app.patch("/", authMiddleware, async (c) => {
  try {
    const contactId = c.req.param("id");
    const body = await c.req.json();
    const { propertyId, organizationId, relation } = updatePropertyRelationSchema.parse(body);
    const user = c.get("user");

    // Find the existing link
    const existingLink = await db.linkedProperty.findFirst({
      where: {
        contactId: contactId,
        propertyId: propertyId,
        organizationId: organizationId,
      },
    });

    if (!existingLink) {
      return c.json({ error: "Property is not linked to this contact" }, 404);
    }

    // Update the relation
    const updatedLink = await db.linkedProperty.update({
      where: {
        id: existingLink.id,
      },
      data: {
        relation: relation,
        updatedBy: user.id,
      },
      include: {
        property: {
          include: {
            location: true,
            financials: true,
            creator: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            updater: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    logger.info(`Updated relation for property ${propertyId} and contact ${contactId} by user ${user.id}`);
    return c.json({ 
      message: "Property relation updated successfully",
      linkedProperty: {
        ...updatedLink.property,
        relation: updatedLink.relation,
        linkedAt: updatedLink.createdAt,
      }
    });
  } catch (error) {
    logger.error("Error updating property relation:", error);
    return c.json({ error: "Failed to update property relation" }, 500);
  }
});

export default app;
