import { z } from "zod";
import { db } from "@repo/database/server";
import type { Session } from "@repo/auth";
import { Hono } from "hono";
import { authMiddleware } from "../../../middleware/auth";

// Schema for related contact validation
const relatedContactSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  label: z.string(),
  phone: z.array(z.object({
    number: z.string(),
    label: z.string(),
    isPrimary: z.boolean(),
    isBad: z.boolean(),
  })).optional(),
  email: z.array(z.object({
    address: z.string().email(),
    label: z.string(),
    isPrimary: z.boolean(),
    isBad: z.boolean(),
  })).optional(),
  address: z.array(z.object({
    street: z.string(),
    street2: z.string().optional(),
    city: z.string(),
    state: z.string(),
    zip: z.string(),
    country: z.string(),
    label: z.string(),
  })).optional(),
  organizationId: z.string(),
});

const updateRelatedContactsSchema = z.object({
  relatedContacts: z.array(relatedContactSchema),
});

// Optimized include configuration for contact queries
const CONTACT_INCLUDE = {
  company: {
    select: {
      id: true,
      name: true,
      website: true,
      logo: true,
    },
  },
  creator: {
    select: {
      id: true,
      name: true,
      email: true,
      image: true,
    },
  },
  updater: {
    select: {
      id: true,
      name: true,
      email: true,
      image: true,
    },
  },
  relatedContacts: true,
};

export const app = new Hono<{
  Variables: { user: Session["user"] };
}>();

app.get("/", authMiddleware, async (c) => {
  try {
    const id = c.req.param("id");
    const organizationId = c.req.query("organizationId");
    const user = c.get("user");

    if (!organizationId) {
      return c.json({ error: "Organization ID is required" }, 400);
    }

    // Fetch related contacts for this contact
    const relatedContacts = await db.relatedContact.findMany({
      where: { 
        contactId: id,
        organizationId: organizationId,
      },
      orderBy: { createdAt: "desc" },
    });

    return c.json({ relatedContacts });
  } catch (error) {
    console.error("Error fetching related contacts:", error);
    return c.json({ error: "Failed to fetch related contacts" }, 500);
  }
});

// PUT /api/contacts/[id]/related-contacts
app.put("/", authMiddleware, async (c) => {
  try {
    const id = c.req.param("id");
    const body = await c.req.json();
    const { relatedContacts } = updateRelatedContactsSchema.parse(body);
    const user = c.get("user");

    // First, delete all existing related contacts for this contact
    await db.relatedContact.deleteMany({
      where: { contactId: id },
    });

    // Then create new related contacts
    await Promise.all(
      relatedContacts.map((contact) => 
        db.relatedContact.create({
          data: {
            firstName: contact.firstName,
            lastName: contact.lastName,
            label: contact.label,
            phone: contact.phone,
            email: contact.email,
            address: contact.address,
            contact: {
              connect: { id },
            },
            organization: {
              connect: { id: contact.organizationId },
            },
            creator: {
              connect: { id: user.id },
            },
          },
        })
      )
    );

    // Fetch updated contact with all relations
    const updatedContact = await db.contact.findUnique({
      where: { id },
      include: CONTACT_INCLUDE,
    });

    if (!updatedContact) {
      return c.json({ message: "Contact not found" }, 404);
    }

    return c.json(updatedContact);
  } catch (error) {
    console.error("Error updating related contacts:", error);
    return c.json({ message: "Failed to update related contacts" }, 400);
  }
});

export default app; 