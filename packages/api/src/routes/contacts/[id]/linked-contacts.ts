import { <PERSON><PERSON> } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { authMiddleware } from "../../../middleware/auth";
import { verifyOrganizationMembership } from "../../organizations/lib/membership";
import { logger } from "@repo/logs";
import { db } from "@repo/database/server";
import type { Session } from "@repo/auth";

export const app = new Hono<{
  Variables: { user: Session["user"] };
}>();

// Validation schemas
const linkContactSchema = z.object({
  contactId: z.string(), // ID of the contact to link
  contact1Relation: z.string().min(1, "Contact1 relation is required"), // Relation from current contact's perspective
  contact2Relation: z.string().min(1, "Contact2 relation is required"), // Relation from linked contact's perspective
});

const updateRelationSchema = z.object({
  linkedContactId: z.string(), // ID of the LinkedContact record
  contact1Relation: z.string().min(1, "Contact1 relation is required"),
  contact2Relation: z.string().min(1, "Contact2 relation is required"),
});

const unlinkContactSchema = z.object({
  linkedContactId: z.string(), // ID of the LinkedContact record to delete
});

// GET /:id/linked-contacts - Get all linked contacts for a contact
app.get(
  "/",
  authMiddleware,
  async (c) => {
    try {
      const user = c.get("user");
      const contactId = c.req.param("id");
      const organizationId = c.req.query("organizationId");

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Fetching linked contacts", { contactId, organizationId });

      // Find all LinkedContact records where this contact is either contact1 or contact2
      const linkedContacts = await db.linkedContact.findMany({
        where: {
          organizationId,
          OR: [
            { contact1Id: contactId },
            { contact2Id: contactId }
          ]
        },
        include: {
          contact1: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          },
          contact2: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          }
        }
      });

      return c.json({
        data: linkedContacts,
        success: true,
      });
    } catch (error) {
      logger.error("Error fetching linked contacts", {
        error: error instanceof Error ? error.message : String(error),
        contactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to fetch linked contacts",
          success: false,
        },
        500
      );
    }
  }
);

// POST /:id/linked-contacts - Link two contacts
app.post(
  "/",
  authMiddleware,
  zValidator("json", linkContactSchema),
  async (c) => {
    try {
      const user = c.get("user");
      const currentContactId = c.req.param("id");
      const { contactId: targetContactId, contact1Relation, contact2Relation } = c.req.valid("json");
      const organizationId = c.req.query("organizationId");

      if (!currentContactId) {
        return c.json({ error: "Contact ID is required" }, 400);
      }

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Linking contacts", {
        currentContactId,
        targetContactId,
        contact1Relation,
        contact2Relation,
        organizationId,
        userId: user.id,
      });

      // Validate that both contacts exist and belong to the organization
      const [currentContact, targetContact] = await Promise.all([
        db.contact.findFirst({
          where: {
            id: currentContactId,
            organizationId,
            isDeleted: false,
          },
        }),
        db.contact.findFirst({
          where: {
            id: targetContactId,
            organizationId,
            isDeleted: false,
          },
        }),
      ]);

      if (!currentContact) {
        return c.json(
          {
            error: "Current contact not found",
            success: false,
          },
          404
        );
      }

      if (!targetContact) {
        return c.json(
          {
            error: "Target contact not found",
            success: false,
          },
          404
        );
      }

      // Prevent linking a contact to itself
      if (currentContactId === targetContactId) {
        return c.json(
          {
            error: "Cannot link a contact to itself",
            success: false,
          },
          400
        );
      }

      // Check if the contacts are already linked
      const existingLink = await db.linkedContact.findFirst({
        where: {
          organizationId,
          OR: [
            {
              contact1Id: currentContactId,
              contact2Id: targetContactId,
            },
            {
              contact1Id: targetContactId,
              contact2Id: currentContactId,
            },
          ],
        },
      });

      if (existingLink) {
        return c.json(
          {
            error: "Contacts are already linked",
            success: false,
          },
          400
        );
      }

      // Create LinkedContact record with bidirectional relationship
      const linkedContact = await db.linkedContact.create({
        data: {
          contact1Id: currentContactId,
          contact1Relation,
          contact2Id: targetContactId,
          contact2Relation,
          organizationId,
          createdBy: user.id,
        },
        include: {
          contact1: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          },
          contact2: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          }
        }
      });

      return c.json({
        data: linkedContact,
        success: true,
      });
    } catch (error) {
      logger.error("Error linking contacts", {
        error: error instanceof Error ? error.message : String(error),
        currentContactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to link contacts",
          success: false,
        },
        500
      );
    }
  }
);

// PUT /:id/linked-contacts - Update relationship labels
app.put(
  "/",
  authMiddleware,
  zValidator("json", updateRelationSchema),
  async (c) => {
    try {
      const user = c.get("user");
      const contactId = c.req.param("id");
      const { linkedContactId, contact1Relation, contact2Relation } = c.req.valid("json");
      const organizationId = c.req.query("organizationId");

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Updating contact relationship", {
        contactId,
        linkedContactId,
        contact1Relation,
        contact2Relation,
        organizationId,
        userId: user.id,
      });

      // Find and update the LinkedContact record
      const existingLink = await db.linkedContact.findFirst({
        where: {
          id: linkedContactId,
          organizationId,
        },
      });

      if (!existingLink) {
        return c.json(
          {
            error: "Linked contact record not found",
            success: false,
          },
          404
        );
      }

      // Update the LinkedContact record with new relation labels
      const updatedLinkedContact = await db.linkedContact.update({
        where: {
          id: linkedContactId,
        },
        data: {
          contact1Relation,
          contact2Relation,
          updatedBy: user.id,
        },
        include: {
          contact1: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          },
          contact2: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          }
        }
      });

      return c.json({
        data: updatedLinkedContact,
        success: true,
      });
    } catch (error) {
      logger.error("Error updating contact relationship", {
        error: error instanceof Error ? error.message : String(error),
        contactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to update contact relationship",
          success: false,
        },
        500
      );
    }
  }
);

// DELETE /:id/linked-contacts - Unlink contacts
app.delete(
  "/",
  authMiddleware,
  zValidator("json", unlinkContactSchema),
  async (c) => {
    try {
      const user = c.get("user");
      const contactId = c.req.param("id");
      const { linkedContactId } = c.req.valid("json");
      const organizationId = c.req.query("organizationId");

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Unlinking contacts", {
        contactId,
        linkedContactId,
        organizationId,
        userId: user.id,
      });

      // Find and delete the LinkedContact record
      const existingLink = await db.linkedContact.findFirst({
        where: {
          id: linkedContactId,
          organizationId,
        },
      });

      if (!existingLink) {
        return c.json(
          {
            error: "Linked contact record not found",
            success: false,
          },
          404
        );
      }

      // Delete the LinkedContact record
      await db.linkedContact.delete({
        where: {
          id: linkedContactId,
        },
      });

      return c.json({
        success: true,
        message: "Contacts unlinked successfully",
      });
    } catch (error) {
      logger.error("Error unlinking contacts", {
        error: error instanceof Error ? error.message : String(error),
        contactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to unlink contacts",
          success: false,
        },
        500
      );
    }
  }
);

export default app; 