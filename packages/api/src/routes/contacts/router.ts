import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	ContactBulkDeleteSchema,
	ContactBulkUpdateSchema,
	ContactCreateSchema,
	ContactQuerySchema,
	ContactUpdateSchema,
	type ContactWithRelations,
} from "./types";
import { app as relatedContactsRouter } from "./[id]/related-contacts";
import { app as propertiesRouter } from "./[id]/properties";
import { app as linkedContactsRouter } from "./[id]/linked-contacts";

export const contactsRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Mount related contacts router
contactsRouter.route("/objects/contacts/:id/related-contacts", relatedContactsRouter);

// Mount properties router
contactsRouter.route("/objects/contacts/:id/properties", propertiesRouter);

// Mount linked contacts router
contactsRouter.route("/objects/contacts/:id/linked-contacts", linkedContactsRouter);

// Optimized include configuration for contact queries
const CONTACT_INCLUDE = {
	company: {
		select: {
			id: true,
			name: true,
			website: true,
			logo: true,
		},
	},
	creator: {
		select: {
			id: true,
			name: true,
			email: true,
			image: true,
		},
	},
	updater: {
		select: {
			id: true,
			name: true,
			email: true,
			image: true,
		},
	},
	lastViewer: {
		select: {
			id: true,
			name: true,
			email: true,
			image: true,
		},
	},
	relatedContacts: true,
};

function transformContact(contact: any): ContactWithRelations {
	const contactData = contact as any;
	
	return {
		...contactData,
		id: contactData.id || contactData._id,
		name: `${contactData.firstName || ""} ${contactData.lastName || ""}`.trim(),
		summary: contactData.summary || null,
		email: Array.isArray(contactData.email)
			? contactData.email.map((e: any) => ({
					address: e.value || e.address || "",
					label: e.label || "Work",
					isPrimary: e.isPrimary || false,
					isBad: e.isBad || false,
				}))
			: [],
		phone: (() => {
			// Handle various phone data structures from MongoDB/Prisma
			const phoneData = contactData.phone;
			
			// If phone is null or undefined, return empty array
			if (!phoneData) {
				return [];
			}
			
			// If phone is already a properly formatted array
			if (Array.isArray(phoneData)) {
				return phoneData.map((p: any) => ({
					number: p.number || p.value || "",
					label: p.label || "Work",
					isPrimary: Boolean(p.isPrimary),
					isBad: Boolean(p.isBad),
				}));
			}
			
			// If phone is a JSON string, try to parse it
			if (typeof phoneData === 'string') {
				try {
					const parsed = JSON.parse(phoneData);
					if (Array.isArray(parsed)) {
						return parsed.map((p: any) => ({
							number: p.number || p.value || "",
							label: p.label || "Work",
							isPrimary: Boolean(p.isPrimary),
							isBad: Boolean(p.isBad),
						}));
					}
				} catch (e) {
					console.error("Failed to parse phone JSON:", e);
					return [];
				}
			}
			
			// If phone is a single object, wrap it in an array
			if (typeof phoneData === 'object' && phoneData.number) {
				return [{
					number: phoneData.number || phoneData.value || "",
					label: phoneData.label || "Work",
					isPrimary: Boolean(phoneData.isPrimary),
					isBad: Boolean(phoneData.isBad),
				}];
			}
			
			// Fallback: return empty array
			console.warn("Unexpected phone data structure:", phoneData);
			return [];
		})(),
		address: Array.isArray(contactData.address)
			? contactData.address.map((a: any) => ({
					street: a.street || "",
					street2: a.street2 || "",
					city: a.city || "",
					state: a.state || "",
					zip: a.zip || "",
					country: a.country || "United States",
					label: a.label || "Work",
					isPrimary: a.isPrimary || false,
				}))
			: [],
		company: contactData.company ? {
			...contactData.company,
			id: contactData.company.id || contactData.company._id,
		} : null,
		creator: contactData.creator ? {
			...contactData.creator,
			id: contactData.creator.id || contactData.creator._id,
		} : null,
		updater: contactData.updater ? {
			...contactData.updater,
			id: contactData.updater.id || contactData.updater._id,
		} : null,
		lastViewer: contactData.lastViewer ? {
			...contactData.lastViewer,
			id: contactData.lastViewer.id || contactData.lastViewer._id,
		} : null,
	};
}

function buildContactSearchConditions(
	search: string,
	additionalFilters: any = {},
) {
	const conditions: any = {
		isDeleted: false,
		...additionalFilters,
	};

	if (search) {
		conditions.OR = [
			{ firstName: { contains: search, mode: "insensitive" } },
			{ lastName: { contains: search, mode: "insensitive" } },
			{ title: { contains: search, mode: "insensitive" } },
			{ summary: { contains: search, mode: "insensitive" } },
			{
				email: {
					path: "$[*].address",
					string_contains: search,
				},
			},
		];
	}

	return conditions;
}

// Create contact
contactsRouter.post(
	"/contacts",
	authMiddleware,
	validator("json", ContactCreateSchema),
	describeRoute({
		tags: ["Contacts"],
		summary: "Create a new contact",
		description: "Creates a new contact in the organization",
		responses: {
			201: {
				description: "Contact created successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ contact: z.any() })),
					},
				},
			},
			400: {
				description: "Invalid input data",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const data = c.req.valid("json");

			// Verify organization membership
			await verifyOrganizationMembership(data.organizationId, user.id);

			// Create contact with optimized transaction
			const contact = await db.contact.create({
				data: {
					...data,
					createdBy: user.id,
					isDeleted: false,
				},
				include: CONTACT_INCLUDE,
			});

			const transformedContact = transformContact(contact);

			logger.info(`Contact created: ${contact.id} by user ${user.id}`);
			return c.json({ contact: transformedContact }, 201);
		} catch (error) {
			logger.error("Failed to create contact:", error);
			if (error instanceof z.ZodError) {
				return c.json(
					{ error: "Invalid input data", details: error.errors },
					400,
				);
			}
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Get contacts with advanced filtering
contactsRouter.get(
	"/contacts",
	authMiddleware,
	validator("query", ContactQuerySchema),
	describeRoute({
		tags: ["Contacts"],
		summary: "Get contacts with filtering and pagination",
		description:
			"Retrieves contacts with advanced filtering, search, and pagination",
		responses: {
			200: {
				description: "Contacts retrieved successfully",
				content: {
					"application/json": {
						schema: resolver(
							z.object({
								contacts: z.array(z.any()),
								pagination: z.object({
									total: z.number(),
									page: z.number(),
									limit: z.number(),
									hasMore: z.boolean(),
								}),
								facets: z.record(z.any()).optional(),
							}),
						),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const query = c.req.valid("query");

			// Verify organization membership
			await verifyOrganizationMembership(query.organizationId, user.id);

			// Build where conditions
			const whereConditions: any = {
				organizationId: query.organizationId,
				isDeleted: query.isDeleted,
			};

			// Add filters
			if (query.status) whereConditions.status = query.status;
			if (query.stage) whereConditions.stage = query.stage;
			if (query.source) whereConditions.source = query.source;
			if (query.companyId) whereConditions.companyId = query.companyId;

			// Add relationship filters
			if (query.hasEmail === true) {
				whereConditions.email = { not: { equals: [] } };
			} else if (query.hasEmail === false) {
				whereConditions.email = { equals: [] };
			}

			if (query.hasPhone === true) {
				whereConditions.phone = { not: { equals: [] } };
			} else if (query.hasPhone === false) {
				whereConditions.phone = { equals: [] };
			}

			if (query.hasCompany === true) {
				whereConditions.companyId = { not: null };
			} else if (query.hasCompany === false) {
				whereConditions.companyId = null;
			}

			// Add date filters
			if (query.createdAfter || query.createdBefore) {
				whereConditions.createdAt = {};
				if (query.createdAfter)
					whereConditions.createdAt.gte = new Date(
						query.createdAfter,
					);
				if (query.createdBefore)
					whereConditions.createdAt.lte = new Date(
						query.createdBefore,
					);
			}

			if (query.updatedAfter || query.updatedBefore) {
				whereConditions.updatedAt = {};
				if (query.updatedAfter)
					whereConditions.updatedAt.gte = new Date(
						query.updatedAfter,
					);
				if (query.updatedBefore)
					whereConditions.updatedAt.lte = new Date(
						query.updatedBefore,
					);
			}

			// Add search conditions
			const finalConditions = buildContactSearchConditions(
				query.search || "",
				whereConditions,
			);

			// Calculate pagination
			const offset = (query.page - 1) * query.limit;

			// Parallel queries for better performance
			const [contacts, total] = await Promise.all([
				db.contact.findMany({
					where: finalConditions,
					include: CONTACT_INCLUDE,
					orderBy: { [query.sortBy]: query.sortOrder },
					take: query.limit,
					skip: offset,
					cacheStrategy: {
						ttl: 300, // 5 minutes cache
						tags: [`contacts_org_${query.organizationId}`, `org_${query.organizationId}`],
					},
				}),
				db.contact.count({ 
					where: finalConditions,
					cacheStrategy: {
						ttl: 300, // 5 minutes cache
						tags: [`contacts_count_org_${query.organizationId}`, `org_${query.organizationId}`],
					},
				}),
			]);

			// Transform contacts
			const transformedContacts = contacts.map(transformContact);

			// Build facets for status distribution
			const facets: any = {};
			if (contacts.length > 0) {
				const statusCounts = await db.contact.groupBy({
					by: ["status"],
					where: {
						organizationId: query.organizationId,
						isDeleted: false,
					},
					_count: { status: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for status facets
						tags: [`contacts_status_facets_org_${query.organizationId}`, `org_${query.organizationId}`],
					},
				});

				facets.status = {
					rows: statusCounts.map((item: any) => ({
						value: item.status || "Unknown",
						total: item._count.status || 0,
					})),
				};
			}

			return c.json({
				contacts: transformedContacts,
				pagination: {
					total,
					page: query.page,
					limit: query.limit,
					hasMore: offset + query.limit < total,
				},
				facets,
			});
		} catch (error) {
			logger.error("Failed to fetch contacts:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Get single contact by ID
contactsRouter.get(
	"/contacts/:id",
	authMiddleware,
	describeRoute({
		tags: ["Contacts"],
		summary: "Get contact by ID",
		description: "Retrieves a single contact by its ID",
		responses: {
			200: {
				description: "Contact retrieved successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ contact: z.any() })),
					},
				},
			},
			404: {
				description: "Contact not found",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const id = c.req.param("id");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			const contact = await db.contact.findFirst({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
				include: CONTACT_INCLUDE,
				cacheStrategy: {
					ttl: 600, // 10 minutes cache for individual contacts
					tags: [`contact_${id}`, `org_${organizationId}`],
				},
			});

			if (!contact) {
				return c.json({ error: "Contact not found" }, 404);
			}

			// Update last viewed
			await db.contact.update({
				where: { id },
				data: {
					lastViewedAt: new Date(),
					lastViewedBy: user.id,
				},
			});

			const transformedContact = transformContact(contact);
			return c.json({ contact: transformedContact });
		} catch (error) {
			logger.error("Failed to fetch contact:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Update contact
contactsRouter.patch(
	"/contacts/:id",
	authMiddleware,
	validator("json", ContactUpdateSchema),
	describeRoute({
		tags: ["Contacts"],
		summary: "Update contact",
		description: "Updates an existing contact",
		responses: {
			200: {
				description: "Contact updated successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ contact: z.any() })),
					},
				},
			},
			404: {
				description: "Contact not found",
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const id = c.req.param("id");
			const data = c.req.valid("json");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			const contact = await db.contact.update({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
				data: {
					...data,
					updatedBy: user.id,
				},
				include: CONTACT_INCLUDE,
			});

			const transformedContact = transformContact(contact);
			logger.info(`Contact updated: ${id} by user ${user.id}`);
			return c.json({ contact: transformedContact });
		} catch (error) {
			logger.error("Failed to update contact:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Bulk update contacts
contactsRouter.patch(
	"/contacts/bulk",
	authMiddleware,
	validator("json", ContactBulkUpdateSchema),
	describeRoute({
		tags: ["Contacts"],
		summary: "Bulk update contacts",
		description: "Updates multiple contacts at once",
		responses: {
			200: {
				description: "Contacts updated successfully",
				content: {
					"application/json": {
						schema: resolver(
							z.object({
								updated: z.number(),
								contacts: z.array(z.any()),
							}),
						),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const { contactIds, updates, organizationId } = c.req.valid("json");

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			// Use transaction for bulk update
			const updatedContacts = await db.$transaction(
				contactIds.map((id: string) =>
					db.contact.update({
						where: {
							id,
							organizationId,
							isDeleted: false,
						},
						data: {
							...updates,
							updatedBy: user.id,
						},
						include: CONTACT_INCLUDE,
					}),
				),
			);

			const transformedContacts = updatedContacts.map(transformContact);
			logger.info(
				`Bulk updated ${contactIds.length} contacts by user ${user.id}`,
			);

			return c.json({
				updated: updatedContacts.length,
				contacts: transformedContacts,
			});
		} catch (error) {
			logger.error("Failed to bulk update contacts:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Soft delete contact
contactsRouter.delete(
	"/contacts/:id",
	authMiddleware,
	describeRoute({
		tags: ["Contacts"],
		summary: "Delete contact",
		description: "Soft deletes a contact",
		responses: {
			204: {
				description: "Contact deleted successfully",
			},
			404: {
				description: "Contact not found",
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const id = c.req.param("id");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			await db.contact.update({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
				data: {
					isDeleted: true,
					deletedAt: new Date(),
					deletedBy: user.id,
				},
			});

			logger.info(`Contact soft deleted: ${id} by user ${user.id}`);
			return c.body(null, 204);
		} catch (error) {
			logger.error("Failed to delete contact:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Bulk delete contacts
contactsRouter.delete(
	"/contacts/bulk",
	authMiddleware,
	validator("json", ContactBulkDeleteSchema),
	describeRoute({
		tags: ["Contacts"],
		summary: "Bulk delete contacts",
		description: "Bulk soft deletes multiple contacts",
		responses: {
			200: {
				description: "Contacts deleted successfully",
				content: {
					"application/json": {
						schema: resolver(z.object({ deleted: z.number() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const { contactIds, organizationId, permanent } =
				c.req.valid("json");

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			let result;
			if (permanent) {
				// Permanent delete
				result = await db.contact.deleteMany({
					where: {
						id: { in: contactIds },
						organizationId,
					},
				});
			} else {
				// Soft delete
				result = await db.contact.updateMany({
					where: {
						id: { in: contactIds },
						organizationId,
						isDeleted: false,
					},
					data: {
						isDeleted: true,
						deletedAt: new Date(),
						deletedBy: user.id,
					},
				});
			}

			logger.info(
				`Bulk deleted ${result.count} contacts by user ${user.id}`,
			);
			return c.json({ deleted: result.count });
		} catch (error) {
			logger.error("Failed to bulk delete contacts:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

export type ContactsRouter = typeof contactsRouter;
