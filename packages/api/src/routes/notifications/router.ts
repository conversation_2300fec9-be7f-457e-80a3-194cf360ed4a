import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { NotificationSchema } from "@repo/database/src/zod";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { notificationSettingsRouter } from "./settings";

const CreateNotificationInputSchema = NotificationSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

export const notificationsRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// CREATE NOTIFICATION
notificationsRouter.post("/notifications", authMiddleware, async (c) => {
	const body = await c.req.json();
	const parse = CreateNotificationInputSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const data = parse.data;

	try {
		const notification = await db.notification.create({
			data: {
				userId: data.userId,
				type: data.type,
				title: data.title,
				body: data.body,
				data: data.data,
				read: data.read ?? false,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});
		return c.json(notification, 201);
	} catch (error) {
		console.error("[API] Failed to create notification:", error);
		return c.json(
			{
				error: "Failed to create notification",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// GET NOTIFICATIONS
notificationsRouter.get("/notifications", authMiddleware, async (c) => {
	const user = c.get("user");

	try {
		const notifications = await db.notification.findMany({
			where: { userId: user.id },
			orderBy: { createdAt: "desc" },
			cacheStrategy: {
				ttl: 60, // 1 minute cache for notifications (very dynamic)
				tags: [`notifications_user_${user.id}`, `user_${user.id}`],
			},
		});
		return c.json(notifications, 200);
	} catch (error) {
		console.error("[API] Failed to fetch notifications:", error);
		return c.json(
			{
				error: "Failed to fetch notifications",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// MARK AS READ
notificationsRouter.patch(
	"/notifications/:id/read",
	authMiddleware,
	async (c) => {
		const user = c.get("user");
		const id = c.req.param("id");

		try {
			const notification = await db.notification.findFirst({
				where: { id, userId: user.id },
				cacheStrategy: {
					ttl: 60, // 1 minute cache for individual notification checks
					tags: [`notification_${id}`, `user_${user.id}`],
				},
			});
			if (!notification) {
				return c.json({ error: "Notification not found" }, 404);
			}
			const updated = await db.notification.update({
				where: { id },
				data: { read: true, updatedAt: new Date() },
			});
			return c.json(updated, 200);
		} catch (error) {
			console.error("[API] Failed to mark notification as read:", error);
			return c.json(
				{
					error: "Failed to mark notification as read",
					details:
						error instanceof Error
							? {
									message: error.message,
									stack: error.stack,
									name: error.name,
								}
							: error,
				},
				500,
			);
		}
	},
);

// ARCHIVE NOTIFICATION
notificationsRouter.patch(
	"/notifications/:id/archive",
	authMiddleware,
	async (c) => {
		const user = c.get("user");
		const id = c.req.param("id");

		try {
			const notification = await db.notification.findFirst({
				where: { id, userId: user.id },
				cacheStrategy: {
					ttl: 60, // 1 minute cache for individual notification checks
					tags: [`notification_${id}`, `user_${user.id}`],
				},
			});
			if (!notification) {
				return c.json({ error: "Notification not found" }, 404);
			}
			const updated = await db.notification.update({
				where: { id },
				data: { archived: true, updatedAt: new Date() },
			});
			return c.json(updated, 200);
		} catch (error) {
			console.error("[API] Failed to archive notification:", error);
			return c.json(
				{
					error: "Failed to archive notification",
					details:
						error instanceof Error
							? {
									message: error.message,
									stack: error.stack,
									name: error.name,
								}
							: error,
				},
				500,
			);
		}
	},
);

// DELETE NOTIFICATION
notificationsRouter.delete("/notifications/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const id = c.req.param("id");

	try {
		const notification = await db.notification.findFirst({
			where: { id, userId: user.id },
		});
		if (!notification) {
			return c.json({ error: "Notification not found" }, 404);
		}
		await db.notification.delete({
			where: { id },
		});
		return c.json({ message: "Notification deleted" }, 200);
	} catch (error) {
		console.error("[API] Failed to delete notification:", error);
		return c.json(
			{
				error: "Failed to delete notification",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

notificationsRouter.route("/notifications", notificationSettingsRouter);

export type NotificationsRouter = typeof notificationsRouter;
