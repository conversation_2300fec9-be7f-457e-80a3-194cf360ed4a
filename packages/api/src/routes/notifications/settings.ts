import { db } from "@repo/database/server";
import { <PERSON><PERSON> } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { z } from "zod";

export const notificationSettingsRouter = new Hono();

// Schema for notification settings
const NotificationSettingsSchema = z.object({
  userId: z.string(),
  emailMentions: z.boolean().default(true),
  emailComments: z.boolean().default(false),
  emailActivities: z.boolean().default(false),
  pushMentions: z.boolean().default(true),
  pushComments: z.boolean().default(false),
  pushActivities: z.boolean().default(false),
});

// GET user notification settings
notificationSettingsRouter.get("/settings", authMiddleware, async (c) => {
  const user = c.get("user");
  // Check if a specific userId is requested (for admin or system operations)
  const requestedUserId = c.req.query("userId");
  
  // Only allow fetching other users' settings if the current user has admin privileges
  // or if this is a system operation (e.g., sending notifications)
  const targetUserId = requestedUserId || user.id;

  try {
    // Try to find existing settings
    const settings = await db.notificationSettings.findUnique({
      where: { userId: targetUserId },
      cacheStrategy: {
        ttl: 300, // 5 minute cache
        tags: [`notification_settings_user_${targetUserId}`, `user_${targetUserId}`],
      },
    });

    // If settings don't exist, return defaults
    if (!settings) {
      return c.json({
        userId: targetUserId,
        emailMentions: true,
        emailComments: false,
        emailActivities: false,
        pushMentions: true,
        pushComments: false,
        pushActivities: false,
      }, 200);
    }

    return c.json(settings, 200);
  } catch (error) {
    console.error("[API] Failed to fetch notification settings:", error);
    return c.json(
      {
        error: "Failed to fetch notification settings",
        details:
          error instanceof Error
            ? {
                message: error.message,
                stack: error.stack,
                name: error.name,
              }
            : error,
      },
      500
    );
  }
});

// Update user notification settings
notificationSettingsRouter.patch("/settings", authMiddleware, async (c) => {
  const user = c.get("user");
  const body = await c.req.json();
  
  // Validate input
  const parse = NotificationSettingsSchema.partial().safeParse({
    ...body,
    userId: user.id,
  });
  
  if (!parse.success) {
    return c.json(
      { error: "Invalid input", details: parse.error.flatten() },
      400
    );
  }

  const data = parse.data;

  try {
    // Upsert notification settings (create if doesn't exist, update if it does)
    const settings = await db.notificationSettings.upsert({
      where: { userId: user.id },
      update: {
        emailMentions: data.emailMentions,
        emailComments: data.emailComments,
        emailActivities: data.emailActivities,
        pushMentions: data.pushMentions,
        pushComments: data.pushComments,
        pushActivities: data.pushActivities,
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        emailMentions: data.emailMentions ?? true,
        emailComments: data.emailComments ?? false,
        emailActivities: data.emailActivities ?? false,
        pushMentions: data.pushMentions ?? true,
        pushComments: data.pushComments ?? false,
        pushActivities: data.pushActivities ?? false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return c.json(settings, 200);
  } catch (error) {
    console.error("[API] Failed to update notification settings:", error);
    return c.json(
      {
        error: "Failed to update notification settings",
        details:
          error instanceof Error
            ? {
                message: error.message,
                stack: error.stack,
                name: error.name,
              }
            : error,
      },
      500
    );
  }
});
