import { auth } from "@repo/auth";
import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth";

const app = new Hono<{ Variables: { user: Session["user"] } }>();

// Helper function to decode JWT token
function parseJWTToken(token: string) {
	try {
		const base64Url = token.split('.')[1];
		const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
		const jsonPayload = decodeURIComponent(
			atob(base64)
				.split('')
				.map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
				.join('')
		);
		return JSON.parse(jsonPayload);
	} catch (error) {
		console.error('Error parsing JWT token:', error);
		return null;
	}
}

app.get("/auth/*", (c) => {
	return auth.handler(c.req.raw);
});

app.post("/auth/*", (c) => {
	return auth.handler(c.req.raw);
});

// New connected accounts endpoint
app.get("/connected-accounts", authMiddleware, async (c) => {
	const user = c.get("user");

	try {
		// Fetch user's connected accounts from database
		const accounts = await db.account.findMany({
			where: {
				userId: user.id,
			},
			select: {
				id: true,
				accountId: true,
				providerId: true,
				idToken: true,
				accessTokenExpiresAt: true,
				refreshToken: true, // Include refresh token to detect missing tokens
				scope: true,
				createdAt: true,
				updatedAt: true,
			},
		});

		// Transform accounts to include parsed user info
		const connectedAccounts = accounts.map((account) => {
			let email = '';
			let name = '';
			let picture = '';
			
			// Parse JWT token to extract user info
			if (account.idToken) {
				const tokenData = parseJWTToken(account.idToken);
				if (tokenData) {
					email = tokenData.email || '';
					name = tokenData.name || '';
					picture = tokenData.picture || '';
				}
			}

			return {
				id: account.id,
				provider: account.providerId,
				accountId: account.accountId,
				email,
				name,
				picture,
				services: account.providerId === 'google' ? ['Email', 'Calendar'] : ['Email'],
				status: 'In Sync',
				accessTokenExpiresAt: account.accessTokenExpiresAt,
				refreshToken: account.refreshToken, // Include refresh token field
				scope: account.scope,
				createdAt: account.createdAt,
			};
		});

		return c.json({ accounts: connectedAccounts });
	} catch (error) {
		console.error('Error fetching connected accounts:', error);
		return c.json({ error: "Failed to fetch connected accounts" }, 500);
	}
});

// Get individual connected account
app.get("/connected-accounts/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const accountId = c.req.param("id");

	try {
		// Fetch the specific account for this user
		const account = await db.account.findFirst({
			where: {
				id: accountId,
				userId: user.id,
			},
			select: {
				id: true,
				accountId: true,
				providerId: true,
				idToken: true,
				accessTokenExpiresAt: true,
				refreshToken: true,
				scope: true,
				createdAt: true,
				updatedAt: true,
			},
		});

		if (!account) {
			return c.json({ error: "Connected account not found" }, 404);
		}

		let email = '';
		let name = '';
		let picture = '';
		
		// Parse JWT token to extract user info
		if (account.idToken) {
			const tokenData = parseJWTToken(account.idToken);
			if (tokenData) {
				email = tokenData.email || '';
				name = tokenData.name || '';
				picture = tokenData.picture || '';
			}
		}

		const connectedAccount = {
			id: account.id,
			provider: account.providerId,
			accountId: account.accountId,
			email,
			name,
			picture,
			services: account.providerId === 'google' ? ['Email', 'Calendar'] : ['Email'],
			status: 'In Sync',
			accessTokenExpiresAt: account.accessTokenExpiresAt,
			refreshToken: account.refreshToken, // Include refresh token field
			scope: account.scope,
			createdAt: account.createdAt,
		};

		return c.json(connectedAccount);
	} catch (error) {
		console.error('Error fetching connected account:', error);
		return c.json({ error: "Failed to fetch connected account" }, 500);
	}
});

// Delete connected account
app.delete("/connected-accounts/:id", authMiddleware, async (c) => {
	const user = c.get("user");
	const accountId = c.req.param("id");

	try {
		// Verify the account belongs to this user and delete it
		const deletedAccount = await db.account.deleteMany({
			where: {
				id: accountId,
				userId: user.id,
			},
		});

		if (deletedAccount.count === 0) {
			return c.json({ error: "Connected account not found" }, 404);
		}

		return c.json({ message: "Connected account removed successfully" });
	} catch (error) {
		console.error('Error deleting connected account:', error);
		return c.json({ error: "Failed to remove connected account" }, 500);
	}
});

// Fetch user/contact data by email addresses for email composition
app.post("/users-by-emails", authMiddleware, async (c) => {
	const user = c.get("user");
	
	try {
		const body = await c.req.json();
		const { emails, organizationId } = body;

		if (!Array.isArray(emails) || !organizationId) {
			return c.json({ error: "emails array and organizationId are required" }, 400);
		}

		// Verify user has access to the organization
		const membership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId,
			},
		});

		if (!membership) {
			return c.json({ error: "Access denied to organization" }, 403);
		}

		const results = [];

		for (const email of emails) {
			if (!email || typeof email !== 'string') continue;

			// First, try to find a user with this email in the organization
			const orgUser = await db.member.findFirst({
				where: {
					organizationId,
					user: {
						email: {
							equals: email,
							mode: "insensitive",
						},
					},
				},
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							image: true,
						},
					},
				},
			}) as any; // Type assertion to bypass TypeScript issue

			if (orgUser && orgUser.user) {
				results.push({
					email,
					name: orgUser.user.name || email,
					avatarUrl: orgUser.user.image,
					type: "user",
					allEmails: [email],
				});
				continue;
			}

			// If not found as a user, try to find as a contact
			const contacts = await db.contact.findMany({
				where: {
					organizationId,
					isDeleted: false,
				},
				select: {
					id: true,
					firstName: true,
					lastName: true,
					image: true,
					email: true,
				},
			});

			// Find contact with matching email
			const matchingContact = contacts.find(contact => {
				if (!contact.email || !Array.isArray(contact.email)) return false;
				return (contact.email as any[]).some((emailObj: any) => 
					(emailObj.address || emailObj.value)?.toLowerCase() === email.toLowerCase()
				);
			});

			if (matchingContact) {
				const name = `${matchingContact.firstName || ""} ${matchingContact.lastName || ""}`.trim();
				// Extract all emails from contact
				const allEmails = matchingContact.email 
					? (matchingContact.email as any[])
						.map((e: any) => e.address || e.value)
						.filter(Boolean)
					: [email];

				results.push({
					email,
					name: name || email,
					avatarUrl: matchingContact.image,
					type: "contact",
					allEmails,
				});
				continue;
			}

			// If neither user nor contact found, return basic info
			results.push({
				email,
				name: email,
				avatarUrl: null,
				type: "unknown",
				allEmails: [email],
			});
		}

		return c.json({ users: results });
	} catch (error) {
		console.error('Error fetching users by emails:', error);
		return c.json({ error: "Failed to fetch users by emails" }, 500);
	}
});

export const authRouter = app;
