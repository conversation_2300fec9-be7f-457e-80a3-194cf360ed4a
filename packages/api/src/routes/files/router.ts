import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { FileSchema, FolderSchema } from "@repo/database/src/zod";
import { getFileCategoryFromMimeType, getFileExtension, getFileTypeFromExtension } from "@repo/storage/types";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import permissionsRouter from "./permissions";

const CreateFileInputSchema = FileSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

const CreateFolderInputSchema = FolderSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

export const filesRouter = new Hono<{ Variables: { user: Session["user"] } }>();

// Create a new folder
filesRouter.post("/folders", authMiddleware, async (c) => {
	const body = await c.req.json();
	const parse = CreateFolderInputSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const data = parse.data;
	const user = c.get("user");

	try {
		// Check if parent folder exists and user has access
		if (data.parentId) {
			const parentFolder = await db.folder.findFirst({
				where: {
					id: data.parentId,
					organizationId: data.organizationId,
					isDeleted: false,
				},
			});
			if (!parentFolder) {
				return c.json({ error: "Parent folder not found" }, 404);
			}
		}

		const folder = await db.folder.create({
			data: {
				name: data.name,
				description: data.description,
				parentId: data.parentId,
				organizationId: data.organizationId,
				isPublic: data.isPublic || false,
				createdBy: user?.id || "",
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		return c.json(folder, 201);
	} catch (error) {
		console.error("[API] Failed to create folder:", error);
		return c.json(
			{
				error: "Failed to create folder",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Get folders for an organization
filesRouter.get("/folders", authMiddleware, async (c) => {
	const organizationId = c.req.query("organizationId");
	const parentId = c.req.query("parentId");
	
	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		const whereClause: any = {
			organizationId,
			isDeleted: false,
		};

		// Filter by parent folder if specified
		if (parentId === "null" || parentId === "") {
			whereClause.parentId = null;
		} else if (parentId) {
			whereClause.parentId = parentId;
		}

		const folders = await db.folder.findMany({
			where: whereClause,
			orderBy: { name: "asc" },
			include: {
				creator: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
				_count: {
					select: {
						files: true,
						children: true,
					},
				},
			},
			cacheStrategy: {
				ttl: 300, // 5 minutes
				tags: [`folders_org_${organizationId}`, `org_${organizationId}`],
			},
		});

		return c.json(folders, 200);
	} catch (error) {
		console.error("[API] Failed to fetch folders:", error);
		return c.json(
			{
				error: "Failed to fetch folders",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Create a new file record
filesRouter.post("/files", authMiddleware, async (c) => {
	const body = await c.req.json();
	const parse = CreateFileInputSchema.safeParse(body);
	if (!parse.success) {
		return c.json(
			{ error: "Invalid input", details: parse.error.flatten() },
			400,
		);
	}
	const data = parse.data;
	const user = c.get("user");

	try {
		// Check if folder exists and user has access
		if (data.folderId) {
			const folder = await db.folder.findFirst({
				where: {
					id: data.folderId,
					organizationId: data.organizationId,
					isDeleted: false,
				},
			});
			if (!folder) {
				return c.json({ error: "Folder not found" }, 404);
			}
		}

		// Determine file type and category
		const extension = getFileExtension(data.originalName);
		const fileCategory = data.mimeType 
			? getFileCategoryFromMimeType(data.mimeType)
			: getFileTypeFromExtension(extension);

		const file = await db.file.create({
			data: {
				name: data.name,
				originalName: data.originalName,
				description: data.description,
				size: data.size,
				mimeType: data.mimeType,
				fileType: fileCategory,
				extension: extension || null,
				url: data.url,
				edgeStoreUrl: data.edgeStoreUrl,
				thumbnailUrl: data.thumbnailUrl,
				folderId: data.folderId,
				organizationId: data.organizationId,
				isPublic: data.isPublic || false,
				uploadedBy: user?.id || "",
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		return c.json(file, 201);
	} catch (error) {
		console.error("[API] Failed to create file:", error);
		return c.json(
			{
				error: "Failed to create file",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Get files for an organization/folder
filesRouter.get("/files", authMiddleware, async (c) => {
	const organizationId = c.req.query("organizationId");
	const folderId = c.req.query("folderId");
	const fileType = c.req.query("fileType");
	const search = c.req.query("search");
	
	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		const whereClause: any = {
			organizationId,
			isDeleted: false,
		};

		// Filter by folder if specified
		if (folderId === "null" || folderId === "") {
			whereClause.folderId = null;
		} else if (folderId) {
			whereClause.folderId = folderId;
		}

		// Filter by file type if specified
		if (fileType) {
			whereClause.fileType = fileType;
		}

		// Search by name if specified
		if (search) {
			whereClause.OR = [
				{ name: { contains: search, mode: "insensitive" } },
				{ originalName: { contains: search, mode: "insensitive" } },
				{ description: { contains: search, mode: "insensitive" } },
			];
		}

		const files = await db.file.findMany({
			where: whereClause,
			orderBy: { createdAt: "desc" },
			include: {
				uploader: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
				folder: {
					select: {
						id: true,
						name: true,
					},
				},
			},
			cacheStrategy: {
				ttl: 300, // 5 minutes
				tags: [`files_org_${organizationId}`, `org_${organizationId}`],
			},
		});

		return c.json(files, 200);
	} catch (error) {
		console.error("[API] Failed to fetch files:", error);
		return c.json(
			{
				error: "Failed to fetch files",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Update a folder
filesRouter.patch("/folders/:id", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const body = await c.req.json();
	const user = c.get("user");

	try {
		const updatedFolder = await db.folder.update({
			where: { id },
			data: {
				...body,
				updatedBy: user?.id,
				updatedAt: new Date(),
			},
		});

		return c.json(updatedFolder, 200);
	} catch (error) {
		console.error("[API] Failed to update folder:", error);
		return c.json({ error: "Failed to update folder" }, 500);
	}
});

// Delete a folder (soft delete)
filesRouter.delete("/folders/:id", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const user = c.get("user");

	try {
		// Check if folder has children or files
		const folder = await db.folder.findUnique({
			where: { id },
			include: {
				children: true,
				files: true,
			},
		});

		if (!folder) {
			return c.json({ error: "Folder not found" }, 404);
		}

		if (folder.children.length > 0 || folder.files.length > 0) {
			return c.json({ error: "Cannot delete folder with contents" }, 400);
		}

		await db.folder.update({
			where: { id },
			data: {
				isDeleted: true,
				deletedBy: user?.id,
				deletedAt: new Date(),
				updatedAt: new Date(),
			},
		});

		return c.body(null, 204);
	} catch (error) {
		console.error("[API] Failed to delete folder:", error);
		return c.json({ error: "Failed to delete folder" }, 500);
	}
});

// Update a file
filesRouter.patch("/files/:id", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const body = await c.req.json();
	const user = c.get("user");

	try {
		const updatedFile = await db.file.update({
			where: { id },
			data: {
				...body,
				updatedBy: user?.id,
				updatedAt: new Date(),
			},
		});

		return c.json(updatedFile, 200);
	} catch (error) {
		console.error("[API] Failed to update file:", error);
		return c.json({ error: "Failed to update file" }, 500);
	}
});

// Delete a file (soft delete)
filesRouter.delete("/files/:id", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const user = c.get("user");

	try {
		await db.file.update({
			where: { id },
			data: {
				isDeleted: true,
				deletedBy: user?.id,
				deletedAt: new Date(),
				updatedAt: new Date(),
			},
		});

		return c.body(null, 204);
	} catch (error) {
		console.error("[API] Failed to delete file:", error);
		return c.json({ error: "Failed to delete file" }, 500);
	}
});

// Move file to different folder
filesRouter.patch("/files/:id/move", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const body = await c.req.json();
	const { folderId } = body;
	const user = c.get("user");

	try {
		// Verify target folder exists if provided
		if (folderId) {
			const targetFolder = await db.folder.findFirst({
				where: {
					id: folderId,
					isDeleted: false,
				},
			});
			if (!targetFolder) {
				return c.json({ error: "Target folder not found" }, 404);
			}
		}

		const updatedFile = await db.file.update({
			where: { id },
			data: {
				folderId: folderId || null,
				updatedBy: user?.id,
				updatedAt: new Date(),
			},
		});

		return c.json(updatedFile, 200);
	} catch (error) {
		console.error("[API] Failed to move file:", error);
		return c.json({ error: "Failed to move file" }, 500);
	}
});

// Copy file
filesRouter.post("/files/:id/copy", authMiddleware, async (c) => {
	const id = c.req.param("id");
	const body = await c.req.json();
	const { folderId, name } = body;
	const user = c.get("user");

	try {
		const originalFile = await db.file.findUnique({
			where: { id },
		});

		if (!originalFile) {
			return c.json({ error: "File not found" }, 404);
		}

		// Verify target folder exists if provided
		if (folderId) {
			const targetFolder = await db.folder.findFirst({
				where: {
					id: folderId,
					isDeleted: false,
				},
			});
			if (!targetFolder) {
				return c.json({ error: "Target folder not found" }, 404);
			}
		}

		const copiedFile = await db.file.create({
			data: {
				name: name || `Copy of ${originalFile.name}`,
				originalName: originalFile.originalName,
				description: originalFile.description,
				size: originalFile.size,
				mimeType: originalFile.mimeType,
				fileType: originalFile.fileType,
				extension: originalFile.extension,
				url: originalFile.url,
				edgeStoreUrl: originalFile.edgeStoreUrl,
				thumbnailUrl: originalFile.thumbnailUrl,
				folderId: folderId || originalFile.folderId,
				organizationId: originalFile.organizationId,
				isPublic: originalFile.isPublic,
				uploadedBy: user?.id || "",
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		return c.json(copiedFile, 201);
	} catch (error) {
		console.error("[API] Failed to copy file:", error);
		return c.json({ error: "Failed to copy file" }, 500);
	}
});

// Mount permissions router
filesRouter.route("/", permissionsRouter);

export type FilesRouter = typeof filesRouter;
