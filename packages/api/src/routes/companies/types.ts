import { z } from "zod";

// Company schema based on Prisma model
export const CompanyCreateSchema = z.object({
	name: z.string().min(1, "Name is required"),
	website: z.string().url().optional().or(z.literal("")),
	industry: z.string().optional(),
	size: z.string().optional(),
	description: z.string().optional(),
	logo: z.string().optional(),
	address: z.any().optional(),
	phone: z.string().optional(),
	email: z.string().email().optional().or(z.literal("")),
	organizationId: z.string(),
});

export const CompanyUpdateSchema = z.object({
	name: z.string().min(1).optional(),
	website: z.string().url().optional().or(z.literal("")),
	industry: z.string().optional(),
	size: z.string().optional(),
	description: z.string().optional(),
	logo: z.string().optional(),
	address: z.any().optional(),
	phone: z.string().optional(),
	email: z.string().email().optional().or(z.literal("")),
	organizationId: z.string().optional(),
});

export const CompanyQuerySchema = z.object({
	organizationId: z.string(),
	limit: z.coerce.number().default(50),
	offset: z.coerce.number().default(0),
	search: z.string().optional(),
	industry: z.string().optional(),
});

export interface CompanyWithRelations {
	id: string;
	name: string;
	website: string | null;
	industry: string | null;
	size: string | null;
	description: string | null;
	logo: string | null;
	address: any;
	phone: string | null;
	email: string | null;
	organizationId: string;
	createdBy: string;
	updatedBy: string | null;
	isDeleted: boolean;
	deletedAt: Date | null;
	deletedBy: string | null;
	createdAt: Date;
	updatedAt: Date;
	contacts: Array<{
		id: string;
		firstName: string | null;
		lastName: string | null;
		email: any;
	}>;
	creator: {
		id: string;
		name: string;
		email: string;
		image: string | null;
	};
	updater?: {
		id: string;
		name: string;
		email: string;
		image: string | null;
	} | null;
}

export type CompanyCreateInput = z.infer<typeof CompanyCreateSchema>;
export type CompanyUpdateInput = z.infer<typeof CompanyUpdateSchema>;
export type CompanyQueryInput = z.infer<typeof CompanyQuerySchema>;
