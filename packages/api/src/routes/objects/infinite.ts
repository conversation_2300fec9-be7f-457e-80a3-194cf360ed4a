import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	getDbTable,
	OBJECT_CONFIG,
	type ObjectType,
	normalizeObjectType,
} from "./config";

export const objectsInfiniteRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Middleware to validate object type for infinite routes only
const validateObjectTypeMiddleware = async (c: any, next: any) => {
	const objectTypeParam = c.req.param("objectType");
	const objectType = normalizeObjectType(objectTypeParam);

	if (!objectType) {
		return c.json({ error: "Invalid object type" }, 400);
	}

	c.set("objectType" as any, objectType);
	await next();
};

// Infinite query endpoint
objectsInfiniteRouter.get(
	"/objects/:objectType/infinite",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;

			// Parse query parameters
			const organizationId = c.req.query("organizationId");
			const limit = Math.min(
				Number.parseInt(c.req.query("limit") || "50"),
				100,
			);
			const cursor = c.req.query("cursor");
			const direction = c.req.query("direction") || "next";
			const search = c.req.query("search");
			const createdAt = c.req.query("createdAt");

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Build base where conditions
			const whereConditions: any = {
				organizationId,
				isDeleted: false,
			};

			// Add object-specific filters based on query parameters
			Object.keys(config.filterFields).forEach((field) => {
				const value = c.req.query(field);
				if (value && field !== "createdAt") {
					const filterType = (config.filterFields as any)[field];

					if (filterType === "string") {
						if (field === "name" && objectType === "contact") {
							// Special handling for name in contacts
							whereConditions.OR = [
								{
									firstName: {
										contains: value,
										mode: "insensitive",
									},
								},
								{
									lastName: {
										contains: value,
										mode: "insensitive",
									},
								},
							];
						} else {
							whereConditions[field] = {
								contains: value,
								mode: "insensitive",
							};
						}
					} else if (
						filterType === "relation" &&
						field === "company" &&
						objectType === "contact"
					) {
						whereConditions.company = {
							name: { contains: value, mode: "insensitive" },
						};
					} else {
						whereConditions[field] = value;
					}
				}
			});

			// Handle tags filtering
			const tags = c.req.query("tags");
			if (tags) {
				const tagNames = tags.split(",").map(tag => tag.trim()).filter(Boolean);
				if (tagNames.length > 0) {
					// Import normalization function
					const { normalizeTagName } = await import("../../lib/tag-utils");
					
					// Normalize tag names for consistent matching
					const normalizedTagNames = tagNames.map(tagName => normalizeTagName(tagName));
					
					whereConditions.objectTags = {
						some: {
							tag: {
								name: {
									in: normalizedTagNames,
								},
								organizationId,
							},
						},
					};
				}
			}

			// Handle search across configured search fields
			if (search) {
				whereConditions.OR = config.searchFields.map((field) => ({
					[field]: { contains: search, mode: "insensitive" },
				}));
			}

			// Handle createdAt date range filter
			if (createdAt) {
				try {
					let startDate: Date | null = null;
					let endDate: Date | null = null;

					// Helper function to parse date string
					const parseDate = (dateStr: string): Date | null => {
						try {
							// Try parsing as timestamp first
							if (/^\d+$/.test(dateStr.trim())) {
								const date = new Date(Number.parseInt(dateStr.trim()));
								if (!isNaN(date.getTime())) return date;
							}
							
							// Try parsing as ISO string
							const date = new Date(dateStr.trim());
							if (!isNaN(date.getTime())) return date;
							
							return null;
						} catch (error) {
							logger.error("Error parsing date:", { dateStr, error });
							return null;
						}
					};

					if (createdAt.includes(",")) {
						// Handle comma-separated date strings (from frontend date picker)
						const [startString, endString] = createdAt.split(",");
						startDate = parseDate(startString);
						endDate = parseDate(endString);
					} else if (createdAt.includes(":")) {
						// Handle colon-separated timestamps (from URL parameters)
						const [startString, endString] = createdAt.split(":");
						startDate = parseDate(startString);
						endDate = parseDate(endString);
					} else {
						// Single date/timestamp
						const date = parseDate(createdAt);
						if (date) {
							startDate = new Date(date);
							startDate.setUTCHours(0, 0, 0, 0);
							endDate = new Date(date);
							endDate.setUTCHours(23, 59, 59, 999);
						}
					}

					// Only add date filter if we have valid dates
					if (startDate && endDate) {
						whereConditions.createdAt = {
							gte: startDate,
							lte: endDate,
						};
					} else {
						logger.warn("Invalid date range:", { createdAt });
					}
				} catch (error) {
					logger.error("Error parsing createdAt filter:", error);
				}
			}

			// Build final where conditions
			const finalWhereConditions = {
				organizationId,
				isDeleted: false,
				...whereConditions,
			};

			// Add cursor conditions for pagination (but don't interfere with date filters)
			if (cursor && !createdAt) {
				// Only apply cursor pagination if no date filter is active
				const cursorDate = new Date(cursor);
				const cursorCondition =
					direction === "next"
						? { lt: cursorDate }
						: { gt: cursorDate };

				finalWhereConditions.createdAt = cursorCondition;
			}

			// Fetch results
			const results = await table.findMany({
				where: finalWhereConditions,
				include: config.include,
				orderBy: { createdAt: direction === "next" ? "desc" : "asc" },
				take: limit + 1,
				cacheStrategy: {
					ttl: 180, // 3 minutes cache for infinite scroll (more dynamic)
					tags: [`infinite_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			// Get total count
			const total = await table.count({
				where: {
					organizationId,
					isDeleted: false,
				},
				cacheStrategy: {
					ttl: 300, // 5 minutes cache for count
					tags: [`total_count_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			const hasMore = results.length > limit;
			const data = hasMore ? results.slice(0, limit) : results;

			// When date filtering is active, disable cursor-based pagination
			const nextCursor =
				hasMore && data.length > 0 && !createdAt
					? data[data.length - 1]?.createdAt?.toISOString()
					: null;

			const prevCursor =
				data.length > 0 && !createdAt
					? data[0]?.createdAt?.toISOString()
					: null;

			// Transform results using object-specific transformer
			const transformedData = data.map(config.transformResult);

			// Calculate facets for all filters
			const facets: Record<string, any> = {};

			// Generate tags facets for all object types that support tags
			// First, get all available tags for this organization and object type
			const allTags = await db.tag.findMany({
				where: {
					organizationId,
					objectType,
				},
				select: {
					id: true,
					name: true,
				},
				cacheStrategy: {
					ttl: 600, // 10 minutes cache for tags
					tags: [`all_tags_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			if (allTags.length > 0) {
				// Get usage counts for tags that are actually attached to objects
				const tagUsageCounts = await db.objectTag.groupBy({
					by: ["tagId"],
					where: {
						objectType,
						tag: {
							organizationId,
						},
					},
					_count: { tagId: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets
						tags: [`facets_tags_${objectType}_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				// Create a map of tag usage counts
				const usageCountsMap = new Map(
					tagUsageCounts.map((item: any) => [item.tagId, item._count.tagId])
				);

				// Generate facet rows for all tags, showing 0 count for unused tags
				const facetRows = allTags.map((tag) => ({
					value: tag.name,
					total: usageCountsMap.get(tag.id) || 0,
				}));
				
				facets.tags = {
					rows: facetRows,
					total: tagUsageCounts.reduce(
						(sum: number, item: any) =>
							sum + (item._count.tagId as number),
						0,
					),
				};
			}

			if (objectType === "contact") {
				const statusCounts = await db.contact.groupBy({
					by: ["status"],
					where: { organizationId, isDeleted: false },
					_count: { status: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets (relatively stable)
						tags: [`facets_contact_status_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				facets.status = {
					rows: statusCounts.map((item: any) => ({
						value: (item.status as string) || "Unknown",
						total: (item._count.status as number) || 0,
					})),
					min: null,
					max: null,
				};
			} else if (objectType === "property") {
				// Property type facet
				const propertyTypeFacet = await db.property.groupBy({
					by: ["propertyType"],
					where: {
						organizationId,
						isDeleted: false,
						propertyType: { not: null },
					},
					_count: { propertyType: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets
						tags: [`facets_property_type_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				facets.propertyType = {
					rows: propertyTypeFacet.map((item: any) => ({
						value: (item.propertyType as string) || "Unknown",
						total: (item._count.propertyType as number) || 0,
					})),
					total: propertyTypeFacet.reduce(
						(sum: number, item: any) =>
							sum + item._count.propertyType,
						0,
					),
				};

				// Status facet
				const statusFacet = await db.property.groupBy({
					by: ["status"],
					where: {
						organizationId,
						isDeleted: false,
						status: { not: null },
					},
					_count: { status: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets
						tags: [`facets_property_status_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				facets.status = {
					rows: statusFacet.map((item: any) => ({
						value: (item.status as string) || "Unknown",
						total: (item._count.status as number) || 0,
					})),
					total: statusFacet.reduce(
						(sum: number, item: any) =>
							sum + (item._count.status as number),
						0,
					),
				};
			}

			return c.json({
				data: transformedData,
				meta: {
					totalRowCount: total,
					filterRowCount: total,
					chartData: [],
					facets,
					metadata: {
						objectType,
						totalCount: total,
						hasNextPage: hasMore,
					},
				},
				prevCursor,
				nextCursor,
			});
		} catch (error) {
			logger.error("Failed to fetch infinite data:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

export type ObjectsInfiniteRouter = typeof objectsInfiniteRouter;
