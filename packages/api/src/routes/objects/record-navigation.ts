import { Hono } from "hono";
import { db } from "@repo/database/server";
import { type ObjectType, pluralToSingularMap, objectTypes } from "@repo/database";
import type { Session } from "@repo/auth";

// Define SortOrder enum since we can't import it
const SortOrder = {
  asc: "asc",
  desc: "desc"
} as const;

export const recordNavigationRouter = new Hono<{
  Variables: { user: Session["user"] };
}>();

recordNavigationRouter.get("/getRecordIds", async (c) => {
  // Get all parameters from query string
  const { objectType, viewId, organizationId, currentId } = c.req.query();

  // Validate input
  if (!objectType || !organizationId || !currentId) {
    return c.json({ error: "Missing required parameters" }, 400);
  }

  // Convert object type to singular form using the mapping
  const normalizedObjectType = objectType.toLowerCase();
  const singularObjectType = pluralToSingularMap[normalizedObjectType] || normalizedObjectType;

  // Validate object type - check if it's a valid type
  if (!objectTypes.includes(singularObjectType as ObjectType)) {
    return c.json({ error: "Invalid object type" }, 400);
  }

  try {
    // Get the view to apply its filters
    const view = viewId ? await db.objectView.findUnique({
      where: { id: viewId },
      select: {
        columnDefs: true,
        filters: true,
        sortBy: true,
        sortDirection: true,
        filterCondition: true,
      }
    }) : null;

    // Build base query conditions
    const whereConditions: any = {
      organizationId,
      isDeleted: false,
    };

    // Apply view filters if they exist
    if (view?.filters && Array.isArray(view.filters) && view.filters.length > 0) {
      const filterConditions = view.filters.map((filter: any) => {
        const { field, logic, text, number } = filter;

        switch (logic) {
          case "contains":
            return {
              [field]: {
                contains: text,
                mode: "insensitive"
              }
            };
          
          case "equals":
            return {
              [field]: text || number
            };
          
          case "in":
            if (text && typeof text === "string") {
              const values = text.split(",").map(v => v.trim()).filter(Boolean);
              return {
                [field]: {
                  in: values
                }
              };
            }
            return null;
          
          case "between":
            if (Array.isArray(number) && number.length === 2) {
              return {
                [field]: {
                  gte: number[0],
                  lte: number[1]
                }
              };
            }
            return null;
          
          default:
            return null;
        }
      }).filter(Boolean);

      // Apply filter conditions based on filterCondition (AND/OR)
      if (filterConditions.length > 0) {
        if (view.filterCondition === "or") {
          whereConditions.OR = filterConditions;
        } else {
          // Default to AND - merge all conditions
          filterConditions.forEach(condition => {
            Object.assign(whereConditions, condition);
          });
        }
      }
    }

    // Apply URL query parameter filters (same logic as the main objects API)
    const queryParams = c.req.query();
    Object.keys(queryParams).forEach((key) => {
      const value = queryParams[key];
      
      // Skip system parameters
      if (["objectType", "viewId", "organizationId", "currentId"].includes(key) || !value) {
        return;
      }

      // Apply basic text search filters
      if (["name", "firstName", "lastName", "email", "title", "company"].includes(key)) {
        whereConditions[key] = {
          contains: value,
          mode: "insensitive"
        };
      }
      
      // Handle tags filtering
      if (key === "tags") {
        const tagNames = value.split(",").map((tag: string) => tag.trim()).filter(Boolean);
        if (tagNames.length > 0) {
          whereConditions.objectTags = {
            some: {
              tag: {
                name: {
                  in: tagNames,
                },
                organizationId,
              },
            },
          };
        }
      }
    });

    // Get sorting from view settings
    const orderBy = view?.sortBy ? {
      [view.sortBy]: (view.sortDirection?.toLowerCase() === 'desc' ? SortOrder.desc : SortOrder.asc)
    } : { createdAt: SortOrder.desc };

    // Get the correct model based on object type (always use singular form)
    const model = singularObjectType === 'contact' ? db.contact :
                 singularObjectType === 'company' ? db.company :
                 singularObjectType === 'property' ? db.property :
                 null;

    if (!model) {
      return c.json({ error: "Invalid object type" }, 400);
    }

    // Execute query with filters
    const records = await model.findMany({
      where: whereConditions,
      select: {
        id: true,
      },
      orderBy,
    });
    
    // Find the index of the current record
    const recordIds = records.map((r: { id: string }) => r.id);
    const currentIndex = recordIds.indexOf(currentId);

    // Get previous and next IDs
    const previousId = currentIndex > 0 ? recordIds[currentIndex - 1] : null;
    const nextId = currentIndex < recordIds.length - 1 ? recordIds[currentIndex + 1] : null;

    return c.json({
      previousId,
      nextId,
      totalRecords: recordIds.length,
      currentIndex
    });
  } catch (error) {
    console.error("Error in record navigation:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
}); 