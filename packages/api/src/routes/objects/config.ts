import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { z } from "zod";

// Valid object types
export const VALID_OBJECT_TYPES = [
	"contact",
	"company",
	"property",
] as const;
export type ObjectType = (typeof VALID_OBJECT_TYPES)[number];

// Mapping from plural/alternative forms to singular forms
const OBJECT_TYPE_MAPPING: Record<string, ObjectType> = {
	// Plural forms (from frontend)
	contacts: "contact",
	companies: "company", 
	properties: "property",
	// Singular forms (keep as-is)
	contact: "contact",
	company: "company",
	property: "property",
};

// Helper function to normalize object type (handles both singular and plural)
export function normalizeObjectType(objectType: string): ObjectType | null {
	const normalized = OBJECT_TYPE_MAPPING[objectType.toLowerCase()];
	return normalized || null;
}

import {
	CompanyCreateSchema,
	CompanyUpdateSchema,
	type CompanyWithRelations,
} from "../companies/types";
// Validation schemas imports
import {
	ContactCreateSchema,
	ContactUpdateSchema,
	type ContactWithRelations,
} from "../contacts/types";
import {
	PropertyCreateSchema,
	PropertyUpdateSchema,
	type PropertyWithRelations,
} from "../properties/types";

// Object configuration mapping
export const OBJECT_CONFIG = {
	contact: {
		tableName: "contact" as const,
		createSchema: ContactCreateSchema,
		updateSchema: ContactUpdateSchema,
		include: {
			company: {
				select: {
					id: true,
					name: true,
					website: true,
					logo: true,
				},
			},
			creator: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
			relatedContacts: true,
			objectTags: {
				include: {
					tag: {
						select: {
							id: true,
							name: true,
							color: true,
						},
					},
					adder: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
			},
		},
		searchFields: [
			"id",
			"firstName",
			"lastName",
			"title",
			"summary",
			"email",
		],
		filterFields: {
			status: "string",
			name: "string",
			firstName: "string",
			lastName: "string",
			title: "string",
			company: "relation",
			createdAt: "dateRange",
		},
		transformResult: (contact: any) => {
			try {
				return {
					id: contact.id,
					name: `${contact.firstName || ""} ${contact.lastName || ""}`.trim(),
					firstName: contact.firstName || "",
					lastName: contact.lastName || "",
					title: contact.title || "",
					image: contact.image || null,
					email: Array.isArray(contact.email)
						? contact.email.map((e: any) => ({
								address: e.value || e.address || "",
								label: e.label || "Work",
								isPrimary: e.isPrimary || false,
								isBad: e.isBad || false,
						  }))
						: [],
					phone: (() => {
						// Handle various phone data structures from MongoDB/Prisma
						const phoneData = contact.phone;
						
						// If phone is null or undefined, return empty array
						if (!phoneData) {
							return [];
						}
						
						// If phone is already a properly formatted array
						if (Array.isArray(phoneData)) {
							return phoneData.map((p: any) => ({
								number: p.number || p.value || "",
								label: p.label || "Work",
								isPrimary: Boolean(p.isPrimary),
								isBad: Boolean(p.isBad),
							}));
						}
						
						// If phone is a JSON string, try to parse it
						if (typeof phoneData === 'string') {
							try {
								const parsed = JSON.parse(phoneData);
								if (Array.isArray(parsed)) {
									return parsed.map((p: any) => ({
										number: p.number || p.value || "",
										label: p.label || "Work",
										isPrimary: Boolean(p.isPrimary),
										isBad: Boolean(p.isBad),
									}));
								}
							} catch (e) {
								console.error("Failed to parse phone JSON:", e);
								return [];
							}
						}
						
						// If phone is a single object, wrap it in an array
						if (typeof phoneData === 'object' && phoneData.number) {
							return [{
								number: phoneData.number || phoneData.value || "",
								label: phoneData.label || "Work",
								isPrimary: Boolean(phoneData.isPrimary),
								isBad: Boolean(phoneData.isBad),
							}];
						}
						
						// Fallback: return empty array
						console.warn("Unexpected phone data structure:", phoneData);
						return [];
					})(),
					address: Array.isArray(contact.address)
						? contact.address.map((a: any) => ({
								street: a.street || "",
								street2: a.street2 || "",
								city: a.city || "",
								state: a.state || "",
								zip: a.zip || "",
								country: a.country || "United States",
								label: a.label || "Work",
								isPrimary: a.isPrimary || false,
						  }))
						: [],
					company: contact.company
						? {
								id: contact.company.id,
								name: contact.company.name,
								website: contact.company.website,
								logo: contact.company.logo,
						  }
						: null,
					creator: contact.creator
						? {
								id: contact.creator.id,
								name: contact.creator.name,
								email: contact.creator.email,
								image: contact.creator.image,
						  }
						: null,
					tags: contact.objectTags?.map((ot: any) => ({
						id: ot.tag.id,
						name: ot.tag.name,
						color: ot.tag.color,
					})) || [],
					status: contact.status || null,
					stage: contact.stage || null,
					source: contact.source || null,
					persona: contact.persona || null,
					website: contact.website || null,
					spouseName: contact.spouseName || null,
					birthday: contact.birthday || null,
					summary: contact.summary || null,
					companyId: contact.companyId || null,
					createdAt: contact.createdAt,
					updatedAt: contact.updatedAt,
					createdBy: contact.createdBy,
					updatedBy: contact.updatedBy,
					isDeleted: contact.isDeleted || false,
				};
			} catch (error) {
				logger.error("Contact transformation error:", error, { contact });
				// Fallback: return minimal valid contact data
				return {
					id: contact?.id || "unknown",
					name: contact ? `${contact.firstName || ""} ${contact.lastName || ""}`.trim() || "Unknown Contact" : "Unknown Contact",
					email: [],
					phone: [],
					tags: [],
					status: null,
					stage: null,
					source: null,
					persona: null,
					website: null,
					spouseName: null,
					birthday: null,
					summary: null,
					companyId: null,
					company: null,
					createdAt: contact?.createdAt || new Date(),
					updatedAt: contact?.updatedAt || new Date(),
					createdBy: contact?.createdBy || null,
					updatedBy: contact?.updatedBy || null,
					isDeleted: true, // Mark as deleted so it won't show up in lists
				};
			}
		},
	},
	company: {
		tableName: "company" as const,
		createSchema: CompanyCreateSchema,
		updateSchema: CompanyUpdateSchema,
		include: {
			contacts: {
				select: {
					id: true,
					firstName: true,
					lastName: true,
					email: true,
				},
				where: { isDeleted: false },
			},
			creator: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
			objectTags: {
				include: {
					tag: {
						select: {
							id: true,
							name: true,
							color: true,
						},
					},
					adder: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
			},
		},
		searchFields: ["name", "description", "industry", "website"],
		filterFields: {
			industry: "string",
			size: "string",
			createdAt: "dateRange",
		},
		transformResult: (company: any) => {
			try {
				return {
					id: company.id,
					name: company.name,
					website: company.website || null,
					industry: company.industry || null,
					size: company.size || null,
					description: company.description || null,
					logo: company.logo || null,
					address: company.address || null,
					phone: company.phone || null,
					email: company.email || null,
					contacts: company.contacts || [],
					tags: company.objectTags?.map((ot: any) => ({
						id: ot.tag.id,
						name: ot.tag.name,
						color: ot.tag.color,
					})) || [],
					creator: company.creator
						? {
								id: company.creator.id,
								name: company.creator.name,
								email: company.creator.email,
								image: company.creator.image,
						  }
						: null,
					createdAt: company.createdAt,
					updatedAt: company.updatedAt,
					createdBy: company.createdBy,
					updatedBy: company.updatedBy,
					isDeleted: company.isDeleted || false,
				};
			} catch (error) {
				logger.error("Company transformation error:", error, { company });
				return {
					id: company?.id || "unknown",
					name: company?.name || "Unknown Company",
					tags: [],
					contacts: [],
					createdAt: company?.createdAt || new Date(),
					updatedAt: company?.updatedAt || new Date(),
					createdBy: company?.createdBy || null,
					updatedBy: company?.updatedBy || null,
					isDeleted: true,
				};
			}
		},
	},
	property: {
		tableName: "property" as const,
		createSchema: PropertyCreateSchema,
		updateSchema: PropertyUpdateSchema,
		include: {
			creator: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
			location: true,
			physicalDetails: true,
			financials: true,
			flags: true,
			mlsData: true,
			legalInfo: true,
			demographics: true,
			unitMixes: true,
			saleHistory: true,
			mortgages: true,
			foreclosureInfo: true,
			mlsHistory: true,
			objectTags: {
				include: {
					tag: {
						select: {
							id: true,
							name: true,
							color: true,
						},
					},
					adder: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
			},
		},
		searchFields: ["name", "propertyType", "market", "subMarket"],
		filterFields: {
			propertyType: "string",
			market: "string",
			subMarket: "string",
			status: "string",
			createdAt: "dateRange",
		},
		transformResult: (property: any) => {
			try {
				return {
					id: property.id,
					name: property.name,
					recordType: property.recordType || "property",
					image: property.image || null,
					propertyType: property.propertyType || null,
					propertySubType: property.propertySubType || null,
					market: property.market || null,
					subMarket: property.subMarket || null,
					listingId: property.listingId || null,
					status: property.status || null,
					tags: property.objectTags?.map((ot: any) => ({
						id: ot.tag.id,
						name: ot.tag.name,
						color: ot.tag.color,
					})) || [],
					lists: property.lists || [],
					linkedContacts: property.linkedContacts || [],
					linkedCompanies: property.linkedCompanies || [],
					tasks: property.tasks || [],
					location: property.location || null,
					physicalDetails: property.physicalDetails || null,
					financials: property.financials || null,
					flags: property.flags || null,
					mlsData: property.mlsData || null,
					legalInfo: property.legalInfo || null,
					demographics: property.demographics || null,
					unitMixes: property.unitMixes || [],
					saleHistory: property.saleHistory || [],
					mortgages: property.mortgages || [],
					foreclosureInfo: property.foreclosureInfo || [],
					mlsHistory: property.mlsHistory || [],
					creator: property.creator
						? {
								id: property.creator.id,
								name: property.creator.name,
								email: property.creator.email,
								image: property.creator.image,
						  }
						: null,
					createdAt: property.createdAt,
					updatedAt: property.updatedAt,
					createdBy: property.createdBy,
					updatedBy: property.updatedBy,
					isDeleted: property.isDeleted || false,
				};
			} catch (error) {
				logger.error("Property transformation error:", error, { property });
				return {
					id: property?.id || "unknown",
					name: property?.name || "Unknown Property",
					recordType: "property",
					tags: [],
					lists: [],
					linkedContacts: [],
					linkedCompanies: [],
					tasks: [],
					createdAt: property?.createdAt || new Date(),
					updatedAt: property?.updatedAt || new Date(),
					createdBy: property?.createdBy || null,
					updatedBy: property?.updatedBy || null,
					isDeleted: true,
				};
			}
		},
	},
} as const;

// Helper function to get database table
export function getDbTable(objectType: ObjectType) {
	const config = OBJECT_CONFIG[objectType];
	return (db as any)[config.tableName];
}

// Helper function to validate object type
export function validateObjectType(
	objectType: string,
): objectType is ObjectType {
	return VALID_OBJECT_TYPES.includes(objectType as ObjectType);
}

// Helper function to build search conditions
export function buildSearchConditions(
	objectType: ObjectType,
	search: string,
	additionalFilters: Record<string, any> = {},
) {
	const config = OBJECT_CONFIG[objectType];
	const conditions: any = {
		isDeleted: false,
		...additionalFilters,
	};

	if (search) {
		conditions.OR = config.searchFields.map((field) => ({
			[field]: { contains: search, mode: "insensitive" },
		}));
	}

	return conditions;
}
