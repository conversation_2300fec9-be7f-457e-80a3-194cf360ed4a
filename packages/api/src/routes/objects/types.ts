// Import types from individual object routers

import type {
	CompanyCreateInput,
	CompanyQueryInput,
	CompanyUpdateInput,
	CompanyWithRelations,
} from "../companies/types";
import type {
	ContactCreateInput,
	ContactQueryInput,
	ContactUpdateInput,
	ContactWithRelations,
} from "../contacts/types";

import type { PropertyWithRelations } from "../properties/types";

export type {
	CompanyCreateInput,
	CompanyQueryInput,
	CompanyUpdateInput,
	CompanyWithRelations,
} from "../companies/types";
// Re-export individual types
export type {
	ContactCreateInput,
	ContactQueryInput,
	ContactUpdateInput,
	ContactWithRelations,
} from "../contacts/types";
export type { PropertyWithRelations } from "../properties/types";

// Unified object types
export type ObjectWithRelations =
	| ContactWithRelations
	| CompanyWithRelations
	| PropertyWithRelations;

export type ObjectCreateInput = ContactCreateInput | CompanyCreateInput;

export type ObjectUpdateInput = ContactUpdateInput | CompanyUpdateInput;

export type ObjectQueryInput = ContactQueryInput | CompanyQueryInput;
