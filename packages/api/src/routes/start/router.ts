import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";

export const startRouter = new Hono<{ Variables: { user: Session["user"] } }>();

startRouter.get("/analytics", authMiddleware, async (c) => {
	const organizationId = c.req.query("organizationId");
	const filter = c.req.query("filter") || "This Week";

	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		const now = new Date();
		let startDate: Date;
		let endDate = new Date(now);

		switch (filter) {
			case "Today":
				startDate = new Date(
					now.getFullYear(),
					now.getMonth(),
					now.getDate(),
				);
				break;
			case "Yesterday":
				startDate = new Date(
					now.getFullYear(),
					now.getMonth(),
					now.getDate() - 1,
				);
				endDate = new Date(
					now.getFullYear(),
					now.getMonth(),
					now.getDate(),
				);
				break;
			case "This Week": {
				const dayOfWeek = now.getDay();
				const diff =
					now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
				startDate = new Date(now.setDate(diff));
				startDate.setHours(0, 0, 0, 0);
				endDate = new Date();
				break;
			}
			case "Last Week": {
				const lastWeek = new Date(now);
				lastWeek.setDate(lastWeek.getDate() - 7);
				const lastWeekDay = lastWeek.getDay();
				const lastWeekDiff =
					lastWeek.getDate() -
					lastWeekDay +
					(lastWeekDay === 0 ? -6 : 1);
				startDate = new Date(lastWeek.setDate(lastWeekDiff));
				startDate.setHours(0, 0, 0, 0);
				endDate = new Date(startDate);
				endDate.setDate(endDate.getDate() + 6);
				endDate.setHours(23, 59, 59, 999);
				break;
			}
			case "This Month":
				startDate = new Date(now.getFullYear(), now.getMonth(), 1);
				break;
			case "Last Month":
				startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
				endDate = new Date(now.getFullYear(), now.getMonth(), 0);
				endDate.setHours(23, 59, 59, 999);
				break;
			case "This Quarter": {
				const quarter = Math.floor(now.getMonth() / 3);
				startDate = new Date(now.getFullYear(), quarter * 3, 1);
				break;
			}
			case "Last Quarter": {
				const lastQuarter = Math.floor(now.getMonth() / 3) - 1;
				const lastQuarterYear =
					lastQuarter < 0 ? now.getFullYear() - 1 : now.getFullYear();
				const lastQuarterMonth = lastQuarter < 0 ? 9 : lastQuarter * 3;
				startDate = new Date(lastQuarterYear, lastQuarterMonth, 1);
				endDate = new Date(lastQuarterYear, lastQuarterMonth + 3, 0);
				endDate.setHours(23, 59, 59, 999);
				break;
			}
			case "This Year":
				startDate = new Date(now.getFullYear(), 0, 1);
				break;
			case "Last Year":
				startDate = new Date(now.getFullYear() - 1, 0, 1);
				endDate = new Date(now.getFullYear() - 1, 11, 31);
				endDate.setHours(23, 59, 59, 999);
				break;
			case "Last 7 Days":
				startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				break;
			case "Last 30 Days":
				startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				break;
			case "Last 90 Days":
				startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
				break;
			case "Last 6 Months":
				startDate = new Date(
					now.getFullYear(),
					now.getMonth() - 6,
					now.getDate(),
				);
				break;
			case "All Time":
				startDate = new Date(2020, 0, 1); // Set a reasonable start date
				break;
			default:
				startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
		}

		const tasks = await db.task.findMany({
			where: {
				organizationId,
				createdAt: {
					gte: startDate,
					lte: endDate,
				},
			},
			include: {
				assignee: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
			cacheStrategy: {
				ttl: 300, // 5 minutes cache
				tags: [`analytics_tasks_org_${organizationId}`, `org_${organizationId}`],
			},
		});

		const totalTasks = tasks.length;
		const tasksCompleted = tasks.filter(
			(task) => task.status === "done",
		).length;
		const tasksNotDoneCount = tasks.filter(
			(task) => task.status !== "done",
		).length;
		const tasksInProgress = tasks.filter(
			(task) => task.status === "in_progress",
		).length;
		const tasksBacklog = tasks.filter(
			(task) => task.status === "backlog",
		).length;
		const tasksTodo = tasks.filter((task) => task.status === "todo").length;
		const tasksReview = tasks.filter(
			(task) => task.status === "review",
		).length;

		const tasksPercentage =
			totalTasks > 0
				? Math.round((tasksCompleted / totalTasks) * 100)
				: 0;
		const callPercentage = 0;

		// Add Prisma Accelerate caching for analytics notes query  
		const notes = await db.note.findMany({
			where: {
				orgId: organizationId,
				createdAt: {
					gte: startDate,
					lte: endDate,
				},
			},
			cacheStrategy: {
				ttl: 300, // 5 minutes cache
				tags: [`analytics_notes_org_${organizationId}`, `org_${organizationId}`],
			},
		});
		const notesCount = notes.length;

		// Generate time-series data for chart
		const generateTimeSeriesData = () => {
			// First, let's make sure we're using the correct date range for the data
			// Since your notes are from 2025, let's adjust the date range to include them
			let adjustedStartDate = new Date(startDate);
			let adjustedEndDate = new Date(endDate);

			// If we have notes but they're outside our date range, expand the range
			if (notes.length > 0) {
				const noteDates = notes.map((note) => new Date(note.createdAt));
				const earliestNote = new Date(
					Math.min(...noteDates.map((d) => d.getTime())),
				);
				const latestNote = new Date(
					Math.max(...noteDates.map((d) => d.getTime())),
				);

				if (earliestNote < adjustedStartDate)
					adjustedStartDate = earliestNote;
				if (latestNote > adjustedEndDate) adjustedEndDate = latestNote;
			}

			if (tasks.length > 0) {
				const taskDates = tasks.map((task) => new Date(task.createdAt));
				const earliestTask = new Date(
					Math.min(...taskDates.map((d) => d.getTime())),
				);
				const latestTask = new Date(
					Math.max(...taskDates.map((d) => d.getTime())),
				);

				if (earliestTask < adjustedStartDate)
					adjustedStartDate = earliestTask;
				if (latestTask > adjustedEndDate) adjustedEndDate = latestTask;
			}

			const data: Array<{
				date: string;
				calls: number;
				notes: number;
				tasks: number;
			}> = [];

			// Generate the correct date format based on filter
			const getDateKey = (date: Date): string => {
				switch (filter) {
					case "Today":
					case "Yesterday":
						return filter;
					case "This Week":
					case "Last Week":
					case "Last 7 Days": {
						// Use simple day names
						const days = [
							"Sun",
							"Mon",
							"Tue",
							"Wed",
							"Thu",
							"Fri",
							"Sat",
						];
						return days[date.getDay()];
					}
					case "This Month":
					case "Last Month":
					case "Last 30 Days":
						return date.getDate().toString();
					case "This Year":
					case "Last Year":
					case "This Quarter":
					case "Last Quarter":
					case "Last 6 Months": {
						const months = [
							"Jan",
							"Feb",
							"Mar",
							"Apr",
							"May",
							"Jun",
							"Jul",
							"Aug",
							"Sep",
							"Oct",
							"Nov",
							"Dec",
						];
						return months[date.getMonth()];
					}
					default:
						return date.toISOString().split("T")[0];
				}
			};

			// Create a map to aggregate data by date key
			const dataMap = new Map<
				string,
				{ calls: number; notes: number; tasks: number }
			>();

			// Initialize periods based on filter type
			if (filter === "Today" || filter === "Yesterday") {
				dataMap.set(filter, { calls: 0, notes: 0, tasks: 0 });
			} else {
				// Generate date range
				const currentDate = new Date(adjustedStartDate);
				while (currentDate <= adjustedEndDate) {
					const dateKey = getDateKey(currentDate);
					if (!dataMap.has(dateKey)) {
						dataMap.set(dateKey, { calls: 0, notes: 0, tasks: 0 });
					}

					// Move to next period
					if (
						filter.includes("Month") ||
						filter.includes("Week") ||
						filter.includes("Days")
					) {
						currentDate.setDate(currentDate.getDate() + 1);
					} else {
						currentDate.setMonth(currentDate.getMonth() + 1);
					}
				}
			}

			// Aggregate notes
			notes.forEach((note) => {
				const noteDate = new Date(note.createdAt);
				const dateKey = getDateKey(noteDate);
				const entry = dataMap.get(dateKey);
				if (entry) {
					entry.notes++;
				}
			});

			// Aggregate tasks
			tasks.forEach((task) => {
				const taskDate = new Date(task.createdAt);
				const dateKey = getDateKey(taskDate);
				const entry = dataMap.get(dateKey);
				if (entry) {
					entry.tasks++;
				}
			});

			// Convert to array and sort appropriately
			const result = Array.from(dataMap.entries()).map(
				([date, counts]) => ({
					date,
					...counts,
				}),
			);

			// Sort the results based on the filter type
			if (
				filter === "This Month" ||
				filter === "Last Month" ||
				filter === "Last 30 Days"
			) {
				result.sort(
					(a, b) => Number.parseInt(a.date) - Number.parseInt(b.date),
				);
			}

			return result;
		};

		const timeSeriesData = generateTimeSeriesData();

		const taskStatusBreakdown = {
			backlog: tasksBacklog,
			todo: tasksTodo,
			in_progress: tasksInProgress,
			review: tasksReview,
			done: tasksCompleted,
		};

		const taskPriorityBreakdown = {
			urgent: tasks.filter((task) => task.priority === "urgent").length,
			high: tasks.filter((task) => task.priority === "high").length,
			medium: tasks.filter((task) => task.priority === "medium").length,
			low: tasks.filter((task) => task.priority === "low").length,
			no_priority: tasks.filter((task) => task.priority === "no_priority")
				.length,
		};

		// Add Prisma Accelerate caching for recent tasks query
		const recentTasks = await db.task.findMany({
			where: {
				organizationId,
				updatedAt: {
					gte: startDate,
					lte: endDate,
				},
			},
			include: {
				assignee: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
				createdBy: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
			orderBy: {
				updatedAt: "desc",
			},
			take: 10,
			cacheStrategy: {
				ttl: 180, // 3 minutes cache for recent tasks (more dynamic)
				tags: [`analytics_recent_tasks_org_${organizationId}`, `org_${organizationId}`],
			},
		});

		return c.json(
			{
				totalTasks,
				tasksCompleted,
				tasksNotDoneCount,
				tasksInProgress,
				tasksBacklog,
				tasksTodo,
				tasksReview,
				tasksPercentage,
				callPercentage,
				callResultsCount: 0,
				notesCount,
				taskStatusBreakdown,
				taskPriorityBreakdown,
				filter,
				dateRange: {
					startDate: startDate.toISOString(),
					endDate: endDate.toISOString(),
				},
				recentTasks,
				timeSeriesData,
			},
			200,
		);
	} catch (error) {
		console.error("[API] Failed to fetch dashboard analytics:", error);
		return c.json(
			{
				error: "Failed to fetch dashboard analytics",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

export type StartRouter = typeof startRouter;
