import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import { z } from "zod";

export const saleHistoryRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Validation schemas
const createSaleHistorySchema = z.object({
	organizationId: z.string().min(1, "Organization ID is required"),
	propertyId: z.string().min(1, "Property ID is required"),
	seller: z.string().optional(),
	buyer: z.string().optional(),
	saleDate: z.coerce.date().optional(),
	salePrice: z.number().optional(),
	askingPrice: z.number().optional(),
	transactionType: z.string().optional(),
	pricePerSquareFoot: z.number().optional(),
	pricePerUnit: z.number().optional(),
	transferredOwnershipPercentage: z.number().optional(),
	capRate: z.number().optional(),
	grmRate: z.number().optional(),
});

const updateSaleHistorySchema = z.object({
	seller: z.string().optional(),
	buyer: z.string().optional(),
	saleDate: z.coerce.date().optional(),
	salePrice: z.number().optional(),
	askingPrice: z.number().optional(),
	transactionType: z.string().optional(),
	pricePerSquareFoot: z.number().optional(),
	pricePerUnit: z.number().optional(),
	transferredOwnershipPercentage: z.number().optional(),
	capRate: z.number().optional(),
	grmRate: z.number().optional(),
});

// Get all sale history for an organization
saleHistoryRouter.get("/sale-history", authMiddleware, async (c) => {
	const organizationId = c.req.query("organizationId");
	
	if (!organizationId) {
		return c.json({ error: "Missing organizationId" }, 400);
	}

	try {
		const user = c.get("user");
		
		// Verify organization membership
		await verifyOrganizationMembership(organizationId, user.id);

		const saleHistory = await db.propertySaleHistory.findMany({
			where: { 
				organizationId,
			},
			include: {
				property: {
					select: {
						id: true,
						name: true,
						propertyType: true,
						market: true,
						subMarket: true,
						status: true,
					},
				},
			},
			orderBy: { createdAt: "desc" },
		});

		return c.json(saleHistory, 200);
	} catch (error) {
		logger.error("[API] Failed to fetch sale history:", error);
		return c.json(
			{
				error: "Failed to fetch sale history",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Create a new sale history entry
saleHistoryRouter.post("/sale-history", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		
		// Validate the request body
		const validatedData = createSaleHistorySchema.parse(body);
		
		// Verify organization membership
		await verifyOrganizationMembership(validatedData.organizationId, user.id);
		
		// Verify the property exists and belongs to the organization
		const property = await db.property.findFirst({
			where: {
				id: validatedData.propertyId,
				organizationId: validatedData.organizationId,
			},
		});
		
		if (!property) {
			return c.json({ error: "Property not found" }, 404);
		}
		
		// Create the sale history entry
		const saleHistory = await db.propertySaleHistory.create({
			data: {
				propertyId: validatedData.propertyId,
				organizationId: validatedData.organizationId,
				seller: validatedData.seller,
				buyer: validatedData.buyer,
				saleDate: validatedData.saleDate?.toISOString(),
				salePrice: validatedData.salePrice,
				askingPrice: validatedData.askingPrice,
				transactionType: validatedData.transactionType,
				pricePerSquareFoot: validatedData.pricePerSquareFoot,
				pricePerUnit: validatedData.pricePerUnit,
				transferredOwnershipPercentage: validatedData.transferredOwnershipPercentage,
				capRate: validatedData.capRate,
				grmRate: validatedData.grmRate,
			},
			include: {
				property: {
					select: {
						id: true,
						name: true,
						propertyType: true,
						market: true,
						subMarket: true,
						status: true,
					},
				},
			},
		});
		
		return c.json({
			id: saleHistory.id,
			propertyId: saleHistory.propertyId,
			organizationId: saleHistory.organizationId,
			seller: saleHistory.seller,
			buyer: saleHistory.buyer,
			saleDate: saleHistory.saleDate,
			salePrice: saleHistory.salePrice,
			askingPrice: saleHistory.askingPrice,
			transactionType: saleHistory.transactionType,
			pricePerSquareFoot: saleHistory.pricePerSquareFoot,
			pricePerUnit: saleHistory.pricePerUnit,
			transferredOwnershipPercentage: saleHistory.transferredOwnershipPercentage,
			capRate: saleHistory.capRate,
			grmRate: saleHistory.grmRate,
			createdAt: saleHistory.createdAt,
			updatedAt: saleHistory.updatedAt,
			property: (saleHistory as any).property,
		}, 201);
	} catch (error) {
		logger.error("[API] Failed to create sale history:", error);
		return c.json(
			{
				error: "Failed to create sale history",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Update a sale history entry
saleHistoryRouter.put("/sale-history/:id", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const saleHistoryId = c.req.param("id");
		const body = await c.req.json();
		
		// Validate the request body
		const validatedData = updateSaleHistorySchema.parse(body);
		
		// Find the sale history entry and verify ownership
		const existingSaleHistory = await db.propertySaleHistory.findFirst({
			where: { id: saleHistoryId },
			include: {
				property: {
					select: {
						organizationId: true,
					},
				},
			},
		});
		
		if (!existingSaleHistory) {
			return c.json({ error: "Sale history not found" }, 404);
		}
		
		// Verify organization membership
		await verifyOrganizationMembership(existingSaleHistory.property.organizationId, user.id);
		
		// Update the sale history entry
		const updatedSaleHistory = await db.propertySaleHistory.update({
			where: { id: saleHistoryId },
			data: {
				...validatedData,
				saleDate: validatedData.saleDate?.toISOString(),
			},
			include: {
				property: {
					select: {
						id: true,
						name: true,
						propertyType: true,
						market: true,
						subMarket: true,
						status: true,
					},
				},
			},
		});
		
		return c.json({
			id: updatedSaleHistory.id,
			propertyId: updatedSaleHistory.propertyId,
			organizationId: updatedSaleHistory.organizationId,
			seller: updatedSaleHistory.seller,
			buyer: updatedSaleHistory.buyer,
			saleDate: updatedSaleHistory.saleDate,
			salePrice: updatedSaleHistory.salePrice,
			askingPrice: updatedSaleHistory.askingPrice,
			transactionType: updatedSaleHistory.transactionType,
			pricePerSquareFoot: updatedSaleHistory.pricePerSquareFoot,
			pricePerUnit: updatedSaleHistory.pricePerUnit,
			transferredOwnershipPercentage: updatedSaleHistory.transferredOwnershipPercentage,
			capRate: updatedSaleHistory.capRate,
			grmRate: updatedSaleHistory.grmRate,
			createdAt: updatedSaleHistory.createdAt,
			updatedAt: updatedSaleHistory.updatedAt,
			property: (updatedSaleHistory as any).property,
		}, 200);
	} catch (error) {
		logger.error("[API] Failed to update sale history:", error);
		return c.json(
			{
				error: "Failed to update sale history",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

// Delete a sale history entry
saleHistoryRouter.delete("/sale-history/:id", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const saleHistoryId = c.req.param("id");
		
		// Find the sale history entry and verify ownership
		const existingSaleHistory = await db.propertySaleHistory.findFirst({
			where: { id: saleHistoryId },
			include: {
				property: {
					select: {
						organizationId: true,
					},
				},
			},
		});
		
		if (!existingSaleHistory) {
			return c.json({ error: "Sale history not found" }, 404);
		}
		
		// Verify organization membership
		await verifyOrganizationMembership(existingSaleHistory.property.organizationId, user.id);
		
		// Delete the sale history entry
		await db.propertySaleHistory.delete({
			where: { id: saleHistoryId },
		});
		
		return c.json({ message: "Sale history deleted successfully" }, 200);
	} catch (error) {
		logger.error("[API] Failed to delete sale history:", error);
		return c.json(
			{
				error: "Failed to delete sale history",
				details:
					error instanceof Error
						? {
								message: error.message,
								stack: error.stack,
								name: error.name,
							}
						: error,
			},
			500,
		);
	}
});

export type SaleHistoryRouter = typeof saleHistoryRouter; 