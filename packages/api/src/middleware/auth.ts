import { auth, type Session } from "@repo/auth";
import { createMiddleware } from "hono/factory";

export const authMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
		auth: {
			userId: string;
			organizationId: string;
		};
	};
}>(async (c, next) => {
	const session = await auth.api.getSession({
		headers: c.req.raw.headers,
	});

	if (!session) {
		return c.json({ error: "Unauthorized" }, 401);
	}

	if (!session.session.activeOrganizationId) {
		return c.json({ error: "No active organization" }, 400);
	}

	c.set("session", session.session);
	c.set("user", session.user);
	c.set("auth", {
		userId: session.user.id,
		organizationId: session.session.activeOrganizationId,
	});

	await next();
});
