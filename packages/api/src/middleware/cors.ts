import { getBaseUrl } from "@repo/utils";
import { cors } from "hono/cors";

const getAllowedOrigins = () => {
	const baseUrl = getBaseUrl();
	const origins = [baseUrl];

	if (baseUrl.includes("reliocrm.com")) {
		if (!baseUrl.includes("www.")) {
			origins.push(baseUrl.replace("://reliocrm.com", "://www.reliocrm.com"));
		} else {
			origins.push(baseUrl.replace("://www.reliocrm.com", "://reliocrm.com"));
		}
	}

	if (process.env.NODE_ENV === "development") {
		origins.push("http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001");
	}

	return origins;
};

export const corsMiddleware = cors({
	origin: (origin) => {
		const allowedOrigins = getAllowedOrigins();
		
		if (!origin) return origin;
		
		const isAllowed = allowedOrigins.includes(origin);
		
		return isAllowed ? origin : null;
	},
	allowHeaders: [
		"Content-Type", 
		"Authorization", 
		"X-Requested-With",
		"Accept",
		"Accept-Version",
		"Content-Length",
		"Content-MD5",
		"Date",
		"X-Api-Version"
	],
	allowMethods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"],
	exposeHeaders: ["Content-Length", "X-Total-Count"],
	maxAge: 600,
	credentials: true,
});
