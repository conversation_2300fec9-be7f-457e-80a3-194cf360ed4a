import { render } from "@react-email/render";
import type { Locale, Messages } from "@repo/i18n";
import { getMessagesForLocale } from "@repo/i18n";
import { mailTemplates } from "../../emails";

export async function getTemplate<T extends TemplateId>({
	templateId,
	context,
	locale,
}: {
	templateId: T;
	context: Omit<
		Parameters<(typeof mailTemplates)[T]>[0],
		"locale" | "translations"
	>;
	locale: Locale;
}) {

	try {
		const template = mailTemplates[templateId];
		if (!template) {
			throw new Error(`Email template not found: ${templateId}`);
		}
		
		const translations = await getMessagesForLocale(locale);

		if (!translations.mail) {
			throw new Error('Mail translations not found');
		}
		
		if (!translations.mail[templateId as keyof Messages["mail"]]) {
			throw new Error(`Template translations not found: ${templateId}`);
		}
		
		const email = template({
			...(context as any),
			locale,
			translations,
		});
		
		let subject = "";
		try {
			const mailTranslations = translations.mail[templateId as keyof Messages["mail"]];
			
			if ("subject" in mailTranslations) {
				subject = mailTranslations.subject;
			} else {
				throw new Error('No subject found in translations for template: ' + templateId);
			}
		} catch (subjectError) {
			throw new Error('Error getting subject from translations: ' + subjectError);
		}
		
		const html = await render(email);
		const text = await render(email, { plainText: true });
		
		return { html, text, subject };
	} catch (error) {
		throw error;
	}
}

export type TemplateId = keyof typeof mailTemplates;
