import {
	Con<PERSON><PERSON>,
	<PERSON><PERSON>,
	Head,
	Html,
	Img,
	Section,
	Tailwind,
} from "@react-email/components";
import React, { type PropsWithChildren } from "react";
import { Logo } from "./Logo";

export default function Wrapper({ children }: PropsWithChildren) {
	return (
		<Html lang="en">
			<Head>
				<Font
					fontFamily="Inter"
					fallbackFontFamily="Arial"
					fontWeight={400}
					fontStyle="normal"
				/>
			</Head>
			<Tailwind
				config={{
					theme: {
						extend: {
							colors: {
								border: "#e3ebf6",
								background: "#fafafe",
								foreground: "#292b35",
								primary: {
									DEFAULT: "#135847",
									foreground: "#f6f7f9",
								},
								secondary: {
									DEFAULT: "#292b35",
									foreground: "#ffffff",
								},
								card: {
									DEFAULT: "#ffffff",
									foreground: "#292b35",
								},
							},
						},
					},
				}}
			>
				<Section className="bg-background p-4">
					<Container style={container} className="rounded-3xl border-primary bg-card p-6 text-card-foreground w-full">
						<Img
							src="https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/relio%2Frelio-black-long.png?alt=media&token=599022ec-3f3f-4533-9f4f-a305d9af3087"
							alt="Logo"
							style={{
								height: "2rem",
								width: "auto",
							}}
						/>
						{children}
					</Container>
				</Section>
			</Tailwind>
		</Html>
	);
}

const container = {
	backgroundColor: "#ffffff",
	border: "1px solid #88888850",
	borderRadius: "25px",
	// boxShadow: "0 5px 10px rgba(20,50,70,.2)",
	margin: "0 auto",
	padding: "2rem",
};