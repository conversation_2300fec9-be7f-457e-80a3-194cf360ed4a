import React from "react";

export function Logo({ withLabel = true }: { withLabel?: boolean }) {
	return (
		<span className="flex items-center font-semibold text-primary leading-none">
			<svg className="h-8 w-8" viewBox="0 0 2048 2048">
				<title>relio</title>
				<path
					transform="translate(1026,25)"
					d="m0 0 42 2 44 3 41 5 57 9 34 7 47 12 36 11 41 14 35 14 30 13 35 17 27 14 23 13 17 10 22 14 24 16 20 14 21 16 16 12 13 11 11 9 14 12 26 24 10 10 8 7 16 17 8 8 7 8 13 14 9 11 13 15 9 11 10 13 16 21 26 38 12 19 13 21 16 28 17 32 16 33 18 42 11 29 15 43 14 49 10 43 7 36 6 38 5 46 2 26 1 33 1 24v14l-1 18-1 27-1 15-2 25-5 38-8 50-6 31-13 52-9 30-16 47-13 33-9 21-13 28-12 25-18 34-17 28-12 20-10 15-13 19-13 18-12 16-11 14-13 16-11 13-9 10-7 8-28 30-24 24-8 7-7 7-8 7-15 13-13 11-10 8-13 10-14 11-19 14-28 19-15 10-28 17-21 12-24 13-27 14-28 13-33 14-29 11-28 10-42 13-38 10-36 8-38 7-43 6-43 4-9 1-17 1h-12l-18 1h-23l-62-2-28-3-6-2-1-3 5-30 18-115 17-108 9-57 13-84 13-83 30-190 4-25 8-5 31-13 35-15 48-20 37-16 41-17 30-13 36-15 42-18 33-14 29-12 42-18 36-15 35-15 43-18 18-8h2l3-22 17-109 9-56 15-97 13-82 12-76 8-52 1-9-21 8-28 12-111 47-36 15-35 15-26 11-31 13-42 18-36 15-47 20-85 36-36 15-30 13-36 15-40 17-99 42-36 15-47 20-99 42-36 15-54 23-2 1-3 14-11 71-11 69-13 84-10 64-9 56-12 77-17 109-11 69-13 84-19 121-6 38-2 5-12-6-17-11-16-11-11-8-21-16-12-9-22-18-13-11-10-9-8-7-12-11-15-14-19-19-7-8-10-10-3-6 6-40 9-56 9-58 13-83 11-70 13-83 9-58 10-62 8-53 15-94 10-65 12-76 8-50 2-4 32-14 29-12 35-15 43-18 42-18 41-17 30-13 118-50 41-17 30-13 36-15 28-12 31-13 80-34 36-15 42-18 29-12 35-15 18-8v-4l-34-5-37-6-33-5-30-5-21-1-20 8-28 12-163 69-78 33-43 18-35 15-26 11-161 68-38 16-35 15-36 15-28 12-8 4-6 35-10 63-11 72-12 75-30 192-57 364-3 18h-3l-4-4-11-16-10-15-11-17-16-27-13-23-17-33-16-33-13-30-16-42-12-35-12-41-11-44-7-35-8-51-4-37-3-43-1-25v-44l1-31 1-14 2-27 5-38 9-57 7-34 11-43 11-37 13-38 15-38 10-23 12-27 8-15 9-19 13-23 15-26 12-19 11-17 8-12 12-17 14-19 8-10 14-18 12-14 9-11 11-12 7-8 9-10 16-16 4-5 8-7 20-20 8-7 10-9 8-7 13-11 11-9 15-12 38-28 36-24 16-10 27-16 18-10 34-18 27-13 30-13 28-11 24-9 49-16 53-14 49-10 36-6 30-4 47-4 39-2z"
					fill="currentColor"
				/>
				<path
					transform="translate(1206,495)"
					d="m0 0 8 1-4 4-4 1z"
					fill="#FEFEFE"
				/>
			</svg>
			{withLabel && <span className="ml-3 text-xl">relio</span>}
		</span>
	);
}
