import React from 'react';
import { <PERSON><PERSON>, Container, Head, Heading, Hr, Html, Preview, Section, Text } from '@react-email/components';
import Wrapper from '../src/components/Wrapper';
import PrimaryButton from '../src/components/PrimaryButton';

interface ReplyNotificationProps {
  locale?: string;
  translations?: Record<string, any>;
  repliedByName: string;
  repliedByImage?: string;
  message: string;
  organizationName: string;
  activityUrl: string;
}

export const ReplyNotification = ({
  locale = 'en',
  translations,
  repliedByName,
  message,
  organizationName,
  activityUrl,
}: ReplyNotificationProps) => {
  const t = translations?.[locale] || {
    subject: `${repliedByName} replied to your comment in ${organizationName}`,
    heading: 'New reply to your comment',
    greeting: 'Hello,',
    replyText: `${repliedByName} replied to your comment in ${organizationName}.`,
    viewButton: 'View Reply',
    footer: 'If you did not expect this email, you can safely ignore it.',
  };

  return (
    <Html>
      <Head />
      <Preview>{t.subject}</Preview>
      <Wrapper>
        <Container>
          <Heading className="text-xl">{t.heading}</Heading>
          <Text>{t.greeting}</Text>
          <Text>{t.replyText}</Text>
          
          {message && (
            <Section className="my-4 rounded-md bg-gray-50 p-4">
              <Text className="text-gray-700" dangerouslySetInnerHTML={{ __html: message }} />
            </Section>
          )}
          
          <Section className="mt-8 text-center">
            <PrimaryButton href={activityUrl}>
              {t.viewButton}
            </PrimaryButton>
          </Section>
          
          <Hr className="my-6 border-gray-200" />
          
          <Text className="text-sm text-gray-500">
            {t.footer}
          </Text>
        </Container>
      </Wrapper>
    </Html>
  );
};

export default ReplyNotification;
