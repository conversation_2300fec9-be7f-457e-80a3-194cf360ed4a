import React from 'react';
import { <PERSON><PERSON>, Container, Head, Heading, Hr, Html, Preview, Section, Text } from '@react-email/components';
import Wrapper from '../src/components/Wrapper';
import PrimaryButton from '../src/components/PrimaryButton';

interface MentionNotificationProps {
  locale?: string;
  translations?: Record<string, any>;
  mentionedByName: string;
  mentionedByImage?: string;
  message: string;
  organizationName: string;
  activityUrl: string;
}

export const MentionNotification = ({
  locale = 'en',
  translations,
  mentionedByName,
  message,
  organizationName,
  activityUrl,
}: MentionNotificationProps) => {
  const t = translations?.[locale] || {
    subject: `${mentionedByName} mentioned you in ${organizationName}`,
    heading: 'You were mentioned',
    greeting: 'Hello,',
    mentionText: `${mentionedByName} mentioned you in a comment in ${organizationName}.`,
    viewButton: 'View Comment',
    footer: 'If you did not expect this email, you can safely ignore it.',
  };

  return (
    <Html>
      <Head />
      <Preview>{t.subject}</Preview>
      <Wrapper>
        <Container>
          <Heading className="text-xl">{t.heading}</Heading>
          <Text>{t.greeting}</Text>
          <Text>{t.mentionText}</Text>
          
          {message && (
            <Section className="my-4 rounded-md bg-gray-50 p-4">
              <Text className="text-gray-700" dangerouslySetInnerHTML={{ __html: message }} />
            </Section>
          )}
          
          <Section className="mt-8 text-center">
            <PrimaryButton href={activityUrl}>
              {t.viewButton}
            </PrimaryButton>
          </Section>
          
          <Hr className="my-6 border-gray-200" />
          
          <Text className="text-sm text-gray-500">
            {t.footer}
          </Text>
        </Container>
      </Wrapper>
    </Html>
  );
};

export default MentionNotification;
