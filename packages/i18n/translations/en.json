{"admin": {"menu": {"organizations": "Organizations", "users": "Users"}, "organizations": {"backToList": "Back to organizations", "confirmDelete": {"confirm": "Delete", "message": "Are you sure you want to delete this organization? This action cannot be undone.", "title": "Delete organization"}, "create": "Create", "delete": "Delete", "deleteOrganization": {"deleted": "Organization has been deleted successfully!", "deleting": "Deleting organization...", "notDeleted": "Organization could not be deleted. Please try again."}, "edit": "Edit", "form": {"createTitle": "Create an organization", "name": "Organization name", "notifications": {"error": "Could not save the organization. Please try again later.", "success": "Organization has been saved."}, "save": "Save", "updateTitle": "Edit organization"}, "loading": "Loading organizations...", "membersCount": "{count} {count, plural, one {member} other {members}}", "search": "Search for an organization...", "title": "Manage organizations"}, "title": "Administration", "users": {"confirmDelete": {"confirm": "Delete", "message": "Are you sure you want to delete this user? This action cannot be undone.", "title": "Delete user"}, "delete": "Delete", "deleteUser": {"deleted": "User has been deleted successfully!", "deleting": "Deleting user...", "notDeleted": "User could not be deleted. Please try again."}, "emailVerified": {"verified": "Email verified", "waiting": "Email waiting for verification"}, "impersonate": "Impersonate", "impersonation": {"impersonating": "Impersonating as {name}..."}, "loading": "Loading users...", "resendVerificationMail": {"error": "Could not resend verification mail. Please try again.", "submitting": "Resending verification mail...", "success": "Verification mail has been sent.", "title": "Resend verification mail"}, "search": "Search for name or email...", "title": "Manage users", "assignAdminRole": "Assign admin role", "removeAdminRole": "Remove admin role"}, "description": "Manage your application."}, "app": {"menu": {"accountSettings": "Account settings", "admin": "Admin", "aiChatbot": "AI Chatbot", "organizationSettings": "Organization settings", "start": "Start"}, "userMenu": {"accountSettings": "Account settings", "colorMode": "Color mode", "documentation": "Documentation", "home": "Home", "logout": "Logout"}, "command": {"actions": "Actions", "quickSearch": "Quick search...", "quickActions": "Quick actions...", "commandOrSearch": "Type a command or search...", "goToDashboard": "Go to Dashboard", "openSettings": "Open Settings", "useLightTheme": "Use Light Theme", "useDarkTheme": "Use Dark Theme", "openHelp": "Open Help", "addToBookmarks": "Add to Bookmarks", "accountSettings": "Account settings", "workspaceSettings": "Workspace settings", "organizationSettings": "Organization Settings", "billing": "Billing", "openHelpDrawer": "Open Help Drawer", "viewDocs": "View Docs", "viewHelp": "View Help", "support": "Support", "button": {"open": "Open", "openNewTab": "Open new tab", "addToBookmarks": "Add to bookmarks", "enableTheme": "Enable theme"}}}, "auth": {"errors": {"invalidEmailOrPassword": "The credentials you entered are invalid. Please check them and try again.", "unknown": "Something went wrong. Please try again.", "userNotFound": "This user does not exists", "failedToCreateUser": "Could not create user. Please try again.", "failedToCreateSession": "Could not create a session. Please try again.", "failedToUpdateUser": "Could not update user. Please try again.", "failedToGetSession": "Could not get the session.", "invalidPassword": "The entered password is incorrect.", "invalidEmail": "The entered email is invalid.", "invalidToken": "The token you entered is invalid or has expired.", "credentialAccountNotFound": "Account not found.", "emailCanNotBeUpdated": "Email could not be updated. Please try again.", "emailNotVerified": "Please verify your email first before logging in.", "failedToGetUserInfo": "Could not load user information.", "idTokenNotSupported": "ID token is not supported.", "passwordTooLong": "Password is too long.", "passwordTooShort": "Password is too short.", "providerNotFound": "This provider is not suppported.", "socialAccountAlreadyLinked": "This account is already linked to a user.", "userEmailNotFound": "<PERSON><PERSON> not found.", "userAlreadyExists": "This user already exists.", "unableToCreateUser": "An account with this email already exists. Please sign in with your existing credentials or try linking your accounts from your profile settings.", "invalidInvitation": "The invitation is invalid or expired.", "sessionExpired": "The session has expired.", "failedToUnlinkLastAccount": "Failed to unlink account", "accountNotFound": "Account not found"}, "forgotPassword": {"backToSignin": "Back to signin", "email": "Email", "hints": {"linkNotSent": {"message": "We are sorry, but we were unable to send you a link to reset your password. Please try again later.", "title": "<PERSON> not sent"}, "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "message": "Please enter your email address and we will send you a link to reset your password.", "submit": "Send link", "title": "Forgot your password?"}, "login": {"or": "Or", "otherOptions": "Other options", "continueWith": "Or continue with", "createAnAccount": "Create an account", "dontHaveAnAccount": "Don't have an account yet?", "forgotPassword": "Forgot password?", "hints": {"invalidCredentials": "The email or password you entered are invalid. Please try again.", "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "loginWithPasskey": "Login with passkey", "modes": {"magicLink": "Magic link", "password": "Password"}, "submit": "Sign in", "subtitle": "Please enter your credentials to sign in.", "title": "Welcome back", "sendMagicLink": "Send magic link"}, "resetPassword": {"backToSignin": "Back to signin", "hints": {"error": "We are sorry, but we were unable to reset your password. Please try again.", "success": "Your password has been reset successfully."}, "message": "Please enter a new password.", "newPassword": "New password", "submit": "Reset password", "title": "Reset your password"}, "signup": {"yourEmail": "Your Email", "signUpWithEmail": "Sign up with email", "alreadyHaveAccount": "Already have an account?", "email": "Email", "hints": {"signupFailed": "We are sorry, but we were unable to create your account. Please try again later.", "verifyEmail": "We have sent you a link to verify your email. Please check your inbox."}, "message": "We are happy that you want to join us. Please fill in the form below to create your account.", "name": "Name", "password": "Password", "signIn": "Sign in", "submit": "Create account", "title": "Create an account"}}, "blog": {"description": "Read our latest articles and updates on our blog", "title": "Our Blog"}, "changelog": {"description": "Stay up to date with the latest changes in our product.", "title": "Changelog", "seeWhatsNew": "See what's new", "dismiss": "<PERSON><PERSON><PERSON>"}, "common": {"confirmation": {"cancel": "Cancel", "confirm": "Confirm"}, "menu": {"blog": "Blog", "changelog": "Changelog", "contact": "Contact", "dashboard": "Dashboard", "help": "Help", "faq": "FAQ", "login": "<PERSON><PERSON>", "signup": "Sign up", "pricing": "Pricing"}, "tableOfContents": {"title": "On this page"}, "theme": {"title": "Theme", "light": "Light", "dark": "Dark", "system": "System"}}, "contact": {"description": "We are here to help you. Please use the form below to get in touch with us.", "form": {"email": "Email", "message": "Message", "name": "Name", "notifications": {"error": "We are sorry, but we were unable to send your message. Please try again later.", "success": "Your message has been sent successfully. We will get back to you as soon as possible."}, "companyName": "Company name (optional)", "employeeCount": "Employee count (optional)", "yourMessage": "Your message", "submit": "Send message"}, "title": "Contact us", "corporateOffice": "Corporate office", "emailUs": "Email us", "followUs": "Follow us", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn"}, "documentation": {"title": "Documentation"}, "help": {"title": "Help"}, "faq": {"description": "Do you have any questions? We have got you covered.", "title": "Frequently asked questions", "category": {"support": "Support", "account": "Account", "features": "Features", "security": "Security"}, "left": {"one": {"question": "Is there a free plan?", "answer": "Yes! We offer a generous free plan with just enough features except that one feature you really want! Our strategy is to get your credit card details on file then steadily double our prices against inflation rates."}, "two": {"question": "Is support free, or do I need to pay?", "answer": "We pride ourselves on our comprehensive support system. Our friendly chatbot is available 24/7 to answer most of your questions. If you need more help, our team of real humans are also available Monday-Friday, 9am-5pm EST. We promise they will be nice and not too sarcastic."}, "three": {"question": "What if I need immediate assistance?", "answerOne": "Email us at", "answerTwo": "and we will get back to you within 3-5 business days. If you are on our Pro or Enterprise plans, you can expect a response within 1-2 business hours."}, "four": {"question": "Is there any AI integrated?", "answer": "Yes! Our powerful AI integrations allow you to have natural language conversations with your data, generate lists, and more. It's like having your own personal data assistant!"}, "five": {"question": "Do you enrich my data?", "answer": "Yes! We automatically enrich your contacts, companies and more with data from Clearbit, FullContact, Apollo and Crunchbase. This means that you will have access to more information about your contacts, companies and more without having to manually enter it or purchase additional data."}}, "right": {"one": {"question": "What makes your platform better than others?", "answer": "We are solely focused on commercial real estate, and our support team is available 24/7 to ensure that you have the best experience possible. We also offer a free trial and a 30-day money-back guarantee, so you can try our platform risk-free. Additionally, our prices are very competitive, so you can get started without breaking the bank."}, "two": {"question": "How secure is my data?", "answer": "Security is a top priority for us. We use the best practices to secure our application and your data. We use end-to-end encryption and secure protocols to protect your data in transit. We also use secure servers and data centers that are protected by robust security controls. Additionally, we perform regular security audits and penetration testing to ensure that our application is secure and that your data is protected. We also use secure coding practices and follow the principle of least privilege to ensure that only the people who need access to your data have access to it. Finally, we have a dedicated security team that is responsible for monitoring and responding to any security incidents 24/7. So you can rest assured that your data is secure with us."}, "three": {"question": "What happens in case of a data breach?", "answer": "In the unlikely event of a data breach, we will take immediate action to contain and remediate the issue. We will notify affected users and provide them with information on how to protect themselves. We will also conduct a thorough investigation and implement additional security measures to prevent such an incident from happening again in the future."}, "four": {"question": "Do you have a backup system?", "answer": "Absolutely! We maintain a robust, automated backup system with multiple redundancies to ensure your data is always protected. All data is encrypted and stored across geographically distributed servers. Additionally, we provide self-service export tools so you can create backups of your data at any time for your own records or peace of mind."}}, "account": {"one": {"question": "How do I update my account?", "answer": "You can easily update your account from your account settings page. Just click on your avatar in the top right corner, then select \"Account Settings\" from the dropdown menu. From there, you can update your name, email address, and password. Simple!"}, "two": {"question": "How do I set up my Google account?", "answer": "You can easily set up your Google account from your account settings page. Just click on your avatar in the top right corner, then select \"Account Settings\" from the dropdown menu. Next, click on \"Email and Calendar Accounts\" and follow the instructions to link your Google account. Easy!"}, "three": {"question": "What about security? Can I use a password manager?", "answer": "Yes! We recommend using a password manager to generate and store unique, strong passwords. We also support passkeys, which you can add from your account settings page. If you have a Google account, you can also use Google Password Manager to generate and store a strong password for your Reliocrm account. And if you have an iPhone or Mac, you can use the built-in iCloud Keychain to store your Reliocrm password. Finally, if you use Chrome, you can use the built-in password manager to generate and store a strong password for your Reliocrm account."}, "four": {"question": "How do I close out active sessions?", "answer": "You can easily close out active sessions from your account settings page. Just click on your avatar in the top right corner, then select \"Account Settings\" from the dropdown menu. From there, click on \"Security\" and then \"Active Sessions\". From there, you can close out any active sessions that you no longer want to be active. This is a great way to keep your account secure if you accidentally left Reliocrm open on a public computer or if someone else has access to your account. "}}}, "mail": {"common": {"openLinkInBrowser": "If you want to open the link in a different browser than your default one, copy and paste this link:", "otp": "One-time password", "useLink": "or use the following link:"}, "emailVerification": {"body": "Hey,\nplease click the link below to verify this new email address.", "confirmEmail": "Verify email", "subject": "Verify your email"}, "forgotPassword": {"body": "Hey,\nyou requested a password reset.\n\nClick the button below to reset your password.", "resetPassword": "Reset password", "subject": "Reset your password"}, "magicLink": {"body": "Hey,\nyou requested a login email from relio.\n\nClick the link below to login.", "login": "<PERSON><PERSON>", "subject": "Login to relio"}, "newUser": {"body": "Hey,\nthanks for signing up for relio.\n\nTo start using our app, please confirm your email address by clicking the link below.", "confirmEmail": "Confirm email", "subject": "Confirm your email"}, "newsletterSignup": {"body": "Thank you for signing up for the relio newsletter. We will keep you updated with the latest news and updates.", "subject": "Welcome to our newsletter"}, "mentionNotification": {"subject": "{mentionedByName} mentioned you in a comment in {organizationName}", "heading": "You were mentioned", "greeting": "Hello,", "mentionText": "{mentionedByName} mentioned you in a comment in {organizationName}.", "viewButton": "View Comment", "footer": "If you did not expect this email, you can safely ignore it."}, "replyNotification": {"subject": "{replied<PERSON><PERSON><PERSON><PERSON>} replied to your comment in {organizationName}", "heading": "New reply to your comment", "greeting": "Hello,", "replyText": "{replied<PERSON>y<PERSON>ame} replied to your comment in {organizationName}.", "viewButton": "View Reply", "footer": "If you did not expect this email, you can safely ignore it."}, "organizationInvitation": {"body": "You have been invited to join the organization {organizationName}. Click the button below or copy and paste the link into your browser of choice to accept the invitation and join the organization.", "headline": "Join the organization {organizationName}", "join": "Join the organization", "subject": "You have been invited to join an organization"}}, "newsletter": {"email": "Email", "hints": {"error": {"message": "Could not subscribe to newsletter. Please try again later."}, "success": {"message": "Thank you for subscribing to our newsletter. We will keep you posted.", "title": "Subscribed"}}, "submit": "Subscribe", "subtitle": "Be among the first to get access to <PERSON><PERSON> and join our waitlist!", "title": "Get early access", "waitlist": {"success": "Thank you for joining the waitlist! We're working hard to bring you the best experience possible as soon as possible."}}, "onboarding": {"account": {"avatar": "Avatar", "avatarDescription": "Click the circle or drop an image to it to upload your avatar.", "name": "Name"}, "continue": "Continue", "message": "Just a few quick steps to get you started.", "notifications": {"accountSetupFailed": "We are sorry, but we were unable to set up your account. Please try again later."}, "step": "Step {step} / {total}", "title": "Set up your account"}, "organizations": {"createForm": {"name": "Organization name", "notifications": {"error": "We are sorry, but we were unable to create your organization. Please try again later.", "success": "Your organization has been created. You can now invite members."}, "submit": "Create organization", "subtitle": "Enter a name for your organization to get started. You can change the name later in the organization settings.", "title": "Create an organization"}, "invitationAlert": {"description": "You need to sign in or create an account to join the organization.", "title": "You have been invited to join an organization."}, "invitationModal": {"accept": "Accept", "decline": "Decline", "description": "You have been invited to join the organization {organizationName}. Do you want to accept the invitation and join the organization?", "title": "Join the organization"}, "organizationSelect": {"createNewOrganization": "Create new organization", "organizations": "Organizations", "personalAccount": "Personal account", "organizationSettings": "Organization Settings", "accountSettings": "Account <PERSON><PERSON>"}, "organizationsGrid": {"createNewOrganization": "Create new organization", "title": "Your organizations"}, "roles": {"admin": "Admin", "member": "Member", "owner": "Owner"}, "settings": {"changeName": {"title": "Organization name"}, "deleteOrganization": {"confirmation": "Are you sure you want to delete your organization?", "description": "Permanently delete your organization. Once you delete your organization, there is no going back. To confirm, please enter your password below:", "submit": "Delete organization", "title": "Delete organization"}, "logo": {"description": "Upload a logo for your organization.", "title": "Organization logo"}, "members": {"activeMembers": "Active members", "description": "See all active members and the pending invites of your organization.", "invitations": {"empty": "You have not invited any members yet.", "expiresAt": "Expires at {date}", "invitationStatus": {"accepted": "Accepted", "canceled": "Canceled", "pending": "Pending", "rejected": "Rejected"}, "revoke": "Revoke invitation", "resend": "Resend invitation"}, "inviteMember": {"description": "To invite a new member, send them an invitation.", "email": "Email", "notifications": {"error": {"description": "We were unable to invite the member. Please try again later.", "title": "Could not invite member"}, "success": {"description": "The member has been invited.", "title": "Member invited"}}, "role": "Role", "submit": "Invite", "title": "Invite member"}, "leaveOrganization": "Leave organization", "notifications": {"removeMember": {"error": {"description": "Could not remove the member from your organization. Please try again."}, "loading": {"description": "Removing member from organization..."}, "success": {"description": "The member has been successfully removed from your organization."}}, "revokeInvitation": {"error": {"description": "The invitation could not be revoked. Please try again later."}, "loading": {"description": "Revoking invitation..."}, "success": {"description": "The invitation has been revoked."}}, "updateMembership": {"error": {"description": "Could not update organization membership. Please try again."}, "loading": {"description": "Updating membership..."}, "success": {"description": "Membership was updated successfully"}}, "resendInvitation": {"loading": {"description": "Resending invitation..."}, "success": {"description": "Invitation sent"}, "error": {"description": "Could not sent invitation. Please try again."}}}, "pendingInvitations": "Pending invitations", "removeMember": "Remove member", "title": "Members"}, "notifications": {"title": "Notifications", "subtitle": "Manage the notifications of the organization.", "organizationDeleted": "Your organization has been deleted.", "organizationNameNotUpdated": "We were unable to update your organization name. Please try again later.", "organizationNameUpdated": "Your organization name has been updated.", "organizationNotDeleted": "We were unable to delete your organization. Please try again later."}, "subtitle": "Manage the settings of the organization.", "title": "Organization", "dangerZone": {"title": "Danger zone"}, "sidebar": {"searchSettings": "Search settings...", "account": {"heading": "Account", "title": "Account", "changeEmail": "Change email", "changePassword": "Change password", "deleteAccount": "Delete account", "notifications": "Notifications", "emailNotifications": "Email notifications", "dangerZone": "Danger zone", "emailCalendarAccounts": {"title": "Email and Calendar Accounts", "connectedAccounts": "Connected Accounts", "forwardingAddress": "Forwarding Address", "shareAccess": "Share Access", "watermark": "Watermark", "blocklist": "Blocklist"}}, "organization": {"heading": "Organization", "general": "General", "members": "Members", "billing": "Billing", "plans": "Plans", "paymentMethods": "Payment Methods"}, "security": {"heading": "Security", "title": "Security"}}}, "start": {"subtitle": "Welcome to the start page of this organization!"}}, "pricing": {"choosePlan": "Choose plan", "contactSales": "Contact sales", "description": "Choose the plan that works best for you.", "getStarted": "Get started", "month": "{count, plural, one {month} other {{count} months}}", "monthly": "Monthly", "products": {"basic": {"description": "Perfect to small teams.", "features": {"anotherFeature": "Another amazing feature", "limitedSupport": "Limited support"}, "title": "Basic"}, "enterprise": {"description": "Custom plan tailored to your requirements", "features": {"enterpriseSupport": "Enterprise support", "unlimitedProjects": "Unlimited projects"}, "title": "Enterprise"}, "free": {"description": "Start for free", "features": {"anotherFeature": "Another amazing feature", "limitedSupport": "Limited support"}, "title": "Free"}, "lifetime": {"description": "Buy once. Use forever.", "features": {"extendSupport": "Extended support", "noRecurringCosts": "No recurring costs"}, "title": "Lifetime"}, "growth": {"description": "Best for brokerages", "features": {"anotherFeature": "Another amazing feature", "fiveMembers": "Up to 5 members", "fullSupport": "Full support"}, "title": "Growth"}}, "purchase": "Purchase", "recommended": "Recommended", "subscribe": "Subscribe", "title": "Pricing", "trialPeriod": "{days} {days, plural, one {day} other {days}} free trial", "year": "{count, plural, one {year} other {{count} years}}", "yearly": "Yearly", "perSeat": "seat"}, "settings": {"account": {"avatar": {"description": "To change your avatar click the picture in this block and select a file from your computer to upload.", "notifications": {"error": "Could not update avatar", "success": "Avatar was updated successfully"}, "title": "Your avatar"}, "changeEmail": {"description": "To change your email, enter the new email and hit save. You will have to confirm the new email before it will become active.", "notifications": {"error": "Could not update email", "success": "Email was updated successfully"}, "title": "Your email"}, "changeName": {"notifications": {"error": "Could not update name", "success": "Name was updated successfully"}, "title": "Your name"}, "deleteAccount": {"confirmation": "Are you sure you want to delete your account?", "description": "Permanently delete your account. Once you delete your account, there is no going back. To confirm, please enter your password below:", "notifications": {"error": "Could not delete account", "success": "Account was deleted successfully"}, "submit": "Delete account", "title": "Delete account"}, "language": {"description": "To change the language of the app for your account, select a language from the list and click save.", "notifications": {"error": "Could not update language", "success": "Language was updated successfully"}, "title": "Your language"}, "security": {"activeSessions": {"description": "These are all the active sessions of your account. Click the X to end a specifc session.", "title": "Active sessions", "notifications": {"revokeSession": {"success": "Session revoked"}}}, "changePassword": {"currentPassword": "Current password", "newPassword": "New password", "notifications": {"error": "Could not update password", "success": "Password was updated successfully"}, "title": "Your password"}, "connectedAccounts": {"connect": "Connect", "disconnect": "Disconnect", "title": "Connected accounts", "connectAccount": "Connect {providerName} Account"}, "passkeys": {"description": "Use passkeys as a secure alternative to passwords.", "notifications": {"addPasskey": {"error": {"title": "Could not add passkey"}, "success": {"title": "Passkey added"}}, "deletePasskey": {"error": {"title": "Could not delete passkey"}, "loading": {"title": "Deleting passkey..."}, "success": {"title": "Passkey deleted"}}}, "title": "Passkeys"}, "setPassword": {"description": "You have not set a password yet. To set one, you need to go through the password reset flow. Click the button below to send an email to reset your password and follow the instructions in the email.", "notifications": {"error": "Could not set password", "success": "Password was set successfully"}, "submit": "Set password", "title": "Your password"}, "title": "Security", "subtitle": "Manage your account security."}, "subtitle": "Manage the settings of your personal account.", "title": "Account settings", "emailCalendarAccounts": {"title": "Email and Calendar Accounts", "subtitle": "Connect your email and calendar accounts to sync your events and stay up-to-date"}}, "billing": {"createCustomerPortal": {"label": "Manage billing", "notifications": {"error": {"title": "Could not create a customer portal session. Please try again."}}}, "activePlan": {"status": {"active": "Active", "canceled": "Canceled", "expired": "Expired", "incomplete": "Incomplete", "past_due": "Past due", "paused": "Paused", "trialing": "Trialing", "unpaid": "Unpaid"}, "title": "Your plan"}, "changePlan": {"description": "Choose a plan to subscribe to.", "title": "Change your plan"}, "title": "Billing"}, "menu": {"account": {"billing": "Billing", "dangerZone": "Danger zone", "general": "General", "security": "Security", "title": "Account"}, "organization": {"billing": "Billing", "general": "General", "members": "Members", "title": "Organization", "dangerZone": "Danger zone"}}, "save": "Save"}, "start": {"subtitle": "See the latest stats of your awesome business.", "welcome": "Welcome {name}!"}, "zod": {"errors": {"invalid_arguments": "Invalid function arguments", "invalid_date": "Invalid date", "invalid_enum_value": "Invalid enum value. Expected {- options}, received '{received}'", "invalid_intersection_types": "Intersection results could not be merged", "invalid_literal": "Invalid literal value, expected {expected}", "invalid_return_type": "Invalid function return type", "invalid_string": {"cuid": "Invalid {validation}", "datetime": "Invalid {validation}", "email": "Invalid {validation}", "endsWith": "Invalid input: must end with \"{endsWith}\"", "regex": "Invalid", "startsWith": "Invalid input: must start with \"{startsWith}\"", "url": "Invalid {validation}", "uuid": "Invalid {validation}"}, "invalid_type": "Expected {expected}, received {received}", "invalid_type_received_undefined": "Required", "invalid_union": "Invalid input", "invalid_union_discriminator": "Invalid discriminator value. Expected {- options}", "not_finite": "Number must be finite", "not_multiple_of": "Number must be a multiple of {multipleOf}", "too_big": {"array": {"exact": "Array must contain exactly {maximum} element(s)", "inclusive": "Array must contain at most {maximum} element(s)", "not_inclusive": "Array must contain less than {maximum} element(s)"}, "date": {"exact": "Date must be exactly {- maximum, datetime}", "inclusive": "Date must be smaller than or equal to {- maximum, datetime}", "not_inclusive": "Date must be smaller than {- maximum, datetime}"}, "number": {"exact": "Number must be exactly {maximum}", "inclusive": "Number must be less than or equal to {maximum}", "not_inclusive": "Number must be less than {maximum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {maximum} character(s)", "inclusive": "String must contain at most {maximum} character(s)", "not_inclusive": "String must contain under {maximum} character(s)"}}, "too_small": {"array": {"exact": "Array must contain exactly {minimum} element(s)", "inclusive": "Array must contain at least {minimum} element(s)", "not_inclusive": "Array must contain more than {minimum} element(s)"}, "date": {"exact": "Date must be exactly {- minimum, datetime}", "inclusive": "Date must be greater than or equal to {- minimum, datetime}", "not_inclusive": "Date must be greater than {- minimum, datetime}"}, "number": {"exact": "Number must be exactly {minimum}", "inclusive": "Number must be greater than or equal to {minimum}", "not_inclusive": "Number must be greater than {minimum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {minimum} character(s)", "inclusive": "String must contain at least {minimum} character(s)", "not_inclusive": "String must contain over {minimum} character(s)"}}, "unrecognized_keys": "Unrecognized key(s) in object: {- keys}"}, "types": {"array": "array", "bigint": "bigint", "boolean": "boolean", "date": "date", "float": "float", "function": "function", "integer": "integer", "map": "map", "nan": "nan", "never": "never", "null": "null", "number": "number", "object": "object", "promise": "promise", "set": "set", "string": "string", "symbol": "symbol", "undefined": "undefined", "unknown": "unknown", "void": "void"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "datetime", "email": "email", "emoji": "emoji", "ip": "up", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}, "choosePlan": {"title": "Choose your plan", "description": "To continue, please select a plan."}, "home": {"title": "Prospect with <PERSON><PERSON>", "subtitle": "Keep all of your data in one place, and access it anywhere.", "slides": {"frontpage": "Frontpage", "dashboard": "Dashboard", "tasks": "Tasks Split"}, "getStarted": "Get Started", "earlyAccess": "Get early access", "features": {"one": {"title": "Streamlined team collaboration", "description": "Bring your team and clients together, manage projects professionally, and create a company intranet - all in one place."}, "two": {"title": "All-in-one communication hub", "description": "Manage all your communication with contacts - from email to phone calls, track every conversation between you and your team."}, "three": {"title": "Actionable insights", "description": "Get detailed analytics on your communication - track phone calls, emails, and other interactions to make data-driven decisions."}, "four": {"title": "AI-powered data enrichment", "description": "Automatically enrich your data with integrations from Clearbit, FullContact, and more - unlock new insights and opportunities."}}, "feature2": {"title": "<PERSON><PERSON> comes with a perfect set of tools to help you grow your business.", "one": {"title": "Lists and AI Assistant.", "description": "With our AI Assistant, you can create lists, query about your properties, contacts and more. Talk to our AI Assistant to get the answers you need about your data."}, "two": {"title": "Managed CRM.", "description": "Our managed CRM helps you to manage your contacts, properties and more. Keep your activity, tasks and calendar events in view no matter where you are."}, "three": {"title": "Dashboard Analytics.", "description": "With Our dashboard analytics helps you to track your tasks, calls and pipeline progress."}, "four": {"title": "Import with ease.", "description": "Import your contacts, properties and more with ease. We support CSV, Excel and more."}, "five": {"title": "Collaborate and share.", "description": "Share your data with your team and collaborate on your projects."}}, "featureSection": {"title": "Purpose-built CRM for commercial real estate brokerages", "description": "Empower your brokerage to manage deals, nurture client relationships, and accelerate transactions—all in one modern platform designed for CRE professionals.", "metrics": {"one": {"value": "35%", "label": "More deals closed"}, "two": {"value": "50%", "label": "Faster pipeline"}, "three": {"value": "2x", "label": "Client retention"}}, "metricsDescription": "Average improvement reported by leading CRE brokerages"}, "featureSection3": {"title": "The CRM built for commercial real estate brokers", "description": "Manage your properties, contacts, deals, and team—all in one place. Relio streamlines your workflow so you can focus on closing more deals and building stronger client relationships.", "one": {"title": "Task Management", "description": "Assign tasks to contacts or properties and never miss a follow-up.", "content": {"title": "Stay on top of every deal.", "description": "Create, assign, and track tasks linked to properties or contacts. Ensure every follow-up, showing, and deadline is managed in one place for maximum efficiency."}}, "two": {"title": "Shareable Notes & Fliers", "description": "Build and share public notes or single-page fliers with clients.", "content": {"title": "Impress with professional fliers.", "description": "Easily create and share beautiful, public-facing notes or fliers for your listings. Keep clients informed and engaged with up-to-date property information."}}, "three": {"title": "Property & Contact Management", "description": "View detailed records and correspondence in one place.", "content": {"title": "Everything you need, organized.", "description": "Access comprehensive property and contact profiles, including all emails, calls, and notes. Never lose track of a conversation or document again."}}, "four": {"title": "Import & Export", "description": "Seamlessly import and export data across platforms.", "content": {"title": "Connect your existing data.", "description": "Import data from any platform with our flexible schema converter. Export your data in multiple formats for brokers, ensuring smooth transitions and data portability."}}, "five": {"title": "Collaboration", "description": "Work together with your team and share updates instantly.", "content": {"title": "Teamwork made easy.", "description": "Collaborate on deals, share notes, and keep everyone in the loop. Real-time updates ensure your team is always aligned."}}}, "logos": {"title": "Empowering commercial real estate brokerages", "description": "From boutique agencies to global franchises."}}, "testimonials": {"trustedBy": "Trusted by", "topCREBrokers": "top CRE brokers", "description": "<PERSON><PERSON> empowers commercial real estate professionals to close more deals, build stronger client relationships, and streamline every step of the brokerage process.", "seeBrokerSuccessStories": "See broker success stories", "brokers": {"nickKazemi": {"quote": "This CRM is a game-changer! It has revolutionized the way we approach problem-solving and decision-making in our organization."}, "tylerLeeson": {"quote": "It's incredibly intuitive and easy to use. Even those without technical expertise can leverage its power to improve their workflows."}, "michaelCavner": {"quote": "A robust solution that fits perfectly into our workflow. It has enhanced our team's capabilities and allowed us to tackle more complex projects."}, "drewHolden": {"quote": "Absolutely revolutionary, a game-changer for our industry. It has streamlined our processes and enhanced our productivity dramatically."}, "matthewKipp": {"quote": "I can't imagine going back to how things were before this. It has not only improved my work efficiency but also my daily life."}, "christianTait": {"quote": "Prospecting has never been easier! This CRM has helped us identify and engage with potential clients more effectively than ever before."}}}}