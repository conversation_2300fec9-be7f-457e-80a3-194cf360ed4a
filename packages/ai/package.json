{"dependencies": {"@ai-sdk/anthropic": "^1.1.10", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "ai": "^4.1.46", "openai": "^4.85.4", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/react": "19.0.0", "typescript": "5.8.2"}, "main": "./index.ts", "name": "@repo/ai", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}