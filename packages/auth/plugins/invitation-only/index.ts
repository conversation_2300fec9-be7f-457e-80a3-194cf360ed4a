import { config } from "@repo/config";
import { db } from "@repo/database/server";
import type { BetterAuthPlugin } from "better-auth";
import { APIError } from "better-auth/api";
import { createAuthMiddleware } from "better-auth/plugins";

export const invitationOnlyPlugin = () =>
	({
		id: "invitationOnlyPlugin",
		hooks: {
			before: [
				{
					matcher: (context) =>
						context.path.startsWith("/sign-up/email"),
					handler: createAuthMiddleware(async (ctx) => {
						if (config.auth.enableSignup) {
							return;
						}

						const { email, invitationId } = ctx.body;

						// check if there is a valid invitation for the email
						const invitation = await db.invitation.findFirst({
							where: {
								OR: [
									{
										id: invitationId,
										status: "pending",
									},
									{
										email,
										status: "pending",
									},
								],
							},
						});

						const hasValidInvitation =
							invitation &&
							(invitation.email === email ||
								invitation.id === invitationId);

						if (!hasValidInvitation) {
							throw new APIError("BAD_REQUEST", {
								code: "INVALID_INVITATION",
								message: "No invitation found for this email",
							});
						}
					}),
				},
			],
		},
		$ERROR_CODES: {
			INVALID_INVITATION: "No invitation found for this email",
		},
	}) satisfies BetterAuthPlugin;
