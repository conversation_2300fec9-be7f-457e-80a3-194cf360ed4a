import { createAccessControl } from "better-auth/plugins/access";

// Create access control configuration with set-active permission
export const ac = createAccessControl({
	organization: ["update", "delete", "set-active"],
	member: ["create", "update", "delete"],
	invitation: ["create", "cancel"],
});

// Define roles with custom permissions - based on better-auth documentation
export const member = ac.newRole({
	organization: ["set-active"], // Members can only set active organization
});

export const admin = ac.newRole({
	organization: ["update", "set-active"], // Admins can update and set active
	member: ["create", "update", "delete"],
	invitation: ["create", "cancel"],
});

export const owner = ac.newRole({
	organization: ["update", "delete", "set-active"], // Owners have all permissions
	member: ["create", "update", "delete"],
	invitation: ["create", "cancel"],
});
