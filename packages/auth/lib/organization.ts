import { db } from "@repo/database/server";
import { objectTypes } from "@repo/database";
import { logger } from "@repo/logs";
import { setSubscriptionSeats } from "@repo/payments";

// Default column definitions for initial views
// const defaultColumnDefs = {
// 	contacts: [
// 		{ field: "firstName", headerName: "First Name", width: 150 },
// 		{ field: "lastName", headerName: "Last Name", width: 150 },
// 		{ field: "title", headerName: "Title", width: 150 },
// 		{ field: "email", headerName: "Email", width: 200 },
// 		{ field: "phone", headerName: "Phone", width: 150 },
// 		{ field: "company.name", headerName: "Company", width: 150 },
// 		{ field: "status", headerName: "Status", width: 120 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// 	companies: [
// 		{ field: "name", headerName: "Company Name", width: 200 },
// 		{ field: "industry", headerName: "Industry", width: 150 },
// 		{ field: "size", headerName: "Size", width: 120 },
// 		{ field: "website", headerName: "Website", width: 200 },
// 		{ field: "phone", headerName: "Phone", width: 150 },
// 		{ field: "email", headerName: "Email", width: 200 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// 	properties: [
// 		{ field: "name", headerName: "Property Name", width: 200 },
// 		{ field: "propertyType", headerName: "Type", width: 150 },
// 		{ field: "address.street", headerName: "Street", width: 200 },
// 		{ field: "address.city", headerName: "City", width: 150 },
// 		{ field: "address.state", headerName: "State", width: 100 },
// 		{ field: "price", headerName: "Price", width: 150 },
// 		{ field: "units", headerName: "Units", width: 100 },
// 	],
// };

export async function getOrganizationMembership(
	userId: string,
	organizationId: string,
) {
	const membership = await db.member.findUnique({
		where: {
			userId_organizationId: {
				userId,
				organizationId,
			},
		},
	});

	return membership;
}

export async function createInitialOrganizationViews(
	organizationId: string,
	createdByUserId: string,
) {
	try {
		logger.info("Creating initial views for organization", {
			organizationId,
			createdByUserId,
		});

		const viewPromises = objectTypes.map(async (objectType) => {
			const viewName = `${objectType.charAt(0).toUpperCase() + objectType.slice(1)}`;

			return db.objectView.create({
				data: {
					name: viewName,
					objectType,
					organizationId,
					viewType: "table",
					columnDefs: [],
					filters: [],
					filterCondition: "and",
					sortBy: "createdAt",
					sortDirection: "desc",
					createdBy: createdByUserId,
					isDefault: true,
					isPublic: true,
				},
			});
		});

		const createdViews = await Promise.all(viewPromises);

		logger.info("Successfully created initial views for organization", {
			organizationId,
			viewsCreated: createdViews.length,
			viewIds: createdViews.map((v) => v.id),
		});

		return createdViews;
	} catch (error) {
		logger.error("Failed to create initial views for organization", {
			organizationId,
			createdByUserId,
			error,
		});
		throw error;
	}
}

export async function updateSeatsInOrganizationSubscription(
	organizationId: string,
) {
	const organization = await db.organization.findUnique({
		where: { id: organizationId },
		include: {
			purchases: true,
			_count: {
				select: {
					members: true,
				},
			},
		},
	});

	if (!organization?.purchases.length) {
		return;
	}

	const activeSubscription = organization.purchases.find(
		(purchase: any) => purchase.type === "SUBSCRIPTION",
	);

	if (!activeSubscription?.subscriptionId) {
		return;
	}

	try {
		await setSubscriptionSeats({
			id: activeSubscription.subscriptionId,
			seats: organization._count.members,
		});
	} catch (error) {
		logger.error("Could not update seats in organization subscription", {
			organizationId,
			error,
		});
	}
}
