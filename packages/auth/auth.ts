import { config } from "@repo/config";
import { db } from "@repo/database/server";
import type { Locale } from "@repo/i18n";
import { logger } from "@repo/logs";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils";
import { betterAuth } from "better-auth";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import {
	admin as adminPlugin,
	createAuthMiddleware,
	magicLink,
	openAPI,
	organization,
	username,
} from "better-auth/plugins";
import { passkey } from "better-auth/plugins/passkey";
import { parse as parseCookies } from "cookie";
import { MongoClient } from "mongodb";
import { ac, admin, member, owner } from "./lib/access-control";
import { updateSeatsInOrganizationSubscription } from "./lib/organization";
import { getUserByEmail } from "./lib/user";
import { invitationOnlyPlugin } from "./plugins/invitation-only";

const client = new MongoClient(process.env.DIRECT_DATABASE_URL || "");

const getLocaleFromRequest = (request?: Request) => {
	const cookies = parseCookies(request?.headers.get("cookie") ?? "");
	return (
		(cookies[config.i18n.localeCookieName] as Locale) ??
		config.i18n.defaultLocale
	);
};

const appUrl = getBaseUrl();

const getTrustedOrigins = () => {
	const baseUrl = appUrl;
	const origins = [baseUrl];

	if (baseUrl.includes("://reliocrm.com")) {
		origins.push(baseUrl.replace("://reliocrm.com", "://www.reliocrm.com"));
	} else if (baseUrl.includes("://www.reliocrm.com")) {
		origins.push(baseUrl.replace("://www.reliocrm.com", "://reliocrm.com"));
	}

	if (process.env.NODE_ENV === "development") {
		origins.push("http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001");
	}

	return origins;
};

export const auth = betterAuth({
	baseURL: appUrl,
	trustedOrigins: getTrustedOrigins(),
	database: mongodbAdapter(client.db("dev")),
	session: {
		expiresIn: config.auth.sessionCookieMaxAge,
		freshAge: 0,
	},
	account: {
		accountLinking: {
			enabled: true,
			trustedProviders: [
				"google", 
				...(process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET ? ["microsoft"] : []), 
				"github"
			],
		},
	},
	hooks: {
		after: createAuthMiddleware(async (ctx) => {
			if (ctx.path.startsWith("/organization/accept-invitation")) {
				const { invitationId } = ctx.body;

				if (!invitationId) {
					return;
				}

				const invitation = await db.invitation.findUnique({
					where: { id: invitationId },
				});

				if (!invitation) {
					return;
				}

				await updateSeatsInOrganizationSubscription(
					invitation.organizationId,
				);
			} else if (ctx.path.startsWith("/organization/remove-member")) {
				const { organizationId } = ctx.body;

				if (!organizationId) {
					return;
				}

				await updateSeatsInOrganizationSubscription(organizationId);
			}
		}),
		before: createAuthMiddleware(async (ctx) => {
			// Force offline access and consent for Google OAuth
			if (ctx.path.includes("/sign-in/google")) {
				// Modify the redirect response to include proper OAuth parameters
				if (ctx.method === "GET" && ctx.query) {
					// Add required parameters for refresh tokens
					if (!ctx.query.access_type) {
						ctx.query.access_type = "offline";
					}
					if (!ctx.query.prompt) {
						ctx.query.prompt = "consent";
					}
					if (!ctx.query.include_granted_scopes) {
						ctx.query.include_granted_scopes = "true";
					}
				}
			}
			
			// Debug OAuth flows
			if (ctx.path.includes("/sign-in/") || ctx.path.includes("/callback/")) {
				logger.info("OAuth Flow Debug", {
					path: ctx.path,
					method: ctx.method,
					query: ctx.query,
					body: ctx.body ? Object.keys(ctx.body) : undefined,
				});
			}
		}),
	},
	user: {
		additionalFields: {
			onboardingComplete: {
				type: "boolean",
				required: false,
			},
			locale: {
				type: "string",
				required: false,
			},
		},
		deleteUser: {
			enabled: true,
		},
		changeEmail: {
			enabled: true,
			sendChangeEmailVerification: async (
				{ user: { email, name }, url },
				request,
			) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "emailVerification",
					context: {
						url,
						name,
					},
					locale,
				});
			},
		},
	},
	emailAndPassword: {
		enabled: true,
		// If signup is disabled, the only way to sign up is via an invitation. So in this case we can auto sign in the user, as the email is already verified by the invitation.
		// If signup is enabled, we can't auto sign in the user, as the email is not verified yet.
		autoSignIn: !config.auth.enableSignup,
		requireEmailVerification: config.auth.enableSignup,
		sendResetPassword: async ({ user, url }, request) => {
			const locale = getLocaleFromRequest(request);
			await sendEmail({
				to: user.email,
				templateId: "forgotPassword",
				context: {
					url,
					name: user.name,
				},
				locale,
			});
		},
	},
	emailVerification: {
		sendOnSignUp: config.auth.enableSignup,
		sendVerificationEmail: async (
			{ user: { email, name }, url },
			request,
		) => {
			const locale = getLocaleFromRequest(request);
			await sendEmail({
				to: email,
				templateId: "emailVerification",
				context: {
					url,
					name,
				},
				locale,
			});
		},
	},
	socialProviders: {
		google: {
			clientId: process.env.GOOGLE_CLIENT_ID as string,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
			scope: [
				"openid",
				"email", 
				"profile", 
				"https://www.googleapis.com/auth/gmail.send",
				"https://www.googleapis.com/auth/gmail.compose",
				"https://www.googleapis.com/auth/gmail.modify",
				"https://www.googleapis.com/auth/gmail.labels",
				"https://www.googleapis.com/auth/gmail.readonly",
				"https://www.googleapis.com/auth/userinfo.email",
				"https://www.googleapis.com/auth/userinfo.profile",
			],
			prompt: "consent",
			accessType: "offline",
		},
		...(process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET && {
			microsoft: {
				clientId: process.env.MICROSOFT_CLIENT_ID as string,
				clientSecret: process.env.MICROSOFT_CLIENT_SECRET as string,
				scope: [
					"openid",
					"profile",
					"email",
					"User.Read",
					"Mail.Send",
					"Mail.Read",
					"offline_access"
				],
			},
		}),
		github: {
			clientId: process.env.GITHUB_CLIENT_ID as string,
			clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
			scope: ["user:email"],
		},
	},
	plugins: [
		username(),
		adminPlugin(),
		passkey(),
		magicLink({
			disableSignUp: true,
			sendMagicLink: async ({ email, url }, request) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "magicLink",
					context: {
						url,
					},
					locale,
				});
			},
		}),
		organization({
			ac,
			roles: {
				owner,
				admin,
				member,
			},
			sendInvitationEmail: async (
				{ email, id, organization },
				request,
			) => {
				const locale = getLocaleFromRequest(request);
				const existingUser = await getUserByEmail(email);

				const url = new URL(
					existingUser ? "/auth/login" : "/auth/signup",
					getBaseUrl(),
				);

				url.searchParams.set("invitationId", id);
				url.searchParams.set("email", email);

				await sendEmail({
					to: email,
					templateId: "organizationInvitation",
					locale,
					context: {
						organizationName: organization.name,
						url: url.toString(),
					},
				});
			},
		}),
		openAPI(),
		invitationOnlyPlugin(),
	],
	onAPIError: {
		onError(error, ctx) {
			logger.error(error, { ctx });
		},
	},
});

export * from "./lib/organization";

export type Session = typeof auth.$Infer.Session;

export type ActiveOrganization = typeof auth.$Infer.ActiveOrganization;

export type Organization = typeof auth.$Infer.Organization;

export type OrganizationMemberRole = typeof auth.$Infer.Member.role;

export type OrganizationInvitationStatus = typeof auth.$Infer.Invitation.status;

export type OrganizationMetadata = Record<string, unknown> | undefined;
