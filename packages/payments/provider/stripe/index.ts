import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import Stripe from "stripe";
import { setCustomerIdToEntity } from "../../src/lib/customer";

import type {
	CreateCheckoutLink,
	CreateCustomerPortalLink,
	GetInvoices,
	SetSubscriptionSeats,
	WebhookHandler,
} from "../../types";

let stripeClient: Stripe | null = null;

export function getStripeClient() {
	if (stripeClient) {
		return stripeClient;
	}

	const stripeSecretKey = process.env.STRIPE_SECRET_KEY as string;

	if (!stripeSecretKey) {
		throw new Error("Missing env variable STRIPE_SECRET_KEY");
	}

	stripeClient = new Stripe(stripeSecretKey);

	return stripeClient;
}

export const createCheckoutLink: CreateCheckoutLink = async (options) => {
	const stripeClient = getStripeClient();
	const {
		type,
		productId,
		redirectUrl,
		customerId,
		organizationId,
		userId,
		trialPeriodDays,
		seats,
	} = options;

	const metadata = {
		organization_id: organizationId || null,
		user_id: userId || null,
	};

	const response = await stripeClient.checkout.sessions.create({
		mode: type === "subscription" ? "subscription" : "payment",
		success_url: redirectUrl ?? "",
		line_items: [
			{
				quantity: seats ?? 1,
				price: productId,
			},
		],
		customer: customerId,
		...(type === "one-time"
			? {
					payment_intent_data: {
						metadata,
					},
					customer_creation: "always",
				}
			: {
					subscription_data: {
						metadata,
						trial_period_days: trialPeriodDays,
					},
				}),
		metadata,
	});

	return response.url;
};

export const createCustomerPortalLink: CreateCustomerPortalLink = async ({
	customerId,
	redirectUrl,
}) => {
	const stripeClient = getStripeClient();

	const response = await stripeClient.billingPortal.sessions.create({
		customer: customerId,
		return_url: redirectUrl ?? "",
	});

	return response.url;
};

export const setSubscriptionSeats: SetSubscriptionSeats = async ({
	id,
	seats,
}) => {
	const stripeClient = getStripeClient();

	const subscription = await stripeClient.subscriptions.retrieve(id);

	if (!subscription) {
		throw new Error("Subscription not found.");
	}

	await stripeClient.subscriptions.update(id, {
		items: [
			{
				id: subscription.items.data[0].id,
				quantity: seats,
			},
		],
	});
};

export const getInvoices: GetInvoices = async ({ customerId }) => {
	const stripeClient = getStripeClient();

	const invoices = await stripeClient.invoices.list({
		customer: customerId,
	});

	return invoices.data.map((invoice) => ({
		id: invoice.id,
		date: invoice.created,
		status: invoice.status ?? undefined,
		downloadUrl: invoice.hosted_invoice_url ?? undefined,
	}));
};

export const webhookHandler: WebhookHandler = async (req) => {
	const stripeClient = getStripeClient();

	if (!req.body) {
		return new Response("Invalid request.", {
			status: 400,
		});
	}

	let event: Stripe.Event | undefined;

	try {
		event = await stripeClient.webhooks.constructEventAsync(
			await req.text(),
			req.headers.get("stripe-signature") as string,
			process.env.STRIPE_WEBHOOK_SECRET as string,
		);
	} catch (e) {
		logger.error(e);

		return new Response("Invalid request.", {
			status: 400,
		});
	}

	try {
		switch (event.type) {
			case "checkout.session.completed": {
				const { mode, metadata, customer, id } = event.data.object;

				if (mode === "subscription") {
					break;
				}

				const checkoutSession =
					await stripeClient.checkout.sessions.retrieve(id, {
						expand: ["line_items"],
					});

				const productId = checkoutSession.line_items?.data[0].price?.id;

				if (!productId) {
					return new Response("Missing product ID.", {
						status: 400,
					});
				}

				// Check if this is a credit purchase by checking against known credit price IDs
				const creditPackages: Record<
					string,
					{ credits: number; name: string }
				> = {
					[process.env.STRIPE_PRICE_STARTER ||
					"price_ai_credits_starter"]: {
						credits: 500,
						name: "starter",
					},
					[process.env.STRIPE_PRICE_PROFESSIONAL ||
					"price_ai_credits_professional"]: {
						credits: 2000,
						name: "professional",
					},
					[process.env.STRIPE_PRICE_ENTERPRISE ||
					"price_ai_credits_enterprise"]: {
						credits: 5000,
						name: "enterprise",
					},
				};

				const creditPackage = creditPackages[productId];

				if (creditPackage && metadata?.user_id) {
					// Handle credit purchase
					const { addPurchasedCredits } = await import(
						"@repo/ai"
					);
					const { db } = await import("@repo/database/server");

					if (!metadata.organization_id) {
						throw new Error(
							"Organization ID required for credit purchase",
						);
					}

					await addPurchasedCredits(
						db,
						metadata.user_id,
						metadata.organization_id,
						creditPackage.credits,
					);

					// Create credit purchase record
					await db.creditPurchase.create({
						data: {
							id: `cp_${id}`, // Unique ID for credit purchase
							userId: metadata.user_id,
							organizationId: metadata.organization_id,
							packageId: creditPackage.name,
							credits: creditPackage.credits,
							price: (checkoutSession.amount_total || 0) / 100, // Convert from cents
							stripePaymentIntentId:
								checkoutSession.payment_intent as string,
							status: "completed",
							createdAt: new Date(),
							updatedAt: new Date(),
						},
					});

					logger.info(
						`Added ${creditPackage.credits} credits to user ${metadata.user_id}`,
					);
				} else {
					// Handle regular purchases (existing code)
					await db.purchase.create({
						data: {
							id: id,
							organizationId: metadata?.organization_id || null,
							userId: metadata?.user_id || null,
							customerId: customer as string,
							type: "one_time",
							productId,
						},
					});
				}

				await setCustomerIdToEntity(customer as string, {
					organizationId: metadata?.organization_id,
					userId: metadata?.user_id,
				});

				break;
			}
			case "customer.subscription.created": {
				const { metadata, customer, items, id } = event.data.object;

				const productId = items?.data[0].price?.id;

				if (!productId) {
					return new Response("Missing product ID.", {
						status: 400,
					});
				}

				await db.purchase.create({
					data: {
						id: id,
						subscriptionId: id,
						organizationId: metadata?.organization_id || null,
						userId: metadata?.user_id || null,
						customerId: customer as string,
						type: "subscription",
						productId,
						status: event.data.object.status,
					},
				});

				await setCustomerIdToEntity(customer as string, {
					organizationId: metadata?.organization_id,
					userId: metadata?.user_id,
				});

				break;
			}
			case "customer.subscription.updated": {
				const subscriptionId = event.data.object.id;

				const existingPurchase = await db.purchase.findUnique({
					where: {
						id: subscriptionId,
					},
				});

				if (existingPurchase) {
					await db.purchase.update({
						data: {
							status: event.data.object.status,
							productId:
								event.data.object.items?.data[0].price?.id,
						},
						where: {
							id: subscriptionId,
						},
					});
				}
				break;
			}
			case "customer.subscription.deleted": {
				await db.purchase.delete({
					where: {
						id: event.data.object.id,
					},
				});

				break;
			}

			default:
				return new Response("Unhandled event type.", {
					status: 200,
				});
		}

		return new Response(null, { status: 204 });
	} catch (error) {
		return new Response(
			`Webhook error: ${error instanceof Error ? error.message : ""}`,
			{
				status: 400,
			},
		);
	}
};
