{"dependencies": {"@prisma/client": "^6.10.1", "@prisma/extension-accelerate": "^2.0.1", "@repo/config": "workspace:*", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "22.13.10", "dotenv-cli": "^8.0.0", "prisma": "^6.10.1", "prisma-json-types-generator": "^3.2.2", "zod-prisma-types": "^3.2.4"}, "main": "./index.ts", "name": "@repo/database", "scripts": {"postinstall": "prisma generate --schema=./prisma", "generate": "prisma generate --schema=./prisma --no-engine --no-hints", "push": "dotenv -c -e ../../.env -- prisma db push --schema=./prisma --skip-generate", "migrate": "dotenv -c -e ../../.env -- prisma migrate dev --schema=./prisma", "studio": "dotenv -c -e ../../.env -- prisma studio --schema=./prisma", "type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}