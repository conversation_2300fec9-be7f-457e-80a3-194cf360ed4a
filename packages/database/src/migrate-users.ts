import { PrismaClient } from '@prisma/client'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

const prisma = new PrismaClient()

interface OldUser {
  _id: { $oid: string }
  email: string
  password?: string
  firstName?: string
  lastName?: string
  profileImg?: string
  company?: string
  phone?: string
  title?: string
  timeZone?: string
  language?: string
  createdAt?: { $date: string }
  updatedAt?: { $date: string }
  isOnBoarded?: boolean
  team?: Array<{
    teamId: { $oid: string }
    status: string
  }>
  teamActive?: { $oid: string }
  lastActive?: { $date: string }
  expireToken?: string
  resetToken?: string
  googleId?: string
  uid?: string
}

interface OldTeam {
  _id: { $oid: string }
  name: string
  slug?: string
  createdAt?: { $date: string }
  ownerId?: { $oid: string }
}

// Helper function to convert MongoDB date to JS Date
function convertDate(mongoDate: { $date: string } | string | undefined): Date {
  if (!mongoDate) return new Date()
  if (typeof mongoDate === 'string') return new Date(mongoDate)
  return new Date(mongoDate.$date)
}

// Helper function to generate organization slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

async function migrateUsers(options: { limit?: number; testEmail?: string } = {}) {
  try {
    console.log('🚀 Starting user migration...')
    console.log('📋 This migration ensures EVERY user gets:')
    console.log('   1. ✅ Account record (credential provider)')
    console.log('   2. ✅ Member record(s) with role "member"') 
    console.log('   3. ✅ UserOrganizationCredits record(s)')
    console.log('')

    // Read the old user data - check multiple possible locations
    let usersJsonPath = join(process.cwd(), 'old_users.json')
    if (!existsSync(usersJsonPath)) {
      // Try in the root of the project (3 levels up from packages/database/src)
      usersJsonPath = join(__dirname, '../../../old_users.json')
    }
    if (!existsSync(usersJsonPath)) {
      // Try in tooling/scripts/src directory
      usersJsonPath = join(__dirname, '../../../tooling/scripts/src/old_users.json')
    }
    if (!existsSync(usersJsonPath)) {
      // Try relative to current working directory if in scripts folder
      usersJsonPath = join(process.cwd(), 'src/old_users.json')
    }
    if (!existsSync(usersJsonPath)) {
      throw new Error('old_users.json file not found. Please place it in the project root or tooling/scripts/src/')
    }
    
    console.log(`📂 Found users file at: ${usersJsonPath}`)
    let oldUsers: OldUser[] = JSON.parse(readFileSync(usersJsonPath, 'utf-8'))

    // Apply filtering options
    if (options.testEmail) {
      oldUsers = oldUsers.filter(user => user.email === options.testEmail)
      console.log(`🧪 TEST MODE: Filtering to user with email: ${options.testEmail}`)
    } else if (options.limit) {
      oldUsers = oldUsers.slice(0, options.limit)
      console.log(`🧪 TEST MODE: Limiting to first ${options.limit} user(s)`)
    }

    console.log(`Found ${oldUsers.length} users to migrate`)
    
    if (oldUsers.length === 0) {
      console.log('❌ No users found matching the criteria. Exiting.')
      return
    }

    // First, let's collect all unique teams/organizations
    const teamMap = new Map<string, { name: string; ownerId?: string; createdAt: Date }>()
    
    for (const user of oldUsers) {
      if (user.team) {
        for (const teamMembership of user.team) {
          if (teamMembership.status === 'Approved') {
            const teamId = teamMembership.teamId.$oid
            if (!teamMap.has(teamId)) {
              // Extract organization name from user's company or create default
              const orgName = user.company || `Team ${teamId.slice(-8)}`
              teamMap.set(teamId, {
                name: orgName,
                ownerId: user._id.$oid,
                createdAt: convertDate(user.createdAt)
              })
            }
          }
        }
      }
    }

    console.log(`Found ${teamMap.size} unique organizations to create`)

    // Create organizations
    const organizationIdMap = new Map<string, string>() // old team ID -> new org ID
    
    for (const oldTeamId of Array.from(teamMap.keys())) {
      const teamData = teamMap.get(oldTeamId)!
      
      try {
        const org = await prisma.organization.create({
          data: {
            name: teamData.name,
            slug: generateSlug(teamData.name),
            createdAt: teamData.createdAt,
            metadata: JSON.stringify({ migratedFromTeamId: oldTeamId })
          }
        })
        organizationIdMap.set(oldTeamId, org.id)
        console.log(`Created organization: ${org.name} (${org.id})`)
      } catch (error) {
        console.error(`Failed to create organization for team ${oldTeamId}:`, error)
      }
    }

    // Migrate users
    const userIdMap = new Map<string, string>() // old user ID -> new user ID
    
    for (const oldUser of oldUsers) {
      try {
        // Prepare user data
        const name = [oldUser.firstName, oldUser.lastName].filter(Boolean).join(' ').trim() || 'Unknown User'
        
        const userData = {
          name,
          email: oldUser.email,
          emailVerified: oldUser.isOnBoarded || false,
          image: oldUser.profileImg || null,
          createdAt: convertDate(oldUser.createdAt),
          updatedAt: convertDate(oldUser.updatedAt),
          onboardingComplete: oldUser.isOnBoarded || false,
          locale: oldUser.language || 'en',
          // Store additional data in preferences
          preferences: JSON.stringify({
            firstName: oldUser.firstName,
            lastName: oldUser.lastName,
            company: oldUser.company,
            phone: oldUser.phone,
            title: oldUser.title,
            timeZone: oldUser.timeZone,
            lastActive: oldUser.lastActive?.$date,
            migratedFromId: oldUser._id.$oid
          })
        }

        const user = await prisma.user.create({
          data: userData
        })

        userIdMap.set(oldUser._id.$oid, user.id)
        console.log(`Created user: ${user.name} (${user.email})`)

        // ====================================================================
        // REQUIRED: Create all three essential records for each user:
        // 1. Account record (for authentication credentials)
        // 2. Member record(s) (linking user to organization(s))  
        // 3. UserOrganizationCredits record(s) (credits system)
        // ====================================================================

        // ALWAYS create a credential account record for every user
        try {
          await prisma.account.create({
            data: {
              accountId: user.id, // Use user ID as account ID for credentials
              providerId: 'credential',
              userId: user.id,
              password: oldUser.password || null, // Password can be null
              createdAt: convertDate(oldUser.createdAt),
              updatedAt: convertDate(oldUser.updatedAt)
            }
          })
          console.log(`  ✅ Created credential account`)
        } catch (error) {
          console.error(`  ❌ Failed to create credential account for ${user.email}:`, error)
        }

        // Handle Google OAuth if googleId exists (separate account record)
        if (oldUser.googleId || oldUser.uid) {
          try {
            await prisma.account.create({
              data: {
                accountId: oldUser.googleId || oldUser.uid!,
                providerId: 'google',
                userId: user.id,
                createdAt: convertDate(oldUser.createdAt),
                updatedAt: convertDate(oldUser.updatedAt)
              }
            })
            console.log(`  ✅ Created Google OAuth account`)
          } catch (error) {
            console.error(`  ❌ Failed to create Google account for ${user.email}:`, error)
          }
        }

        // Create organization memberships and credits for ALL approved teams
        let membershipCount = 0
        if (oldUser.team && oldUser.team.length > 0) {
          console.log(`  Processing ${oldUser.team.length} team memberships...`)
          
          for (const teamMembership of oldUser.team) {
            if (teamMembership.status === 'Approved') {
              const newOrgId = organizationIdMap.get(teamMembership.teamId.$oid)
              if (newOrgId) {
                try {
                  // Create Member record
                  await prisma.member.create({
                    data: {
                      userId: user.id,
                      organizationId: newOrgId,
                      role: 'member', // Default role, you might want to make some users admins
                      createdAt: convertDate(oldUser.createdAt)
                    }
                  })
                  console.log(`    ✅ Added as member to organization: ${newOrgId}`)

                  // Create UserOrganizationCredits record
                  await prisma.userOrganizationCredits.create({
                    data: {
                      userId: user.id,
                      organizationId: newOrgId,
                      creditsTotal: 10, // Default free credits
                      creditsUsed: 0,
                      creditsResetAt: new Date(),
                      creditsPurchased: 0,
                      createdAt: new Date(),
                      updatedAt: new Date()
                    }
                  })
                  console.log(`    ✅ Created credits record for organization: ${newOrgId}`)
                  
                  membershipCount++
                } catch (error) {
                  console.error(`    ❌ Failed to create membership/credits for user ${user.email} in org ${newOrgId}:`, error)
                }
              } else {
                console.log(`    ⚠️  Organization not found for old team ID: ${teamMembership.teamId.$oid}`)
              }
            } else {
              console.log(`    ⏭️  Skipping ${teamMembership.status} membership for team: ${teamMembership.teamId.$oid}`)
            }
          }
          console.log(`  ✅ Created ${membershipCount} memberships with credits`)
        } else {
          console.log(`  ⚠️  User has no team memberships`)
        }

        // Create a session if there's token data (optional)
        if (oldUser.lastActive) {
          try {
            // Set active organization if teamActive exists
            let activeOrgId = null
            if (oldUser.teamActive) {
              activeOrgId = organizationIdMap.get(oldUser.teamActive.$oid) || null
            }

            await prisma.session.create({
              data: {
                userId: user.id,
                token: `migrated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                activeOrganizationId: activeOrgId,
                createdAt: convertDate(oldUser.lastActive),
                updatedAt: new Date()
              }
            })
          } catch (error) {
            console.error(`Failed to create session for user ${user.email}:`, error)
          }
        }

      } catch (error) {
        console.error(`Failed to migrate user ${oldUser.email}:`, error)
      }
    }

    console.log('\n🎉 Migration completed!')
    console.log(`✅ Migrated ${userIdMap.size} users`)
    console.log(`✅ Created ${organizationIdMap.size} organizations`)

    // Verify the migration results
    console.log('\n📊 Verifying migration results...')
    try {
      const userCount = await prisma.user.count()
      const accountCount = await prisma.account.count()
      const memberCount = await prisma.member.count()
      const creditsCount = await prisma.userOrganizationCredits.count()
      const orgCount = await prisma.organization.count()

      console.log(`📈 Database totals:`)
      console.log(`   Users: ${userCount}`)
      console.log(`   Accounts: ${accountCount}`)
      console.log(`   Organization memberships: ${memberCount}`)
      console.log(`   Credits records: ${creditsCount}`)
      console.log(`   Organizations: ${orgCount}`)

      // Check for users without accounts
      const usersWithoutAccounts = await prisma.user.count({
        where: {
          accounts: {
            none: {}
          }
        }
      })

      // Check for users without memberships
      const usersWithoutMemberships = await prisma.user.count({
        where: {
          memberships: {
            none: {}
          }
        }
      })

      // Check for users without credits
      const usersWithoutCredits = await prisma.user.count({
        where: {
          userOrgCredits: {
            none: {}
          }
        }
      })

      console.log(`\n⚠️  Issues to review:`)
      console.log(`   Users without accounts: ${usersWithoutAccounts}`)
      console.log(`   Users without memberships: ${usersWithoutMemberships}`)
      console.log(`   Users without credits: ${usersWithoutCredits}`)

      if (usersWithoutAccounts === 0 && usersWithoutMemberships === 0 && usersWithoutCredits === 0) {
        console.log(`\n✅ All users have required records: accounts, memberships, and credits!`)
      } else {
        console.log(`\n❌ Some users are missing required records. Review the issues above.`)
      }

    } catch (error) {
      console.error('❌ Error verifying migration results:', error)
    }

  } catch (error) {
    console.error('Migration failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the migration
if (require.main === module) {
  migrateUsers()
}

export { migrateUsers } 