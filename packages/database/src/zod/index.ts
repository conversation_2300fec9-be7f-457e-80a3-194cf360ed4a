import { z } from 'zod';
import { Prisma } from '@prisma/client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;


/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const CreditPurchaseScalarFieldEnumSchema = z.enum(['id','userId','organizationId','purchaseId','packageId','credits','price','stripePaymentIntentId','status','createdAt','updatedAt']);

export const AiUsageScalarFieldEnumSchema = z.enum(['id','userId','organizationId','chatId','feature','activity','creditsUsed','promptTokens','completionTokens','toolCalls','model','success','error','metadata','createdAt']);

export const ChatScalarFieldEnumSchema = z.enum(['id','title','userId','organizationId','isPublic','status','lastMessageTimestamp','branchId','createdAt','updatedAt']);

export const MessageScalarFieldEnumSchema = z.enum(['id','prompt','userId','chatId','modelId','uiMessages','responseStreamId','tool','error','content','searchContent','promptTokens','completionTokens','totalTokens','creditsSpent','createdAt','updatedAt']);

export const CompanyScalarFieldEnumSchema = z.enum(['id','name','website','industry','size','description','logo','address','phone','email','organizationId','createdBy','updatedBy','isDeleted','deletedAt','deletedBy','createdAt','updatedAt']);

export const ContactScalarFieldEnumSchema = z.enum(['id','mongoId','apolloId','firstName','lastName','image','title','persona','status','address','phone','email','website','social','source','stage','birthday','age','spouseName','summary','companyId','buyerNeeds','generatedSummary','organizationId','createdBy','updatedBy','lastViewedAt','lastViewedBy','isDeleted','deletedAt','deletedBy','createdAt','updatedAt']);

export const RelatedContactScalarFieldEnumSchema = z.enum(['id','firstName','lastName','label','address','phone','email','contactId','organizationId','createdBy','createdAt','updatedAt']);

export const LinkedContactScalarFieldEnumSchema = z.enum(['id','mongoId','contact1Id','contact1Relation','contact2Id','contact2Relation','organizationId','createdBy','updatedBy','createdAt','updatedAt']);

export const CustomFieldDefinitionScalarFieldEnumSchema = z.enum(['id','name','label','icon','type','objectType','organizationId','isRequired','isSystem','options','position','isActive','createdAt','updatedAt']);

export const CustomObjectScalarFieldEnumSchema = z.enum(['id','objectType','organizationId','createdBy','title','description','status','tags','customFields','lastViewedAt','lastViewedBy','isDeleted','deletedAt','deletedBy','createdAt','updatedAt']);

export const FavoriteScalarFieldEnumSchema = z.enum(['id','objectId','objectType','userId','organizationId','folderId','position','createdAt','updatedAt']);

export const FavoriteFolderScalarFieldEnumSchema = z.enum(['id','name','organizationId','userId','isOpen','position','createdAt','updatedAt']);

export const FeedbackScalarFieldEnumSchema = z.enum(['id','userId','feedback','createdAt','updatedAt']);

export const ListScalarFieldEnumSchema = z.enum(['id','name','description','objectType','filters','isStatic','organizationId','createdBy','createdAt','updatedAt']);

export const ListPermissionScalarFieldEnumSchema = z.enum(['id','listId','userId','role','createdAt','updatedAt']);

export const ListItemScalarFieldEnumSchema = z.enum(['id','listId','objectId','objectType','position','addedBy','createdAt']);

export const ModelScalarFieldEnumSchema = z.enum(['id','name','model','provider','searchField','icon','capabilities','description','isPremium','isDisabled','cost','createdAt','updatedAt']);

export const NoteScalarFieldEnumSchema = z.enum(['id','orgId','userId','title','isArchived','parentDocument','content','coverImage','icon','objectId','objectType','isPublished','createdAt','updatedAt','deletedAt','deletedBy','isDeleted']);

export const NotificationScalarFieldEnumSchema = z.enum(['id','userId','organizationId','type','title','body','data','read','archived','createdAt','updatedAt']);

export const NotificationSettingsScalarFieldEnumSchema = z.enum(['id','userId','emailMentions','emailComments','emailActivities','pushMentions','pushComments','pushActivities','createdAt','updatedAt']);

export const ObjectViewScalarFieldEnumSchema = z.enum(['id','name','objectType','organizationId','columnDefs','cardRowFields','showAttributeLabels','filters','filterCondition','viewType','statusAttribute','kanbanConfig','sortBy','sortDirection','mapConfig','createdBy','isDefault','isPublic','createdAt','updatedAt']);

export const ObjectTagScalarFieldEnumSchema = z.enum(['id','tagId','objectId','objectType','addedBy','createdAt']);

export const ObjectStatusHistoryScalarFieldEnumSchema = z.enum(['id','objectId','objectType','statusField','fromStatus','toStatus','userId','organizationId','createdAt','updatedAt']);

export const InvitationScalarFieldEnumSchema = z.enum(['id','organizationId','email','role','status','expiresAt','inviterId']);

export const MemberScalarFieldEnumSchema = z.enum(['id','organizationId','userId','role','createdAt']);

export const OrganizationScalarFieldEnumSchema = z.enum(['id','name','slug','logo','createdAt','metadata','paymentsCustomerId','emailWatermarkEnabled']);

export const PurchaseScalarFieldEnumSchema = z.enum(['id','organizationId','userId','type','customerId','subscriptionId','productId','status','createdAt','updatedAt']);

export const PinScalarFieldEnumSchema = z.enum(['id','userId','organizationId','objectType','objectId','name','image','position','createdAt','updatedAt']);

export const PropertyScalarFieldEnumSchema = z.enum(['id','mongoId','apolloId','name','recordType','image','organizationId','propertyType','propertySubType','market','subMarket','listingId','status','createdBy','updatedBy','lastViewedAt','lastViewedBy','isDeleted','deletedAt','deletedBy','createdAt','updatedAt']);

export const LinkedPropertyScalarFieldEnumSchema = z.enum(['id','mongoId','relation','contactId','propertyId','organizationId','createdAt','updatedAt','createdBy','updatedBy']);

export const PropertyLocationScalarFieldEnumSchema = z.enum(['id','propertyId','address','location','website','neighborhood','county','subdivision','lotNumber','parcelNumber','zoning','createdAt','updatedAt']);

export const PropertyPhysicalDetailsScalarFieldEnumSchema = z.enum(['id','propertyId','yearBuilt','squareFootage','units','floors','structures','bedrooms','bathrooms','roomsCount','buildingSquareFeet','garageSquareFeet','livingSquareFeet','lotSquareFeet','lotSize','lotType','lotAcres','construction','primaryUse','propertyUse','class','parking','parkingSpaces','garageType','heatingType','meterType','legalDescription','createdAt','updatedAt']);

export const PropertyFinancialsScalarFieldEnumSchema = z.enum(['id','propertyId','price','estimatedValue','pricePerSquareFoot','equity','equityPercent','estimatedEquity','saleDate','salePrice','lastSalePrice','lastSaleDate','landValue','buildingValue','cap','exchange','exchangeId','taxInfo','createdAt','updatedAt']);

export const PropertyFlagsScalarFieldEnumSchema = z.enum(['id','propertyId','absenteeOwner','inStateAbsenteeOwner','outOfStateAbsenteeOwner','ownerOccupied','corporateOwned','vacant','mobileHome','carport','auction','cashBuyer','investorBuyer','freeClear','highEquity','privateLender','deedInLieu','quitClaim','sheriffsDeed','warrantyDeed','inherited','spousalDeath','lien','taxLien','preForeclosure','trusteeSale','floodZone','createdAt','updatedAt']);

export const PropertyMLSScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','mlsActive','mlsCancelled','mlsFailed','mlsHasPhotos','mlsPending','mlsSold','mlsDaysOnMarket','mlsListingPrice','mlsListingPricePerSquareFoot','mlsSoldPrice','mlsStatus','mlsType','mlsListingDate','createdAt','updatedAt']);

export const PropertyLegalScalarFieldEnumSchema = z.enum(['id','propertyId','floodZoneDescription','floodZoneType','noticeType','reaId','lastUpdateDate','createdAt','updatedAt']);

export const PropertyUnitMixScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','name','units','minSquareFootage','maxSquareFootage','minPrice','maxPrice','minRent','maxRent','createdAt','updatedAt']);

export const PropertySaleHistoryScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','seller','buyer','saleDate','salePrice','askingPrice','transactionType','pricePerSquareFoot','pricePerUnit','transferredOwnershipPercentage','capRate','grmRate','createdAt','updatedAt']);

export const PropertyMortgageScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','amount','assumable','deedType','documentDate','documentNumber','granteeName','interestRate','interestRateType','lenderCode','lenderName','lenderType','loanType','loanTypeCode','maturityDate','mortgageId','open','position','recordingDate','seqNo','term','termType','transactionType','createdAt','updatedAt']);

export const PropertyDemographicsScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','fmrEfficiency','fmrFourBedroom','fmrOneBedroom','fmrThreeBedroom','fmrTwoBedroom','fmrYear','hudAreaCode','hudAreaName','medianIncome','suggestedRent','createdAt','updatedAt']);

export const PropertyForeclosureInfoScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','foreclosureId','originalLoanAmount','estimatedBankValue','defaultAmount','recordingDate','openingBid','auctionDate','auctionTime','auctionStreetAddress','documentType','trusteeSaleNumber','typeName','active','lenderName','lenderPhone','noticeType','seqNo','trusteeAddress','trusteeName','trusteePhone','judgmentDate','judgmentAmount','createdAt','updatedAt']);

export const PropertyMlsHistoryScalarFieldEnumSchema = z.enum(['id','propertyId','organizationId','mlsId','type','price','beds','baths','daysOnMarket','agentName','agentOffice','agentPhone','agentEmail','status','statusDate','lastStatusDate','createdAt','updatedAt']);

export const TagScalarFieldEnumSchema = z.enum(['id','name','color','organizationId','objectType','createdBy','createdAt','updatedAt']);

export const TagPermissionScalarFieldEnumSchema = z.enum(['id','tagId','userId','role','createdAt','updatedAt']);

export const TaskScalarFieldEnumSchema = z.enum(['id','title','description','createdById','dueDate','status','assigneeId','relatedObjectId','relatedObjectType','organizationId','priority','position','createdAt','updatedAt']);

export const ColumnPreferenceScalarFieldEnumSchema = z.enum(['id','organizationId','column','trackTimeInStatus','showConfetti','hidden','targetTimeInStatus','createdAt','updatedAt']);

export const AccountScalarFieldEnumSchema = z.enum(['id','accountId','providerId','userId','accessToken','refreshToken','idToken','expiresAt','password','accessTokenExpiresAt','refreshTokenExpiresAt','scope','createdAt','updatedAt']);

export const UserOrganizationCreditsScalarFieldEnumSchema = z.enum(['id','userId','organizationId','creditsTotal','creditsUsed','creditsResetAt','creditsPurchased','createdAt','updatedAt']);

export const PasskeyScalarFieldEnumSchema = z.enum(['id','name','publicKey','userId','credentialID','counter','deviceType','backedUp','transports','createdAt']);

export const UserScalarFieldEnumSchema = z.enum(['id','name','email','emailVerified','image','createdAt','updatedAt','username','role','banned','banReason','banExpires','onboardingComplete','paymentsCustomerId','locale','modelId','preferences','appearance']);

export const UserViewPreferenceScalarFieldEnumSchema = z.enum(['id','userId','organizationId','objectType','viewId','createdAt','updatedAt']);

export const SessionScalarFieldEnumSchema = z.enum(['id','expiresAt','ipAddress','userAgent','userId','impersonatedBy','activeOrganizationId','token','createdAt','updatedAt']);

export const VerificationScalarFieldEnumSchema = z.enum(['id','identifier','value','expiresAt','createdAt','updatedAt']);

export const ForwardedEmailScalarFieldEnumSchema = z.enum(['id','organizationId','messageId','from','to','subject','body','attachments','headers','participants','processedAt','linkedRecords','forwardedBy','sharingLevel','isBlocked','createdAt','updatedAt']);

export const ForwardingEmailConfigScalarFieldEnumSchema = z.enum(['id','organizationId','address','isActive','defaultSharingLevel','individualSharing','blockedEmails','blockedDomains','autoCreateContacts','autoCreateCompanies','createdAt','updatedAt']);

export const ActivityScalarFieldEnumSchema = z.enum(['id','organizationId','userId','recordId','recordType','type','message','resolved','resolvedBy','phone','result','system','edited','editedAt','mentionedUsers','createdAt','updatedAt']);

export const ActivityReplyScalarFieldEnumSchema = z.enum(['id','activityId','userId','message','edited','editedAt','mentionedUsers','createdAt','updatedAt']);

export const EmailActivityScalarFieldEnumSchema = z.enum(['id','organizationId','userId','sourceType','sourceId','messageId','threadId','inReplyTo','references','from','to','cc','bcc','subject','body','bodyPlain','attachments','headers','direction','timestamp','isRead','isImportant','processedAt','processingNotes','createdAt','updatedAt']);

export const ActivityReactionScalarFieldEnumSchema = z.enum(['id','activityId','userId','emoji','createdAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const ChatStatusSchema = z.enum(['ready','submitted','streaming']);

export type ChatStatusType = `${z.infer<typeof ChatStatusSchema>}`

export const ToolSchema = z.enum(['searchWeb','searchContacts','searchProperties','searchCompanies','getTaskSummary','getAnalytics','getRecentActivity','createTask']);

export type ToolType = `${z.infer<typeof ToolSchema>}`

export const ModelCapabilitySchema = z.enum(['thinking','vision','tools']);

export type ModelCapabilityType = `${z.infer<typeof ModelCapabilitySchema>}`

export const ModelProviderSchema = z.enum(['azure','openrouter']);

export type ModelProviderType = `${z.infer<typeof ModelProviderSchema>}`

export const ObjectViewTypeSchema = z.enum(['table','kanban','map']);

export type ObjectViewTypeType = `${z.infer<typeof ObjectViewTypeSchema>}`

export const PurchaseTypeSchema = z.enum(['subscription','one_time']);

export type PurchaseTypeType = `${z.infer<typeof PurchaseTypeSchema>}`

export const TagRoleSchema = z.enum(['viewer','editor','admin']);

export type TagRoleType = `${z.infer<typeof TagRoleSchema>}`

export const ListRoleSchema = z.enum(['viewer','editor','admin']);

export type ListRoleType = `${z.infer<typeof ListRoleSchema>}`

export const TaskStatusSchema = z.enum(['backlog','todo','in_progress','review','done']);

export type TaskStatusType = `${z.infer<typeof TaskStatusSchema>}`

export const TaskPrioritySchema = z.enum(['no_priority','urgent','high','medium','low']);

export type TaskPriorityType = `${z.infer<typeof TaskPrioritySchema>}`

export const EmailSourceTypeSchema = z.enum(['connected_account','forwarded','manual']);

export type EmailSourceTypeType = `${z.infer<typeof EmailSourceTypeSchema>}`

export const EmailDirectionSchema = z.enum(['inbound','outbound']);

export type EmailDirectionType = `${z.infer<typeof EmailDirectionSchema>}`

export const ActivityTypeSchema = z.enum(['note','call','email','meeting','task','system']);

export type ActivityTypeType = `${z.infer<typeof ActivityTypeSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// CREDIT PURCHASE SCHEMA
/////////////////////////////////////////

export const CreditPurchaseSchema = z.object({
  id: z.string().cuid(),
  userId: z.string().nullable(),
  organizationId: z.string().nullable(),
  purchaseId: z.string().nullable(),
  packageId: z.string(),
  credits: z.number().int(),
  price: z.number(),
  stripePaymentIntentId: z.string().nullable(),
  status: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type CreditPurchase = z.infer<typeof CreditPurchaseSchema>

/////////////////////////////////////////
// AI USAGE SCHEMA
/////////////////////////////////////////

export const AiUsageSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  organizationId: z.string().nullable(),
  chatId: z.string().nullable(),
  feature: z.string(),
  activity: z.string().nullable(),
  creditsUsed: z.number().int(),
  promptTokens: z.number().int().nullable(),
  completionTokens: z.number().int().nullable(),
  toolCalls: z.number().int().nullable(),
  model: z.string().nullable(),
  success: z.boolean(),
  error: z.string().nullable(),
  metadata: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
})

export type AiUsage = z.infer<typeof AiUsageSchema>

/////////////////////////////////////////
// CHAT SCHEMA
/////////////////////////////////////////

export const ChatSchema = z.object({
  status: ChatStatusSchema,
  id: z.string(),
  title: z.string(),
  userId: z.string(),
  organizationId: z.string().nullable(),
  isPublic: z.boolean(),
  lastMessageTimestamp: z.coerce.date(),
  branchId: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Chat = z.infer<typeof ChatSchema>

/////////////////////////////////////////
// MESSAGE SCHEMA
/////////////////////////////////////////

export const MessageSchema = z.object({
  tool: ToolSchema.nullable(),
  id: z.string(),
  prompt: z.string(),
  userId: z.string(),
  chatId: z.string(),
  modelId: z.string(),
  uiMessages: z.string().nullable(),
  responseStreamId: z.string(),
  error: JsonValueSchema.nullable(),
  content: z.string().nullable(),
  searchContent: z.string().nullable(),
  promptTokens: z.number().int().nullable(),
  completionTokens: z.number().int().nullable(),
  totalTokens: z.number().int().nullable(),
  creditsSpent: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Message = z.infer<typeof MessageSchema>

/////////////////////////////////////////
// COMPANY SCHEMA
/////////////////////////////////////////

export const CompanySchema = z.object({
  id: z.string(),
  name: z.string(),
  website: z.string().nullable(),
  industry: z.string().nullable(),
  size: z.string().nullable(),
  description: z.string().nullable(),
  logo: z.string().nullable(),
  address: JsonValueSchema.nullable(),
  phone: z.string().nullable(),
  email: z.string().nullable(),
  organizationId: z.string(),
  createdBy: z.string(),
  updatedBy: z.string().nullable(),
  isDeleted: z.boolean(),
  deletedAt: z.coerce.date().nullable(),
  deletedBy: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Company = z.infer<typeof CompanySchema>

/////////////////////////////////////////
// CONTACT SCHEMA
/////////////////////////////////////////

export const ContactSchema = z.object({
  id: z.string(),
  mongoId: z.string().nullable(),
  apolloId: z.string().nullable(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  image: z.string().nullable(),
  title: z.string().nullable(),
  persona: z.string().nullable(),
  status: z.string().nullable(),
  address: JsonValueSchema.nullable(),
  phone: JsonValueSchema.nullable(),
  email: JsonValueSchema.nullable(),
  website: z.string().nullable(),
  social: JsonValueSchema.nullable(),
  source: z.string().nullable(),
  stage: z.string().nullable(),
  birthday: z.string().nullable(),
  age: z.number().nullable(),
  spouseName: z.string().nullable(),
  summary: z.string().nullable(),
  companyId: z.string().nullable(),
  buyerNeeds: JsonValueSchema.nullable(),
  generatedSummary: z.string().nullable(),
  organizationId: z.string(),
  createdBy: z.string(),
  updatedBy: z.string().nullable(),
  lastViewedAt: z.coerce.date().nullable(),
  lastViewedBy: z.string().nullable(),
  isDeleted: z.boolean(),
  deletedAt: z.coerce.date().nullable(),
  deletedBy: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Contact = z.infer<typeof ContactSchema>

/////////////////////////////////////////
// RELATED CONTACT SCHEMA
/////////////////////////////////////////

export const RelatedContactSchema = z.object({
  id: z.string(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  label: z.string().nullable(),
  address: JsonValueSchema.nullable(),
  phone: JsonValueSchema.nullable(),
  email: JsonValueSchema.nullable(),
  contactId: z.string(),
  organizationId: z.string(),
  createdBy: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type RelatedContact = z.infer<typeof RelatedContactSchema>

/////////////////////////////////////////
// LINKED CONTACT SCHEMA
/////////////////////////////////////////

export const LinkedContactSchema = z.object({
  id: z.string(),
  mongoId: z.string().nullable(),
  contact1Id: z.string(),
  contact1Relation: z.string(),
  contact2Id: z.string(),
  contact2Relation: z.string(),
  organizationId: z.string(),
  createdBy: z.string(),
  updatedBy: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type LinkedContact = z.infer<typeof LinkedContactSchema>

/////////////////////////////////////////
// CUSTOM FIELD DEFINITION SCHEMA
/////////////////////////////////////////

export const CustomFieldDefinitionSchema = z.object({
  id: z.string(),
  name: z.string(),
  label: z.string(),
  icon: z.string().nullable(),
  type: z.string(),
  objectType: z.string(),
  organizationId: z.string(),
  isRequired: z.boolean(),
  isSystem: z.boolean(),
  options: JsonValueSchema.nullable(),
  position: z.number().int(),
  isActive: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type CustomFieldDefinition = z.infer<typeof CustomFieldDefinitionSchema>

/////////////////////////////////////////
// CUSTOM OBJECT SCHEMA
/////////////////////////////////////////

export const CustomObjectSchema = z.object({
  id: z.string(),
  objectType: z.string(),
  organizationId: z.string(),
  createdBy: z.string(),
  title: z.string().nullable(),
  description: z.string().nullable(),
  status: z.string().nullable(),
  tags: z.string().array(),
  customFields: JsonValueSchema.nullable(),
  lastViewedAt: z.coerce.date().nullable(),
  lastViewedBy: z.string().nullable(),
  isDeleted: z.boolean(),
  deletedAt: z.coerce.date().nullable(),
  deletedBy: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type CustomObject = z.infer<typeof CustomObjectSchema>

/////////////////////////////////////////
// FAVORITE SCHEMA
/////////////////////////////////////////

export const FavoriteSchema = z.object({
  id: z.string(),
  objectId: z.string(),
  objectType: z.string(),
  userId: z.string(),
  organizationId: z.string(),
  folderId: z.string().nullable(),
  position: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Favorite = z.infer<typeof FavoriteSchema>

/////////////////////////////////////////
// FAVORITE FOLDER SCHEMA
/////////////////////////////////////////

export const FavoriteFolderSchema = z.object({
  id: z.string(),
  name: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  isOpen: z.boolean().nullable(),
  position: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type FavoriteFolder = z.infer<typeof FavoriteFolderSchema>

/////////////////////////////////////////
// FEEDBACK SCHEMA
/////////////////////////////////////////

export const FeedbackSchema = z.object({
  id: z.string(),
  userId: z.string().nullable(),
  feedback: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Feedback = z.infer<typeof FeedbackSchema>

/////////////////////////////////////////
// LIST SCHEMA
/////////////////////////////////////////

export const ListSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  objectType: z.string(),
  filters: JsonValueSchema.nullable(),
  isStatic: z.boolean(),
  organizationId: z.string(),
  createdBy: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type List = z.infer<typeof ListSchema>

/////////////////////////////////////////
// LIST PERMISSION SCHEMA
/////////////////////////////////////////

export const ListPermissionSchema = z.object({
  role: ListRoleSchema,
  id: z.string(),
  listId: z.string(),
  userId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ListPermission = z.infer<typeof ListPermissionSchema>

/////////////////////////////////////////
// LIST ITEM SCHEMA
/////////////////////////////////////////

export const ListItemSchema = z.object({
  id: z.string(),
  listId: z.string(),
  objectId: z.string(),
  objectType: z.string(),
  position: z.number().nullable(),
  addedBy: z.string(),
  createdAt: z.coerce.date(),
})

export type ListItem = z.infer<typeof ListItemSchema>

/////////////////////////////////////////
// MODEL SCHEMA
/////////////////////////////////////////

export const ModelSchema = z.object({
  provider: ModelProviderSchema,
  capabilities: ModelCapabilitySchema.array(),
  id: z.string(),
  name: z.string(),
  model: z.string(),
  searchField: z.string(),
  icon: z.string(),
  description: z.string(),
  isPremium: z.boolean(),
  isDisabled: z.boolean(),
  cost: z.number().int(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Model = z.infer<typeof ModelSchema>

/////////////////////////////////////////
// NOTE SCHEMA
/////////////////////////////////////////

export const NoteSchema = z.object({
  id: z.string(),
  orgId: z.string(),
  userId: z.string(),
  title: z.string(),
  isArchived: z.boolean(),
  parentDocument: z.string().nullable(),
  content: z.string().nullable(),
  coverImage: z.string().nullable(),
  icon: z.string().nullable(),
  objectId: z.string().nullable(),
  objectType: z.string().nullable(),
  isPublished: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  deletedAt: z.coerce.date().nullable(),
  deletedBy: z.string().nullable(),
  isDeleted: z.boolean(),
})

export type Note = z.infer<typeof NoteSchema>

/////////////////////////////////////////
// NOTIFICATION SCHEMA
/////////////////////////////////////////

export const NotificationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  organizationId: z.string().nullable(),
  type: z.string(),
  title: z.string(),
  body: z.string().nullable(),
  data: JsonValueSchema.nullable(),
  read: z.boolean(),
  archived: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Notification = z.infer<typeof NotificationSchema>

/////////////////////////////////////////
// NOTIFICATION SETTINGS SCHEMA
/////////////////////////////////////////

export const NotificationSettingsSchema = z.object({
  id: z.string(),
  userId: z.string(),
  emailMentions: z.boolean(),
  emailComments: z.boolean(),
  emailActivities: z.boolean(),
  pushMentions: z.boolean(),
  pushComments: z.boolean(),
  pushActivities: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type NotificationSettings = z.infer<typeof NotificationSettingsSchema>

/////////////////////////////////////////
// OBJECT VIEW SCHEMA
/////////////////////////////////////////

export const ObjectViewSchema = z.object({
  viewType: ObjectViewTypeSchema,
  id: z.string(),
  name: z.string(),
  objectType: z.string(),
  organizationId: z.string(),
  columnDefs: JsonValueSchema,
  cardRowFields: JsonValueSchema.nullable(),
  showAttributeLabels: z.boolean().nullable(),
  filters: JsonValueSchema.nullable(),
  filterCondition: z.string().nullable(),
  statusAttribute: z.string().nullable(),
  kanbanConfig: JsonValueSchema.nullable(),
  sortBy: z.string().nullable(),
  sortDirection: z.string().nullable(),
  mapConfig: JsonValueSchema.nullable(),
  createdBy: z.string(),
  isDefault: z.boolean(),
  isPublic: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ObjectView = z.infer<typeof ObjectViewSchema>

/////////////////////////////////////////
// OBJECT TAG SCHEMA
/////////////////////////////////////////

export const ObjectTagSchema = z.object({
  id: z.string(),
  tagId: z.string(),
  objectId: z.string(),
  objectType: z.string(),
  addedBy: z.string(),
  createdAt: z.coerce.date(),
})

export type ObjectTag = z.infer<typeof ObjectTagSchema>

/////////////////////////////////////////
// OBJECT STATUS HISTORY SCHEMA
/////////////////////////////////////////

export const ObjectStatusHistorySchema = z.object({
  id: z.string(),
  objectId: z.string(),
  objectType: z.string(),
  statusField: z.string(),
  fromStatus: z.string().nullable(),
  toStatus: z.string(),
  userId: z.string(),
  organizationId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ObjectStatusHistory = z.infer<typeof ObjectStatusHistorySchema>

/////////////////////////////////////////
// INVITATION SCHEMA
/////////////////////////////////////////

export const InvitationSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  email: z.string(),
  role: z.string().nullable(),
  status: z.string(),
  expiresAt: z.coerce.date(),
  inviterId: z.string(),
})

export type Invitation = z.infer<typeof InvitationSchema>

/////////////////////////////////////////
// MEMBER SCHEMA
/////////////////////////////////////////

export const MemberSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  role: z.string(),
  createdAt: z.coerce.date(),
})

export type Member = z.infer<typeof MemberSchema>

/////////////////////////////////////////
// ORGANIZATION SCHEMA
/////////////////////////////////////////

export const OrganizationSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string().nullable(),
  logo: z.string().nullable(),
  createdAt: z.coerce.date(),
  metadata: z.string().nullable(),
  paymentsCustomerId: z.string().nullable(),
  emailWatermarkEnabled: z.boolean(),
})

export type Organization = z.infer<typeof OrganizationSchema>

/////////////////////////////////////////
// PURCHASE SCHEMA
/////////////////////////////////////////

export const PurchaseSchema = z.object({
  type: PurchaseTypeSchema,
  id: z.string(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  customerId: z.string(),
  subscriptionId: z.string().nullable(),
  productId: z.string(),
  status: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Purchase = z.infer<typeof PurchaseSchema>

/////////////////////////////////////////
// PIN SCHEMA
/////////////////////////////////////////

export const PinSchema = z.object({
  id: z.string(),
  userId: z.string(),
  organizationId: z.string(),
  objectType: z.string(),
  objectId: z.string(),
  name: z.string(),
  image: z.string().nullable(),
  position: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Pin = z.infer<typeof PinSchema>

/////////////////////////////////////////
// PROPERTY SCHEMA
/////////////////////////////////////////

export const PropertySchema = z.object({
  id: z.string(),
  mongoId: z.string().nullable(),
  apolloId: z.string().nullable(),
  name: z.string(),
  recordType: z.string(),
  image: z.string().nullable(),
  organizationId: z.string(),
  propertyType: z.string().nullable(),
  propertySubType: z.string().nullable(),
  market: z.string().nullable(),
  subMarket: z.string().nullable(),
  listingId: z.string().nullable(),
  status: z.string().nullable(),
  createdBy: z.string(),
  updatedBy: z.string().nullable(),
  lastViewedAt: z.coerce.date().nullable(),
  lastViewedBy: z.string().nullable(),
  isDeleted: z.boolean(),
  deletedAt: z.coerce.date().nullable(),
  deletedBy: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Property = z.infer<typeof PropertySchema>

/////////////////////////////////////////
// LINKED PROPERTY SCHEMA
/////////////////////////////////////////

export const LinkedPropertySchema = z.object({
  id: z.string(),
  mongoId: z.string().nullable(),
  relation: z.string(),
  contactId: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  createdBy: z.string().nullable(),
  updatedBy: z.string().nullable(),
})

export type LinkedProperty = z.infer<typeof LinkedPropertySchema>

/////////////////////////////////////////
// PROPERTY LOCATION SCHEMA
/////////////////////////////////////////

export const PropertyLocationSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  address: JsonValueSchema.nullable(),
  location: JsonValueSchema.nullable(),
  website: z.string().nullable(),
  neighborhood: JsonValueSchema.nullable(),
  county: z.string().nullable(),
  subdivision: z.string().nullable(),
  lotNumber: z.string().nullable(),
  parcelNumber: z.string().nullable(),
  zoning: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyLocation = z.infer<typeof PropertyLocationSchema>

/////////////////////////////////////////
// PROPERTY PHYSICAL DETAILS SCHEMA
/////////////////////////////////////////

export const PropertyPhysicalDetailsSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  yearBuilt: z.number().int().nullable(),
  squareFootage: z.number().int().nullable(),
  units: z.number().int().nullable(),
  floors: z.number().int().nullable(),
  structures: z.number().int().nullable(),
  bedrooms: z.number().int().nullable(),
  bathrooms: z.number().int().nullable(),
  roomsCount: z.number().int().nullable(),
  buildingSquareFeet: z.number().int().nullable(),
  garageSquareFeet: z.number().int().nullable(),
  livingSquareFeet: z.number().int().nullable(),
  lotSquareFeet: z.number().int().nullable(),
  lotSize: z.number().nullable(),
  lotType: z.string().nullable(),
  lotAcres: z.number().nullable(),
  construction: z.string().nullable(),
  primaryUse: z.string().nullable(),
  propertyUse: z.string().nullable(),
  class: z.string().nullable(),
  parking: z.string().nullable(),
  parkingSpaces: z.number().int().nullable(),
  garageType: z.string().nullable(),
  heatingType: z.string().nullable(),
  meterType: z.string().nullable(),
  legalDescription: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyPhysicalDetails = z.infer<typeof PropertyPhysicalDetailsSchema>

/////////////////////////////////////////
// PROPERTY FINANCIALS SCHEMA
/////////////////////////////////////////

export const PropertyFinancialsSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  price: z.number().nullable(),
  estimatedValue: z.number().nullable(),
  pricePerSquareFoot: z.number().nullable(),
  equity: z.number().nullable(),
  equityPercent: z.number().nullable(),
  estimatedEquity: z.number().nullable(),
  saleDate: z.coerce.date().nullable(),
  salePrice: z.number().nullable(),
  lastSalePrice: z.number().nullable(),
  lastSaleDate: z.coerce.date().nullable(),
  landValue: z.number().nullable(),
  buildingValue: z.number().nullable(),
  cap: z.number().nullable(),
  exchange: z.boolean().nullable(),
  exchangeId: z.string().nullable(),
  taxInfo: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyFinancials = z.infer<typeof PropertyFinancialsSchema>

/////////////////////////////////////////
// PROPERTY FLAGS SCHEMA
/////////////////////////////////////////

export const PropertyFlagsSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  absenteeOwner: z.boolean(),
  inStateAbsenteeOwner: z.boolean(),
  outOfStateAbsenteeOwner: z.boolean(),
  ownerOccupied: z.boolean(),
  corporateOwned: z.boolean(),
  vacant: z.boolean(),
  mobileHome: z.boolean(),
  carport: z.boolean(),
  auction: z.boolean(),
  cashBuyer: z.boolean(),
  investorBuyer: z.boolean(),
  freeClear: z.boolean(),
  highEquity: z.boolean(),
  privateLender: z.boolean(),
  deedInLieu: z.boolean(),
  quitClaim: z.boolean(),
  sheriffsDeed: z.boolean(),
  warrantyDeed: z.boolean(),
  inherited: z.boolean(),
  spousalDeath: z.boolean(),
  lien: z.boolean(),
  taxLien: z.boolean(),
  preForeclosure: z.boolean(),
  trusteeSale: z.boolean(),
  floodZone: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyFlags = z.infer<typeof PropertyFlagsSchema>

/////////////////////////////////////////
// PROPERTY MLS SCHEMA
/////////////////////////////////////////

export const PropertyMLSSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  mlsActive: z.boolean(),
  mlsCancelled: z.boolean(),
  mlsFailed: z.boolean(),
  mlsHasPhotos: z.boolean(),
  mlsPending: z.boolean(),
  mlsSold: z.boolean(),
  mlsDaysOnMarket: z.number().int().nullable(),
  mlsListingPrice: z.number().nullable(),
  mlsListingPricePerSquareFoot: z.number().nullable(),
  mlsSoldPrice: z.number().nullable(),
  mlsStatus: z.string().nullable(),
  mlsType: z.string().nullable(),
  mlsListingDate: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyMLS = z.infer<typeof PropertyMLSSchema>

/////////////////////////////////////////
// PROPERTY LEGAL SCHEMA
/////////////////////////////////////////

export const PropertyLegalSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  floodZoneDescription: z.string().nullable(),
  floodZoneType: z.string().nullable(),
  noticeType: z.string().nullable(),
  reaId: z.string().nullable(),
  lastUpdateDate: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyLegal = z.infer<typeof PropertyLegalSchema>

/////////////////////////////////////////
// PROPERTY UNIT MIX SCHEMA
/////////////////////////////////////////

export const PropertyUnitMixSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  name: z.string(),
  units: z.number().nullable(),
  minSquareFootage: z.number().nullable(),
  maxSquareFootage: z.number().nullable(),
  minPrice: z.number().nullable(),
  maxPrice: z.number().nullable(),
  minRent: z.number().nullable(),
  maxRent: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyUnitMix = z.infer<typeof PropertyUnitMixSchema>

/////////////////////////////////////////
// PROPERTY SALE HISTORY SCHEMA
/////////////////////////////////////////

export const PropertySaleHistorySchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  seller: z.string().nullable(),
  buyer: z.string().nullable(),
  saleDate: z.coerce.date().nullable(),
  salePrice: z.number().nullable(),
  askingPrice: z.number().nullable(),
  transactionType: z.string().nullable(),
  pricePerSquareFoot: z.number().nullable(),
  pricePerUnit: z.number().nullable(),
  transferredOwnershipPercentage: z.number().nullable(),
  capRate: z.number().nullable(),
  grmRate: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertySaleHistory = z.infer<typeof PropertySaleHistorySchema>

/////////////////////////////////////////
// PROPERTY MORTGAGE SCHEMA
/////////////////////////////////////////

export const PropertyMortgageSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  amount: z.number().nullable(),
  assumable: z.boolean().nullable(),
  deedType: JsonValueSchema.nullable(),
  documentDate: z.string().nullable(),
  documentNumber: JsonValueSchema.nullable(),
  granteeName: z.string().nullable(),
  interestRate: z.number().nullable(),
  interestRateType: z.string().nullable(),
  lenderCode: z.string().nullable(),
  lenderName: z.string().nullable(),
  lenderType: z.string().nullable(),
  loanType: z.string().nullable(),
  loanTypeCode: z.string().nullable(),
  maturityDate: z.string().nullable(),
  mortgageId: z.number().int().nullable(),
  open: z.boolean().nullable(),
  position: z.string().nullable(),
  recordingDate: z.string().nullable(),
  seqNo: z.number().int().nullable(),
  term: z.number().int().nullable(),
  termType: z.string().nullable(),
  transactionType: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyMortgage = z.infer<typeof PropertyMortgageSchema>

/////////////////////////////////////////
// PROPERTY DEMOGRAPHICS SCHEMA
/////////////////////////////////////////

export const PropertyDemographicsSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  fmrEfficiency: z.number().nullable(),
  fmrFourBedroom: z.number().nullable(),
  fmrOneBedroom: z.number().nullable(),
  fmrThreeBedroom: z.number().nullable(),
  fmrTwoBedroom: z.number().nullable(),
  fmrYear: z.number().nullable(),
  hudAreaCode: z.string().nullable(),
  hudAreaName: z.string().nullable(),
  medianIncome: z.number().nullable(),
  suggestedRent: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyDemographics = z.infer<typeof PropertyDemographicsSchema>

/////////////////////////////////////////
// PROPERTY FORECLOSURE INFO SCHEMA
/////////////////////////////////////////

export const PropertyForeclosureInfoSchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  foreclosureId: JsonValueSchema.nullable(),
  originalLoanAmount: z.number().nullable(),
  estimatedBankValue: z.number().nullable(),
  defaultAmount: z.number().nullable(),
  recordingDate: z.string().nullable(),
  openingBid: z.number().nullable(),
  auctionDate: z.string().nullable(),
  auctionTime: z.string().nullable(),
  auctionStreetAddress: z.string().nullable(),
  documentType: z.string().nullable(),
  trusteeSaleNumber: JsonValueSchema.nullable(),
  typeName: z.string().nullable(),
  active: z.boolean().nullable(),
  lenderName: z.string().nullable(),
  lenderPhone: z.string().nullable(),
  noticeType: z.string().nullable(),
  seqNo: z.number().int().nullable(),
  trusteeAddress: z.string().nullable(),
  trusteeName: z.string().nullable(),
  trusteePhone: z.string().nullable(),
  judgmentDate: z.string().nullable(),
  judgmentAmount: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyForeclosureInfo = z.infer<typeof PropertyForeclosureInfoSchema>

/////////////////////////////////////////
// PROPERTY MLS HISTORY SCHEMA
/////////////////////////////////////////

export const PropertyMlsHistorySchema = z.object({
  id: z.string(),
  propertyId: z.string(),
  organizationId: z.string(),
  mlsId: z.number().int().nullable(),
  type: z.string().nullable(),
  price: z.number().nullable(),
  beds: z.number().int().nullable(),
  baths: z.number().int().nullable(),
  daysOnMarket: z.number().int().nullable(),
  agentName: z.string().nullable(),
  agentOffice: z.string().nullable(),
  agentPhone: z.string().nullable(),
  agentEmail: z.string().nullable(),
  status: z.string().nullable(),
  statusDate: z.string().nullable(),
  lastStatusDate: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PropertyMlsHistory = z.infer<typeof PropertyMlsHistorySchema>

/////////////////////////////////////////
// TAG SCHEMA
/////////////////////////////////////////

export const TagSchema = z.object({
  id: z.string(),
  name: z.string(),
  color: z.string().nullable(),
  organizationId: z.string(),
  objectType: z.string(),
  createdBy: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Tag = z.infer<typeof TagSchema>

/////////////////////////////////////////
// TAG PERMISSION SCHEMA
/////////////////////////////////////////

export const TagPermissionSchema = z.object({
  role: TagRoleSchema,
  id: z.string(),
  tagId: z.string(),
  userId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type TagPermission = z.infer<typeof TagPermissionSchema>

/////////////////////////////////////////
// TASK SCHEMA
/////////////////////////////////////////

export const TaskSchema = z.object({
  status: TaskStatusSchema,
  priority: TaskPrioritySchema,
  id: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  createdById: z.string(),
  dueDate: z.coerce.date().nullable(),
  assigneeId: z.string().nullable(),
  relatedObjectId: z.string().nullable(),
  relatedObjectType: z.string().nullable(),
  organizationId: z.string(),
  position: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Task = z.infer<typeof TaskSchema>

/////////////////////////////////////////
// COLUMN PREFERENCE SCHEMA
/////////////////////////////////////////

export const ColumnPreferenceSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  column: z.string(),
  trackTimeInStatus: z.boolean(),
  showConfetti: z.boolean(),
  hidden: z.boolean(),
  targetTimeInStatus: z.number().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ColumnPreference = z.infer<typeof ColumnPreferenceSchema>

/////////////////////////////////////////
// ACCOUNT SCHEMA
/////////////////////////////////////////

export const AccountSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  providerId: z.string(),
  userId: z.string(),
  accessToken: z.string().nullable(),
  refreshToken: z.string().nullable(),
  idToken: z.string().nullable(),
  expiresAt: z.coerce.date().nullable(),
  password: z.string().nullable(),
  accessTokenExpiresAt: z.coerce.date().nullable(),
  refreshTokenExpiresAt: z.coerce.date().nullable(),
  scope: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Account = z.infer<typeof AccountSchema>

/////////////////////////////////////////
// USER ORGANIZATION CREDITS SCHEMA
/////////////////////////////////////////

export const UserOrganizationCreditsSchema = z.object({
  id: z.string(),
  userId: z.string(),
  organizationId: z.string(),
  creditsTotal: z.number().int(),
  creditsUsed: z.number().int(),
  creditsResetAt: z.coerce.date(),
  creditsPurchased: z.number().int(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type UserOrganizationCredits = z.infer<typeof UserOrganizationCreditsSchema>

/////////////////////////////////////////
// PASSKEY SCHEMA
/////////////////////////////////////////

export const PasskeySchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  publicKey: z.string(),
  userId: z.string(),
  credentialID: z.string(),
  counter: z.number().int(),
  deviceType: z.string(),
  backedUp: z.boolean(),
  transports: z.string().nullable(),
  createdAt: z.coerce.date().nullable(),
})

export type Passkey = z.infer<typeof PasskeySchema>

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  username: z.string().nullable(),
  role: z.string().nullable(),
  banned: z.boolean().nullable(),
  banReason: z.string().nullable(),
  banExpires: z.coerce.date().nullable(),
  onboardingComplete: z.boolean(),
  paymentsCustomerId: z.string().nullable(),
  locale: z.string().nullable(),
  modelId: z.string().nullable(),
  preferences: JsonValueSchema.nullable(),
  appearance: JsonValueSchema.nullable(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// USER VIEW PREFERENCE SCHEMA
/////////////////////////////////////////

export const UserViewPreferenceSchema = z.object({
  id: z.string(),
  userId: z.string(),
  organizationId: z.string(),
  objectType: z.string(),
  viewId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type UserViewPreference = z.infer<typeof UserViewPreferenceSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  id: z.string(),
  expiresAt: z.coerce.date(),
  ipAddress: z.string().nullable(),
  userAgent: z.string().nullable(),
  userId: z.string(),
  impersonatedBy: z.string().nullable(),
  activeOrganizationId: z.string().nullable(),
  token: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Session = z.infer<typeof SessionSchema>

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const VerificationSchema = z.object({
  id: z.string(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date().nullable(),
})

export type Verification = z.infer<typeof VerificationSchema>

/////////////////////////////////////////
// FORWARDED EMAIL SCHEMA
/////////////////////////////////////////

export const ForwardedEmailSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  messageId: z.string(),
  from: z.string(),
  to: z.string(),
  subject: z.string(),
  body: z.string(),
  attachments: JsonValueSchema.nullable(),
  headers: JsonValueSchema.nullable(),
  participants: z.string().array(),
  processedAt: z.coerce.date().nullable(),
  linkedRecords: JsonValueSchema.nullable(),
  forwardedBy: z.string(),
  sharingLevel: z.string(),
  isBlocked: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ForwardedEmail = z.infer<typeof ForwardedEmailSchema>

/////////////////////////////////////////
// FORWARDING EMAIL CONFIG SCHEMA
/////////////////////////////////////////

export const ForwardingEmailConfigSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  address: z.string(),
  isActive: z.boolean(),
  defaultSharingLevel: z.string(),
  individualSharing: JsonValueSchema.nullable(),
  blockedEmails: z.string().array(),
  blockedDomains: z.string().array(),
  autoCreateContacts: z.boolean(),
  autoCreateCompanies: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ForwardingEmailConfig = z.infer<typeof ForwardingEmailConfigSchema>

/////////////////////////////////////////
// ACTIVITY SCHEMA
/////////////////////////////////////////

export const ActivitySchema = z.object({
  type: ActivityTypeSchema,
  id: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  recordId: z.string().nullable(),
  recordType: z.string().nullable(),
  message: z.string(),
  resolved: z.boolean(),
  resolvedBy: z.string().nullable(),
  phone: z.string().nullable(),
  result: z.string().nullable(),
  system: z.boolean(),
  edited: z.boolean(),
  editedAt: z.coerce.date().nullable(),
  mentionedUsers: z.string().array(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Activity = z.infer<typeof ActivitySchema>

/////////////////////////////////////////
// ACTIVITY REPLY SCHEMA
/////////////////////////////////////////

export const ActivityReplySchema = z.object({
  id: z.string(),
  activityId: z.string(),
  userId: z.string(),
  message: z.string(),
  edited: z.boolean(),
  editedAt: z.coerce.date().nullable(),
  mentionedUsers: z.string().array(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ActivityReply = z.infer<typeof ActivityReplySchema>

/////////////////////////////////////////
// EMAIL ACTIVITY SCHEMA
/////////////////////////////////////////

export const EmailActivitySchema = z.object({
  sourceType: EmailSourceTypeSchema,
  direction: EmailDirectionSchema,
  id: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  sourceId: z.string().nullable(),
  messageId: z.string(),
  threadId: z.string().nullable(),
  inReplyTo: z.string().nullable(),
  references: z.string().array(),
  from: z.string(),
  to: z.string().array(),
  cc: z.string().array(),
  bcc: z.string().array(),
  subject: z.string(),
  body: z.string(),
  bodyPlain: z.string().nullable(),
  attachments: JsonValueSchema.nullable(),
  headers: JsonValueSchema.nullable(),
  timestamp: z.coerce.date(),
  isRead: z.boolean(),
  isImportant: z.boolean(),
  processedAt: z.coerce.date().nullable(),
  processingNotes: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type EmailActivity = z.infer<typeof EmailActivitySchema>

/////////////////////////////////////////
// ACTIVITY REACTION SCHEMA
/////////////////////////////////////////

export const ActivityReactionSchema = z.object({
  id: z.string(),
  activityId: z.string(),
  userId: z.string(),
  emoji: z.string(),
  createdAt: z.coerce.date(),
})

export type ActivityReaction = z.infer<typeof ActivityReactionSchema>
