import { initEdgeStore } from "@edgestore/server";

const es = initEdgeStore.create();

export const edgeStoreRouter = es.router({
	publicFiles: es.fileBucket().beforeDelete(() => {
		return true;
	}),
	avatars: es.imageBucket({
		maxSize: 1024 * 1024 * 4, // 4MB
		accept: ['image/jpeg', 'image/png'],
	}).beforeDelete(() => {
		return true;
	}),
});

export type EdgeStoreRouter = typeof edgeStoreRouter;

export type CreateBucketHandler = (
	name: string,
	options?: {
		public?: boolean;
	},
) => Promise<void>;

export type GetSignedUploadUrlHandler = (
	path: string,
	options: {
		bucket: string;
	},
) => Promise<string>;

export type GetSignedUrlHander = (
	path: string,
	options: {
		bucket: string;
		expiresIn?: number;
	},
) => Promise<string>;
