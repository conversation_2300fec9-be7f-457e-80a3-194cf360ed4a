{"dependencies": {"@aws-sdk/client-s3": "3.437.0", "@aws-sdk/s3-request-presigner": "3.437.0", "@edgestore/react": "^0.5.2", "@edgestore/server": "^0.5.2", "@repo/config": "workspace:*", "@repo/logs": "workspace:*", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.13.10"}, "main": "./index.ts", "name": "@repo/storage", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.ts", "version": "0.0.0"}