# Email Forwarding Setup Guide

This guide will help you set up email forwarding for your Relio CRM, allowing users to forward emails directly into the CRM to automatically create and link contacts.

## Overview

The email forwarding system allows organization members to:
- Forward emails from their registered email addresses to `{organization-slug}@inbox.reliocrm.com`
- Automatically create contacts from email participants
- Link emails to existing contacts based on email addresses
- Use plus addressing for categorization (e.g., `<EMAIL>`)

## Prerequisites

1. **Domain Configuration**: You'll need access to DNS settings for `reliocrm.com`
2. **Email Service Provider**: A service like Mailgun, Postmark, or similar that supports inbound email processing
3. **Database**: The ForwardedEmail model must be added to your database (see migration steps below)

## Setup Steps

### 1. Database Migration

First, ensure the ForwardedEmail model is added to your database:

```bash
cd packages/database
npx prisma generate
npx prisma db push
```

### 2. DNS Configuration

Set up MX records for the `inbox.reliocrm.com` subdomain:

```
inbox.reliocrm.com MX 10 mxa.mailgun.org
inbox.reliocrm.com MX 10 mxb.mailgun.org
```

*Note: Replace with your email provider's MX records*

### 3. Email Provider Configuration

#### Development Setup (Using ngrok):

For local development, you'll need to make your localhost publicly accessible:

1. **Install ngrok**:
   ```bash
   # Install ngrok
   npm install -g ngrok
   # OR
   brew install ngrok  # macOS
   ```

2. **Expose your local server**:
   ```bash
   # In a separate terminal, expose port 3000
   ngrok http 3000
   ```
   
   This will give you a public URL like: `https://abc123.ngrok.io`

3. **Configure Mailgun with your ngrok URL**:
   ```bash
  curl -s --user 'api:**************************************************' \
   https://api.mailgun.net/v3/routes \
   -F priority=0 \
   -F description="CRM Email Forwarding" \
   -F expression="match_recipient('.*@inbox.reliocrm.com')" \
   -F action="forward('https://b444-2603-8080-bc00-efa8-1947-1567-5843-4423.ngrok-free.app/api/inbound-email')" \
   -F action="stop()"
   ```

   **⚠️ Important**: Replace `abc123.ngrok.io` with your actual ngrok URL!

#### Production Setup:

For production, use your actual domain:

```bash
curl -s --user 'api:**************************************************' \
https://api.mailgun.net/v3/routes \
-F priority=0 \
-F description="CRM Email Forwarding" \
-F expression="match_recipient('.*@inbox.reliocrm.com')" \
-F action="forward('https://reliocrm.com/api/inbound-email')" \
-F action="stop()"
```

#### Alternative Development Tools:

1. **Cloudflare Tunnel (free)**:
   ```bash
   # Install cloudflared
   brew install cloudflared  # macOS
   
   # Create tunnel
   cloudflared tunnel --url http://localhost:3000
   ```

2. **LocalTunnel**:
   ```bash
   npm install -g localtunnel
   lt --port 3000
   ```

3. **Serveo** (no installation):
   ```bash
   ssh -R 80:localhost:3000 serveo.net
   ```

### 4. Environment Variables

Add these to your `.env` file:

```env
# Optional: For webhook signature verification
MAILGUN_WEBHOOK_SIGNING_KEY=your_signing_key

# If using Mailgun for outbound emails too
MAILGUN_API_KEY=your_api_key
MAILGUN_DOMAIN=your_domain
```

### 5. Test the Setup

#### Verify Your Webhook is Accessible:

1. **Test the endpoint directly**:
   ```bash
   # Test with your actual ngrok URL
   curl -X POST https://b444-2603-8080-bc00-efa8-1947-1567-5843-4423.ngrok-free.app/api/inbound-email \
     -F "recipient=<EMAIL>" \
     -F "sender=<EMAIL>" \
     -F "subject=Test Email" \
     -F "body-plain=This is a test email" \
     -F "message-id=test-$(date +%s)"
   ```

2. **Check your application logs** for the test request

3. **Verify Mailgun route is active**:
   ```bash
   # List all routes to verify yours was created
   curl -s --user 'api:**************************************************' \
   https://api.mailgun.net/v3/routes
   ```

#### Full Email Test:

1. **Verify DNS**: Use `dig inbox.reliocrm.com MX` to confirm MX records
2. **Test Email Processing**: 
   - Have an organization member forward an email to `{org-slug}@inbox.reliocrm.com`
   - Check your logs for successful processing
   - Verify contacts were created/linked in the CRM

#### Common Webhook Issues:

- **Endpoint not reachable**: Ensure your ngrok/tunnel is running
- **SSL certificate errors**: Use `https://` URLs for production
- **Timeout errors**: Your endpoint should respond within 15 seconds
- **Authentication errors**: Verify the sender is an organization member

## Usage

### For Organization Members:

1. **Find Your Forwarding Address**:
   - Go to Settings > Account > Email & Calendar
   - Copy your organization's forwarding address: `{org-slug}@inbox.reliocrm.com`

2. **Forward Emails**:
   - Forward or BCC emails to the forwarding address
   - Only emails from registered organization members will be processed

3. **Use Plus Addressing** (Optional):
   - `{org-slug}+<EMAIL>` for deal-related emails
   - `{org-slug}+<EMAIL>` for lead generation
   - Any text after the `+` is for your own organization

### Security Features:

- **Member Verification**: Only emails from organization members are processed
- **Duplicate Prevention**: Each email is processed only once based on message ID
- **Contact Matching**: Automatic linking to existing contacts or creation of new ones

## Monitoring and Troubleshooting

### Check Email Processing:

```bash
# View forwarded emails for an organization
curl "https://your-app.com/api/forwarded-emails?organizationId=YOUR_ORG_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Common Issues:

1. **Emails Not Processing**:
   - Verify sender is an organization member
   - Check webhook URL is accessible
   - Verify DNS configuration

2. **Contacts Not Being Created**:
   - Check application logs for errors
   - Verify database permissions
   - Ensure email addresses are valid

3. **Duplicate Processing**:
   - System prevents duplicates via message ID
   - Check logs if issues persist

### Logs to Monitor:

- Successful email processing: `Successfully processed email {messageId}`
- Authorization failures: `Unauthorized forwarding attempt`
- Processing errors: `Inbound email processing failed`

## Advanced Configuration

### Custom Email Domains

To use a custom domain instead of `inbox.reliocrm.com`:

1. Update the domain check in `/api/inbound-email/route.ts`
2. Configure DNS for your custom domain
3. Update the ForwardingAddressSection component

### Attachment Handling

The system currently logs attachments but doesn't store them. To add attachment storage:

1. Configure your storage provider (EdgeStore, S3, etc.)
2. Update the attachment processing in the inbound email handler
3. Link attachments to contact records

### Custom Contact Creation Rules

Modify the contact creation logic in `packages/api/src/lib/email-forwarding.ts`:

- Customize name parsing from email addresses
- Add company domain detection
- Implement custom field mappings

## API Reference

### Inbound Email Endpoint

```
POST /api/inbound-email
Content-Type: multipart/form-data

Expected fields:
- recipient: The forwarding address
- sender: Sender's email address
- subject: Email subject
- body-plain: Plain text body
- body-html: HTML body (optional)
- message-id: Unique message identifier
- timestamp: Email timestamp
- attachment-count: Number of attachments
- attachment-1, attachment-2, etc.: Attachment files
```

### Response:

```json
{
  "success": true,
  "emailId": "forwarded-email-id",
  "linkedRecords": {
    "contacts": ["contact-id-1", "contact-id-2"],
    "companies": []
  }
}
```

## Support

For issues or questions:
1. Check the application logs
2. Verify your configuration against this guide
3. Test with a simple email first
4. Contact support if issues persist 