{"name": "relio", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "2.0.5", "@repo/tsconfig": "workspace:*", "@types/node": "22.13.10", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "dotenv-cli": "^8.0.0", "prettier": "3.6.0", "ts-node": "^10.9.2", "turbo": "^2.4.4", "typescript": "5.8.2", "ultracite": "5.0.12"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}, "dependencies": {"@hono/zod-validator": "^0.7.0", "axios": "^1.9.0", "dotenv": "^16.4.7", "keyv": "^5.3.4", "mongodb": "^6.17.0"}}